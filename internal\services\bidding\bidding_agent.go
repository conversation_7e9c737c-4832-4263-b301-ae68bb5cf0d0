package bidding

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"github.com/sirupsen/logrus"
)

// BiddingAgent 招投标Agent实现
type BiddingAgent struct {
	*core.BaseAgent
	templateService core.AgentTemplate
}

// NewBiddingAgent 创建招投标Agent
func NewBiddingAgent(id string, llmClient llm.LLMClient, templateService core.AgentTemplate) *BiddingAgent {
	capabilities := []models.AgentCapability{
		{
			Name:        "generate_summary",
			Description: "生成招投标项目摘要",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"tender_data": map[string]interface{}{
						"type":        "object",
						"description": "招投标原始数据",
					},
				},
				"required": []string{"tender_data"},
			},
			OutputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"project_overview": map[string]interface{}{
						"type": "string",
						"description": "项目概述",
					},
					"main_requirements": map[string]interface{}{
						"type": "string",
						"description": "主要需求",
					},
					"technical_requirements": map[string]interface{}{
						"type": "string",
						"description": "技术要求",
					},
					"contract_info": map[string]interface{}{
						"type": "string",
						"description": "合同信息",
					},
					"deadline": map[string]interface{}{
						"type": "string",
						"description": "截止时间",
					},
					"budget": map[string]interface{}{
						"type": "string",
						"description": "预算范围",
					},
				},
			},
			Required: []string{"tender_data"},
		},
		{
			Name:        "analyze_background",
			Description: "分析招投标项目背景",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"summary": map[string]interface{}{
						"type":        "object",
						"description": "项目摘要",
					},
					"search_results": map[string]interface{}{
						"type":        "array",
						"description": "搜索结果",
					},
					"requirements_analysis": map[string]interface{}{
						"type":        "string",
						"description": "需求分析结果",
					},
				},
				"required": []string{"summary"},
			},
			OutputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"background_analysis": map[string]interface{}{
						"type": "string",
						"description": "项目背景分析",
					},
					"technical_analysis": map[string]interface{}{
						"type": "string",
						"description": "技术需求分析",
					},
					"market_analysis": map[string]interface{}{
						"type": "string",
						"description": "市场环境分析",
					},
					"competitor_analysis": map[string]interface{}{
						"type": "string",
						"description": "竞争对手分析",
					},
					"risk_assessment": map[string]interface{}{
						"type": "string",
						"description": "风险和机会评估",
					},
				},
			},
			Required: []string{"summary"},
		},
		{
			Name:        "extract_requirements",
			Description: "提取和分析项目需求",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"summary": map[string]interface{}{
						"type":        "object",
						"description": "项目摘要",
					},
				},
				"required": []string{"summary"},
			},
			OutputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"functional_requirements": map[string]interface{}{
						"type": "string",
						"description": "功能需求",
					},
					"technical_requirements": map[string]interface{}{
						"type": "string",
						"description": "技术需求",
					},
					"business_requirements": map[string]interface{}{
						"type": "string",
						"description": "业务需求",
					},
					"compliance_requirements": map[string]interface{}{
						"type": "string",
						"description": "合规需求",
					},
				},
			},
			Required: []string{"summary"},
		},
	}

	baseAgent := core.NewBaseAgent(id, models.AgentTypeBidding, "招投标Agent", "专业的招投标项目分析Agent", capabilities, llmClient)
	
	return &BiddingAgent{
		BaseAgent:       baseAgent,
		templateService: templateService,
	}
}

// Execute 执行Agent能力
func (ba *BiddingAgent) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	start := time.Now()
	requestID := fmt.Sprintf("req_%d", time.Now().UnixNano())
	responseID := fmt.Sprintf("resp_%d", time.Now().UnixNano())

	// 验证输入
	if err := ba.ValidateInput(capability, input); err != nil {
		return &models.AgentResponse{
			ID:        responseID,
			RequestID: requestID,
			Success:   false,
			Error:     fmt.Sprintf("输入验证失败: %v", err),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}

	ba.Logger.WithFields(logrus.Fields{
		"agent_id":   ba.ID,
		"capability": capability,
		"request_id": requestID,
	}).Info("开始执行招投标Agent能力")

	var output map[string]interface{}
	var err error

	switch capability {
	case "generate_summary":
		output, err = ba.generateSummary(ctx, input, config)
	case "analyze_background":
		output, err = ba.analyzeBackground(ctx, input, config)
	case "extract_requirements":
		output, err = ba.extractRequirements(ctx, input, config)
	default:
		err = fmt.Errorf("未知的能力: %s", capability)
	}

	success := err == nil
	errorMsg := ""
	if err != nil {
		errorMsg = err.Error()
		ba.Logger.WithError(err).WithFields(logrus.Fields{
			"agent_id":   ba.ID,
			"capability": capability,
			"request_id": requestID,
		}).Error("执行Agent能力失败")
	} else {
		ba.Logger.WithFields(logrus.Fields{
			"agent_id":   ba.ID,
			"capability": capability,
			"request_id": requestID,
			"duration":   time.Since(start).Milliseconds(),
		}).Info("执行Agent能力成功")
	}

	return &models.AgentResponse{
		ID:        responseID,
		RequestID: requestID,
		Success:   success,
		Output:    output,
		Error:     errorMsg,
		Duration:  time.Since(start).Milliseconds(),
		CreatedAt: time.Now(),
	}, nil
}

// generateSummary 生成招投标摘要
func (ba *BiddingAgent) generateSummary(ctx context.Context, input map[string]interface{}, config map[string]interface{}) (map[string]interface{}, error) {
	// 获取模板
	systemPrompt, userPrompt, err := ba.templateService.RenderPrompt(ctx, "bidding_summary", input)
	if err != nil {
		return nil, fmt.Errorf("渲染提示词失败: %w", err)
	}

	// 调用LLM
	response, err := ba.LLMClient.Chat(ctx, &llm.ChatRequest{
		Model: ba.GetConfigString("model", "deepseek-v3-250324"),
		Messages: []llm.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Temperature: float32(ba.GetConfigInt("temperature", 1)) / 10.0,
		MaxTokens:   ba.GetConfigInt("max_tokens", 2000),
	})
	if err != nil {
		return nil, fmt.Errorf("LLM调用失败: %w", err)
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &result); err != nil {
		// 如果JSON解析失败，返回原始文本
		result = map[string]interface{}{
			"raw_output": response.Choices[0].Message.Content,
		}
	}

	return result, nil
}

// analyzeBackground 分析项目背景
func (ba *BiddingAgent) analyzeBackground(ctx context.Context, input map[string]interface{}, config map[string]interface{}) (map[string]interface{}, error) {
	// 获取模板
	systemPrompt, userPrompt, err := ba.templateService.RenderPrompt(ctx, "bidding_analysis", input)
	if err != nil {
		return nil, fmt.Errorf("渲染提示词失败: %w", err)
	}

	// 调用LLM
	response, err := ba.LLMClient.Chat(ctx, &llm.ChatRequest{
		Model: ba.GetConfigString("model", "deepseek-v3-250324"),
		Messages: []llm.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Temperature: float32(ba.GetConfigInt("temperature", 2)) / 10.0,
		MaxTokens:   ba.GetConfigInt("max_tokens", 4000),
	})
	if err != nil {
		return nil, fmt.Errorf("LLM调用失败: %w", err)
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &result); err != nil {
		// 如果JSON解析失败，返回原始文本
		result = map[string]interface{}{
			"analysis": response.Choices[0].Message.Content,
		}
	}

	return result, nil
}

// extractRequirements 提取需求
func (ba *BiddingAgent) extractRequirements(ctx context.Context, input map[string]interface{}, config map[string]interface{}) (map[string]interface{}, error) {
	// 构建专门的需求提取提示词
	systemPrompt := `你是一个专业的需求分析专家。请从招投标项目摘要中提取和分析各类需求。

请按照以下结构输出JSON格式的结果：
{
  "functional_requirements": "功能需求描述",
  "technical_requirements": "技术需求描述", 
  "business_requirements": "业务需求描述",
  "compliance_requirements": "合规需求描述"
}`

	// 构建用户提示词
	summaryData, _ := json.Marshal(input["summary"])
	userPrompt := fmt.Sprintf(`请分析以下项目摘要，提取各类需求：

项目摘要：
%s

请严格按照JSON格式输出需求分析结果。`, string(summaryData))

	// 调用LLM
	response, err := ba.LLMClient.Chat(ctx, &llm.ChatRequest{
		Model: ba.GetConfigString("model", "deepseek-v3-250324"),
		Messages: []llm.Message{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		Temperature: 0.1,
		MaxTokens:   2000,
	})
	if err != nil {
		return nil, fmt.Errorf("LLM调用失败: %w", err)
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &result); err != nil {
		// 如果JSON解析失败，返回原始文本
		result = map[string]interface{}{
			"requirements": response.Choices[0].Message.Content,
		}
	}

	return result, nil
}