package models

import (
	"time"
)

// A2ARequest represents a standard A2A protocol request
type A2ARequest struct {
	JsonRPC string      `json:"jsonrpc" validate:"required,eq=2.0"`
	ID      string      `json:"id" validate:"required"`
	Method  string      `json:"method" validate:"required"`
	Params  A2AParams   `json:"params" validate:"required"`
}

// A2AParams represents the parameters section of an A2A request
type A2AParams struct {
	SkillID string                 `json:"skill_id" validate:"required"`
	Input   map[string]interface{} `json:"input" validate:"required"`
	Context A2AContext             `json:"context"`
}

// A2AContext represents the context information for an A2A request
type A2AContext struct {
	UserID     string `json:"user_id"`
	CompanyID  string `json:"company_id"`
	TraceID    string `json:"trace_id"`
	Language   string `json:"language"`
	RequestID  string `json:"request_id"`
	Timestamp  time.Time `json:"timestamp"`
}

// A2AResponse represents a standard A2A protocol response
type A2AResponse struct {
	JsonRPC string      `json:"jsonrpc"`
	ID      string      `json:"id"`
	Result  *A2AResult  `json:"result,omitempty"`
	Error   *A2AError   `json:"error,omitempty"`
}

// A2AResult represents the result section of a successful A2A response
type A2AResult struct {
	TaskID   string                 `json:"task_id"`
	Status   string                 `json:"status"`
	Output   map[string]interface{} `json:"output"`
	Metadata A2AMetadata            `json:"metadata"`
}

// A2AMetadata represents metadata about the execution
type A2AMetadata struct {
	ExecutionTime int    `json:"execution_time"` // milliseconds
	TokensUsed    int    `json:"tokens_used"`
	ModelVersion  string `json:"model_version,omitempty"`
	AgentVersion  string `json:"agent_version"`
}

// A2AError represents an error in A2A protocol
type A2AError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// AgentCard represents an Agent's capabilities and metadata
type AgentCard struct {
	Name            string            `json:"name"`
	Description     string            `json:"description"`
	URL             string            `json:"url"`
	Provider        AgentProvider     `json:"provider"`
	Version         string            `json:"version"`
	DocumentationURL string           `json:"documentationUrl,omitempty"`
	Capabilities    AgentCapabilities `json:"capabilities"`
	Authentication  AgentAuth         `json:"authentication"`
	DefaultInputModes  []string       `json:"defaultInputModes"`
	DefaultOutputModes []string       `json:"defaultOutputModes"`
	Skills          []AgentSkill      `json:"skills"`
}

// AgentProvider represents the provider information of an agent
type AgentProvider struct {
	Organization string `json:"organization"`
	URL          string `json:"url,omitempty"`
}

// AgentCapabilities represents what the agent can do
type AgentCapabilities struct {
	Streaming               bool `json:"streaming"`
	PushNotifications       bool `json:"pushNotifications"`
	StateTransitionHistory  bool `json:"stateTransitionHistory"`
}

// AgentAuth represents authentication configuration
type AgentAuth struct {
	Schemes []string `json:"schemes"`
}

// AgentSkill represents a specific capability of an agent
type AgentSkill struct {
	ID           string      `json:"id"`
	Name         string      `json:"name"`
	Description  string      `json:"description"`
	Tags         []string    `json:"tags"`
	Examples     []string    `json:"examples,omitempty"`
	InputModes   []string    `json:"inputModes"`
	OutputModes  []string    `json:"outputModes"`
	InputSchema  interface{} `json:"inputSchema"`
	OutputSchema interface{} `json:"outputSchema"`
}

// AgentStatus represents the current status of an agent
type AgentStatus struct {
	Status       string                 `json:"status"`
	Version      string                 `json:"version"`
	Uptime       int64                  `json:"uptime"`
	Dependencies map[string]string      `json:"dependencies"`
	Performance  AgentPerformance       `json:"performance"`
	LastUpdated  time.Time              `json:"last_updated"`
}

// AgentPerformance represents performance metrics of an agent
type AgentPerformance struct {
	AvgResponseTime    int     `json:"avg_response_time"`
	RequestsPerMinute  int     `json:"requests_per_minute"`
	ErrorRate          float64 `json:"error_rate"`
	SuccessRate        float64 `json:"success_rate"`
}

// RegisteredAgent represents an agent registered in the system
type RegisteredAgent struct {
	ID          string    `json:"id"`
	AgentCard   AgentCard `json:"agent_card"`
	Status      AgentStatus `json:"status"`
	RegisterTime time.Time `json:"register_time"`
	LastSeen    time.Time `json:"last_seen"`
	Endpoints   []string  `json:"endpoints"`
}

// HealthStatus represents the health status of an agent
type HealthStatus struct {
	Status       string                 `json:"status"`
	Version      string                 `json:"version"`
	Uptime       int64                  `json:"uptime"`
	Dependencies map[string]string      `json:"dependencies"`
	Performance  AgentPerformance       `json:"performance"`
	Timestamp    time.Time              `json:"timestamp"`
}

// Standard A2A error codes
const (
	// JSON-RPC 2.0 standard error codes
	ErrorCodeParseError     = -32700
	ErrorCodeInvalidRequest = -32600
	ErrorCodeMethodNotFound = -32601
	ErrorCodeInvalidParams  = -32602
	ErrorCodeInternalError  = -32603

	// Business logic error codes
	ErrorCodeBusinessLogic  = -40001
	ErrorCodeAIService      = -40002
	ErrorCodeDataNotFound   = -40003
	ErrorCodeValidation     = -40004
	ErrorCodeTimeout        = -40005
	ErrorCodeRateLimit      = -40006
)

// Standard agent statuses
const (
	AgentStatusHealthy     = "healthy"
	AgentStatusUnhealthy   = "unhealthy"
	AgentStatusUnavailable = "unavailable"
	AgentStatusStarting    = "starting"
	AgentStatusStopping    = "stopping"
)

// Standard task statuses
const (
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusCancelled = "cancelled"
)