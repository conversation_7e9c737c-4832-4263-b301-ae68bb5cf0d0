// 主要存放工具的具体逻辑实现
package bidding

import (
	"fmt"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ReportSummaryService 提供报告总结功能
type ReportSummaryService struct {
	llmClient     llm.LLMClient
	promptManager *prompts.PromptManager
}

// NewReportSummaryService 创建 ReportSummaryService 实例
func NewReportSummaryService(llmClient llm.LLMClient, pm *prompts.PromptManager) *ReportSummaryService {
	return &ReportSummaryService{
		llmClient:     llmClient,
		promptManager: pm,
	}
}

// Summarize 处理报告总结请求
func (s *ReportSummaryService) Summarize(req models.ReportSummaryRequest) (*models.ReportSummaryResponse, error) {
	promptSet, err := s.promptManager.GetPromptSet("report_summary")
	if err != nil {
		return nil, fmt.Errorf("获取 report_summary 的 prompts 失败: %w", err)
	}

	// 准备 prompt 格式化所需的数据
	promptData := map[string]interface{}{
		"language":       req.Language,
		"target_company": req.TargetCompany,
		"text":           req.Text,
	}
	if req.PromptArgs != nil {
		for k, v := range req.PromptArgs {
			promptData[k] = v
		}
	}

	sysContent, err := s.promptManager.FormatPrompt(promptSet.SysPrompt, promptData)
	if err != nil {
		return nil, fmt.Errorf("格式化 system prompt 失败: %w", err)
	}
	userContent, err := s.promptManager.FormatPrompt(promptSet.UserPrompt, promptData)
	if err != nil {
		return nil, fmt.Errorf("格式化 user prompt 失败: %w", err)
	}

	messages := []common.LLMMessage{
		{Role: "system", Content: sysContent},
		{Role: "user", Content: userContent},
	}

	llmParams := models.OpenAICompatibleRequestParams{
		Model:            req.ModelAlias, // 服务层可以传递模型别名，如果为空，LLM 客户端将使用其默认模型
		Messages:         messages,
		Temperature:      req.Temperature,
		MaxTokens:        req.MaxTokens,
		FrequencyPenalty: req.FrequencyPenalty,
		PresencePenalty:  req.PresencePenalty,
		// ProviderSpecificParams: map[string]interface{}{ ... } // 如果有其他参数
	}

	utils.Log.Debugf("System Prompt: %s", sysContent)
	utils.Log.Debugf("User Prompt: %s", userContent)

	result, err := s.llmClient.ChatCompletions(llmParams)
	if err != nil {
		utils.Log.Errorf("LLM ChatCompletions 调用失败: %v", err)
		return nil, fmt.Errorf("LLM 交互失败: %w", err)
	}

	return &models.ReportSummaryResponse{Result: result}, nil
}
