package setup

import (
	"context"

	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/interfaces"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services/chat"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ChatModule 聊天模块
type ChatModule struct {
	// Services
	SocketConnectionService *chat.SocketConnectionService
	ChatSessionService      *chat.ChatSessionService
	ChatMessageService      *chat.ChatMessageService
	ChatAgentService        *chat.ChatAgentService
	ChatCleanupService      *chat.ChatCleanupService

	// Handler
	ChatHandler *handlers.ChatHandler

	// Config
	Config *models.ChatConfig
}

// SetupChatModule 设置聊天模块
func SetupChatModule(
	cfg *config.Config,
	db store.DBTX,
	llmClient llm.LLMClient,
	tokenService interfaces.TokenServiceInterface,
) (*ChatModule, error) {
	utils.Log.Info("Setting up chat module...")

	// 转换配置
	chatConfig := &models.ChatConfig{
		MaxSocketConnections:  cfg.Chat.MaxSocketConnections,
		SessionTimeoutMinutes: cfg.Chat.SessionTimeoutMinutes,
		DefaultTokenLimit:     cfg.Chat.DefaultTokenLimit,
		CleanupIntervalHours:  cfg.Chat.CleanupIntervalHours,
		PingIntervalSeconds:   cfg.Chat.PingIntervalSeconds,
		SystemPrompt:          cfg.Chat.SystemPrompt,
		ModelProvider:         cfg.Chat.ModelProvider,
		ModelAlias:            cfg.Chat.ModelAlias,
		Temperature:           cfg.Chat.Temperature,
		MaxTokens:             cfg.Chat.MaxTokens,
		EnableTokenTracking:   cfg.Chat.EnableTokenTracking,
	}

	// 初始化服务
	socketConnectionService := chat.NewSocketConnectionService(db, chatConfig)
	chatSessionService := chat.NewChatSessionService(db, chatConfig)
	chatMessageService := chat.NewChatMessageService(db, chatConfig)

	chatAgentService := chat.NewChatAgentService(
		llmClient,
		chatSessionService,
		chatMessageService,
		tokenService,
		chatConfig,
	)

	chatCleanupService := chat.NewChatCleanupService(
		chatSessionService,
		socketConnectionService,
		chatMessageService,
		chatConfig,
	)

	// 初始化处理器
	chatHandler := handlers.NewChatHandler(
		chatAgentService,
		chatSessionService,
		socketConnectionService,
		chatCleanupService,
		chatConfig,
	)

	module := &ChatModule{
		SocketConnectionService: socketConnectionService,
		ChatSessionService:      chatSessionService,
		ChatMessageService:      chatMessageService,
		ChatAgentService:        chatAgentService,
		ChatCleanupService:      chatCleanupService,
		ChatHandler:             chatHandler,
		Config:                  chatConfig,
	}

	utils.Log.Info("Chat module setup completed")
	return module, nil
}

// Start 启动聊天模块
func (m *ChatModule) Start(ctx context.Context) error {
	utils.Log.Info("Starting chat module services...")

	// 启动清理服务
	if err := m.ChatCleanupService.Start(ctx); err != nil {
		utils.Log.Errorf("Failed to start chat cleanup service: %v", err)
		return err
	}

	utils.Log.Info("Chat module services started successfully")
	return nil
}

// Stop 停止聊天模块
func (m *ChatModule) Stop() error {
	utils.Log.Info("Stopping chat module services...")

	// 停止清理服务
	if err := m.ChatCleanupService.Stop(); err != nil {
		utils.Log.Errorf("Failed to stop chat cleanup service: %v", err)
		return err
	}

	utils.Log.Info("Chat module services stopped successfully")
	return nil
}
