package agents

import (
	"gitlab.com/specific-ai/taskd/internal/interfaces"
	biddingInterfaces "gitlab.com/specific-ai/taskd/internal/modules/bidding/interfaces"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"go.mongodb.org/mongo-driver/mongo"
)

// AgentRegistryManager 管理所有预处理Agent的注册
type AgentRegistryManager struct {
	registry biddingInterfaces.AgentRegistry
}

// NewAgentRegistryManager 创建Agent注册管理器
func NewAgentRegistryManager(registry biddingInterfaces.AgentRegistry) *AgentRegistryManager {
	return &AgentRegistryManager{
		registry: registry,
	}
}

// RegisterAllPreprocessingAgents 注册所有预处理Agent
func (m *AgentRegistryManager) RegisterAllPreprocessingAgents(
	llmService interfaces.LLMServiceInterface,
	mongoClient *mongo.Client,
	databaseName, collectionName string,
) error {
	utils.Log.Info("Starting registration of all preprocessing agents")

	// 注意：由于循环依赖问题，agent创建逻辑移至更高层级
	// promptManager和agent实例化由调用方负责

	utils.Log.Info("Successfully registered all preprocessing agents")
	return nil
}

// GetRegisteredAgentsList 获取已注册Agent列表
func (m *AgentRegistryManager) GetRegisteredAgentsList() []AgentInfo {
	agents := m.registry.ListAgents()
	var agentInfos []AgentInfo

	for _, agent := range agents {
		agentInfos = append(agentInfos, AgentInfo{
			ID:       agent.ID,
			Name:     agent.AgentCard.Name,
			URL:      agent.AgentCard.URL,
			Status:   agent.Status.Status,
			Skills:   agent.AgentCard.Skills,
			Provider: agent.AgentCard.Provider.Organization,
		})
	}

	return agentInfos
}

// AgentInfo Agent信息结构
type AgentInfo struct {
	ID       string                     `json:"id"`
	Name     string                     `json:"name"`
	URL      string                     `json:"url"`
	Status   string                     `json:"status"`
	Skills   []biddingModels.AgentSkill `json:"skills"`
	Provider string                     `json:"provider"`
}

// GetAgentStats 获取Agent统计信息
func (m *AgentRegistryManager) GetAgentStats() map[string]interface{} {
	return m.registry.GetAgentStats()
}
