package tender_preprocess

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// EnhancementAgent 内容增强Agent
type EnhancementAgent struct {
	*agents.BaseAgent
	llmService services.LLMService
}

// NewEnhancementAgent 创建内容增强Agent
func NewEnhancementAgent(llmService services.LLMService) *EnhancementAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentEnhancementName,
		Description: "对招投标内容进行增强处理，包括关键词提取、标题优化、质量评分和摘要生成",
		URL:         "http://taskd-service:8601/agents/tender-content-enhancement",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillEnhanceContent,
				Name:        "内容增强处理",
				Description: "提取关键词、优化标题、计算质量评分并生成智能摘要",
				Tags:        []string{"enhancement", "ai", "keywords", "scoring", "summary"},
				Examples: []string{
					"为政府采购项目生成优化标题和关键词",
					"计算招投标项目的五维度质量评分",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentEnhancementName, agentCard)

	return &EnhancementAgent{
		BaseAgent:  baseAgent,
		llmService: llmService,
	}
}

// ExecuteSkill 执行技能
func (a *EnhancementAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillEnhanceContent:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.enhanceContentHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// enhanceContentHandler 内容增强处理器
func (a *EnhancementAgent) enhanceContentHandler(input map[string]interface{}, a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseEnhancementParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting content enhancement")

	// 验证语言
	if !agentCommon.ValidateLanguage(params.Language) {
		return nil, fmt.Errorf("unsupported language: %s", params.Language)
	}

	// 执行内容增强
	result, metadata, err := a.performEnhancement(params, a2aContext)
	if err != nil {
		return nil, fmt.Errorf("content enhancement failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"keywords":            result.Keywords,
		"optimized_title":     result.OptimizedTitle,
		"quality_score":       result.QualityScore,
		"summary":             result.Summary,
		"processing_metadata": metadata,
	}

	return output, nil
}

// EnhancementParams 内容增强参数
type EnhancementParams struct {
	ExtractedData      *agentCommon.TenderData           `json:"extracted_data"`
	ClassificationData *agentCommon.ClassificationResult `json:"classification_data"`
	EnhancementOptions *EnhancementOptions               `json:"enhancement_options"`
	Language           string                            `json:"language"`
}

// EnhancementOptions 增强选项
type EnhancementOptions struct {
	GenerateKeywords bool `json:"generate_keywords"`
	OptimizeTitle    bool `json:"optimize_title"`
	CalculateScore   bool `json:"calculate_score"`
	GenerateSummary  bool `json:"generate_summary"`
}

// parseEnhancementParams 解析增强参数
func (a *EnhancementAgent) parseEnhancementParams(input map[string]interface{}) (*EnhancementParams, error) {
	// 解析extracted_data
	extractedDataInterface, ok := input["extracted_data"]
	if !ok {
		return nil, fmt.Errorf("extracted_data is required")
	}

	extractedDataBytes, err := json.Marshal(extractedDataInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal extracted_data: %v", err)
	}

	var extractedData agentCommon.TenderData
	if err := json.Unmarshal(extractedDataBytes, &extractedData); err != nil {
		return nil, fmt.Errorf("failed to parse extracted_data: %v", err)
	}

	// 解析classification_data
	classificationDataInterface, ok := input["classification_data"]
	if !ok {
		return nil, fmt.Errorf("classification_data is required")
	}

	classificationDataBytes, err := json.Marshal(classificationDataInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal classification_data: %v", err)
	}

	var classificationData agentCommon.ClassificationResult
	if err := json.Unmarshal(classificationDataBytes, &classificationData); err != nil {
		return nil, fmt.Errorf("failed to parse classification_data: %v", err)
	}

	// 解析enhancement_options（可选）
	var enhancementOptions EnhancementOptions
	if optionsInterface, ok := input["enhancement_options"]; ok {
		optionsBytes, err := json.Marshal(optionsInterface)
		if err == nil {
			json.Unmarshal(optionsBytes, &enhancementOptions)
		}
	} else {
		// 默认启用所有选项
		enhancementOptions = EnhancementOptions{
			GenerateKeywords: true,
			OptimizeTitle:    true,
			CalculateScore:   true,
			GenerateSummary:  true,
		}
	}

	language, _ := input["language"].(string)
	if language == "" {
		language = agentCommon.LanguageChinese
	}

	return &EnhancementParams{
		ExtractedData:      &extractedData,
		ClassificationData: &classificationData,
		EnhancementOptions: &enhancementOptions,
		Language:           language,
	}, nil
}

// performEnhancement 执行内容增强
func (a *EnhancementAgent) performEnhancement(params *EnhancementParams, a2aContext biddingModels.A2AContext) (*agentCommon.EnhancementResult, *agentCommon.ProcessingMetadata, error) {
	startTime := time.Now()

	// 构建增强上下文
	contextText := a.buildEnhancementContext(params.ExtractedData, params.ClassificationData)

	// 构建增强prompt
	systemPrompt := a.getEnhancementSystemPrompt(params.Language, params.EnhancementOptions)
	userPrompt := fmt.Sprintf("请对以下招投标项目进行内容增强处理：\n\n%s", contextText)

	// 构建LLM请求
	temperature := 0.2
	maxTokens := 2000
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model: "gpt-4",
		Messages: []common.LLMMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	// 调用LLM服务
	ctx := context.Background()
	response, err := a.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return nil, nil, fmt.Errorf("LLM request failed: %v", err)
	}

	// 解析增强结果
	enhancementResult, err := a.parseEnhancementResult(response.Result)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse enhancement result: %v", err)
	}

	// 构建处理元数据
	metadata := &agentCommon.ProcessingMetadata{
		ModelUsed:      llmRequest.Model,
		TokensConsumed: response.TokenUsage.TotalTokens,
		ProcessingTime: time.Since(startTime).Seconds(),
	}

	return enhancementResult, metadata, nil
}

// buildEnhancementContext 构建增强上下文
func (a *EnhancementAgent) buildEnhancementContext(tenderData *agentCommon.TenderData, classificationData *agentCommon.ClassificationResult) string {
	var contextParts []string

	// 基本信息
	if tenderData.BasicInfo.ProjectName != "" {
		contextParts = append(contextParts, fmt.Sprintf("项目名称：%s", tenderData.BasicInfo.ProjectName))
	}
	if tenderData.BasicInfo.Budget != "" {
		contextParts = append(contextParts, fmt.Sprintf("预算：%s", tenderData.BasicInfo.Budget))
	}

	// 分类信息
	if classificationData.IndustryClassification != nil {
		contextParts = append(contextParts, fmt.Sprintf("行业分类：%s > %s > %s",
			classificationData.IndustryClassification.Level1,
			classificationData.IndustryClassification.Level2,
			classificationData.IndustryClassification.Level3))
	}

	if classificationData.ProcurementType != nil {
		contextParts = append(contextParts, fmt.Sprintf("采购类型：%s", classificationData.ProcurementType.MainType))
	}

	// 技术要求
	if len(tenderData.TenderRequirements.TechnicalRequirements) > 0 {
		contextParts = append(contextParts, fmt.Sprintf("技术要求：%s",
			strings.Join(tenderData.TenderRequirements.TechnicalRequirements, "; ")))
	}

	// 采购人信息
	if tenderData.Organization.PurchaserName != "" {
		contextParts = append(contextParts, fmt.Sprintf("采购人：%s", tenderData.Organization.PurchaserName))
	}

	return strings.Join(contextParts, "\n")
}

// getEnhancementSystemPrompt 获取增强系统提示词
func (a *EnhancementAgent) getEnhancementSystemPrompt(language string, options *EnhancementOptions) string {
	if language == agentCommon.LanguageEnglish {
		return a.getEnglishEnhancementPrompt(options)
	}
	return a.getChineseEnhancementPrompt(options)
}

// getChineseEnhancementPrompt 获取中文增强提示词
func (a *EnhancementAgent) getChineseEnhancementPrompt(options *EnhancementOptions) string {
	prompt := `你是一个专业的招投标内容增强专家。请对提供的招投标项目进行内容增强处理。

请严格按照以下JSON格式返回结果：
{`

	if options.GenerateKeywords {
		prompt += `
  "keywords": {
    "technical_keywords": ["技术关键词1", "技术关键词2"],
    "business_keywords": ["商务关键词1", "商务关键词2"],  
    "core_products": ["核心产品1", "核心产品2"]
  },`
	}

	if options.OptimizeTitle {
		prompt += `
  "optimized_title": {
    "original_title": "原始标题",
    "new_title": "优化后的标题",
    "optimization_reason": "优化原因说明"
  },`
	}

	if options.CalculateScore {
		prompt += `
  "quality_score": {
    "overall_score": 7.5,
    "dimension_scores": {
      "project_value": 8.0,
      "supplier_preference": 7.0,
      "client_authority": 8.5,
      "cooperation_potential": 7.5,
      "information_completeness": 6.5
    },
    "scoring_reasons": ["评分理由1", "评分理由2", "评分理由3"]
  },`
	}

	if options.GenerateSummary {
		prompt += `
  "summary": {
    "executive_summary": "执行摘要，简明概述项目核心信息",
    "key_points": ["关键要点1", "关键要点2", "关键要点3"],
    "risk_factors": ["风险因子1", "风险因子2"]
  }`
	}

	prompt += `
}

注意事项：
1. 关键词要准确反映项目特点，技术关键词侧重技术规格，商务关键词侧重商业价值
2. 标题优化要提高信息密度和检索性，但保持简洁
3. 质量评分：各维度1-10分，综合考虑项目价值、竞争难度、合作潜力等
4. 摘要要简洁明了，突出项目亮点和关键信息
5. 只返回JSON格式，不要添加任何额外文本`

	return prompt
}

// getEnglishEnhancementPrompt 获取英文增强提示词
func (a *EnhancementAgent) getEnglishEnhancementPrompt(options *EnhancementOptions) string {
	prompt := `You are a professional tender content enhancement expert. Please perform content enhancement processing for the provided tender project.

Please return results strictly in the following JSON format:
{`

	if options.GenerateKeywords {
		prompt += `
  "keywords": {
    "technical_keywords": ["technical keyword1", "technical keyword2"],
    "business_keywords": ["business keyword1", "business keyword2"],
    "core_products": ["core product1", "core product2"]
  },`
	}

	if options.OptimizeTitle {
		prompt += `
  "optimized_title": {
    "original_title": "Original title",
    "new_title": "Optimized title", 
    "optimization_reason": "Optimization reason"
  },`
	}

	if options.CalculateScore {
		prompt += `
  "quality_score": {
    "overall_score": 7.5,
    "dimension_scores": {
      "project_value": 8.0,
      "supplier_preference": 7.0,
      "client_authority": 8.5,
      "cooperation_potential": 7.5,
      "information_completeness": 6.5
    },
    "scoring_reasons": ["scoring reason1", "scoring reason2", "scoring reason3"]
  },`
	}

	if options.GenerateSummary {
		prompt += `
  "summary": {
    "executive_summary": "Executive summary briefly outlining core project information",
    "key_points": ["key point1", "key point2", "key point3"],
    "risk_factors": ["risk factor1", "risk factor2"]
  }`
	}

	prompt += `
}

Notes:
1. Keywords should accurately reflect project characteristics
2. Title optimization should improve information density and searchability while remaining concise
3. Quality scoring: 1-10 points for each dimension, considering project value, competition difficulty, cooperation potential
4. Summary should be concise and highlight project highlights and key information
5. Return only JSON format without any additional text`

	return prompt
}

// parseEnhancementResult 解析增强结果
func (a *EnhancementAgent) parseEnhancementResult(jsonResult string) (*agentCommon.EnhancementResult, error) {
	var result struct {
		Keywords struct {
			TechnicalKeywords []string `json:"technical_keywords"`
			BusinessKeywords  []string `json:"business_keywords"`
			CoreProducts      []string `json:"core_products"`
		} `json:"keywords"`
		OptimizedTitle struct {
			OriginalTitle      string `json:"original_title"`
			NewTitle           string `json:"new_title"`
			OptimizationReason string `json:"optimization_reason"`
		} `json:"optimized_title"`
		QualityScore struct {
			OverallScore    float64                     `json:"overall_score"`
			DimensionScores agentCommon.DimensionScores `json:"dimension_scores"`
			ScoringReasons  []string                    `json:"scoring_reasons"`
		} `json:"quality_score"`
		Summary struct {
			ExecutiveSummary string   `json:"executive_summary"`
			KeyPoints        []string `json:"key_points"`
			RiskFactors      []string `json:"risk_factors"`
		} `json:"summary"`
	}

	if err := json.Unmarshal([]byte(jsonResult), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// 构建返回结果
	enhancementResult := &agentCommon.EnhancementResult{
		Keywords: &agentCommon.Keywords{
			TechnicalKeywords: result.Keywords.TechnicalKeywords,
			BusinessKeywords:  result.Keywords.BusinessKeywords,
			CoreProducts:      result.Keywords.CoreProducts,
		},
		OptimizedTitle: &agentCommon.OptimizedTitle{
			OriginalTitle:      result.OptimizedTitle.OriginalTitle,
			NewTitle:           result.OptimizedTitle.NewTitle,
			OptimizationReason: result.OptimizedTitle.OptimizationReason,
		},
		QualityScore: &agentCommon.QualityScore{
			OverallScore:    result.QualityScore.OverallScore,
			DimensionScores: result.QualityScore.DimensionScores,
			ScoringReasons:  result.QualityScore.ScoringReasons,
		},
		Summary: &agentCommon.Summary{
			ExecutiveSummary: result.Summary.ExecutiveSummary,
			KeyPoints:        result.Summary.KeyPoints,
			RiskFactors:      result.Summary.RiskFactors,
		},
	}

	return enhancementResult, nil
}
