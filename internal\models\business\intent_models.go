package business

import (
	"time"
	"gitlab.com/specific-ai/taskd/internal/models/common"
)

// IntentRecognizeRequest represents the request for intent recognition
type IntentRecognizeRequest struct {
	Messages       []common.LLMMessage `json:"messages" binding:"required"`
	UserID         string              `json:"user_id" binding:"required"`
	OrganizationID string              `json:"organization_id" binding:"required"`
}

// IntentRecognizeResponse represents the response from intent recognition
type IntentRecognizeResponse struct {
	Intent      string                 `json:"intent"`
	Confidence  float64                `json:"confidence"`
	Explanation string                 `json:"explanation"`
	Metadata    IntentMetadata         `json:"metadata"`
	Error       *string                `json:"error,omitempty"`
}

// IntentMetadata contains metadata about the intent recognition process
type IntentMetadata struct {
	ModelUsed   string              `json:"model_used"`
	TokensUsed  common.TokenUsage   `json:"tokens_used"`
	DurationMs  int64               `json:"duration_ms"`
	ProcessedAt time.Time           `json:"processed_at"`
}

// SupportedIntents defines the available intent types
type SupportedIntents struct {
	BiddingAnalysis    string `json:"bidding_analysis"`
	BusinessNews       string `json:"business_news"`
	ChatSummary        string `json:"chat_summary"`
	CasualChat         string `json:"casual_chat"`
}

// GetSupportedIntents returns the list of supported intents
func GetSupportedIntents() SupportedIntents {
	return SupportedIntents{
		BiddingAnalysis: "bidding_analysis",
		BusinessNews:    "business_news", 
		ChatSummary:     "chat_summary",
		CasualChat:      "casual_chat",
	}
}

// IntentAsyncRequest represents the request for async intent recognition
type IntentAsyncRequest struct {
	Messages       []common.LLMMessage `json:"messages" binding:"required"`
	UserID         string              `json:"user_id" binding:"required"`
	OrganizationID string              `json:"organization_id" binding:"required"`
	SessionID      string              `json:"session_id,omitempty"`
	CallbackURL    string              `json:"callback_url" binding:"required"`
}

// IntentCallbackData represents the callback data structure
type IntentCallbackData struct {
	TaskID    string                  `json:"task_id"`
	UserID    string                  `json:"user_id"`
	SessionID string                  `json:"session_id,omitempty"`
	Status    string                  `json:"status"` // "completed", "failed"
	Result    *IntentCallbackResult   `json:"result,omitempty"`
	Error     *IntentError           `json:"error,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// IntentCallbackResult represents the successful result in callback
type IntentCallbackResult struct {
	Intent      string         `json:"intent"`
	Confidence  float64        `json:"confidence"`
	Explanation string         `json:"explanation"`
	Metadata    IntentMetadata `json:"metadata"`
}

// IntentError represents error information in callback
type IntentError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

 