package setup

import (
	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/modules/profile"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/agents"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/prompts"
)

// ProfileModule 企业画像模块
type ProfileModule struct {
	ProfileService services.ProfileServiceInterface
	StorageService services.StorageServiceInterface
	SchemaService  services.SchemaServiceInterface
	ProfileHandler *handlers.ProfileHandler
	ProfileAgent   *agents.ProfileAgent   // 新增：Profile Agent
	AgentRegistry  *profile.AgentRegistry // 新增：Agent注册表
	Registry       *profile.Registry
}

// ProfileModuleDependencies 企业画像模块依赖
type ProfileModuleDependencies struct {
	ProfileModule *ProfileModule
}

// InitializeProfileModule 初始化企业画像模块
func InitializeProfileModule(
	llmClient llm.LLMClient,
	promptManager *prompts.PromptManager,
) (*ProfileModule, error) {

	// 获取profile模块注册表
	registry := profile.GetRegistry()

	// 初始化注册表
	if err := registry.Initialize(llmClient, promptManager); err != nil {
		return nil, err
	}

	// 从注册表获取服务
	profileService, err := registry.GetProfileService()
	if err != nil {
		return nil, err
	}

	storageService, err := registry.GetStorageService()
	if err != nil {
		return nil, err
	}

	schemaService, err := registry.GetSchemaService()
	if err != nil {
		return nil, err
	}

	// 创建画像处理器（需要兼容现有接口）
	profileHandler := handlers.NewProfileHandler(profileService)

	// 创建Agent注册表和Agent实例
	agentRegistry := profile.NewAgentRegistry(profileService)
	profileAgent := agents.NewProfileAgent(profileService)

	module := &ProfileModule{
		ProfileService: profileService,
		StorageService: storageService,
		SchemaService:  schemaService,
		ProfileHandler: profileHandler,
		ProfileAgent:   profileAgent,
		AgentRegistry:  agentRegistry,
		Registry:       registry,
	}

	return module, nil
}

// GetProfileAgent 获取Profile Agent（用于Agent模块注册）
func (m *ProfileModule) GetProfileAgent() *agents.ProfileAgent {
	return m.ProfileAgent
}

// GetAgentRegistry 获取Agent注册表
func (m *ProfileModule) GetAgentRegistry() *profile.AgentRegistry {
	return m.AgentRegistry
}
