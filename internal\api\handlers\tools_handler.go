// internal/api/handlers/tools_handler.go
package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ToolsHandler 负责处理工具类服务的API请求，例如相关性计算
type ToolsHandler struct {
	relevanceService services.RelevanceService
	// 如果未来有其他工具类服务，也可以在这里添加
}

// NewToolsHandler 创建 ToolsHandler 实例
func NewToolsHandler(relevanceSvc services.RelevanceService) *ToolsHandler {
	return &ToolsHandler{
		relevanceService: relevanceSvc,
	}
}

// HandleCalculateRelevance 处理相关性计算的 API 请求
// @Summary 计算内容相关性 (基于词向量平均)
// @Description 接收目标关键词列表和内容文本列表，使用平均词向量的方法计算相关性，返回超过阈值的内容项及其分数。
// @Tags Tools
// @Accept  json
// @Produce  json
// @Param   relevanceRequest body models.RelevanceCalculationRequest true "相关性计算请求"
// @Success 200 {object} models.RelevanceCalculationResponse
// @Failure 400 {object} models.StandardErrorResponse "请求参数错误"
// @Failure 500 {object} models.StandardErrorResponse "服务器内部错误"
// @Router /tools/v1/calculate_relevance [post]
func (h *ToolsHandler) HandleCalculateRelevance(c *gin.Context) {
	var req models.RelevanceCalculationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Warnf("绑定相关性计算请求 JSON 失败: %v", err)
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{Error: "无效的请求负载: " + err.Error()})
		return
	}

	if len(req.TargetList) == 0 {
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{Error: "target_list 不能为空"})
		return
	}
	if len(req.ContentList) == 0 {
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{Error: "content_list 不能为空"})
		return
	}

	resp, err := h.relevanceService.CalculateRelevance(req)
	if err != nil {
		utils.Log.Errorf("计算相关性时出错: %v", err)
		c.JSON(http.StatusInternalServerError, models.StandardErrorResponse{Error: "计算相关性失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}
