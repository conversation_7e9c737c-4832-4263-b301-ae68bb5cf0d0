// Package handlers provides HTTP handlers for intent recognition.
// This async handler is kept for future use but not currently active.
package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/models/business"
	"gitlab.com/specific-ai/taskd/internal/services"
)

type IntentAsyncHandler struct {
	intentService *services.IntentRecognitionService
}

func NewIntentAsyncHandler(intentService *services.IntentRecognitionService) *IntentAsyncHandler {
	return &IntentAsyncHandler{
		intentService: intentService,
	}
}

// AsyncRecognizeIntent handles POST /api/v1/intent/recognize-async
func (h *IntentAsyncHandler) AsyncRecognizeIntent(c *gin.Context) {
	var req business.IntentAsyncRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_REQUEST",
			"message": "请求格式错误: " + err.Error(),
		})
		return
	}

	// 验证必填字段
	if len(req.Messages) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "VALIDATION_FAILED",
			"message": "messages字段不能为空",
		})
		return
	}

	if req.UserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "VALIDATION_FAILED",
			"message": "user_id字段不能为空",
		})
		return
	}

	if req.CallbackURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "VALIDATION_FAILED",
			"message": "callback_url字段不能为空",
		})
		return
	}

	// 验证每条消息
	for i, msg := range req.Messages {
		if msg.Role == "" || msg.Content == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    "VALIDATION_FAILED",
				"message": fmt.Sprintf("第%d条消息的role和content字段不能为空", i+1),
			})
			return
		}
		if msg.Role != "user" && msg.Role != "assistant" && msg.Role != "system" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    "VALIDATION_FAILED", 
				"message": fmt.Sprintf("第%d条消息的role字段值无效: %s", i+1, msg.Role),
			})
			return
		}
	}

	// 生成任务ID
	taskID := fmt.Sprintf("intent_%d_%s", time.Now().UnixNano(), req.UserID)

	// 立即返回任务ID
	c.JSON(http.StatusAccepted, gin.H{
		"task_id": taskID,
		"status":  "processing",
		"message": "意图识别任务已开始，结果将通过回调返回",
	})

	// 异步处理意图识别
	go h.processIntentAsync(context.Background(), taskID, &req)
}

func (h *IntentAsyncHandler) processIntentAsync(ctx context.Context, taskID string, req *business.IntentAsyncRequest) {
	// 转换为同步请求格式
	syncReq := &business.IntentRecognizeRequest{
		Messages:       req.Messages,
		UserID:         req.UserID,
		OrganizationID: req.OrganizationID,
	}

	// 调用同步意图识别服务
	result, err := h.intentService.RecognizeIntent(ctx, syncReq)
	
	// 构建回调数据
	callbackData := business.IntentCallbackData{
		TaskID:    taskID,
		UserID:    req.UserID,
		SessionID: req.SessionID,
		Timestamp: time.Now(),
	}

	if err != nil {
		// 处理错误
		callbackData.Status = "failed"
		callbackData.Error = &business.IntentError{
			Code:    "RECOGNITION_FAILED",
			Message: err.Error(),
		}
	} else {
		// 处理成功
		callbackData.Status = "completed"
		callbackData.Result = &business.IntentCallbackResult{
			Intent:      result.Intent,
			Confidence:  result.Confidence,
			Explanation: result.Explanation,
			Metadata:    result.Metadata,
		}
	}

	// 发送回调
	if err := h.sendCallback(req.CallbackURL, &callbackData); err != nil {
		// 记录回调发送失败的日志
		fmt.Printf("Failed to send callback for task %s: %v\n", taskID, err)
	}
}

func (h *IntentAsyncHandler) sendCallback(callbackURL string, data *business.IntentCallbackData) error {
	// 这里应该实现HTTP POST到回调URL的逻辑
	// 为了简化，这里只打印日志
	fmt.Printf("Sending callback to %s: %+v\n", callbackURL, data)
	
	// 实际实现应该包含：
	// 1. 创建HTTP客户端
	// 2. 序列化数据为JSON
	// 3. 发送POST请求
	// 4. 处理重试逻辑
	// 5. 记录日志
	
	return nil
}

// GetAsyncTaskStatus handles GET /api/v1/intent/task/{task_id}/status
func (h *IntentAsyncHandler) GetAsyncTaskStatus(c *gin.Context) {
	taskID := c.Param("task_id")
	
	if taskID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_REQUEST",
			"message": "task_id不能为空",
		})
		return
	}

	// 这里应该从缓存或数据库中查询任务状态
	// 为了简化，返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"task_id": taskID,
		"status":  "processing",
		"message": "任务正在处理中",
	})
}