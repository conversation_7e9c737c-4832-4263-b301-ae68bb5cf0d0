package setup

import (
	"github.com/apache/pulsar-client-go/pulsar"
	"github.com/sirupsen/logrus"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"go.mongodb.org/mongo-driver/mongo"
)

// AppDependencies 包含应用启动所需的核心依赖
type AppDependencies struct {
	Config                 *config.Config
	Logger                 *logrus.Logger // 假设你的 utils.Log 是 *utils.Logger 类型
	DB                     *mongo.Client  // 假设 store.InitMongoDB 返回的是这个类型或类似
	LLMClient              llm.LLMClient
	PromptManager          *prompts.PromptManager
	PulsarClient           pulsar.Client
	TokenDependencies      *TokenModuleDependencies
	ConcurrentDependencies *ConcurrentModuleDependencies
	BiddingDependencies    *BiddingModuleDependencies
}

// ConcurrentModuleDependencies 并发控制模块依赖
type ConcurrentModuleDependencies struct {
	ConcurrentModule *ConcurrentModule
}

// BiddingModuleDependencies 招投标模块依赖
type BiddingModuleDependencies struct {
	BiddingModule *BiddingModule
}
