package api

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	//_ "gitlab.com/specific-ai/taskd/docs"
	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// SetupRouter 配置 Gin 路由
func SetupRouter(
	cfg *config.Config,
	llmHandler *handlers.LLMHandler,
	toolsHandler *handlers.ToolsHandler,
	intentHandler *handlers.IntentHandler,
	tokenHandler *handlers.TokenHandler,
	concurrentHandler *handlers.ConcurrentHandler,
	a2aHandler *handlers.A2AHandler,
	chatHandler *handlers.ChatHandler,
	agentHandler *handlers.AgentHandler,
) *gin.Engine {
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// Swagger 文档
	if cfg.Server.Mode != "release" {
		router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// 健康检查
	router.GET("/healthz", handlers.HealthCheck)

	// API v1 路由组
	v1 := router.Group("/v1")

	// LLM 相关路由 (迁移到 /v1/llms 下)
	llmGroup := v1.Group("/llms")
	{
		llmGroup.POST("/summary_report", llmHandler.HandleSummaryReport)
		llmGroup.POST("/sentiment_analysis", llmHandler.HandleSentimentAnalysis)
		// 在此添加其他 LLM 工具的路由
	}

	// 工具类路由 (迁移到 /v1/tools 下)
	toolsGroup := v1.Group("/tools")
	{
		toolsGroup.POST("/calculate_relevance", toolsHandler.HandleCalculateRelevance)
		// 在此添加其他工具类服务的路由
	}

	// 意图识别路由
	intentGroup := v1.Group("/intent")
	{
		intentGroup.POST("/recognize", intentHandler.RecognizeIntent)
		intentGroup.GET("/supported", intentHandler.GetSupportedIntents)
	}

	// Token管理路由
	tokenGroup := v1.Group("/tokens")
	{
		tokenGroup.POST("/consumption", tokenHandler.LogTokenConsumption)
		tokenGroup.GET("/limits/:user_id", tokenHandler.CheckTokenLimits)
		tokenGroup.GET("/stats/user/:user_id", tokenHandler.GetTokenUsageStats)
		tokenGroup.GET("/stats/company/:company_id", tokenHandler.GetCompanyTokenStats)
		tokenGroup.GET("/history/:user_id", tokenHandler.GetTokenConsumptionHistory)
	}

	// 用户和公司管理路由
	v1.POST("/users", tokenHandler.CreateUser)
	v1.POST("/companies", tokenHandler.CreateCompany)

	// 并发控制和队列管理路由
	concurrentGroup := v1.Group("/concurrent")
	{
		concurrentGroup.POST("/submit", concurrentHandler.SubmitLLMRequest)
		concurrentGroup.GET("/stats", concurrentHandler.GetQueueStats)
	}

	// LLM高级接口（支持自动并发控制）
	llmAdvanced := v1.Group("/llm")
	{
		llmAdvanced.POST("/chat", concurrentHandler.SubmitLLMRequest)
		llmAdvanced.POST("/summarize", concurrentHandler.SubmitLLMRequest)
	}

	// A2A Agent路由 (支持招投标业务模块)
	agentGroup := router.Group("/agents")
	{
		// Agent列表和注册
		agentGroup.GET("", a2aHandler.HandleListAgents)
		agentGroup.POST("/register", a2aHandler.HandleAgentRegistry)

		// Agent文档界面
		agentGroup.GET("/docs", a2aHandler.HandleAgentDocumentation)

		// 具体Agent的特殊路由（必须在通配符路由之前）
		agentGroup.GET("/execute/.well-known/agent.json", a2aHandler.HandleAgentCard)
		agentGroup.GET("/execute/:agentName/health", a2aHandler.HandleAgentHealth)

		// Agent请求处理（通配符路由必须最后）
		agentGroup.POST("/execute/*agentPath", a2aHandler.HandleAgentRequest)
	}

	// Core Agent管理路由 (如果提供了AgentHandler)
	if agentHandler != nil {
		agentHandler.RegisterRoutes(v1)
	}

	// 聊天路由 (SocketIO + REST API)
	if chatHandler != nil {
		// SocketIO 路由
		router.GET("/socket.io/*any", chatHandler.GetSocketIOHandler())
		router.POST("/socket.io/*any", chatHandler.GetSocketIOHandler())

		// 聊天REST API路由
		chatGroup := v1.Group("/chat")
		{
			chatGroup.POST("/sessions", chatHandler.CreateChatSession)
			chatGroup.GET("/sessions/:session_id/history", chatHandler.GetChatHistory)
			chatGroup.DELETE("/sessions/:session_id", chatHandler.CloseChatSession)
			chatGroup.GET("/stats", chatHandler.GetChatStats)
			chatGroup.POST("/cleanup", chatHandler.ForceCleanup)
		}
	}

	utils.Log.Info("路由已设置")
	return router
}
