chinese:
  system: |
    你是招投标需求分析专家。请对招投标文档进行深度需求分析，识别技术要求、商务条件和关键匹配点。

  template: |
    招投标摘要信息：
    标题：{{.Summary.Title}}
    摘要：{{.Summary.SummaryText}}
    关键要求：{{join .Summary.KeyRequirements "、"}}
    {{if .Summary.BudgetInfo}}预算信息：{{.Summary.BudgetInfo.Amount}} {{.Summary.BudgetInfo.Type}}{{end}}
    {{if .Summary.Timeline}}时间安排：投标截止 {{.Summary.Timeline.BiddingDeadline}}，项目周期 {{.Summary.Timeline.ProjectDuration}}{{end}}

    {{if .FocusAreas}}分析重点：{{join .FocusAreas "、"}}{{end}}
    {{if .CompanyCapabilities}}公司能力：{{join .CompanyCapabilities "、"}}{{end}}

    请进行全面的需求分析，返回JSON格式：
    {
      "technical_analysis": {
        "core_requirements": ["核心技术要求1", "核心技术要求2", ...],
        "optional_requirements": ["可选技术要求1", "可选技术要求2", ...],
        "technical_challenges": ["技术挑战1", "技术挑战2", ...],
        "compliance_requirements": ["合规要求1", "合规要求2", ...]
      },
      "business_analysis": {
        "budget_assessment": {
          "budget_range": "预算范围评估",
          "cost_feasibility": "成本可行性",
          "pricing_strategy": "定价策略建议"
        },
        "timeline_analysis": {
          "project_phases": ["阶段1", "阶段2", ...],
          "critical_milestones": ["里程碑1", "里程碑2", ...],
          "timeline_feasibility": "时间安排可行性"
        },
        "commercial_terms": {
          "payment_terms": "付款条件分析",
          "contract_risks": ["合同风险1", "合同风险2", ...],
          "negotiable_items": ["可协商项1", "可协商项2", ...]
        }
      },
      "competitive_analysis": {
        "our_advantages": ["我方优势1", "我方优势2", ...],
        "potential_weaknesses": ["潜在劣势1", "潜在劣势2", ...],
        "differentiation_strategy": "差异化策略",
        "win_probability": "获胜概率评估"
      },
      "recommendation": {
        "bid_decision": "是否投标建议",
        "key_success_factors": ["成功关键因素1", "成功关键因素2", ...],
        "risk_mitigation": ["风险缓解措施1", "风险缓解措施2", ...],
        "next_steps": ["下一步行动1", "下一步行动2", ...]
      }
    }

    确保返回有效的JSON格式。

english:
  system: |
    You are an expert in tender requirement analysis. Please conduct in-depth analysis of tender documents, identifying technical requirements, commercial conditions, and key matching points.

  template: |
    Tender Summary Information:
    Title: {{.Summary.Title}}
    Summary: {{.Summary.SummaryText}}
    Key Requirements: {{join .Summary.KeyRequirements ", "}}
    {{if .Summary.BudgetInfo}}Budget Information: {{.Summary.BudgetInfo.Amount}} {{.Summary.BudgetInfo.Type}}{{end}}
    {{if .Summary.Timeline}}Timeline: Bidding deadline {{.Summary.Timeline.BiddingDeadline}}, Project duration {{.Summary.Timeline.ProjectDuration}}{{end}}

    {{if .FocusAreas}}Analysis Focus: {{join .FocusAreas ", "}}{{end}}
    {{if .CompanyCapabilities}}Company Capabilities: {{join .CompanyCapabilities ", "}}{{end}}

    Please conduct comprehensive requirement analysis and return in JSON format:
    {
      "technical_analysis": {
        "core_requirements": ["core technical requirement 1", "core technical requirement 2", ...],
        "optional_requirements": ["optional technical requirement 1", "optional technical requirement 2", ...],
        "technical_challenges": ["technical challenge 1", "technical challenge 2", ...],
        "compliance_requirements": ["compliance requirement 1", "compliance requirement 2", ...]
      },
      "business_analysis": {
        "budget_assessment": {
          "budget_range": "budget range assessment",
          "cost_feasibility": "cost feasibility",
          "pricing_strategy": "pricing strategy recommendation"
        },
        "timeline_analysis": {
          "project_phases": ["phase 1", "phase 2", ...],
          "critical_milestones": ["milestone 1", "milestone 2", ...],
          "timeline_feasibility": "timeline feasibility"
        },
        "commercial_terms": {
          "payment_terms": "payment terms analysis",
          "contract_risks": ["contract risk 1", "contract risk 2", ...],
          "negotiable_items": ["negotiable item 1", "negotiable item 2", ...]
        }
      },
      "competitive_analysis": {
        "our_advantages": ["our advantage 1", "our advantage 2", ...],
        "potential_weaknesses": ["potential weakness 1", "potential weakness 2", ...],
        "differentiation_strategy": "differentiation strategy",
        "win_probability": "win probability assessment"
      },
      "recommendation": {
        "bid_decision": "bidding recommendation",
        "key_success_factors": ["key success factor 1", "key success factor 2", ...],
        "risk_mitigation": ["risk mitigation 1", "risk mitigation 2", ...],
        "next_steps": ["next step 1", "next step 2", ...]
      }
    }

    Ensure the response is valid JSON format.