# 实体提取Agent说明文档

## 概述

实体提取Agent是一个基于TaskD Agent架构的通用实体提取系统，专门用于从自然语言文本和结构化文本中提取符合业务模型的结构化数据。该Agent支持基于JSON Schema的动态模型定义，能够适应各种业务场景的实体提取需求。

### 主要特性

- **通用性**: 支持任意JSON Schema定义的业务模型
- **多语言**: 支持中文、英文和混合语言文本处理
- **高精度**: 基于大语言模型的智能提取，准确率高
- **可扩展**: 模块化设计，易于扩展和维护
- **标准化**: 符合TaskD Agent架构规范
- **容错性**: 完善的错误处理和降级机制

## Agent信息

- **Agent ID**: `entity-extraction-agent-001`
- **Agent名称**: 通用实体提取Agent
- **Agent类型**: `custom`
- **版本**: 1.0.0
- **作者**: TaskD Team

## 支持的能力

### extract_structured_data

通用结构化数据提取能力，这是该Agent的唯一核心能力，支持所有业务场景的实体提取需求。

#### 输入Schema

```json
{
  "type": "object",
  "properties": {
    "input_text": {
      "type": "string",
      "description": "输入文本内容（自然语言文本或结构化文本）"
    },
    "target_schema": {
      "type": "object",
      "description": "目标JSON Schema定义，包含实体模型结构",
      "properties": {
        "name": {
          "type": "string",
          "description": "模型名称"
        },
        "description": {
          "type": "string",
          "description": "模型描述"
        },
        "type": {
          "type": "string",
          "enum": ["object"],
          "description": "数据类型，固定为object"
        },
        "properties": {
          "type": "object",
          "description": "实体字段定义"
        },
        "required": {
          "type": "array",
          "items": {"type": "string"},
          "description": "必填字段列表"
        },
        "examples": {
          "type": "array",
          "description": "示例数据列表"
        }
      },
      "required": ["name", "description", "type", "properties"]
    },
    "language": {
      "type": "string",
      "enum": ["zh", "en", "mixed"],
      "default": "zh",
      "description": "输入文本语言"
    },
    "extraction_config": {
      "type": "object",
      "properties": {
        "confidence_threshold": {
          "type": "number",
          "default": 0.7,
          "description": "置信度阈值"
        },
        "max_retries": {
          "type": "integer",
          "default": 3,
          "description": "最大重试次数"
        },
        "timeout": {
          "type": "integer",
          "default": 30,
          "description": "超时时间(秒)"
        },
        "enable_fallback": {
          "type": "boolean",
          "default": true,
          "description": "是否启用降级策略"
        }
      }
    }
  },
  "required": ["input_text", "target_schema"]
}
```

#### 输出Schema

```json
{
  "type": "object",
  "properties": {
    "success": {
      "type": "boolean",
      "description": "提取是否成功"
    },
    "data": {
      "type": "object",
      "description": "提取的结构化数据，符合target_schema定义"
    },
    "confidence": {
      "type": "number",
      "minimum": 0,
      "maximum": 1,
      "description": "提取结果的置信度"
    },
    "error": {
      "type": "object",
      "properties": {
        "code": {"type": "string", "description": "错误代码"},
        "message": {"type": "string", "description": "错误信息"},
        "details": {"type": "object", "description": "错误详情"}
      },
      "description": "错误信息（仅当success为false时存在）"
    },
    "metadata": {
      "type": "object",
      "properties": {
        "model_used": {"type": "string", "description": "使用的LLM模型"},
        "processing_time_ms": {"type": "integer", "description": "处理时间（毫秒）"},
        "token_usage": {
          "type": "object",
          "properties": {
            "input_tokens": {"type": "integer"},
            "output_tokens": {"type": "integer"},
            "total_tokens": {"type": "integer"}
          }
        },
        "language": {"type": "string", "description": "检测到的语言"},
        "retry_count": {"type": "integer", "description": "实际重试次数"},
        "processed_at": {"type": "string", "format": "date-time", "description": "处理时间"}
      }
    },
    "raw_output": {
      "type": "string",
      "description": "LLM原始输出（仅在解析失败时提供）"
    }
  },
  "required": ["success", "confidence", "metadata"]
}
```

## 使用示例

### 示例1: 商机维度提取

**请求:**
```json
{
  "capability": "extract_structured_data",
  "input": {
    "input_text": "请你帮我分析这条新闻的商业相关的机会",
    "target_schema": {
      "name": "OpportunityDimension",
      "description": "商机维度模型，用于分析商业机会的各个维度",
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "维度名称，如'商业机会'、'技术创新'等"
        },
        "description": {
          "type": "string", 
          "description": "维度的详细描述，解释该维度的含义和作用"
        },
        "keywords": {
          "type": "array",
          "description": "与该维度相关的关键字列表，支持中英文",
          "items": {"type": "string"}
        }
      },
      "required": ["name", "description"],
      "examples": [
        {
          "name": "商业机会",
          "description": "与企业增长或盈利潜力相关的机会。",
          "keywords": ["市场扩张", "新客户", "收入增长"]
        }
      ]
    },
    "language": "zh"
  }
}
```

**响应:**
```json
{
  "success": true,
  "output": {
    "success": true,
    "data": {
      "name": "商业机会",
      "description": "与企业增长或盈利潜力相关的机会。",
      "keywords": ["市场扩张", "新客户", "收入增长", "market expansion", "new clients", "revenue growth"]
    },
    "confidence": 0.92,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 1250,
      "token_usage": {
        "input_tokens": 150,
        "output_tokens": 80,
        "total_tokens": 230
      },
      "language": "zh",
      "retry_count": 0,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  }
}
```

### 示例2: 招标关键词信息提取

**请求:**
```json
{
  "capability": "extract_structured_data",
  "input": {
    "input_text": "我想找德国2025年5月后发布的低平板挂车采购项目",
    "target_schema": {
      "name": "BiddingKeyInfo",
      "description": "招标关键词信息模型，用于提取招标项目的关键信息",
      "type": "object",
      "properties": {
        "subject": {
          "type": "string",
          "description": "标题或主要采购内容"
        },
        "country": {
          "type": "string",
          "description": "国家或地区"
        },
        "publication_date_from": {
          "type": "string",
          "format": "date-time",
          "description": "发布日期起始时间"
        },
        "procurement_type": {
          "type": "string",
          "description": "采购类型，如物品采购、服务采购、工程采购"
        }
      },
      "required": ["subject"],
      "examples": [
        {
          "subject": "低平板挂车",
          "country": "德国",
          "publication_date_from": "2025-05-01T00:00:00Z",
          "procurement_type": "物品采购"
        }
      ]
    },
    "language": "zh"
  }
}
```

### 示例3: 查询条件提取

**请求:**
```json
{
  "capability": "extract_structured_data",
  "input": {
    "input_text": "请你帮我查找中国的大于7月18号的新闻",
    "target_schema": {
      "name": "QueryConditionsList",
      "description": "查询条件列表模型，用于解析用户的查询需求",
      "type": "object",
      "properties": {
        "query_conditions_list": {
          "type": "array",
          "description": "查询条件列表",
          "items": {
            "type": "object",
            "properties": {
              "query_fields": {
                "type": "string",
                "description": "需要查询的字段名",
                "enum": ["country", "update_at"]
              },
              "query_value": {
                "type": "string",
                "description": "查询字段的值"
              },
              "query_operate": {
                "type": "string",
                "description": "查询操作符",
                "enum": ["eq", "ne", "gt", "gte", "lt", "lte"]
              }
            },
            "required": ["query_fields", "query_value", "query_operate"]
          }
        }
      },
      "required": ["query_conditions_list"],
      "examples": [
        {
          "query_conditions_list": [
            {
              "query_fields": "country",
              "query_value": "china",
              "query_operate": "eq"
            }
          ]
        }
      ]
    },
    "language": "zh"
  }
}
```

## 错误码说明

### 输入相关错误

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `INVALID_INPUT` | 输入参数无效 | 检查输入参数格式和类型 |
| `EMPTY_INPUT` | 输入文本为空 | 提供非空的输入文本 |
| `INPUT_TOO_LONG` | 输入文本过长 | 缩短输入文本或分段处理 |
| `UNSUPPORTED_LANGUAGE` | 不支持的语言 | 使用支持的语言：zh、en、mixed |

### 模型相关错误

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `INVALID_MODEL_SCHEMA` | 模型Schema无效 | 检查Schema格式是否符合JSON Schema规范 |
| `SCHEMA_VALIDATION_FAILED` | Schema验证失败 | 修正Schema定义中的错误 |

### 业务相关错误

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `EXTRACTION_IMPOSSIBLE` | 无法提取有意义信息 | 检查输入文本与目标模型的相关性 |
| `LOW_CONFIDENCE` | 提取结果置信度过低 | 降低置信度阈值或改进输入文本 |

### LLM相关错误

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `LLM_CALL_FAILED` | LLM调用失败 | 检查网络连接和LLM服务状态 |
| `LLM_TIMEOUT` | LLM调用超时 | 增加超时时间或简化输入 |
| `LLM_RATE_LIMIT` | LLM调用频率限制 | 降低调用频率或等待后重试 |
| `TOKEN_EXCEEDED` | Token数量超限 | 缩短输入文本或使用更大的模型 |

### 解析相关错误

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `RESPONSE_PARSE_FAILED` | 响应解析失败 | 检查LLM输出格式 |
| `INVALID_JSON` | 无效的JSON格式 | 重试请求或检查模型配置 |

### 系统相关错误

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| `CONFIG_NOT_FOUND` | 配置未找到 | 检查Agent配置 |
| `INTERNAL_ERROR` | 内部错误 | 联系系统管理员 |

## 配置说明

### Agent配置

```json
{
  "llm_model": "deepseek-v3-250324",
  "temperature": 0.1,
  "max_tokens": 4000,
  "timeout": 30,
  "max_retries": 3,
  "confidence_threshold": 0.7,
  "enable_fallback": true,
  "auto_track_tokens": true
}
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `llm_model` | string | "deepseek-v3-250324" | 使用的LLM模型 |
| `temperature` | number | 0.1 | 生成温度，控制输出随机性 |
| `max_tokens` | integer | 4000 | 最大Token数量 |
| `timeout` | integer | 30 | 超时时间（秒） |
| `max_retries` | integer | 3 | 最大重试次数 |
| `confidence_threshold` | number | 0.7 | 置信度阈值 |
| `enable_fallback` | boolean | true | 是否启用降级策略 |
| `auto_track_tokens` | boolean | true | 是否自动跟踪Token使用 |

## 性能指标

### 响应时间

- **单次请求**: < 3秒
- **并发处理**: 支持100+并发连接
- **批量处理**: 支持批量请求处理

### 准确率

- **结构化文本**: > 90%
- **自然语言文本**: > 85%
- **复杂嵌套结构**: > 80%

### 资源使用

- **内存使用**: < 500MB
- **CPU使用**: < 50%（正常负载）
- **Token消耗**: 平均200-500 tokens/请求

## 最佳实践

### 1. Schema设计

- **明确字段描述**: 为每个字段提供清晰的描述
- **合理设置必填字段**: 只将真正必需的字段设为required
- **提供示例数据**: 在Schema中包含examples帮助模型理解
- **使用适当的数据类型**: 选择最合适的JSON Schema类型

### 2. 输入文本优化

- **保持文本相关性**: 确保输入文本与目标模型相关
- **适当的文本长度**: 避免过长或过短的输入
- **清晰的表达**: 使用清晰、结构化的语言
- **包含关键信息**: 确保文本包含提取所需的关键信息

### 3. 配置调优

- **调整置信度阈值**: 根据业务需求调整阈值
- **设置合理超时**: 根据文本复杂度设置超时时间
- **启用重试机制**: 对于重要请求启用重试
- **监控Token使用**: 关注Token消耗情况

### 4. 错误处理

- **实现重试逻辑**: 对临时性错误进行重试
- **降级处理**: 在高置信度提取失败时提供降级方案
- **日志记录**: 记录详细的错误信息用于调试
- **用户友好提示**: 为用户提供清晰的错误说明

## 监控和运维

### 健康检查

Agent提供标准的健康检查接口：

```bash
GET /api/agents/entity-extraction-agent-001/health
```

### 指标监控

- **请求成功率**: 监控提取成功率
- **响应时间**: 监控平均响应时间
- **Token使用量**: 监控Token消耗情况
- **错误率**: 监控各类错误的发生率

### 日志级别

- **INFO**: 正常操作日志
- **WARN**: 警告信息（如低置信度）
- **ERROR**: 错误信息
- **DEBUG**: 调试信息（包含详细的处理过程）

## 版本历史

### v1.0.0 (当前版本)

- 实现基于JSON Schema的通用实体提取
- 支持中英文混合文本处理
- 完善的错误处理和降级机制
- 集成TaskD Agent框架
- 支持Token管理和使用统计

## 技术支持

如有问题或建议，请联系：

- **技术文档**: 参考本文档和相关API文档
- **问题反馈**: 通过系统日志和监控平台
- **开发团队**: TaskD Team

## 许可证

本Agent遵循TaskD项目的许可证协议。