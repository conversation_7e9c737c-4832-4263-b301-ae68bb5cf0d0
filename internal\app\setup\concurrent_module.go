package setup

import (
	"context"
	"fmt"

	"github.com/apache/pulsar-client-go/pulsar"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/services/concurrent"
	"gitlab.com/specific-ai/taskd/internal/services/infrastructure"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ConcurrentModule 并发控制模块
type ConcurrentModule struct {
	Config            *models.ConcurrentConfig
	ConcurrentService services.EnhancedConcurrentService
	QueueManager      infrastructure.PulsarQueueManager
	LLMService        services.LLMService
}

// SetupConcurrentModule 设置并发控制模块
func SetupConcurrentModule(
	appConfig *config.Config,
	tokenService *concurrent.TokenService,
	llmClient llm.LLMClient,
	pulsarClient pulsar.Client,
) (*ConcurrentModule, error) {
	utils.Log.Info("Setting up concurrent control module...")

	// 1. 创建并发控制配置
	concurrentConfig := createConcurrentConfig(appConfig)

	// 2. 创建LLM服务
	llmService := services.NewLLMService(llmClient, tokenService)

	// 3. 创建Pulsar队列管理器
	var queueManager infrastructure.PulsarQueueManager
	var err error

	if pulsarClient != nil {
		queueManager, err = infrastructure.NewPulsarQueueManager(
			pulsarClient,
			concurrentConfig,
			appConfig.Pulsar.Consumer,
		)
		if err != nil {
			utils.Log.Warnf("Failed to create Pulsar queue manager: %v", err)
			// 继续运行，但没有Pulsar支持
		} else {
			utils.Log.Info("Pulsar queue manager created successfully")
		}
	} else {
		utils.Log.Warn("Pulsar client not available, running without Pulsar support")
	}

	// 4. 创建增强的并发控制服务
	concurrentService := concurrent.NewEnhancedConcurrentService(
		concurrentConfig,
		tokenService,
		llmService,
		queueManager,
	)

	// 5. 启动服务
	ctx := context.Background()
	if err := concurrentService.Start(ctx); err != nil {
		return nil, fmt.Errorf("failed to start concurrent service: %w", err)
	}

	utils.Log.Info("Concurrent control module setup completed")

	return &ConcurrentModule{
		Config:            concurrentConfig,
		ConcurrentService: concurrentService,
		QueueManager:      queueManager,
		LLMService:        llmService,
	}, nil
}

// createConcurrentConfig 创建并发控制配置
func createConcurrentConfig(appConfig *config.Config) *models.ConcurrentConfig {
	config := models.DefaultConcurrentConfig()

	// 从应用配置中读取并发控制参数
	if appConfig.Server.Port != "" {
		// 可以根据环境或配置文件调整参数
	}

	// 从环境变量读取配置（如果需要）
	// if maxConcurrent := os.Getenv("MAX_CONCURRENT_REQUESTS"); maxConcurrent != "" {
	//     if val, err := strconv.Atoi(maxConcurrent); err == nil {
	//         config.MaxConcurrentRequests = val
	//     }
	// }

	// 根据生产环境调整参数
	if appConfig.Server.Mode == "release" {
		config.MaxConcurrentRequests = 1000
		config.MainQueueSize = 1000
		config.WorkerCount = 50
	} else {
		// 开发环境使用较小的值
		config.MaxConcurrentRequests = 100
		config.MainQueueSize = 100
		config.WorkerCount = 10
	}

	// 设置Pulsar相关配置
	if appConfig.Pulsar.Consumer.TopicReportSummary != "" {
		config.PulsarTopic = "persistent://public/default/llm-requests"
		config.PulsarRetryTopic = "persistent://public/default/llm-retries"
		config.PulsarSubscription = "llm-processor"
	}

	utils.Log.Infof("Concurrent config: max_concurrent=%d, queue_size=%d, workers=%d",
		config.MaxConcurrentRequests, config.MainQueueSize, config.WorkerCount)

	return config
}

// Shutdown 关闭并发控制模块
func (m *ConcurrentModule) Shutdown() error {
	utils.Log.Info("Shutting down concurrent control module...")

	var errors []error

	// 停止并发服务
	if m.ConcurrentService != nil {
		if err := m.ConcurrentService.Stop(); err != nil {
			errors = append(errors, fmt.Errorf("failed to stop concurrent service: %w", err))
		}
	}

	// 关闭队列管理器
	if m.QueueManager != nil {
		if err := m.QueueManager.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close queue manager: %w", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors during shutdown: %v", errors)
	}

	utils.Log.Info("Concurrent control module shutdown completed")
	return nil
}
