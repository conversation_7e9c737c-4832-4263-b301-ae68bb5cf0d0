# 企业画像API对接开发文档

## 概述

TaskD企业画像服务是一个基于AI的企业多维度画像分析平台，通过分析企业基础信息，生成包含业务能力、招投标适配性、竞争力分析、海外业务能力和风险承受能力在内的五维度企业画像报告。

### 核心特性

- **五维度分析框架**: 业务能力、招投标适配性、竞争力分析、国际化能力、风险承受能力
- **AI驱动分析**: 基于大语言模型的智能企业画像生成
- **中英文支持**: 支持中英文双语prompt模板和分析
- **结构化输出**: 标准化JSON格式的分析结果
- **完整CRUD**: 支持画像的创建、查询、列表和删除操作
- **多租户支持**: 基于用户ID的数据隔离

### 服务版本

- **服务版本**: v1.0.0
- **API版本**: v1
- **基础路径**: `/v1/profiles`

## API接口清单

### 1. 生成企业画像

生成基于企业基础信息的五维度画像分析报告。

**接口信息**
- **URL**: `POST /v1/profiles/company/generate`
- **Content-Type**: `application/json`
- **功能**: 基于企业基础信息生成五维度企业画像分析报告

**请求参数**

| 字段名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| user_id | string | 是 | 用户ID | "user_12345" |
| company_name | string | 是 | 企业名称 | "华为技术有限公司" |
| industry | string | 是 | 所属行业 | "信息技术" |
| company_scale | string | 否 | 企业规模 | "大型企业" |
| business_scope | string | 否 | 业务范围 | "通信设备、智能终端、云计算" |
| location | string | 否 | 企业位置 | "深圳市龙岗区" |
| established_year | int | 否 | 成立年份 | 1987 |
| description | string | 否 | 企业描述 | "全球领先的ICT基础设施和智能终端提供商" |
| website | string | 否 | 官网地址 | "https://www.huawei.com" |
| additional_info | object | 否 | 补充信息 | 见下方示例 |

**请求示例**
```json
{
  "user_id": "user_12345",
  "company_name": "华为技术有限公司",
  "industry": "信息技术",
  "company_scale": "大型企业",
  "business_scope": "通信设备、智能终端、云计算服务",
  "location": "深圳市龙岗区",
  "established_year": 1987,
  "description": "全球领先的ICT基础设施和智能终端提供商",
  "website": "https://www.huawei.com",
  "additional_info": {
    "employee_count": "约19.7万人",
    "annual_revenue": "6369亿元人民币(2022年)",
    "global_presence": "170多个国家和地区"
  }
}
```

**成功响应 (200)**
```json
{
  "user_id": "user_12345",
  "company_name": "华为技术有限公司",
  "business_capabilities": {
    "core_business_areas": [
      "通信网络设备",
      "智能终端设备",
      "云计算服务",
      "企业数字化解决方案"
    ],
    "product_service_types": [
      "5G基站设备",
      "智能手机",
      "服务器",
      "云服务平台"
    ],
    "technical_capabilities": [
      {
        "name": "5G技术",
        "level": "advanced",
        "description": "在5G标准制定和设备研发方面处于全球领先地位"
      }
    ],
    "business_scale": "大型企业",
    "project_experience": [
      {
        "project_type": "5G网络建设",
        "scale": "大型项目",
        "count": 100,
        "description": "全球范围内参与多个国家的5G网络建设项目"
      }
    ],
    "certifications": [
      "ISO9001质量管理体系",
      "ISO27001信息安全管理体系"
    ]
  },
  "tender_matching": {
    "project_types": [
      "通信基础设施建设",
      "智慧城市项目",
      "数据中心建设"
    ],
    "project_scale": ["中型项目", "大型项目", "超大型项目"],
    "geographic_coverage": ["全球"],
    "qualification_match": [
      {
        "type": "通信工程施工总承包",
        "level": "特级",
        "description": "具备承接大型通信工程项目的资质"
      }
    ],
    "bidding_strategy": "技术领先策略，注重长期合作关系建立",
    "historical_win_rate": 0.75
  },
  "competitive_profile": {
    "market_position": "全球通信设备市场领导者",
    "core_advantages": [
      "技术研发能力强",
      "全球化布局完善",
      "产业链整合能力"
    ],
    "differentiators": [
      "端到端解决方案",
      "持续创新投入",
      "本地化服务能力"
    ],
    "main_competitors": [
      {
        "name": "爱立信",
        "relationship": "direct",
        "advantage": "在中国市场具有本土化优势"
      }
    ],
    "price_competitiveness": "具有较强的成本控制能力和价格竞争力",
    "market_influence": "行业标准制定的重要参与者"
  },
  "international_capabilities": {
    "overseas_markets": [
      {
        "region": "欧洲",
        "experience": "丰富",
        "market_share": "领先"
      }
    ],
    "cross_border_experience": "在170多个国家和地区开展业务",
    "international_certifications": [
      "CE认证",
      "FCC认证"
    ],
    "partner_network": [
      {
        "name": "全球运营商伙伴",
        "region": "全球",
        "partnership": "战略合作伙伴关系"
      }
    ],
    "language_capabilities": ["中文", "英文", "法文", "德文"],
    "cultural_adaptability": "强，具备深度本地化能力"
  },
  "risk_tolerance": {
    "policy_risk_sensitivity": "中",
    "exchange_rate_risk": "中",
    "project_cycle_preference": ["中期", "长期"],
    "funding_capability": {
      "capital_scale": "千亿级",
      "funding_sources": ["自有资金", "银行授信", "债券发行"],
      "cash_flow_stability": "稳定，现金流充裕"
    },
    "risk_control_mechanisms": [
      "多元化业务布局",
      "汇率对冲机制"
    ],
    "insurance_coverage": [
      "出口信用保险",
      "项目履约保险"
    ]
  },
  "confidence_score": 92,
  "generated_at": "2025-01-15T10:30:00Z",
  "metadata": {
    "model_used": "company_profile_model_structured",
    "tokens_used": {
      "prompt_tokens": 1250,
      "completion_tokens": 2800,
      "total_tokens": 4050
    },
    "processing_time_ms": 3500,
    "strategy_version": "v2.0.0",
    "data_sources": ["企业官网", "行业报告", "公开财报"]
  }
}
```

**错误响应**

```json
// 400 Bad Request - 参数错误
{
  "error": "无效的请求参数: 用户ID不能为空"
}

// 500 Internal Server Error - 服务器错误
{
  "error": "生成企业画像失败: 模型服务暂时不可用"
}
```

### 2. 获取企业画像

根据用户ID和企业名称获取已生成的企业画像。

**接口信息**
- **URL**: `GET /v1/profiles/company`
- **方法**: GET
- **功能**: 获取指定企业的画像信息

**请求参数 (Query Parameters)**

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| user_id | string | 是 | 用户ID | user_12345 |
| company_name | string | 是 | 企业名称 | 华为技术有限公司 |

**请求示例**
```
GET /v1/profiles/company?user_id=user_12345&company_name=华为技术有限公司
```

**成功响应 (200)**
```json
{
  "user_id": "user_12345",
  "company_name": "华为技术有限公司",
  "business_capabilities": { ... },
  "tender_matching": { ... },
  "competitive_profile": { ... },
  "international_capabilities": { ... },
  "risk_tolerance": { ... },
  "confidence_score": 92,
  "generated_at": "2025-01-15T10:30:00Z",
  "metadata": { ... }
}
```

**错误响应**
```json
// 404 Not Found - 未找到画像
{
  "error": "未找到企业画像: 该企业的画像尚未生成"
}
```

### 3. 获取用户企业画像列表

获取用户所有已生成的企业画像列表，支持分页。

**接口信息**
- **URL**: `GET /v1/profiles/company/list`
- **方法**: GET
- **功能**: 获取用户的企业画像列表

**请求参数 (Query Parameters)**

| 参数名 | 类型 | 必填 | 描述 | 默认值 | 示例 |
|--------|------|------|------|--------|------|
| user_id | string | 是 | 用户ID | - | user_12345 |
| limit | int | 否 | 分页限制 | 10 | 20 |
| offset | int | 否 | 分页偏移 | 0 | 0 |

**请求示例**
```
GET /v1/profiles/company/list?user_id=user_12345&limit=10&offset=0
```

**成功响应 (200)**
```json
[
  {
    "user_id": "user_12345",
    "company_name": "华为技术有限公司",
    "business_capabilities": { ... },
    "confidence_score": 92,
    "generated_at": "2025-01-15T10:30:00Z"
  },
  {
    "user_id": "user_12345", 
    "company_name": "腾讯控股有限公司",
    "business_capabilities": { ... },
    "confidence_score": 88,
    "generated_at": "2025-01-14T15:20:00Z"
  }
]
```

### 4. 删除企业画像

根据用户ID和企业名称删除企业画像。

**接口信息**
- **URL**: `DELETE /v1/profiles/company`
- **方法**: DELETE
- **功能**: 删除指定的企业画像

**请求参数 (Query Parameters)**

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| user_id | string | 是 | 用户ID | user_12345 |
| company_name | string | 是 | 企业名称 | 华为技术有限公司 |

**请求示例**
```
DELETE /v1/profiles/company?user_id=user_12345&company_name=华为技术有限公司
```

**成功响应 (200)**
```json
{
  "message": "企业画像删除成功"
}
```

**错误响应**
```json
// 404 Not Found - 未找到画像
{
  "error": "删除企业画像失败: 未找到指定的企业画像"
}
```

### 5. 获取画像服务信息

获取企业画像服务的基本信息和支持的功能。

**接口信息**
- **URL**: `GET /v1/profiles/info`
- **方法**: GET
- **功能**: 获取服务信息和能力说明

**成功响应 (200)**
```json
{
  "service": "企业画像生成服务",
  "version": "v1.0.0",
  "description": "基于企业基础信息生成五维度企业画像分析报告",
  "capabilities": [
    "业务能力画像分析",
    "招投标适配性分析",
    "竞争力分析",
    "海外业务能力分析",
    "风险承受能力分析"
  ],
  "supported_industries": [
    "制造业",
    "建筑业", 
    "信息技术",
    "金融服务",
    "贸易",
    "能源",
    "交通运输",
    "房地产",
    "教育",
    "医疗健康",
    "其他"
  ],
  "input_fields": {
    "user_id": "必填 - 用户ID",
    "company_name": "必填 - 企业名称",
    "industry": "必填 - 所属行业",
    "company_scale": "可选 - 企业规模",
    "business_scope": "可选 - 业务范围",
    "location": "可选 - 企业位置",
    "established_year": "可选 - 成立年份",
    "description": "可选 - 企业描述",
    "website": "可选 - 官网地址",
    "additional_info": "可选 - 补充信息"
  }
}
```

## 数据模型详解

### 企业画像请求模型 (CompanyProfileRequest)

```go
type CompanyProfileRequest struct {
    UserID          string                 `json:"user_id" binding:"required"`
    CompanyName     string                 `json:"company_name" binding:"required"`
    Industry        string                 `json:"industry" binding:"required"`
    CompanyScale    string                 `json:"company_scale,omitempty"`
    BusinessScope   string                 `json:"business_scope,omitempty"`
    Location        string                 `json:"location,omitempty"`
    EstablishedYear int                    `json:"established_year,omitempty"`
    Description     string                 `json:"description,omitempty"`
    Website         string                 `json:"website,omitempty"`
    AdditionalInfo  map[string]interface{} `json:"additional_info,omitempty"`
}
```

### 企业画像响应模型 (CompanyProfileResponse)

完整的响应模型包含以下五个核心维度：

#### 1. 业务能力画像 (BusinessCapabilities)

```go
type BusinessCapabilities struct {
    CoreBusinessAreas     []string               `json:"core_business_areas"`
    ProductServiceTypes   []string               `json:"product_service_types"`
    TechnicalCapabilities []TechnicalCapability  `json:"technical_capabilities"`
    BusinessScale         string                 `json:"business_scale"`
    ProjectExperience     []ProjectExperience    `json:"project_experience"`
    Certifications        []string               `json:"certifications"`
}

type TechnicalCapability struct {
    Name        string `json:"name"`
    Level       string `json:"level"`        // advanced, intermediate, basic
    Description string `json:"description"`
}

type ProjectExperience struct {
    ProjectType string `json:"project_type"`
    Scale       string `json:"scale"`
    Count       int    `json:"count"`
    Description string `json:"description"`
}
```

#### 2. 招投标适配性 (TenderMatching)

```go
type TenderMatching struct {
    ProjectTypes        []string            `json:"project_types"`
    ProjectScale        []string            `json:"project_scale"`
    GeographicCoverage  []string            `json:"geographic_coverage"`
    QualificationMatch  []QualificationInfo `json:"qualification_match"`
    BiddingStrategy     string              `json:"bidding_strategy"`
    HistoricalWinRate   float64             `json:"historical_win_rate"`
}

type QualificationInfo struct {
    Type        string `json:"type"`
    Level       string `json:"level"`
    Description string `json:"description"`
}
```

#### 3. 竞争力分析 (CompetitiveProfile)

```go
type CompetitiveProfile struct {
    MarketPosition       string           `json:"market_position"`
    CoreAdvantages       []string         `json:"core_advantages"`
    Differentiators      []string         `json:"differentiators"`
    MainCompetitors      []CompetitorInfo `json:"main_competitors"`
    PriceCompetitiveness string           `json:"price_competitiveness"`
    MarketInfluence      string           `json:"market_influence"`
}

type CompetitorInfo struct {
    Name         string `json:"name"`
    Relationship string `json:"relationship"` // direct, indirect
    Advantage    string `json:"advantage"`
}
```

#### 4. 海外业务能力 (InternationalCapabilities)

```go
type InternationalCapabilities struct {
    OverseasMarkets             []OverseasMarket `json:"overseas_markets"`
    CrossBorderExperience       string           `json:"cross_border_experience"`
    InternationalCertifications []string         `json:"international_certifications"`
    PartnerNetwork              []PartnerInfo    `json:"partner_network"`
    LanguageCapabilities        []string         `json:"language_capabilities"`
    CulturalAdaptability        string           `json:"cultural_adaptability"`
}

type OverseasMarket struct {
    Region      string `json:"region"`
    Experience  string `json:"experience"`
    MarketShare string `json:"market_share"`
}

type PartnerInfo struct {
    Name        string `json:"name"`
    Region      string `json:"region"`
    Partnership string `json:"partnership"`
}
```

#### 5. 风险承受能力 (RiskTolerance)

```go
type RiskTolerance struct {
    PolicyRiskSensitivity  string             `json:"policy_risk_sensitivity"`
    ExchangeRateRisk       string             `json:"exchange_rate_risk"`
    ProjectCyclePreference []string           `json:"project_cycle_preference"`
    FundingCapability      *FundingCapability `json:"funding_capability"`
    RiskControlMechanisms  []string           `json:"risk_control_mechanisms"`
    InsuranceCoverage      []string           `json:"insurance_coverage"`
}

type FundingCapability struct {
    CapitalScale      string   `json:"capital_scale"`
    FundingSources    []string `json:"funding_sources"`
    CashFlowStability string   `json:"cash_flow_stability"`
}
```

## 业务逻辑说明

### 五维度分析框架

企业画像服务采用五维度分析框架，每个维度针对不同的业务场景：

1. **业务能力画像**: 评估企业的核心竞争力和技术实力
2. **招投标适配性**: 分析企业在招投标项目中的匹配度
3. **竞争力分析**: 识别企业在市场中的定位和优势
4. **海外业务能力**: 评估企业的国际化能力和经验
5. **风险承受能力**: 分析企业的风险管理和承受能力

### AI分析流程

1. **信息预处理**: 标准化输入的企业信息
2. **模板渲染**: 根据语言偏好选择中英文prompt模板
3. **AI推理**: 调用大语言模型进行五维度分析
4. **结果解析**: 将AI输出解析为结构化JSON格式
5. **质量评估**: 生成置信度分数(0-100)
6. **数据存储**: 保存到PostgreSQL数据库

### 置信度评分机制

置信度分数基于以下因素计算：
- 输入信息完整度 (30%)
- 行业匹配度 (20%)
- 分析逻辑一致性 (25%)
- 数据源可靠性 (25%)

## 集成指南

### 基础配置

**基础URL**: `https://your-taskd-api.com`
**认证方式**: 根据项目配置 (Token/API Key)
**Content-Type**: `application/json`

### 调用示例 (Python)

```python
import requests
import json

# 配置
BASE_URL = "https://your-taskd-api.com"
headers = {
    "Content-Type": "application/json",
    # "Authorization": "Bearer your-token"  # 如需要认证
}

# 生成企业画像
def generate_company_profile(company_data):
    url = f"{BASE_URL}/v1/profiles/company/generate"
    response = requests.post(url, headers=headers, json=company_data)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return None

# 示例调用
company_info = {
    "user_id": "user_12345",
    "company_name": "示例科技公司",
    "industry": "信息技术",
    "company_scale": "中型企业",
    "business_scope": "软件开发、系统集成",
    "location": "北京市海淀区",
    "established_year": 2010,
    "description": "专注于企业数字化转型的软件服务商"
}

result = generate_company_profile(company_info)
if result:
    print(f"画像生成成功，置信度: {result['confidence_score']}")
```

### 调用示例 (JavaScript)

```javascript
const axios = require('axios');

const BASE_URL = 'https://your-taskd-api.com';

// 生成企业画像
async function generateCompanyProfile(companyData) {
    try {
        const response = await axios.post(
            `${BASE_URL}/v1/profiles/company/generate`,
            companyData,
            {
                headers: {
                    'Content-Type': 'application/json',
                    // 'Authorization': 'Bearer your-token'  // 如需要认证
                }
            }
        );
        
        return response.data;
    } catch (error) {
        console.error('Error:', error.response?.data || error.message);
        return null;
    }
}

// 示例调用
const companyInfo = {
    user_id: "user_12345",
    company_name: "示例科技公司",
    industry: "信息技术",
    company_scale: "中型企业",
    business_scope: "软件开发、系统集成",
    location: "北京市海淀区",
    established_year: 2010,
    description: "专注于企业数字化转型的软件服务商"
};

generateCompanyProfile(companyInfo).then(result => {
    if (result) {
        console.log(`画像生成成功，置信度: ${result.confidence_score}`);
    }
});
```

## 错误处理

### HTTP状态码说明

| 状态码 | 含义 | 描述 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 400 | Bad Request | 请求参数错误 |
| 404 | Not Found | 资源未找到 |
| 500 | Internal Server Error | 服务器内部错误 |

### 常见错误类型

#### 1. 参数验证错误 (400)
```json
{
  "error": "无效的请求参数: 企业名称不能为空"
}
```

#### 2. 资源未找到 (404)
```json
{
  "error": "未找到企业画像: 该企业的画像尚未生成"
}
```

#### 3. 服务异常 (500)
```json
{
  "error": "生成企业画像失败: 模型服务暂时不可用"
}
```

### 错误处理建议

1. **网络超时**: 设置合理的超时时间(建议30-60秒)
2. **重试机制**: 对于500错误实施指数退避重试
3. **参数校验**: 客户端提前验证必填参数
4. **错误日志**: 记录详细的错误信息用于问题排查

## 数据库Schema

企业画像数据存储在PostgreSQL中，采用双Schema设计：

### 主要表结构

#### 1. taskd.company_profiles
```sql
CREATE TABLE taskd.company_profiles (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_name VARCHAR(500) NOT NULL,
    industry VARCHAR(200) NOT NULL,
    -- ... 其他基础字段
    business_capabilities JSONB NOT NULL,
    tender_matching JSONB NOT NULL,
    competitive_profile JSONB NOT NULL,
    international_capabilities JSONB NOT NULL,
    risk_tolerance JSONB NOT NULL,
    confidence_score INTEGER DEFAULT 75 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. ovs-profile.company_profiles  
海外业务专用Schema，结构与主表相同，用于海外业务场景的数据隔离。

### 索引优化
- `idx_company_profiles_user_id`: 用户查询优化
- `idx_company_profiles_company_name`: 企业名称查询优化
- `idx_company_profiles_industry`: 行业分析查询优化
- GIN索引: 支持JSONB字段的高效查询

## 性能指标

### 响应时间
- 画像生成: 2-5秒 (取决于输入复杂度)
- 查询操作: <100ms
- 列表查询: <200ms

### 并发能力
- 支持1000+并发请求
- 内置队列管理和流控机制

### 数据限制
- 企业名称: 最大500字符
- 描述字段: 最大文本长度
- 补充信息: JSONB格式，建议<10KB

## 版本更新日志

### v1.0.0 (Current)
- 实现五维度企业画像分析
- 支持CRUD完整操作
- 中英文双语支持
- PostgreSQL双Schema存储
- AI驱动的智能分析

## 技术支持

如遇到技术问题，请联系：
- **项目仓库**: https://github.com/your-org/taskd
- **问题反馈**: 通过GitHub Issues提交
- **文档更新**: 参考项目CLAUDE.md文件

---

*本文档基于TaskD项目commit版本: 81fc4f5, 5dcb42f9, 172fb54*