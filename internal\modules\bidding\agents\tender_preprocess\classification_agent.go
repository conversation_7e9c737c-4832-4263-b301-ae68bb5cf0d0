package tender_preprocess

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ClassificationAgent 智能分类Agent
type ClassificationAgent struct {
	*agents.BaseAgent
	llmService services.LLMService
}

// NewClassificationAgent 创建智能分类Agent
func NewClassificationAgent(llmService services.LLMService) *ClassificationAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentClassificationName,
		Description: "对招投标数据进行多维度智能分类",
		URL:         "http://taskd-service:8601/agents/tender-classification",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillClassifyTender,
				Name:        "招投标分类",
				Description: "对招投标进行行业分类、采购类型分类和业务领域分类",
				Tags:        []string{"classification", "ai", "llm", "industry"},
				Examples: []string{
					"将IT设备采购项目分类到计算机制造业",
					"识别建筑工程项目的具体行业类别",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentClassificationName, agentCard)

	return &ClassificationAgent{
		BaseAgent:  baseAgent,
		llmService: llmService,
	}
}

// ExecuteSkill 执行技能
func (a *ClassificationAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillClassifyTender:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.classifyTenderHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// classifyTenderHandler 招投标分类处理器
func (a *ClassificationAgent) classifyTenderHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseClassificationParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting tender classification")

	// 验证语言
	if !agentCommon.ValidateLanguage(params.Language) {
		return nil, fmt.Errorf("unsupported language: %s", params.Language)
	}

	// 验证分类深度
	if !agentCommon.ValidateClassificationDepth(params.ClassificationDepth) {
		return nil, fmt.Errorf("invalid classification depth: %d", params.ClassificationDepth)
	}

	// 执行分类
	result, metadata, err := a.performClassification(params, context)
	if err != nil {
		return nil, fmt.Errorf("classification failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"industry_classification": result.IndustryClassification,
		"procurement_type":        result.ProcurementType,
		"business_domain":         result.BusinessDomain,
		"processing_metadata":     metadata,
	}

	return output, nil
}

// ClassificationParams 分类参数
type ClassificationParams struct {
	ExtractedData       *agentCommon.TenderData `json:"extracted_data"`
	ClassificationDepth int                     `json:"classification_depth"`
	Language            string                  `json:"language"`
}

// parseClassificationParams 解析分类参数
func (a *ClassificationAgent) parseClassificationParams(input map[string]interface{}) (*ClassificationParams, error) {
	extractedDataInterface, ok := input["extracted_data"]
	if !ok {
		return nil, fmt.Errorf("extracted_data is required")
	}

	// 将interface{}转换为TenderData
	extractedDataBytes, err := json.Marshal(extractedDataInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal extracted_data: %v", err)
	}

	var extractedData agentCommon.TenderData
	if err := json.Unmarshal(extractedDataBytes, &extractedData); err != nil {
		return nil, fmt.Errorf("failed to parse extracted_data: %v", err)
	}

	classificationDepth, _ := input["classification_depth"].(float64)
	if classificationDepth == 0 {
		classificationDepth = agentCommon.ClassificationDepthLevel3
	}

	language, _ := input["language"].(string)
	if language == "" {
		language = agentCommon.LanguageChinese
	}

	return &ClassificationParams{
		ExtractedData:       &extractedData,
		ClassificationDepth: int(classificationDepth),
		Language:            language,
	}, nil
}

// performClassification 执行分类
func (a *ClassificationAgent) performClassification(params *ClassificationParams, a2aContext biddingModels.A2AContext) (*agentCommon.ClassificationResult, *agentCommon.ProcessingMetadata, error) {
	startTime := time.Now()

	// 构建分类上下文文本
	contextText := a.buildClassificationContext(params.ExtractedData)

	// 构建分类prompt
	systemPrompt := a.getClassificationSystemPrompt(params.Language, params.ClassificationDepth)
	userPrompt := fmt.Sprintf("请对以下招投标项目进行分类：\n\n%s", contextText)

	// 构建LLM请求
	temperature := 0.1
	maxTokens := 1000
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model: "gpt-4",
		Messages: []common.LLMMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	// 调用LLM服务
	ctx := context.Background()
	response, err := a.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return nil, nil, fmt.Errorf("LLM request failed: %v", err)
	}

	// 解析分类结果
	classificationResult, err := a.parseClassificationResult(response.Result)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse classification result: %v", err)
	}

	// 构建处理元数据
	metadata := &agentCommon.ProcessingMetadata{
		ModelUsed:      llmRequest.Model,
		TokensConsumed: response.TokenUsage.TotalTokens,
		ProcessingTime: time.Since(startTime).Seconds(),
	}

	return classificationResult, metadata, nil
}

// buildClassificationContext 构建分类上下文文本
func (a *ClassificationAgent) buildClassificationContext(tenderData *agentCommon.TenderData) string {
	var contextParts []string

	// 添加项目名称
	if tenderData.BasicInfo.ProjectName != "" {
		contextParts = append(contextParts, fmt.Sprintf("项目名称：%s", tenderData.BasicInfo.ProjectName))
	}

	// 添加预算信息
	if tenderData.BasicInfo.Budget != "" {
		contextParts = append(contextParts, fmt.Sprintf("预算：%s", tenderData.BasicInfo.Budget))
	}

	// 添加技术要求
	if len(tenderData.TenderRequirements.TechnicalRequirements) > 0 {
		contextParts = append(contextParts, fmt.Sprintf("技术要求：%s",
			fmt.Sprintf("[%s]", strings.Join(tenderData.TenderRequirements.TechnicalRequirements, ", "))))
	}

	// 添加商务要求
	if len(tenderData.TenderRequirements.CommercialRequirements) > 0 {
		contextParts = append(contextParts, fmt.Sprintf("商务要求：%s",
			fmt.Sprintf("[%s]", strings.Join(tenderData.TenderRequirements.CommercialRequirements, ", "))))
	}

	// 添加采购人信息
	if tenderData.Organization.PurchaserName != "" {
		contextParts = append(contextParts, fmt.Sprintf("采购人：%s", tenderData.Organization.PurchaserName))
	}

	return strings.Join(contextParts, "\n")
}

// getClassificationSystemPrompt 获取分类系统提示词
func (a *ClassificationAgent) getClassificationSystemPrompt(language string, depth int) string {
	if language == agentCommon.LanguageEnglish {
		return a.getEnglishClassificationPrompt(depth)
	}
	return a.getChineseClassificationPrompt(depth)
}

// getChineseClassificationPrompt 获取中文分类提示词
func (a *ClassificationAgent) getChineseClassificationPrompt(depth int) string {
	basePrompt := `你是一个专业的招投标项目分类专家。请对提供的招投标项目进行详细分类分析。`

	industryPrompt := ""
	switch depth {
	case 1:
		industryPrompt = `对行业分类，请按照以下一级分类标准：
- 制造业
- 信息传输、软件和信息技术服务业  
- 建筑业
- 批发和零售业
- 交通运输、仓储和邮政业
- 教育
- 卫生和社会工作
- 公共管理、社会保障和社会组织
- 其他`
	case 2:
		industryPrompt = `对行业分类，请提供到二级分类：
- 制造业 -> 计算机、通信和其他电子设备制造业 / 汽车制造业 / 其他
- 信息传输、软件和信息技术服务业 -> 软件和信息技术服务业 / 互联网和相关服务 / 其他
- 建筑业 -> 房屋建筑业 / 土木工程建筑业 / 建筑安装业 / 建筑装饰装修业 / 其他`
	default: // depth 3
		industryPrompt = `对行业分类，请提供到三级分类：
- 制造业 -> 计算机、通信和其他电子设备制造业 -> 计算机整机制造 / 计算机零部件制造 / 通信设备制造
- 信息传输、软件和信息技术服务业 -> 软件和信息技术服务业 -> 软件开发 / 信息系统集成服务 / 数据处理服务
- 建筑业 -> 土木工程建筑业 -> 铁路、道路、隧道和桥梁工程建筑 / 水利和港口工程建筑`
	}

	return fmt.Sprintf(`%s

%s

请严格按照以下JSON格式返回分类结果：
{
  "industry_classification": {
    "level1": "一级行业分类",
    "level2": "二级行业分类",  
    "level3": "三级行业分类",
    "confidence_scores": {
      "level1": 0.95,
      "level2": 0.88,
      "level3": 0.75
    }
  },
  "procurement_type": {
    "main_type": "product/service/hybrid",
    "sub_types": ["具体类型1", "具体类型2"],
    "confidence_score": 0.92
  },
  "business_domain": {
    "primary_domain": "主要业务领域",
    "secondary_domains": ["次要业务领域1", "次要业务领域2"],
    "domain_tags": ["标签1", "标签2", "标签3"]
  }
}

注意：
1. 如果分类深度不足，相应级别填入空字符串""
2. 置信度分数为0-1之间的数值
3. main_type必须是product、service或hybrid之一
4. 只返回JSON格式，不要添加任何额外文本`, basePrompt, industryPrompt)
}

// getEnglishClassificationPrompt 获取英文分类提示词
func (a *ClassificationAgent) getEnglishClassificationPrompt(depth int) string {
	basePrompt := `You are a professional tender project classification expert. Please provide detailed classification analysis for the given tender project.`

	industryPrompt := ""
	switch depth {
	case 1:
		industryPrompt = `For industry classification, use the following level-1 categories:
- Manufacturing
- Information Transmission, Software and Information Technology Services
- Construction
- Wholesale and Retail Trade
- Transportation, Storage and Postal Services
- Education
- Health and Social Work
- Public Administration, Social Security and Social Organizations
- Others`
	case 2:
		industryPrompt = `For industry classification, provide up to level-2 categories:
- Manufacturing -> Computer, Communication and Other Electronic Equipment Manufacturing / Automobile Manufacturing / Others
- Information Transmission, Software and IT Services -> Software and IT Services / Internet and Related Services / Others
- Construction -> Building Construction / Civil Engineering Construction / Building Installation / Building Decoration / Others`
	default: // depth 3
		industryPrompt = `For industry classification, provide up to level-3 categories:
- Manufacturing -> Computer, Communication and Electronic Equipment -> Computer Manufacturing / Computer Parts Manufacturing / Communication Equipment Manufacturing
- Information Transmission, Software and IT Services -> Software and IT Services -> Software Development / System Integration Services / Data Processing Services
- Construction -> Civil Engineering Construction -> Railway, Road, Tunnel and Bridge Construction / Water and Port Engineering Construction`
	}

	return fmt.Sprintf(`%s

%s

Please return classification results strictly in the following JSON format:
{
  "industry_classification": {
    "level1": "Level 1 industry classification",
    "level2": "Level 2 industry classification",
    "level3": "Level 3 industry classification", 
    "confidence_scores": {
      "level1": 0.95,
      "level2": 0.88,
      "level3": 0.75
    }
  },
  "procurement_type": {
    "main_type": "product/service/hybrid",
    "sub_types": ["specific type 1", "specific type 2"],
    "confidence_score": 0.92
  },
  "business_domain": {
    "primary_domain": "Primary business domain",
    "secondary_domains": ["Secondary domain 1", "Secondary domain 2"],
    "domain_tags": ["tag1", "tag2", "tag3"]
  }
}

Notes:
1. If classification depth is insufficient, fill corresponding levels with empty string ""
2. Confidence scores should be between 0-1
3. main_type must be one of: product, service, hybrid
4. Return only JSON format without any additional text`, basePrompt, industryPrompt)
}

// parseClassificationResult 解析分类结果
func (a *ClassificationAgent) parseClassificationResult(jsonResult string) (*agentCommon.ClassificationResult, error) {
	var result struct {
		IndustryClassification struct {
			Level1           string                       `json:"level1"`
			Level2           string                       `json:"level2"`
			Level3           string                       `json:"level3"`
			ConfidenceScores agentCommon.ConfidenceScores `json:"confidence_scores"`
		} `json:"industry_classification"`
		ProcurementType struct {
			MainType        string   `json:"main_type"`
			SubTypes        []string `json:"sub_types"`
			ConfidenceScore float64  `json:"confidence_score"`
		} `json:"procurement_type"`
		BusinessDomain struct {
			PrimaryDomain    string   `json:"primary_domain"`
			SecondaryDomains []string `json:"secondary_domains"`
			DomainTags       []string `json:"domain_tags"`
		} `json:"business_domain"`
	}

	if err := json.Unmarshal([]byte(jsonResult), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// 构建返回结果
	classificationResult := &agentCommon.ClassificationResult{
		IndustryClassification: &agentCommon.IndustryClassification{
			Level1:           result.IndustryClassification.Level1,
			Level2:           result.IndustryClassification.Level2,
			Level3:           result.IndustryClassification.Level3,
			ConfidenceScores: result.IndustryClassification.ConfidenceScores,
		},
		ProcurementType: &agentCommon.ProcurementType{
			MainType:        result.ProcurementType.MainType,
			SubTypes:        result.ProcurementType.SubTypes,
			ConfidenceScore: result.ProcurementType.ConfidenceScore,
		},
		BusinessDomain: &agentCommon.BusinessDomain{
			PrimaryDomain:    result.BusinessDomain.PrimaryDomain,
			SecondaryDomains: result.BusinessDomain.SecondaryDomains,
			DomainTags:       result.BusinessDomain.DomainTags,
		},
	}

	return classificationResult, nil
}
