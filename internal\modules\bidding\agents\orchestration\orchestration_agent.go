package orchestration

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// OrchestrationAgent 编排Agent
type OrchestrationAgent struct {
	*agents.BaseAgent
	agentRegistry map[string]AgentInterface
	mu            sync.RWMutex
}

// AgentInterface Agent接口定义
type AgentInterface interface {
	ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error)
	GetAgentCard() biddingModels.AgentCard
}

// NewOrchestrationAgent 创建编排Agent
func NewOrchestrationAgent() *OrchestrationAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentOrchestrationName,
		Description: "协调和编排多个预处理Agent的执行流程",
		URL:         "http://taskd-service:8601/agents/tender-orchestration",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillProcessTenderWorkflow,
				Name:        "招投标工作流处理",
				Description: "协调执行完整的招投标预处理工作流",
				Tags:        []string{"orchestration", "workflow", "coordination", "pipeline"},
				Examples: []string{
					"执行完整的招投标预处理流水线",
					"协调多个Agent的并行处理",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
			{
				ID:          agentCommon.SkillProcessTenderBatch,
				Name:        "批量招投标处理",
				Description: "批量处理多个招投标项目",
				Tags:        []string{"batch", "parallel", "bulk"},
				Examples: []string{
					"批量处理100个招投标项目",
					"并行处理多个项目以提高效率",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentOrchestrationName, agentCard)

	return &OrchestrationAgent{
		BaseAgent:     baseAgent,
		agentRegistry: make(map[string]AgentInterface),
	}
}

// RegisterAgent 注册Agent
func (a *OrchestrationAgent) RegisterAgent(name string, agent AgentInterface) {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.agentRegistry[name] = agent
}

// ExecuteSkill 执行技能
func (a *OrchestrationAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillProcessTenderWorkflow:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.processTenderWorkflowHandler)
	case agentCommon.SkillProcessTenderBatch:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.processTenderBatchHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// processTenderWorkflowHandler 招投标工作流处理器
func (a *OrchestrationAgent) processTenderWorkflowHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseWorkflowParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting tender workflow processing")

	// 执行工作流
	result, err := a.executeWorkflow(params, context)
	if err != nil {
		return nil, fmt.Errorf("workflow execution failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"workflow_result":     result,
		"processing_metadata": result.ProcessingMetadata,
	}

	return output, nil
}

// processTenderBatchHandler 批量招投标处理器
func (a *OrchestrationAgent) processTenderBatchHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseBatchParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting batch processing for %d items", len(params.BatchItems))

	// 执行批量处理
	results, err := a.executeBatchProcessing(params, context)
	if err != nil {
		return nil, fmt.Errorf("batch processing failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"batch_results":       results,
		"processing_metadata": a.generateBatchMetadata(results),
	}

	return output, nil
}

// WorkflowParams 工作流参数
type WorkflowParams struct {
	RawText           string                 `json:"raw_text"`
	Language          string                 `json:"language"`
	ProcessingOptions *ProcessingOptions     `json:"processing_options"`
	WorkflowConfig    *WorkflowConfiguration `json:"workflow_config"`
}

// BatchParams 批量处理参数
type BatchParams struct {
	BatchItems        []*BatchItem        `json:"batch_items"`
	ProcessingOptions *ProcessingOptions  `json:"processing_options"`
	BatchConfig       *BatchConfiguration `json:"batch_config"`
}

// BatchItem 批量处理项
type BatchItem struct {
	ID       string `json:"id"`
	RawText  string `json:"raw_text"`
	Language string `json:"language"`
}

// ProcessingOptions 处理选项
type ProcessingOptions struct {
	EnableExtraction     bool     `json:"enable_extraction"`
	EnableClassification bool     `json:"enable_classification"`
	EnableEnhancement    bool     `json:"enable_enhancement"`
	EnableTranslation    bool     `json:"enable_translation"`
	TargetLanguages      []string `json:"target_languages"`
	EnableAggregation    bool     `json:"enable_aggregation"`
}

// WorkflowConfiguration 工作流配置
type WorkflowConfiguration struct {
	ParallelExecution bool          `json:"parallel_execution"`
	Timeout           time.Duration `json:"timeout"`
	RetryAttempts     int           `json:"retry_attempts"`
	FailFast          bool          `json:"fail_fast"`
}

// BatchConfiguration 批量配置
type BatchConfiguration struct {
	MaxConcurrency int           `json:"max_concurrency"`
	BatchSize      int           `json:"batch_size"`
	Timeout        time.Duration `json:"timeout"`
}

// WorkflowResult 工作流结果
type WorkflowResult struct {
	FinalResult        *agentCommon.PreprocessingResult `json:"final_result"`
	StepResults        map[string]interface{}           `json:"step_results"`
	ProcessingMetadata *agentCommon.ProcessingMetadata  `json:"processing_metadata"`
	ExecutionPath      []string                         `json:"execution_path"`
}

// parseWorkflowParams 解析工作流参数
func (a *OrchestrationAgent) parseWorkflowParams(input map[string]interface{}) (*WorkflowParams, error) {
	rawText, ok := input["raw_text"].(string)
	if !ok || rawText == "" {
		return nil, fmt.Errorf("raw_text is required and must be non-empty string")
	}

	language, _ := input["language"].(string)
	if language == "" {
		language = agentCommon.LanguageChinese
	}

	// 解析处理选项
	var processingOptions ProcessingOptions
	if optionsInterface, ok := input["processing_options"]; ok {
		optionsBytes, err := json.Marshal(optionsInterface)
		if err == nil {
			json.Unmarshal(optionsBytes, &processingOptions)
		}
	} else {
		// 默认选项
		processingOptions = ProcessingOptions{
			EnableExtraction:     true,
			EnableClassification: true,
			EnableEnhancement:    true,
			EnableTranslation:    false,
			TargetLanguages:      []string{agentCommon.LanguageEnglish},
			EnableAggregation:    true,
		}
	}

	// 解析工作流配置
	var workflowConfig WorkflowConfiguration
	if configInterface, ok := input["workflow_config"]; ok {
		configBytes, err := json.Marshal(configInterface)
		if err == nil {
			json.Unmarshal(configBytes, &workflowConfig)
		}
	} else {
		// 默认配置
		workflowConfig = WorkflowConfiguration{
			ParallelExecution: false, // 默认串行执行
			Timeout:           300 * time.Second,
			RetryAttempts:     2,
			FailFast:          true,
		}
	}

	return &WorkflowParams{
		RawText:           rawText,
		Language:          language,
		ProcessingOptions: &processingOptions,
		WorkflowConfig:    &workflowConfig,
	}, nil
}

// parseBatchParams 解析批量参数
func (a *OrchestrationAgent) parseBatchParams(input map[string]interface{}) (*BatchParams, error) {
	batchItemsInterface, ok := input["batch_items"]
	if !ok {
		return nil, fmt.Errorf("batch_items is required")
	}

	batchItemsBytes, err := json.Marshal(batchItemsInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal batch_items: %v", err)
	}

	var batchItems []*BatchItem
	if err := json.Unmarshal(batchItemsBytes, &batchItems); err != nil {
		return nil, fmt.Errorf("failed to parse batch_items: %v", err)
	}

	if len(batchItems) == 0 {
		return nil, fmt.Errorf("batch_items cannot be empty")
	}

	// 解析处理选项
	var processingOptions ProcessingOptions
	if optionsInterface, ok := input["processing_options"]; ok {
		optionsBytes, err := json.Marshal(optionsInterface)
		if err == nil {
			json.Unmarshal(optionsBytes, &processingOptions)
		}
	} else {
		// 默认选项
		processingOptions = ProcessingOptions{
			EnableExtraction:     true,
			EnableClassification: true,
			EnableEnhancement:    true,
			EnableTranslation:    false,
			EnableAggregation:    true,
		}
	}

	// 解析批量配置
	var batchConfig BatchConfiguration
	if configInterface, ok := input["batch_config"]; ok {
		configBytes, err := json.Marshal(configInterface)
		if err == nil {
			json.Unmarshal(configBytes, &batchConfig)
		}
	} else {
		// 默认配置
		batchConfig = BatchConfiguration{
			MaxConcurrency: 5,
			BatchSize:      10,
			Timeout:        600 * time.Second,
		}
	}

	return &BatchParams{
		BatchItems:        batchItems,
		ProcessingOptions: &processingOptions,
		BatchConfig:       &batchConfig,
	}, nil
}

// executeWorkflow 执行工作流
func (a *OrchestrationAgent) executeWorkflow(params *WorkflowParams, context biddingModels.A2AContext) (*WorkflowResult, error) {
	startTime := time.Now()
	stepResults := make(map[string]interface{})
	executionPath := []string{}

	// 1. 数据提取步骤
	var extractedData *agentCommon.TenderData
	if params.ProcessingOptions.EnableExtraction {
		utils.Log.Infof("Executing data extraction step")
		executionPath = append(executionPath, "extraction")

		extractionResult, err := a.executeExtractionStep(params.RawText, params.Language, context)
		if err != nil {
			if params.WorkflowConfig.FailFast {
				return nil, fmt.Errorf("extraction step failed: %v", err)
			}
			utils.Log.Warnf("Extraction step failed, continuing: %v", err)
		} else {
			stepResults["extraction"] = extractionResult
			if extractionData, ok := extractionResult["extracted_data"].(*agentCommon.TenderData); ok {
				extractedData = extractionData
			}
		}
	}

	// 2. 分类步骤
	var classificationData *agentCommon.ClassificationResult
	if params.ProcessingOptions.EnableClassification && extractedData != nil {
		utils.Log.Infof("Executing classification step")
		executionPath = append(executionPath, "classification")

		classificationResult, err := a.executeClassificationStep(extractedData, params.Language, context)
		if err != nil {
			if params.WorkflowConfig.FailFast {
				return nil, fmt.Errorf("classification step failed: %v", err)
			}
			utils.Log.Warnf("Classification step failed, continuing: %v", err)
		} else {
			stepResults["classification"] = classificationResult
			if classData, ok := classificationResult["industry_classification"].(*agentCommon.ClassificationResult); ok {
				classificationData = classData
			}
		}
	}

	// 3. 增强步骤
	var enhancementData *agentCommon.EnhancementResult
	if params.ProcessingOptions.EnableEnhancement && extractedData != nil && classificationData != nil {
		utils.Log.Infof("Executing enhancement step")
		executionPath = append(executionPath, "enhancement")

		enhancementResult, err := a.executeEnhancementStep(extractedData, classificationData, params.Language, context)
		if err != nil {
			if params.WorkflowConfig.FailFast {
				return nil, fmt.Errorf("enhancement step failed: %v", err)
			}
			utils.Log.Warnf("Enhancement step failed, continuing: %v", err)
		} else {
			stepResults["enhancement"] = enhancementResult
			if enhData, ok := enhancementResult["enhancement_result"].(*agentCommon.EnhancementResult); ok {
				enhancementData = enhData
			}
		}
	}

	// 4. 翻译步骤
	var translationData map[string]*agentCommon.TranslationData
	if params.ProcessingOptions.EnableTranslation && enhancementData != nil && len(params.ProcessingOptions.TargetLanguages) > 0 {
		utils.Log.Infof("Executing translation step")
		executionPath = append(executionPath, "translation")

		translationResult, err := a.executeTranslationStep(enhancementData, params.ProcessingOptions.TargetLanguages, context)
		if err != nil {
			if params.WorkflowConfig.FailFast {
				return nil, fmt.Errorf("translation step failed: %v", err)
			}
			utils.Log.Warnf("Translation step failed, continuing: %v", err)
		} else {
			stepResults["translation"] = translationResult
			if transData, ok := translationResult["translated_data"].(map[string]*agentCommon.TranslationData); ok {
				translationData = transData
			}
		}
	}

	// 5. 聚合步骤
	var finalResult *agentCommon.PreprocessingResult
	if params.ProcessingOptions.EnableAggregation {
		utils.Log.Infof("Executing aggregation step")
		executionPath = append(executionPath, "aggregation")

		aggregationResult, err := a.executeAggregationStep(extractedData, classificationData, enhancementData, translationData, context)
		if err != nil {
			return nil, fmt.Errorf("aggregation step failed: %v", err)
		}

		stepResults["aggregation"] = aggregationResult
		if aggData, ok := aggregationResult["final_result"].(*agentCommon.PreprocessingResult); ok {
			finalResult = aggData
		}
	}

	// 构建处理元数据
	processingMetadata := &agentCommon.ProcessingMetadata{
		ModelUsed:       "orchestration-v1.0",
		TokensConsumed:  0, // 编排Agent本身不消耗tokens
		ProcessingTime:  time.Since(startTime).Seconds(),
		ConfidenceScore: 1.0,
	}

	return &WorkflowResult{
		FinalResult:        finalResult,
		StepResults:        stepResults,
		ProcessingMetadata: processingMetadata,
		ExecutionPath:      executionPath,
	}, nil
}

// executeExtractionStep 执行提取步骤
func (a *OrchestrationAgent) executeExtractionStep(rawText, language string, context biddingModels.A2AContext) (map[string]interface{}, error) {
	a.mu.RLock()
	extractionAgent, exists := a.agentRegistry[agentCommon.AgentExtractionName]
	a.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("extraction agent not registered")
	}

	input := map[string]interface{}{
		"raw_text":        rawText,
		"language":        language,
		"extraction_mode": agentCommon.ExtractionModeStandard,
	}

	result, err := extractionAgent.ExecuteSkill(agentCommon.SkillExtractStructuredData, input, context)
	if err != nil {
		return nil, err
	}

	return result.Output, nil
}

// executeClassificationStep 执行分类步骤
func (a *OrchestrationAgent) executeClassificationStep(extractedData *agentCommon.TenderData, language string, context biddingModels.A2AContext) (map[string]interface{}, error) {
	a.mu.RLock()
	classificationAgent, exists := a.agentRegistry[agentCommon.AgentClassificationName]
	a.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("classification agent not registered")
	}

	input := map[string]interface{}{
		"extracted_data":       extractedData,
		"classification_depth": agentCommon.ClassificationDepthLevel3,
		"language":             language,
	}

	result, err := classificationAgent.ExecuteSkill(agentCommon.SkillClassifyTender, input, context)
	if err != nil {
		return nil, err
	}

	return result.Output, nil
}

// executeEnhancementStep 执行增强步骤
func (a *OrchestrationAgent) executeEnhancementStep(extractedData *agentCommon.TenderData, classificationData *agentCommon.ClassificationResult, language string, context biddingModels.A2AContext) (map[string]interface{}, error) {
	a.mu.RLock()
	enhancementAgent, exists := a.agentRegistry[agentCommon.AgentEnhancementName]
	a.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("enhancement agent not registered")
	}

	input := map[string]interface{}{
		"extracted_data":      extractedData,
		"classification_data": classificationData,
		"language":            language,
	}

	result, err := enhancementAgent.ExecuteSkill(agentCommon.SkillEnhanceContent, input, context)
	if err != nil {
		return nil, err
	}

	return result.Output, nil
}

// executeTranslationStep 执行翻译步骤
func (a *OrchestrationAgent) executeTranslationStep(enhancementData *agentCommon.EnhancementResult, targetLanguages []string, context biddingModels.A2AContext) (map[string]interface{}, error) {
	a.mu.RLock()
	translationAgent, exists := a.agentRegistry[agentCommon.AgentMultilingualName]
	a.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("translation agent not registered")
	}

	input := map[string]interface{}{
		"chinese_data":     enhancementData,
		"target_languages": targetLanguages,
	}

	result, err := translationAgent.ExecuteSkill(agentCommon.SkillProcessMultilingual, input, context)
	if err != nil {
		return nil, err
	}

	return result.Output, nil
}

// executeAggregationStep 执行聚合步骤
func (a *OrchestrationAgent) executeAggregationStep(extractedData *agentCommon.TenderData, classificationData *agentCommon.ClassificationResult, enhancementData *agentCommon.EnhancementResult, translationData map[string]*agentCommon.TranslationData, context biddingModels.A2AContext) (map[string]interface{}, error) {
	a.mu.RLock()
	aggregationAgent, exists := a.agentRegistry[agentCommon.AgentAggregationName]
	a.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("aggregation agent not registered")
	}

	input := map[string]interface{}{
		"extracted_data":      extractedData,
		"classification_data": classificationData,
		"enhancement_data":    enhancementData,
		"translation_data":    translationData,
	}

	result, err := aggregationAgent.ExecuteSkill(agentCommon.SkillAggregateResults, input, context)
	if err != nil {
		return nil, err
	}

	return result.Output, nil
}

// executeBatchProcessing 执行批量处理
func (a *OrchestrationAgent) executeBatchProcessing(params *BatchParams, context biddingModels.A2AContext) ([]*WorkflowResult, error) {
	var results []*WorkflowResult
	var mu sync.Mutex
	var wg sync.WaitGroup

	semaphore := make(chan struct{}, params.BatchConfig.MaxConcurrency)

	for _, item := range params.BatchItems {
		wg.Add(1)
		go func(batchItem *BatchItem) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			utils.Log.Infof("Processing batch item: %s", batchItem.ID)

			// 构建工作流参数
			workflowParams := &WorkflowParams{
				RawText:           batchItem.RawText,
				Language:          batchItem.Language,
				ProcessingOptions: params.ProcessingOptions,
				WorkflowConfig: &WorkflowConfiguration{
					ParallelExecution: false,
					Timeout:           params.BatchConfig.Timeout,
					RetryAttempts:     2,
					FailFast:          false, // 批量处理不应该因为单个失败而停止
				},
			}

			// 执行单个工作流
			result, err := a.executeWorkflow(workflowParams, context)
			if err != nil {
				utils.Log.Errorf("Batch item %s failed: %v", batchItem.ID, err)
				// 创建错误结果
				result = &WorkflowResult{
					ProcessingMetadata: &agentCommon.ProcessingMetadata{
						ModelUsed:       "orchestration-v1.0",
						ProcessingTime:  0,
						ConfidenceScore: 0,
					},
					ExecutionPath: []string{"error"},
				}
			}

			// 添加到结果集
			mu.Lock()
			results = append(results, result)
			mu.Unlock()

		}(item)
	}

	wg.Wait()

	return results, nil
}

// generateBatchMetadata 生成批量处理元数据
func (a *OrchestrationAgent) generateBatchMetadata(results []*WorkflowResult) *agentCommon.ProcessingMetadata {
	var totalTime float64
	var successCount int
	var totalTokens int

	for _, result := range results {
		if result.ProcessingMetadata != nil {
			totalTime += result.ProcessingMetadata.ProcessingTime
			totalTokens += result.ProcessingMetadata.TokensConsumed
			if result.ProcessingMetadata.ConfidenceScore > 0 {
				successCount++
			}
		}
	}

	return &agentCommon.ProcessingMetadata{
		ModelUsed:       "orchestration-batch-v1.0",
		TokensConsumed:  totalTokens,
		ProcessingTime:  totalTime,
		ConfidenceScore: float64(successCount) / float64(len(results)),
	}
}
