package models

import "gitlab.com/specific-ai/taskd/internal/models/common"

// UserContext 包含用户上下文信息，用于自动token跟踪
type UserContext struct {
	UserID    string `json:"user_id"`
	CompanyID string `json:"company_id"`
	RequestID string `json:"request_id,omitempty"`
	Endpoint  string `json:"endpoint,omitempty"`
}

// ChatCompletionResponseFormat 定义响应格式
type ChatCompletionResponseFormat struct {
	Type       string                             `json:"type"`                 // "text" 或 "json_object" 或 "json_schema"
	JSONSchema *ChatCompletionResponseFormatJSON `json:"json_schema,omitempty"` // JSON Schema定义(仅当type为json_schema时)
}

// ChatCompletionResponseFormatJSON 定义JSON Schema响应格式
type ChatCompletionResponseFormatJSON struct {
	Name   string                 `json:"name"`             // Schema名称
	Schema map[string]interface{} `json:"schema"`           // JSON Schema定义
	Strict bool                   `json:"strict,omitempty"` // 是否使用严格模式
}

// OpenAICompatibleRequestParams 封装 OpenAI 兼容的聊天补全请求参数
type OpenAICompatibleRequestParams struct {
	Model                  string                         `json:"model"` // 模型别名或实际 ID
	Messages               []common.LLMMessage            `json:"messages"`
	Temperature            *float64                       `json:"temperature,omitempty"`
	MaxTokens              *int                           `json:"max_tokens,omitempty"`
	Stream                 bool                           `json:"stream,omitempty"` // 未来可支持流式响应
	FrequencyPenalty       *float64                       `json:"frequency_penalty,omitempty"`
	PresencePenalty        *float64                       `json:"presence_penalty,omitempty"`
	ResponseFormat         *ChatCompletionResponseFormat  `json:"response_format,omitempty"`    // 响应格式定义
	ProviderSpecificParams map[string]interface{}         `json:"provider_specific_params,omitempty"` // 预留给非标准参数

	// Token跟踪相关
	UserContext     *UserContext `json:"user_context,omitempty"`      // 用户上下文，用于自动token跟踪
	AutoTrackTokens bool         `json:"auto_track_tokens,omitempty"` // 是否自动跟踪token消耗
}