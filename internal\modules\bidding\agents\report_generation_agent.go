package agents

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ReportGenerationAgent handles final report generation
type ReportGenerationAgent struct {
	*BaseAgent
	llmService    services.LLMService
	promptManager *prompts.BiddingPromptManager
	modelID       string
}

// NewReportGenerationAgent creates a new report generation agent
func NewReportGenerationAgent(llmService services.LLMService, promptManager *prompts.BiddingPromptManager, modelID string) *ReportGenerationAgent {
	agentCard := biddingModels.AgentCard{
		Name:        "Report Generation Agent",
		Description: "基于搜索结果和分析数据生成最终的招投标背景分析报告",
		URL:         "http://taskd-service:8601/agents/report-generation",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version:          "1.0.0",
		DocumentationURL: "https://docs.taskd.platform/agents/report-generation",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          "generate_comprehensive_report",
				Name:        "生成最终分析报告",
				Description: "整合所有分析数据，生成comprehensive的招投标背景分析报告",
				Tags:        []string{"report", "analysis", "aggregation", "ai"},
				Examples: []string{
					"生成政府AI采购项目的完整背景分析报告",
					"整合市场信息生成投标建议报告",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
				InputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"search_results": map[string]interface{}{
							"type":        "array",
							"description": "需求分析Agent输出的搜索结果",
						},
						"summary_data": map[string]interface{}{
							"type":        "object",
							"description": "AI摘要数据",
						},
						"requirement_analysis": map[string]interface{}{
							"type":        "object",
							"description": "需求分析结果",
						},
						"report_template": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"standard", "executive", "technical", "market_analysis"},
							"default":     "standard",
							"description": "报告模板类型",
						},
						"focus_areas": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "报告重点关注领域",
						},
						"target_audience": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"technical", "business", "executive", "general"},
							"default":     "business",
							"description": "目标受众类型",
						},
						"language": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"chinese", "english"},
							"default":     "chinese",
							"description": "报告语言偏好 / Report language preference",
						},
					},
					"required": []string{"search_results", "summary_data", "requirement_analysis"},
				},
				OutputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"final_report": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"executive_summary":     map[string]interface{}{"type": "string", "description": "执行摘要"},
								"project_overview":      map[string]interface{}{"type": "string", "description": "项目概述"},
								"market_analysis":       map[string]interface{}{"type": "string", "description": "市场分析"},
								"technical_background":  map[string]interface{}{"type": "string", "description": "技术背景"},
								"competitive_landscape": map[string]interface{}{"type": "string", "description": "竞争格局"},
								"recommendations":       map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}, "description": "建议列表"},
								"risk_assessment":       map[string]interface{}{"type": "array", "description": "风险评估"},
							},
						},
						"report_metadata": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"generation_time":  map[string]interface{}{"type": "string"},
								"word_count":       map[string]interface{}{"type": "integer"},
								"confidence_score": map[string]interface{}{"type": "number"},
								"sources_count":    map[string]interface{}{"type": "integer"},
								"report_version":   map[string]interface{}{"type": "string"},
							},
						},
						"appendices": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"source_references": map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "object"}},
								"data_tables":       map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "object"}},
							},
						},
					},
				},
			},
		},
	}

	baseAgent := NewBaseAgent("report-generation", agentCard)

	return &ReportGenerationAgent{
		BaseAgent:     baseAgent,
		llmService:    llmService,
		promptManager: promptManager,
		modelID:       modelID,
	}
}

// ExecuteSkill executes the specified skill
func (agent *ReportGenerationAgent) ExecuteSkill(skillID string, input map[string]interface{},
	context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {

	switch skillID {
	case "generate_comprehensive_report":
		return agent.ExecuteSkillWithHandler(skillID, input, context, agent.generateFinalReport)
	default:
		return nil, fmt.Errorf("unknown skill: %s", skillID)
	}
}

// generateFinalReport handles the final report generation logic
func (agent *ReportGenerationAgent) generateFinalReport(input map[string]interface{},
	a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {

	// Parse input parameters
	searchResultsRaw, ok := input["search_results"]
	if !ok {
		return nil, fmt.Errorf("search_results is required")
	}

	summaryDataRaw, ok := input["summary_data"]
	if !ok {
		return nil, fmt.Errorf("summary_data is required")
	}

	requirementAnalysisRaw, ok := input["requirement_analysis"]
	if !ok {
		return nil, fmt.Errorf("requirement_analysis is required")
	}

	// Convert to proper structures
	searchResults, err := agent.parseSearchResults(searchResultsRaw)
	if err != nil {
		return nil, fmt.Errorf("failed to parse search results: %w", err)
	}

	summaryData, err := agent.parseSummaryData(summaryDataRaw)
	if err != nil {
		return nil, fmt.Errorf("failed to parse summary data: %w", err)
	}

	requirementAnalysis, err := agent.parseRequirementAnalysis(requirementAnalysisRaw)
	if err != nil {
		return nil, fmt.Errorf("failed to parse requirement analysis: %w", err)
	}

	reportTemplate := "standard"
	if template, exists := input["report_template"]; exists {
		if templateStr, ok := template.(string); ok {
			reportTemplate = templateStr
		}
	}

	var focusAreas []string
	if areas, exists := input["focus_areas"]; exists {
		if areasArray, ok := areas.([]interface{}); ok {
			for _, area := range areasArray {
				if areaStr, ok := area.(string); ok {
					focusAreas = append(focusAreas, areaStr)
				}
			}
		}
	}

	targetAudience := "business"
	if audience, exists := input["target_audience"]; exists {
		if audienceStr, ok := audience.(string); ok {
			targetAudience = audienceStr
		}
	}

	utils.Log.Infof("Generating final report: Template=%s, Audience=%s, Sources=%d",
		reportTemplate, targetAudience, len(searchResults))

	// Generate the report
	var finalReport biddingModels.FinalReport
	var processingMetadata biddingModels.ProcessingMetadata

	ctx, cancel := context.WithTimeout(context.Background(), 180*time.Second)
	defer cancel()
	err = agent.generateReportWithLLM(ctx, searchResults, summaryData, requirementAnalysis,
		reportTemplate, focusAreas, targetAudience, &finalReport, &processingMetadata, a2aContext)

	if err != nil {
		return nil, fmt.Errorf("failed to generate report: %w", err)
	}

	// Update metrics
	agent.UpdateMetrics(processingMetadata.TokensConsumed)

	// Build metadata
	reportMetadata := biddingModels.ReportMetadata{
		GenerationTime:  time.Now(),
		WordCount:       agent.calculateWordCount(finalReport),
		ConfidenceScore: agent.calculateConfidenceScore(finalReport, searchResults),
		SourcesCount:    len(searchResults),
		ReportVersion:   "1.0",
	}

	// Build appendices
	appendices := biddingModels.ReportAppendices{
		SourceReferences: agent.buildSourceReferences(searchResults),
		DataTables:       agent.buildDataTables(summaryData),
	}

	// Build response
	response := map[string]interface{}{
		"final_report":    finalReport,
		"report_metadata": reportMetadata,
		"appendices":      appendices,
	}

	utils.Log.Infof("Successfully generated final report (words: %d, confidence: %.2f)",
		reportMetadata.WordCount, reportMetadata.ConfidenceScore)

	return response, nil
}

// parseSearchResults converts raw search results to structured format
func (agent *ReportGenerationAgent) parseSearchResults(raw interface{}) ([]biddingModels.SearchResult, error) {
	var searchResults []biddingModels.SearchResult

	jsonData, err := json.Marshal(raw)
	if err != nil {
		return searchResults, fmt.Errorf("failed to marshal search results: %w", err)
	}

	err = json.Unmarshal(jsonData, &searchResults)
	if err != nil {
		return searchResults, fmt.Errorf("failed to unmarshal search results: %w", err)
	}

	return searchResults, nil
}

// parseSummaryData converts raw summary data to structured format
func (agent *ReportGenerationAgent) parseSummaryData(raw interface{}) (biddingModels.TenderSummary, error) {
	var summaryData biddingModels.TenderSummary

	jsonData, err := json.Marshal(raw)
	if err != nil {
		return summaryData, fmt.Errorf("failed to marshal summary data: %w", err)
	}

	err = json.Unmarshal(jsonData, &summaryData)
	if err != nil {
		return summaryData, fmt.Errorf("failed to unmarshal summary data: %w", err)
	}

	return summaryData, nil
}

// parseRequirementAnalysis converts raw requirement analysis to structured format
func (agent *ReportGenerationAgent) parseRequirementAnalysis(raw interface{}) (biddingModels.RequirementAnalysis, error) {
	var requirementAnalysis biddingModels.RequirementAnalysis

	jsonData, err := json.Marshal(raw)
	if err != nil {
		return requirementAnalysis, fmt.Errorf("failed to marshal requirement analysis: %w", err)
	}

	err = json.Unmarshal(jsonData, &requirementAnalysis)
	if err != nil {
		return requirementAnalysis, fmt.Errorf("failed to unmarshal requirement analysis: %w", err)
	}

	return requirementAnalysis, nil
}

// generateReportWithLLM uses the LLM service to generate the final report
func (agent *ReportGenerationAgent) generateReportWithLLM(ctx context.Context,
	searchResults []biddingModels.SearchResult, summaryData biddingModels.TenderSummary,
	requirementAnalysis biddingModels.RequirementAnalysis, reportTemplate string,
	focusAreas []string, targetAudience string, finalReport *biddingModels.FinalReport,
	metadata *biddingModels.ProcessingMetadata, a2aContext biddingModels.A2AContext) error {

	// Build the report generation prompt
	prompt := agent.buildReportPrompt(searchResults, summaryData, requirementAnalysis,
		reportTemplate, focusAreas, targetAudience)

	// Prepare LLM request
	maxTokens := 6000
	temperature := 0.4
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model:    agent.modelID, // Or get from config
		Messages: []common.LLMMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   &maxTokens,
		Temperature: &temperature,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	startTime := time.Now()

	// Call LLM service
	response, err := agent.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return fmt.Errorf("LLM service error: %w", err)
	}

	processingTime := time.Since(startTime).Seconds()

	// Parse response
	err = agent.parseReportResponse(response.Result, finalReport)
	if err != nil {
		return fmt.Errorf("failed to parse LLM response: %w", err)
	}

	// Set metadata
	*metadata = biddingModels.ProcessingMetadata{
		ModelUsed:      llmRequest.Model,
		TokensConsumed: response.TokenUsage.TotalTokens,
		ProcessingTime: processingTime,
	}

	// 不再需要手动记录token消耗，因为LLM服务会自动记录
	// 如果需要额外的日志记录，可以保留以下代码
	utils.Log.Debugf("报告生成Agent %s 消耗tokens: input=%d, output=%d, total=%d",
		llmRequest.Model, response.TokenUsage.InputTokens, response.TokenUsage.OutputTokens, response.TokenUsage.TotalTokens)

	return nil
}

// buildReportPrompt constructs the prompt for report generation
func (agent *ReportGenerationAgent) buildReportPrompt(searchResults []biddingModels.SearchResult,
	summaryData biddingModels.TenderSummary, requirementAnalysis biddingModels.RequirementAnalysis,
	reportTemplate string, focusAreas []string, targetAudience string) string {

	var promptBuilder strings.Builder

	promptBuilder.WriteString("你是资深的招投标分析师，请基于以下信息生成一份专业的招投标背景分析报告。\n\n")

	// Add project summary
	promptBuilder.WriteString("## 项目基本信息\n")
	promptBuilder.WriteString(fmt.Sprintf("项目标题：%s\n", summaryData.Title))
	promptBuilder.WriteString(fmt.Sprintf("优化标题：%s\n", summaryData.NewTitle))
	promptBuilder.WriteString(fmt.Sprintf("项目摘要：%s\n", summaryData.SummaryText))
	promptBuilder.WriteString(fmt.Sprintf("截止日期：%s\n", summaryData.Deadline))

	if len(summaryData.KeyRequirements) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("关键要求：%s\n", strings.Join(summaryData.KeyRequirements, "、")))
	}

	// Add requirement analysis
	promptBuilder.WriteString("\n## 需求分析结果\n")
	if len(requirementAnalysis.KeyRequirements) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("核心需求：%s\n", strings.Join(requirementAnalysis.KeyRequirements, "、")))
	}
	if len(requirementAnalysis.TechnicalKeywords) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("技术关键词：%s\n", strings.Join(requirementAnalysis.TechnicalKeywords, "、")))
	}
	if len(requirementAnalysis.BusinessKeywords) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("商务关键词：%s\n", strings.Join(requirementAnalysis.BusinessKeywords, "、")))
	}

	// Add search results
	promptBuilder.WriteString("\n## 背景搜索结果\n")
	for i, result := range searchResults {
		promptBuilder.WriteString(fmt.Sprintf("%d. %s\n", i+1, result.Title))
		promptBuilder.WriteString(fmt.Sprintf("   来源：%s\n", result.Source))
		promptBuilder.WriteString(fmt.Sprintf("   摘要：%s\n", result.Snippet))
		promptBuilder.WriteString(fmt.Sprintf("   相关度：%.2f\n\n", result.RelevanceScore))
	}

	// Add focus areas if specified
	if len(focusAreas) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("\n特别关注领域：%s\n", strings.Join(focusAreas, "、")))
	}

	// Add target audience specification
	audienceMap := map[string]string{
		"technical": "技术人员",
		"business":  "商务人员",
		"executive": "高管决策层",
		"general":   "一般业务人员",
	}
	if audience, exists := audienceMap[targetAudience]; exists {
		promptBuilder.WriteString(fmt.Sprintf("\n目标读者：%s\n", audience))
	}

	// Add output format instruction
	promptBuilder.WriteString(`
请按以下JSON格式生成完整的分析报告：
{
  "executive_summary": "执行摘要（200-300字，概括项目核心信息和关键发现）",
  "project_overview": "项目概述（300-400字，详细描述项目背景、目标和范围）",
  "market_analysis": "市场分析（400-500字，基于搜索结果分析市场现状、趋势和机会）",
  "technical_background": "技术背景（400-500字，分析相关技术发展状况和要求）",
  "competitive_landscape": "竞争格局（300-400字，分析主要竞争者和市场格局）",
  "recommendations": ["建议1", "建议2", "建议3", "..."],
  "risk_assessment": [
    {
      "risk": "风险描述",
      "impact": "影响程度（高/中/低）",
      "mitigation": "缓解措施"
    }
  ]
}

要求：
1. 内容要专业、客观、有深度
2. 基于提供的搜索结果进行分析，不要编造信息
3. 建议要具体、可操作
4. 风险评估要全面、实用
5. 确保返回有效的JSON格式`)

	return promptBuilder.String()
}

// parseReportResponse parses the LLM response into final report structure
func (agent *ReportGenerationAgent) parseReportResponse(content string,
	finalReport *biddingModels.FinalReport) error {

	// Clean and extract JSON
	content = strings.TrimSpace(content)
	start := strings.Index(content, "{")
	end := strings.LastIndex(content, "}") + 1

	if start == -1 || end <= start {
		return fmt.Errorf("no valid JSON found in report response")
	}

	jsonContent := content[start:end]

	// Parse JSON response
	var response map[string]interface{}
	err := json.Unmarshal([]byte(jsonContent), &response)
	if err != nil {
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// Extract report sections
	if executiveSummary, ok := response["executive_summary"].(string); ok {
		finalReport.ExecutiveSummary = executiveSummary
	}
	if projectOverview, ok := response["project_overview"].(string); ok {
		finalReport.ProjectOverview = projectOverview
	}
	if marketAnalysis, ok := response["market_analysis"].(string); ok {
		finalReport.MarketAnalysis = marketAnalysis
	}
	if technicalBackground, ok := response["technical_background"].(string); ok {
		finalReport.TechnicalBackground = technicalBackground
	}
	if competitiveLandscape, ok := response["competitive_landscape"].(string); ok {
		finalReport.CompetitiveLandscape = competitiveLandscape
	}

	// Extract recommendations
	if recommendations, ok := response["recommendations"].([]interface{}); ok {
		for _, rec := range recommendations {
			if recStr, ok := rec.(string); ok {
				finalReport.Recommendations = append(finalReport.Recommendations, recStr)
			}
		}
	}

	// Extract risk assessment
	if risks, ok := response["risk_assessment"].([]interface{}); ok {
		for _, risk := range risks {
			if riskMap, ok := risk.(map[string]interface{}); ok {
				riskItem := biddingModels.RiskItem{}
				if riskDesc, ok := riskMap["risk"].(string); ok {
					riskItem.Risk = riskDesc
				}
				if impact, ok := riskMap["impact"].(string); ok {
					riskItem.Impact = impact
				}
				if mitigation, ok := riskMap["mitigation"].(string); ok {
					riskItem.Mitigation = mitigation
				}
				finalReport.RiskAssessment = append(finalReport.RiskAssessment, riskItem)
			}
		}
	}

	return nil
}

// calculateWordCount estimates the word count of the report
func (agent *ReportGenerationAgent) calculateWordCount(report biddingModels.FinalReport) int {
	totalText := report.ExecutiveSummary + " " + report.ProjectOverview + " " +
		report.MarketAnalysis + " " + report.TechnicalBackground + " " +
		report.CompetitiveLandscape

	// Rough word count (considering Chinese characters)
	return len([]rune(totalText))
}

// calculateConfidenceScore calculates confidence based on report completeness and source quality
func (agent *ReportGenerationAgent) calculateConfidenceScore(report biddingModels.FinalReport,
	sources []biddingModels.SearchResult) float64 {

	score := 0.0
	maxScore := 8.0

	// Check report completeness
	if report.ExecutiveSummary != "" {
		score += 1.0
	}
	if report.ProjectOverview != "" {
		score += 1.0
	}
	if report.MarketAnalysis != "" {
		score += 1.5
	}
	if report.TechnicalBackground != "" {
		score += 1.5
	}
	if report.CompetitiveLandscape != "" {
		score += 1.0
	}
	if len(report.Recommendations) > 0 {
		score += 1.0
	}
	if len(report.RiskAssessment) > 0 {
		score += 1.0
	}

	// Adjust based on source quality
	if len(sources) >= 3 {
		avgRelevance := 0.0
		for _, source := range sources {
			avgRelevance += source.RelevanceScore
		}
		avgRelevance /= float64(len(sources))
		score *= avgRelevance
	} else {
		score *= 0.8 // Penalty for insufficient sources
	}

	confidence := score / maxScore
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// buildSourceReferences creates source references for the appendix
func (agent *ReportGenerationAgent) buildSourceReferences(results []biddingModels.SearchResult) []biddingModels.SourceReference {
	var references []biddingModels.SourceReference

	for i, result := range results {
		reference := biddingModels.SourceReference{
			ID:        i + 1,
			Title:     result.Title,
			URL:       result.URL,
			Relevance: fmt.Sprintf("%.0f%%", result.RelevanceScore*100),
		}
		references = append(references, reference)
	}

	return references
}

// buildDataTables creates data tables for the appendix
func (agent *ReportGenerationAgent) buildDataTables(summaryData biddingModels.TenderSummary) []biddingModels.DataTable {
	var tables []biddingModels.DataTable

	// Budget information table
	if summaryData.BudgetInfo.Amount != "" {
		budgetTable := biddingModels.DataTable{
			Title: "项目预算信息",
			Data: map[string]interface{}{
				"预算金额": summaryData.BudgetInfo.Amount,
				"预算类型": summaryData.BudgetInfo.Type,
				"付款条件": summaryData.BudgetInfo.PaymentTerms,
			},
		}
		tables = append(tables, budgetTable)
	}

	// Timeline table
	if len(summaryData.Timeline.KeyMilestones) > 0 {
		timelineTable := biddingModels.DataTable{
			Title: "项目时间线",
			Data: map[string]interface{}{
				"投标截止":  summaryData.Timeline.BiddingDeadline,
				"项目周期":  summaryData.Timeline.ProjectDuration,
				"关键里程碑": summaryData.Timeline.KeyMilestones,
			},
		}
		tables = append(tables, timelineTable)
	}

	return tables
}

// HealthCheck returns enhanced health status for this agent
func (agent *ReportGenerationAgent) HealthCheck() biddingModels.HealthStatus {
	status := agent.BaseAgent.HealthCheck()

	// Check LLM service availability
	if agent.llmService == nil {
		status.Status = biddingModels.AgentStatusUnhealthy
		status.Dependencies["llm_service"] = "unavailable"
	} else {
		status.Dependencies["llm_service"] = "available"
	}

	return status
}
