package services

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/business"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	profileModels "gitlab.com/specific-ai/taskd/internal/modules/profile/models"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

const (
	PromptKeyCompanyProfile = "company_profile" // 企业画像业务专用
	ProfilePromptModule     = "ovs-profile"     // 企业画像prompt模块路径
)

// ProfileService 公司画像服务实现（只提供AI生成能力）
type ProfileService struct {
	llmClient     llm.LLMClient
	promptManager *prompts.PromptManager
	schemaService *SchemaService
}

// NewProfileService 创建 ProfileService 实例
func NewProfileService(llmClient llm.LLMClient, pm *prompts.PromptManager) *ProfileService {
	return &ProfileService{
		llmClient:     llmClient,
		promptManager: pm,
		schemaService: NewSchemaService(),
	}
}

// GenerateCompanyProfile 生成公司画像
func (s *ProfileService) GenerateCompanyProfile(req *profileModels.ProfileRequest) (*profileModels.ProfileResponse, error) {
	startTime := time.Now()

	// 设置默认语言
	if req.Language == "" {
		req.Language = "english"
	}

	utils.Log.Infof("开始为企业 %s (用户ID: %s) 生成画像，语言: %s", req.CompanyName, req.UserContext.UserID, req.Language)

	// 根据语言获取 prompt 模板 (从 ovs-profile 模块)
	promptSet, err := s.promptManager.GetPromptSetByLanguage(ProfilePromptModule, req.Language, PromptKeyCompanyProfile)
	if err != nil {
		return nil, fmt.Errorf("获取 '%s' prompts (语言: %s) 失败: %w", PromptKeyCompanyProfile, req.Language, err)
	}

	// 构建prompt数据
	promptData := s.buildPromptData(req)

	// 格式化系统和用户prompt
	sysContent, err := s.promptManager.FormatPrompt(promptSet.SysPrompt, promptData)
	if err != nil {
		return nil, fmt.Errorf("格式化 system prompt 失败: %w", err)
	}
	userContent, err := s.promptManager.FormatPrompt(promptSet.UserPrompt, promptData)
	if err != nil {
		return nil, fmt.Errorf("格式化 user prompt 失败: %w", err)
	}

	// 准备 LLM 请求
	messages := []common.LLMMessage{
		{
			Role:    "system",
			Content: sysContent,
		},
		{
			Role:    "user",
			Content: userContent,
		},
	}

	// 设置请求参数
	temperature := 0.1 // 使用更低的温度以获得更一致的结构化结果
	maxTokens := 4000  // 为结构化输出预留足够空间

	llmReqParams := models.OpenAICompatibleRequestParams{
		Messages:    messages,
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		ResponseFormat: &models.ChatCompletionResponseFormat{
			Type: "json_schema",
			JSONSchema: &models.ChatCompletionResponseFormatJSON{
				Name:   "company_profile",
				Schema: s.schemaService.GetCompanyProfileSchema(),
				Strict: true, // 使用严格模式确保输出符合schema
			},
		},
	}

	// 调用 LLM
	rawContent, err := s.llmClient.ChatCompletions(llmReqParams)
	if err != nil {
		utils.Log.Errorf("LLM 调用失败: %v", err)
		return nil, fmt.Errorf("生成企业画像失败: %w", err)
	}

	// 使用Structured Outputs后，应该直接得到有效的JSON
	rawContent = strings.TrimSpace(rawContent)

	// 解析企业画像数据（Structured Outputs应该保证输出格式正确）
	profileData, err := s.parseProfileData(rawContent)
	if err != nil {
		utils.Log.Errorf("解析画像数据失败: %v, 原始内容: %s", err, rawContent)
		return nil, fmt.Errorf("解析企业画像数据失败: %w", err)
	}

	// 构建响应
	processingTime := time.Since(startTime).Milliseconds()
	response := &profileModels.ProfileResponse{
		UserID:                    req.UserContext.UserID,
		CompanyName:               req.CompanyName,
		BusinessCapabilities:      profileData.BusinessCapabilities,
		TenderMatching:            profileData.TenderMatching,
		CompetitiveProfile:        profileData.CompetitiveProfile,
		InternationalCapabilities: profileData.InternationalCapabilities,
		RiskTolerance:             profileData.RiskTolerance,
		ConfidenceScore:           profileData.ConfidenceScore,
		GeneratedAt:               time.Now(),
		Metadata: &business.ProfileMetadata{
			ModelUsed:       "company_profile_model_structured",
			TokensUsed:      common.TokenUsage{}, // 暂时为空，后续可添加token统计
			ProcessingTime:  processingTime,
			StrategyVersion: "v2.0.0", // 更新版本号反映Structured Outputs升级
			DataSources:     []string{"llm_structured_analysis", "company_info", "json_schema_validation"},
		},
	}

	utils.Log.Infof("成功生成企业画像，用时 %dms，置信度 %d", processingTime, profileData.ConfidenceScore)

	// 只返回AI生成结果，不保存到数据库
	return response, nil
}

// parseProfileData 解析画像数据结构体
type parseProfileData struct {
	BusinessCapabilities      *business.BusinessCapabilities      `json:"business_capabilities"`
	TenderMatching            *business.TenderMatching            `json:"tender_matching"`
	CompetitiveProfile        *business.CompetitiveProfile        `json:"competitive_profile"`
	InternationalCapabilities *business.InternationalCapabilities `json:"international_capabilities"`
	RiskTolerance             *business.RiskTolerance             `json:"risk_tolerance"`
	ConfidenceScore           int                                 `json:"confidence_score"`
}

// parseProfileData 解析企业画像数据
func (s *ProfileService) parseProfileData(jsonContent string) (*parseProfileData, error) {
	var profileData parseProfileData

	if err := json.Unmarshal([]byte(jsonContent), &profileData); err != nil {
		return nil, fmt.Errorf("JSON 解析失败: %w", err)
	}

	// 验证必要字段
	if profileData.BusinessCapabilities == nil {
		return nil, fmt.Errorf("缺少业务能力画像数据")
	}
	if profileData.TenderMatching == nil {
		return nil, fmt.Errorf("缺少招投标适配性数据")
	}
	if profileData.CompetitiveProfile == nil {
		return nil, fmt.Errorf("缺少竞争力分析数据")
	}
	if profileData.InternationalCapabilities == nil {
		return nil, fmt.Errorf("缺少海外业务能力数据")
	}
	if profileData.RiskTolerance == nil {
		return nil, fmt.Errorf("缺少风险承受能力数据")
	}

	// 设置默认置信度分数
	if profileData.ConfidenceScore == 0 {
		profileData.ConfidenceScore = 75 // 默认置信度
	}

	return &profileData, nil
}

// buildPromptData 构建prompt数据，主要基于web搜索结果和用户输入
func (s *ProfileService) buildPromptData(req *profileModels.ProfileRequest) map[string]interface{} {
	// 基础prompt数据，只包含必要字段
	promptData := map[string]interface{}{
		"CompanyName": req.CompanyName,
	}

	// 添加web搜索结果（主要数据源）
	if len(req.WebSearchResults) > 0 {
		promptData["WebSearchResults"] = req.WebSearchResults
	}

	// 添加用户输入字段
	if len(req.UserInput) > 0 {
		promptData["UserInput"] = req.UserInput

		// 解析用户输入中的特殊要求
		userRequirements := s.parseUserRequirements(req.UserInput)
		if len(userRequirements) > 0 {
			promptData["UserRequirements"] = userRequirements
		}
	}

	return promptData
}

// parseUserRequirements 解析用户输入中的特殊要求
func (s *ProfileService) parseUserRequirements(userInputs []business.UserInputField) []string {
	var requirements []string

	for _, input := range userInputs {
		if input.UserInput != "" {
			// 构建用户要求的描述
			requirement := fmt.Sprintf("用户对%s字段的要求: %s", input.Field, input.UserInput)
			if len(input.Options) > 0 {
				requirement += fmt.Sprintf(" (可选项: %v)", input.Options)
			}
			if input.Description != "" {
				requirement += fmt.Sprintf(" - %s", input.Description)
			}
			requirements = append(requirements, requirement)
		}
	}

	return requirements
}
