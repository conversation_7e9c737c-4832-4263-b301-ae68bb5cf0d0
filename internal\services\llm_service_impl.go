package services

import (
	"context"
	"time"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// llmServiceImpl LLM服务实现
type llmServiceImpl struct {
	llmClient    llm.LLMClient
	tokenService TokenService
}

// NewLLMService 创建LLM服务实例
func NewLLMService(llmClient llm.LLMClient, tokenService TokenService) LLMService {
	return &llmServiceImpl{
		llmClient:    llmClient,
		tokenService: tokenService,
	}
}

// ProcessRequest 实现LLMService接口
func (s *llmServiceImpl) ProcessRequest(ctx context.Context, params models.OpenAICompatibleRequestParams) (*models.LLMResponseMessage, error) {
	startTime := time.Now()

	// 检查是否启用了token跟踪且有用户上下文
	if params.AutoTrackTokens && params.UserContext != nil {
		// 使用支持token跟踪的方法
		response, err := s.llmClient.ChatCompletionsWithUsage(params)
		duration := time.Since(startTime)

		if err != nil {
			utils.Log.Errorf("LLM request failed: %v", err)
			return &models.LLMResponseMessage{
				Success:  false,
				Error:    err.Error(),
				Duration: duration,
			}, err
		}

		result := ""
		if len(response.Choices) > 0 {
			result = response.Choices[0].Message.Content
		}

		tokenUsage := common.TokenUsage{
			InputTokens:  response.Usage.PromptTokens,
			OutputTokens: response.Usage.CompletionTokens,
			TotalTokens:  response.Usage.TotalTokens,
		}

		utils.Log.Infof("LLM request completed with token tracking in %v, tokens: input=%d, output=%d, total=%d",
			duration, tokenUsage.InputTokens, tokenUsage.OutputTokens, tokenUsage.TotalTokens)

		return &models.LLMResponseMessage{
			Success:    true,
			Result:     result,
			TokenUsage: tokenUsage,
			Duration:   duration,
		}, nil
	} else {
		// 使用普通方法（向后兼容）
		result, err := s.llmClient.ChatCompletions(params)
		duration := time.Since(startTime)

		if err != nil {
			utils.Log.Errorf("LLM request failed: %v", err)
			return &models.LLMResponseMessage{
				Success:  false,
				Error:    err.Error(),
				Duration: duration,
			}, err
		}

		// 估算token使用情况（向后兼容）
		tokenUsage := common.TokenUsage{
			InputTokens:  estimatePromptTokens(params.Messages),
			OutputTokens: estimateCompletionTokens(result),
			TotalTokens:  0, // 将在下面计算
		}
		tokenUsage.TotalTokens = tokenUsage.InputTokens + tokenUsage.OutputTokens

		utils.Log.Infof("LLM request completed in %v, tokens: %+v", duration, tokenUsage)

		return &models.LLMResponseMessage{
			Success:    true,
			Result:     result,
			TokenUsage: tokenUsage,
			Duration:   duration,
		}, nil
	}
}

// estimatePromptTokens 估算输入token数
func estimatePromptTokens(messages []common.LLMMessage) int {
	totalChars := 0
	for _, msg := range messages {
		totalChars += len(msg.Content)
	}
	return totalChars / 4 // 粗略估算
}

// estimateCompletionTokens 估算输出token数
func estimateCompletionTokens(result string) int {
	return len(result) / 4 // 粗略估算
}
