package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// errorHandler 错误处理器实现
type errorHandler struct {
	promptBuilder   PromptBuilder
	responseParser  ResponseParser
	inputValidator  InputValidator
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(promptBuilder PromptBuilder, responseParser ResponseParser, inputValidator InputValidator) ErrorHandler {
	return &errorHandler{
		promptBuilder:  promptBuilder,
		responseParser: responseParser,
		inputValidator: inputValidator,
	}
}

// HandleError 处理错误
func (eh *errorHandler) HandleError(ctx context.Context, err error, req *models.ExtractionRequest) *models.ExtractionResult {
	errorCode := eh.classifyError(err)
	
	switch errorCode {
	case models.ErrLLMCallFailed, models.ErrLLMTimeout, models.ErrLLMRateLimit:
		return eh.handleLLMError(ctx, err, req, errorCode)
	case models.ErrResponseParseFailed, models.ErrInvalidJSON:
		return eh.handleParseError(ctx, err, req, errorCode)
	case models.ErrExtractionImpossible:
		return eh.HandleExtractionImpossible(ctx, req.InputText, req.TargetSchema.Name)
	default:
		return eh.handleGenericError(ctx, err, req, errorCode)
	}
}

// classifyError 分类错误
func (eh *errorHandler) classifyError(err error) string {
	errorMsg := strings.ToLower(err.Error())
	
	// LLM相关错误
	if strings.Contains(errorMsg, "timeout") || strings.Contains(errorMsg, "超时") {
		return models.ErrLLMTimeout
	}
	if strings.Contains(errorMsg, "rate limit") || strings.Contains(errorMsg, "限流") {
		return models.ErrLLMRateLimit
	}
	if strings.Contains(errorMsg, "llm") || strings.Contains(errorMsg, "api") {
		return models.ErrLLMCallFailed
	}
	
	// 解析相关错误
	if strings.Contains(errorMsg, "json") || strings.Contains(errorMsg, "parse") || strings.Contains(errorMsg, "解析") {
		return models.ErrResponseParseFailed
	}
	
	// 提取不可能错误
	if strings.Contains(errorMsg, "extraction_impossible") || strings.Contains(errorMsg, "无法提取") {
		return models.ErrExtractionImpossible
	}
	
	// 输入相关错误
	if strings.Contains(errorMsg, "input") || strings.Contains(errorMsg, "输入") {
		return models.ErrInvalidInput
	}
	
	// 默认为内部错误
	return models.ErrInternalError
}

// handleLLMError 处理LLM错误
func (eh *errorHandler) handleLLMError(ctx context.Context, err error, req *models.ExtractionRequest, errorCode string) *models.ExtractionResult {
	return &models.ExtractionResult{
		Success:    false,
		Confidence: 0.0,
		Error: models.NewExtractionError(
			errorCode,
			eh.getLocalizedErrorMessage(errorCode, req.Language),
			map[string]interface{}{
				"original_error": err.Error(),
				"retry_suggested": models.IsRetryableError(errorCode),
				"suggestions": eh.getLLMErrorSuggestions(errorCode, req.Language),
			},
		),
		Metadata: models.ExtractionMetadata{
			ProcessingTime: 0,
			Language:       req.Language,
			ProcessedAt:    time.Now(),
		},
	}
}

// handleParseError 处理解析错误
func (eh *errorHandler) handleParseError(ctx context.Context, err error, req *models.ExtractionRequest, errorCode string) *models.ExtractionResult {
	return &models.ExtractionResult{
		Success:    false,
		Confidence: 0.0,
		Error: models.NewExtractionError(
			errorCode,
			eh.getLocalizedErrorMessage(errorCode, req.Language),
			map[string]interface{}{
				"original_error": err.Error(),
				"suggestions": eh.getParseErrorSuggestions(req.Language),
			},
		),
		Metadata: models.ExtractionMetadata{
			ProcessingTime: 0,
			Language:       req.Language,
			ProcessedAt:    time.Now(),
		},
	}
}

// handleGenericError 处理通用错误
func (eh *errorHandler) handleGenericError(ctx context.Context, err error, req *models.ExtractionRequest, errorCode string) *models.ExtractionResult {
	return &models.ExtractionResult{
		Success:    false,
		Confidence: 0.0,
		Error: models.NewExtractionError(
			errorCode,
			eh.getLocalizedErrorMessage(errorCode, req.Language),
			map[string]interface{}{
				"original_error": err.Error(),
				"suggestions": eh.getGenericErrorSuggestions(req.Language),
			},
		),
		Metadata: models.ExtractionMetadata{
			ProcessingTime: 0,
			Language:       req.Language,
			ProcessedAt:    time.Now(),
		},
	}
}

// HandleExtractionImpossible 处理无法提取情况
func (eh *errorHandler) HandleExtractionImpossible(ctx context.Context, inputText, modelName string) *models.ExtractionResult {
	language := eh.detectLanguage(inputText)
	
	return &models.ExtractionResult{
		Success:    false,
		Confidence: 0.0,
		Error: models.NewExtractionError(
			models.ErrExtractionImpossible,
			eh.getExtractionImpossibleMessage(modelName, language),
			map[string]interface{}{
				"input_text": inputText,
				"model_name": modelName,
				"reason":     "输入文本与目标模型无关联性",
				"suggestions": eh.getExtractionImpossibleSuggestions(language),
			},
		),
		Metadata: models.ExtractionMetadata{
			ProcessingTime: 0,
			Language:       language,
			ProcessedAt:    time.Now(),
		},
	}
}

// HandleLowConfidence 处理置信度过低情况
func (eh *errorHandler) HandleLowConfidence(ctx context.Context, result *models.ExtractionResult, threshold float64) *models.ExtractionResult {
	language := result.Metadata.Language
	
	// 创建新的错误结果，但保留原始数据用于参考
	return &models.ExtractionResult{
		Success:    false,
		Data:       result.Data, // 保留提取的数据
		Confidence: result.Confidence,
		Error: models.NewExtractionError(
			models.ErrLowConfidence,
			eh.getLowConfidenceMessage(result.Confidence, threshold, language),
			map[string]interface{}{
				"confidence":       result.Confidence,
				"threshold":        threshold,
				"extracted_data":   result.Data,
				"suggestions":      eh.getLowConfidenceSuggestions(language),
				"partial_result":   true,
			},
		),
		Metadata: result.Metadata,
		RawOutput: result.RawOutput,
	}
}

// ShouldRetry 判断是否应该重试
func (eh *errorHandler) ShouldRetry(err error, retryCount int) bool {
	if retryCount >= 3 { // 最大重试3次
		return false
	}
	
	errorCode := eh.classifyError(err)
	return models.IsRetryableError(errorCode)
}

// getLocalizedErrorMessage 获取本地化错误消息
func (eh *errorHandler) getLocalizedErrorMessage(errorCode, language string) string {
	messages := map[string]map[string]string{
		models.ErrLLMCallFailed: {
			"zh": "LLM调用失败，请稍后重试",
			"en": "LLM call failed, please try again later",
		},
		models.ErrLLMTimeout: {
			"zh": "LLM调用超时，请检查网络连接或减少输入文本长度",
			"en": "LLM call timeout, please check network connection or reduce input text length",
		},
		models.ErrLLMRateLimit: {
			"zh": "请求频率过高，请稍后重试",
			"en": "Request rate limit exceeded, please try again later",
		},
		models.ErrResponseParseFailed: {
			"zh": "响应解析失败，LLM输出格式异常",
			"en": "Response parsing failed, LLM output format is abnormal",
		},
		models.ErrInvalidJSON: {
			"zh": "无效的JSON格式",
			"en": "Invalid JSON format",
		},
		models.ErrInternalError: {
			"zh": "内部服务器错误",
			"en": "Internal server error",
		},
	}
	
	if langMessages, exists := messages[errorCode]; exists {
		if message, exists := langMessages[language]; exists {
			return message
		}
		if message, exists := langMessages["zh"]; exists {
			return message
		}
	}
	
	return "未知错误 / Unknown error"
}

// getExtractionImpossibleMessage 获取无法提取的消息
func (eh *errorHandler) getExtractionImpossibleMessage(modelName, language string) string {
	if language == "en" {
		return fmt.Sprintf("Cannot extract meaningful information that conforms to the %s model from the input text", modelName)
	}
	return fmt.Sprintf("无法从输入文本中提取符合%s模型的有意义信息", modelName)
}

// getLowConfidenceMessage 获取低置信度消息
func (eh *errorHandler) getLowConfidenceMessage(confidence, threshold float64, language string) string {
	if language == "en" {
		return fmt.Sprintf("Extraction result confidence is too low (%.2f < %.2f)", confidence, threshold)
	}
	return fmt.Sprintf("提取结果置信度过低 (%.2f < %.2f)", confidence, threshold)
}

// getLLMErrorSuggestions 获取LLM错误建议
func (eh *errorHandler) getLLMErrorSuggestions(errorCode, language string) []string {
	suggestions := map[string]map[string][]string{
		models.ErrLLMTimeout: {
			"zh": {
				"检查网络连接是否稳定",
				"减少输入文本长度",
				"增加超时时间设置",
				"稍后重试",
			},
			"en": {
				"Check if network connection is stable",
				"Reduce input text length",
				"Increase timeout setting",
				"Try again later",
			},
		},
		models.ErrLLMRateLimit: {
			"zh": {
				"等待一段时间后重试",
				"减少请求频率",
				"检查API配额限制",
			},
			"en": {
				"Wait for a while and try again",
				"Reduce request frequency",
				"Check API quota limits",
			},
		},
		models.ErrLLMCallFailed: {
			"zh": {
				"检查API配置是否正确",
				"验证网络连接",
				"稍后重试",
				"联系技术支持",
			},
			"en": {
				"Check if API configuration is correct",
				"Verify network connection",
				"Try again later",
				"Contact technical support",
			},
		},
	}
	
	if langSuggestions, exists := suggestions[errorCode]; exists {
		if suggestion, exists := langSuggestions[language]; exists {
			return suggestion
		}
		if suggestion, exists := langSuggestions["zh"]; exists {
			return suggestion
		}
	}
	
	return []string{}
}

// getParseErrorSuggestions 获取解析错误建议
func (eh *errorHandler) getParseErrorSuggestions(language string) []string {
	if language == "en" {
		return []string{
			"Try again, LLM output format may be unstable",
			"Check if the model schema is too complex",
			"Simplify the target schema",
			"Check raw_output field for details",
		}
	}
	return []string{
		"可以重试，LLM输出格式可能不稳定",
		"检查模型Schema是否过于复杂",
		"简化目标Schema",
		"查看raw_output字段了解详情",
	}
}

// getExtractionImpossibleSuggestions 获取无法提取的建议
func (eh *errorHandler) getExtractionImpossibleSuggestions(language string) []string {
	if language == "en" {
		return []string{
			"Provide text content related to the business scenario",
			"Check if input text contains target entity information",
			"Try using other business models",
			"Ensure input text matches the model purpose",
		}
	}
	return []string{
		"请提供与业务场景相关的文本内容",
		"检查输入文本是否包含目标实体信息",
		"尝试使用其他业务模型",
		"确保输入文本与模型用途匹配",
	}
}

// getLowConfidenceSuggestions 获取低置信度建议
func (eh *errorHandler) getLowConfidenceSuggestions(language string) []string {
	if language == "en" {
		return []string{
			"Provide clearer input text",
			"Lower the confidence threshold",
			"Use other business models",
			"Check if partial results are usable",
		}
	}
	return []string{
		"尝试提供更清晰的输入文本",
		"降低置信度阈值",
		"使用其他业务模型",
		"检查部分结果是否可用",
	}
}

// getGenericErrorSuggestions 获取通用错误建议
func (eh *errorHandler) getGenericErrorSuggestions(language string) []string {
	if language == "en" {
		return []string{
			"Try again later",
			"Check input parameters",
			"Contact technical support",
		}
	}
	return []string{
		"稍后重试",
		"检查输入参数",
		"联系技术支持",
	}
}

// detectLanguage 简单的语言检测
func (eh *errorHandler) detectLanguage(text string) string {
	chineseCount := 0
	englishCount := 0
	
	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			chineseCount++
		} else if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			englishCount++
		}
	}
	
	if chineseCount > englishCount {
		return "zh"
	} else if englishCount > chineseCount {
		return "en"
	}
	
	return "mixed"
}

// createFallbackResult 创建降级结果
func (eh *errorHandler) createFallbackResult(ctx context.Context, req *models.ExtractionRequest, originalError error) *models.ExtractionResult {
	// 尝试提取基本信息作为降级结果
	basicData := eh.extractBasicInfo(req.InputText)
	
	return &models.ExtractionResult{
		Success:    false,
		Data:       basicData,
		Confidence: 0.3, // 降级结果置信度较低
		Error: models.NewExtractionError(
			models.ErrExtractionFailed,
			"主要提取方法失败，提供降级结果",
			map[string]interface{}{
				"original_error": originalError.Error(),
				"fallback_used":  true,
				"basic_info":     basicData,
			},
		),
		Metadata: models.ExtractionMetadata{
			ProcessingTime: 0,
			Language:       req.Language,
			ProcessedAt:    time.Now(),
		},
	}
}

// extractBasicInfo 提取基本信息作为降级
func (eh *errorHandler) extractBasicInfo(text string) map[string]interface{} {
	// 简单的关键词提取作为降级
	words := strings.Fields(text)
	keywords := []string{}
	
	for _, word := range words {
		if len(word) > 2 && !eh.isStopWord(word) {
			keywords = append(keywords, word)
			if len(keywords) >= 10 { // 最多10个关键词
				break
			}
		}
	}
	
	return map[string]interface{}{
		"extracted_keywords": keywords,
		"text_length":        len(text),
		"word_count":         len(words),
		"fallback_info":      "基本信息提取",
	}
}

// isStopWord 检查是否为停用词
func (eh *errorHandler) isStopWord(word string) bool {
	stopWords := []string{
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这",
		"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should",
	}
	
	lowerWord := strings.ToLower(word)
	for _, stopWord := range stopWords {
		if lowerWord == stopWord {
			return true
		}
	}
	
	return false
}