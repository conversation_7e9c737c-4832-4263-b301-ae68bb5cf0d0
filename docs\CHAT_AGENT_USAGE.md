# 闲聊Agent使用说明

## 概述

闲聊Agent是TaskD项目中的一个重要组件，用于处理用户的日常对话需求。当意图识别系统将用户输入识别为`casual_chat`时，会路由到闲聊Agent进行处理。

## 功能特性

### 1. SocketIO实时通信
- 支持WebSocket长连接，实现实时对话
- 自动连接管理，最大支持100个并发连接
- 心跳检测机制，自动清理僵死连接

### 2. 会话管理
- 智能会话超时：1小时无活动自动关闭
- 上下文记忆：支持可配置的token上下文限制
- 会话持久化：所有对话历史永久保存

### 3. 数据持久化
- PostgreSQL存储：overseas数据库，taskd模式
- 三张核心表：连接管理、会话管理、消息历史
- 自动清理机制：过期数据定期清理

## 数据库表结构

### socket_connections - Socket连接管理
```sql
- socket_id: Socket连接唯一标识
- user_id: 用户ID
- session_id: 会话ID
- status: 连接状态 (active/inactive/disconnected)
- connected_at: 连接时间
- last_ping_at: 最后心跳时间
```

### chat_sessions - 聊天会话
```sql
- session_id: 会话唯一标识
- user_id: 用户ID  
- status: 会话状态 (active/inactive/expired)
- expires_at: 过期时间
- context_token_limit: 上下文token限制
- current_token_count: 当前token计数
```

### chat_messages - 聊天消息
```sql
- message_id: 消息唯一标识
- session_id: 所属会话ID
- role: 消息角色 (user/assistant/system)
- content: 消息内容
- token_count: token计数
- metadata: 元数据 (JSON格式)
```

## API接口

### SocketIO事件

#### 客户端 → 服务器

1. **join_session** - 加入会话
```javascript
socket.emit('join_session', {
  user_id: 'user123',
  company_id: 'company456',
  client_ip: '***********',
  user_agent: 'Mozilla/5.0...'
});
```

2. **chat_message** - 发送消息  
```javascript
socket.emit('chat_message', {
  content: '你好，今天天气怎么样？',
  metadata: { source: 'web' }
});
```

3. **ping** - 心跳检测
```javascript
socket.emit('ping', {});
```

#### 服务器 → 客户端

1. **join_session** - 连接成功响应
```javascript
{
  event: 'join_session',
  data: {
    socket_id: 'uuid',
    session_id: 'uuid', 
    status: 'connected'
  }
}
```

2. **response** - AI回复
```javascript
{
  event: 'response',
  data: {
    message_id: 'uuid',
    content: '今天天气不错，阳光明媚！',
    token_count: 25,
    created_at: '2023-...'
  }
}
```

3. **error** - 错误消息
```javascript
{
  event: 'error', 
  error: '错误描述',
  timestamp: '2023-...'
}
```

### REST API

#### 1. 创建会话
```http
POST /v1/chat/sessions
Content-Type: application/json

{
  "user_id": "user123",
  "company_id": "company456", 
  "context_token_limit": 4000
}
```

#### 2. 获取历史记录
```http
GET /v1/chat/sessions/{session_id}/history?limit=50&offset=0
```

#### 3. 关闭会话
```http
DELETE /v1/chat/sessions/{session_id}
```

#### 4. 获取统计信息
```http
GET /v1/chat/stats
```

#### 5. 强制清理
```http
POST /v1/chat/cleanup
```

## 配置说明

### chat配置项
```yaml
chat:
  max_socket_connections: 100     # 最大连接数
  session_timeout_minutes: 60     # 会话超时(分钟)
  default_token_limit: 4000       # 默认token限制
  cleanup_interval_hours: 1       # 清理间隔(小时)
  ping_interval_seconds: 30       # 心跳间隔(秒)
  system_prompt: "..."            # 系统提示词
  model_provider: "volcengine_ark" # 模型提供商
  model_alias: "doubao-1-5-pro-32k-250115"   # 模型别名
  temperature: 0.7                # 温度参数
  max_tokens: 1000                # 最大输出token
  enable_token_tracking: true     # 启用token跟踪
```

## 部署步骤

### 1. 数据库初始化
```bash
# 连接到overseas数据库
psql -h localhost -U admin -d overseas

# 执行schema.sql中的聊天相关表创建脚本
\i internal/store/schema.sql
```

### 2. 配置文件
```bash
# 复制示例配置
cp configs/chat-example.yaml configs/config.yaml

# 修改数据库连接信息和API密钥
vim configs/config.yaml
```

### 3. 启动服务
```bash
# 编译项目
go build -o taskd cmd/taskd/main.go

# 启动服务
./taskd
```

### 4. 验证部署
```bash
# 检查SocketIO端点
curl http://localhost:8601/socket.io/

# 检查聊天统计
curl http://localhost:8601/v1/chat/stats
```

## 使用示例

### JavaScript客户端示例
```javascript
// 连接SocketIO
const socket = io('http://localhost:8601');

// 连接事件
socket.on('connect', () => {
  console.log('Connected to server');
  
  // 加入会话
  socket.emit('join_session', {
    user_id: 'test_user',
    company_id: 'test_company'
  });
});

// 监听会话加入成功
socket.on('join_session', (data) => {
  console.log('Joined session:', data.session_id);
  
  // 发送消息
  socket.emit('chat_message', {
    content: '你好！'
  });
});

// 监听AI回复
socket.on('response', (data) => {
  console.log('AI回复:', data.data.content);
});

// 监听错误
socket.on('error', (data) => {
  console.error('错误:', data.error);
});

// 心跳检测
setInterval(() => {
  socket.emit('ping', {});
}, 30000);
```

## 监控和维护

### 1. 监控指标
- 活跃连接数
- 活跃会话数  
- 消息处理速度
- Token消费统计

### 2. 定期维护
- 查看清理日志
- 监控数据库大小
- 检查连接池状态
- 更新系统提示词

### 3. 故障排查
- 检查数据库连接
- 验证LLM API密钥
- 查看SocketIO连接状态
- 检查会话是否过期

## 性能优化建议

1. **数据库优化**
   - 定期执行VACUUM和REINDEX
   - 监控索引使用情况
   - 优化查询语句

2. **内存管理**
   - 合理设置上下文token限制
   - 定期清理过期数据
   - 监控内存使用情况

3. **并发控制**
   - 限制最大连接数
   - 实现请求队列
   - 负载均衡部署

4. **缓存策略**
   - Redis缓存热点数据
   - 会话信息缓存
   - 减少数据库查询

## 常见问题

### Q: 会话超时后消息历史会丢失吗？
A: 不会。消息历史永久保存在数据库中，只是会话状态变为expired。

### Q: 如何修改系统提示词？  
A: 修改配置文件中的system_prompt字段，重启服务即可生效。

### Q: 支持多轮对话吗？
A: 是的，系统会自动维护对话上下文，支持多轮连续对话。

### Q: 如何集成到现有系统？
A: 通过意图识别API，将casual_chat意图的请求路由到聊天Agent即可。