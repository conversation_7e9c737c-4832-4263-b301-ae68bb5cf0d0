# 招投标预处理Agent规格说明

## 概述

本文档定义了招投标预处理系统的6个核心Agent，每个Agent负责处理流程中的特定步骤。所有Agent遵循A2A协议标准，支持独立调用和编排调用。

## 架构原则

1. **独立性**：每个Agent都是独立的服务，可单独部署和调用
2. **解耦性**：Agent之间没有直接依赖，通过A2A协议通信
3. **并发性**：支持多条数据并发处理，每条数据处理过程中数据隔离
4. **容错性**：单个Agent失败不影响整体服务可用性
5. **标准化**：统一的Agent Card规范和消息协议

## Agent 1: 数据提取与验证Agent (Data Extraction & Validation Agent)

### Agent Card
```json
{
  "name": "Tender Data Extraction & Validation Agent",
  "description": "从原始招投标文本中提取结构化信息并进行数据验证",
  "url": "http://taskd-service:8601/agents/tender-data-extraction",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "skills": [
    {
      "id": "extract_structured_data",
      "name": "提取结构化数据",
      "description": "从原始招投标文本中并行提取基本信息、机构信息、招标要求和供应商要求",
      "inputSchema": {
        "type": "object",
        "properties": {
          "raw_text": {
            "type": "string",
            "description": "原始招投标文本内容"
          },
          "language": {
            "type": "string",
            "enum": ["chinese", "english"],
            "default": "chinese"
          }
        },
        "required": ["raw_text"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "extracted_data": {
            "type": "object",
            "properties": {
              "basic_info": {"type": "object"},
              "organization": {"type": "object"},
              "tender_requirements": {"type": "object"},
              "supplier_requirements": {"type": "object"}
            }
          },
          "validation_result": {
            "type": "object",
            "properties": {
              "status": {"type": "string"},
              "completeness_score": {"type": "number"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 2: 智能分类Agent (Classification Agent)

### Agent Card
```json
{
  "name": "Tender Classification Agent",
  "description": "对招投标数据进行多维度智能分类",
  "url": "http://taskd-service:8601/agents/tender-classification",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "skills": [
    {
      "id": "classify_tender",
      "name": "招投标分类",
      "description": "对招投标进行行业分类、采购类型分类和业务领域分类",
      "inputSchema": {
        "type": "object",
        "properties": {
          "extracted_data": {
            "type": "object",
            "description": "数据提取Agent输出的结构化数据"
          },
          "classification_depth": {
            "type": "integer",
            "minimum": 1,
            "maximum": 3,
            "default": 3
          }
        },
        "required": ["extracted_data"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "industry_classification": {
            "type": "object",
            "properties": {
              "level1": {"type": "string"},
              "level2": {"type": "string"},
              "level3": {"type": "string"}
            }
          },
          "procurement_type": {
            "type": "object",
            "properties": {
              "main_type": {"type": "string"},
              "sub_types": {"type": "array"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 3: 内容增强Agent (Content Enhancement Agent)

### Agent Card
```json
{
  "name": "Tender Content Enhancement Agent",
  "description": "对招投标内容进行增强处理，包括关键词提取、标题优化、质量评分和摘要生成",
  "url": "http://taskd-service:8601/agents/tender-content-enhancement",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "skills": [
    {
      "id": "enhance_content",
      "name": "内容增强处理",
      "description": "提取关键词、优化标题、计算质量评分并生成智能摘要",
      "inputSchema": {
        "type": "object",
        "properties": {
          "extracted_data": {
            "type": "object",
            "description": "数据提取Agent输出的结构化数据"
          },
          "classification_data": {
            "type": "object",
            "description": "分类Agent输出的分类数据"
          }
        },
        "required": ["extracted_data", "classification_data"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "keywords": {
            "type": "object",
            "properties": {
              "technical_keywords": {"type": "array"},
              "business_keywords": {"type": "array"},
              "core_products": {"type": "array"}
            }
          },
          "optimized_title": {
            "type": "object",
            "properties": {
              "original_title": {"type": "string"},
              "new_title": {"type": "string"}
            }
          },
          "quality_score": {
            "type": "object",
            "properties": {
              "overall_score": {"type": "number"},
              "dimension_scores": {"type": "object"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 4: 多语言处理Agent (Multilingual Processing Agent)

### Agent Card
```json
{
  "name": "Tender Multilingual Processing Agent",
  "description": "对招投标数据进行多语言翻译和本地化处理",
  "url": "http://taskd-service:8601/agents/tender-multilingual",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "skills": [
    {
      "id": "process_multilingual",
      "name": "多语言处理",
      "description": "将中文处理结果翻译为英文并进行本地化处理",
      "inputSchema": {
        "type": "object",
        "properties": {
          "chinese_data": {
            "type": "object",
            "description": "中文处理结果数据"
          },
          "target_languages": {
            "type": "array",
            "items": {"type": "string"},
            "default": ["english"]
          }
        },
        "required": ["chinese_data"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "translated_data": {
            "type": "object",
            "description": "按语言分组的翻译结果"
          },
          "translation_quality": {
            "type": "object",
            "properties": {
              "overall_quality": {"type": "number"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 5: 结果聚合Agent (Result Aggregation Agent)

### Agent Card
```json
{
  "name": "Tender Result Aggregation Agent",
  "description": "聚合所有处理结果并生成最终的招投标处理结果",
  "url": "http://taskd-service:8601/agents/tender-result-aggregation",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "skills": [
    {
      "id": "aggregate_results",
      "name": "结果聚合",
      "description": "整合所有Agent的处理结果并生成最终输出",
      "inputSchema": {
        "type": "object",
        "properties": {
          "extraction_result": {"type": "object"},
          "classification_result": {"type": "object"},
          "enhancement_result": {"type": "object"},
          "multilingual_result": {"type": "object"}
        },
        "required": ["extraction_result", "classification_result", "enhancement_result"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "final_result": {
            "type": "object",
            "properties": {
              "id": {"type": "string"},
              "extracted_data": {"type": "object"},
              "classification": {"type": "object"},
              "enhancement": {"type": "object"},
              "multilingual": {"type": "object"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 6: 编排Agent (Orchestration Agent)

### Agent Card
```json
{
  "name": "Tender Preprocessing Orchestration Agent",
  "description": "编排招投标预处理的完整流程，支持多条数据并发处理",
  "url": "http://taskd-service:8601/agents/tender-preprocessing-orchestration",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "skills": [
    {
      "id": "orchestrate_preprocessing",
      "name": "编排预处理流程",
      "description": "编排调用所有预处理Agent完成完整的招投标预处理流程",
      "inputSchema": {
        "type": "object",
        "properties": {
          "input_data": {
            "oneOf": [
              {"type": "object"},
              {"type": "array"}
            ],
            "description": "单条或多条招投标原始数据"
          },
          "processing_options": {
            "type": "object",
            "properties": {
              "concurrent_limit": {"type": "integer", "default": 10},
              "timeout": {"type": "integer", "default": 300}
            }
          }
        },
        "required": ["input_data"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "orchestration_result": {
            "type": "object",
            "properties": {
              "total_items": {"type": "integer"},
              "successful_items": {"type": "integer"},
              "failed_items": {"type": "integer"},
              "results": {"type": "array"}
            }
          }
        }
      }
    }
  ]
}
```

## A2A协议通信规范

### 消息格式
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "string",
    "input": "object",
    "context": {
      "user_id": "string",
      "trace_id": "string"
    }
  },
  "id": "string"
}
```

### 响应格式
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "string",
    "status": "completed|failed|in_progress",
    "output": "object"
  },
  "id": "string"
}
```

## 部署和运维规范

1. **独立部署**：每个Agent作为独立服务
2. **服务发现**：通过Agent Registry注册
3. **监控日志**：统一的结构化日志
4. **配置管理**：环境变量注入
5. **安全策略**：Bearer Token认证