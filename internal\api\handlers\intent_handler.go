package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/models/business"
	"gitlab.com/specific-ai/taskd/internal/services"
)

type IntentHandler struct {
	intentService *services.IntentRecognitionService
}

func NewIntentHandler(intentService *services.IntentRecognitionService) *IntentHandler {
	return &IntentHandler{
		intentService: intentService,
	}
}

// RecognizeIntent handles POST /api/v1/intent/recognize
func (h *IntentHandler) RecognizeIntent(c *gin.Context) {
	var req business.IntentRecognizeRequest
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_REQUEST",
			"message": "请求格式错误: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if len(req.Messages) == 0 {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"code":    "VALIDATION_FAILED",
			"message": "messages字段不能为空",
		})
		return
	}

	if req.UserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "VALIDATION_FAILED",
			"message": "user_id字段不能为空",
		})
		return
	}

	if req.OrganizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "VALIDATION_FAILED",
			"message": "organization_id字段不能为空",
		})
		return
	}

	// Validate each message
	for i, msg := range req.Messages {
		if msg.Role == "" || msg.Content == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    "VALIDATION_FAILED",
				"message": fmt.Sprintf("第%d条消息的role和content字段不能为空", i+1),
			})
			return
		}
		if msg.Role != "user" && msg.Role != "assistant" && msg.Role != "system" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    "VALIDATION_FAILED", 
				"message": fmt.Sprintf("第%d条消息的role字段值无效: %s", i+1, msg.Role),
			})
			return
		}
	}

	// Use request context (timeout handled internally by service)
	ctx := c.Request.Context()

	// Call intent recognition service
	response, err := h.intentService.RecognizeIntent(ctx, &req)
	if err != nil {
		// Check if it's a timeout error
		if ctx.Err() == context.DeadlineExceeded {
			c.JSON(http.StatusRequestTimeout, gin.H{
				"code":    "TIMEOUT",
				"message": "意图识别请求超时",
			})
			return
		}

		// Handle other errors with specific error codes
		statusCode := http.StatusInternalServerError
		errorCode := "EXECUTION_FAILED"
		
		// Check for specific error types
		if strings.Contains(err.Error(), "insufficient tokens") || strings.Contains(err.Error(), "Token limit exceeded") {
			statusCode = http.StatusForbidden
			errorCode = "TOKEN_LIMIT_EXCEEDED"
		} else if strings.Contains(err.Error(), "token check failed") {
			statusCode = http.StatusServiceUnavailable
			errorCode = "TOKEN_SERVICE_ERROR"
		}
		
		c.JSON(statusCode, gin.H{
			"code":    errorCode,
			"message": "意图识别失败: " + err.Error(),
		})
		return
	}

	// Return successful response
	c.JSON(http.StatusOK, response)
}

// GetSupportedIntents handles GET /api/v1/intent/supported
func (h *IntentHandler) GetSupportedIntents(c *gin.Context) {
	intents := h.intentService.GetSupportedIntents()
	c.JSON(http.StatusOK, gin.H{
		"supported_intents": intents,
		"total_count":      4,
		"description": map[string]string{
			"bidding_analysis": "招投标分析 - 用户询问招投标项目、采购分析等相关业务机会",
			"business_news":    "商机新闻分析 - 用户想要分析商业新闻、市场机会或行业趋势",
			"chat_summary":     "聊天记录总结 - 用户请求对之前的对话或聊天历史进行总结",
			"casual_chat":      "闲聊 - 一般性对话、问候或与业务分析无关的话题",
		},
	})
}


