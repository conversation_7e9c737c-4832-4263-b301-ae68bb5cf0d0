#!/usr/bin/env python3
"""
招投标核心Agent测试套件
包含AI摘要、数据检索、需求分析、报告生成等核心业务Agent
"""

import json
import requests
import os
import time
import pytest

# 招投标专用测试用户 - 方便Token消耗记录跟踪
BIDDING_TEST_USER_ID = "test_user_bidding"
BIDDING_TEST_COMPANY_ID = "test_company_bidding"

class TestBiddingAgents:
    """招投标核心Agent测试套件"""
    
    def _handle_a2a_response(self, response, test_name, check_token=True):
        """处理A2A响应的通用方法"""
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            data = response.json()
            if "result" in data:
                result = data["result"]
                status = result.get("status", "unknown")
                
                if status == "completed":
                    print(f"{test_name}成功")
                    
                    # 检查token使用情况
                    if check_token:
                        metadata = result.get("metadata", {})
                        tokens_used = metadata.get("tokens_used", 0)
                        output = result.get("output", {})
                        
                        # 检查多种可能的token信息来源
                        # 1. 通用的metadata.token_usage
                        output_metadata = output.get("metadata", {})
                        token_usage = output_metadata.get("token_usage", {})
                        
                        # 2. 招投标Agent特有的processing_metadata.tokens_consumed
                        processing_metadata = output.get("processing_metadata", {})
                        tokens_consumed = processing_metadata.get("tokens_consumed", 0)
                        
                        print(f"任务级别token使用: {tokens_used}")
                        if token_usage:
                            print(f"输出级别Token信息: input={token_usage.get('input_tokens', 0)}, "
                                  f"output={token_usage.get('output_tokens', 0)}, total={token_usage.get('total_tokens', 0)}")
                        if tokens_consumed > 0:
                            print(f"业务级别Token消耗: {tokens_consumed}")
                        
                        # 返回token使用信息供进一步检查（支持多种格式）
                        return True, {
                            "task_tokens": tokens_used,
                            "output_tokens": token_usage,
                            "consumed_tokens": tokens_consumed
                        }
                    return True, None
                elif status == "failed":
                    error_msg = result.get("error", "未知错误")
                    print(f"{test_name}失败: {error_msg}")
                    return False, None
            elif "error" in data:
                print(f"A2A协议错误: {data['error']}")
                return False, None
        
        print(f"{test_name}请求失败: {response.status_code}")
        if response.status_code == 404:
            pytest.fail(f"Agent未注册或URL错误: {response.status_code}")
        return response.status_code == 200, None

    def test_ai_summary_agent(self):
        """测试AI摘要生成Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== AI摘要生成Agent测试 ===")
        
        # 使用文档中的示例数据
        payload = {
            "jsonrpc": "2.0",
            "id": "bidding_ai_summary_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "generate_tender_summary",
                "input": {
                    "tender_data": {
                        "id": "TENDER_2024_AI_GOV_001",
                        "title": "某市政府人工智能服务平台建设项目",
                        "content": "项目旨在建设一套集成AI服务能力的政务平台，包括智能客服、文档处理、数据分析等功能。平台需要支持自然语言处理、机器学习模型部署、数据可视化等核心功能。",
                        "deadline": "2024-08-15T16:00:00Z",
                        "budget": {
                            "total": "5000000",
                            "currency": "CNY",
                            "budget_type": "maximum"
                        }
                    },
                    "language": "chinese",
                    "summary_type": "detailed",
                    "focus_areas": ["技术要求", "预算范围", "交付时间"]
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': BIDDING_TEST_USER_ID,
            'X-Company-ID': BIDDING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/ai-summary"
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        success, token_info = self._handle_a2a_response(response, "AI摘要生成")
        if not success:
            pytest.fail("AI摘要生成Agent测试失败")

    def test_data_retrieval_agent(self):
        """测试招投标数据检索Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 招投标数据检索Agent测试 ===")
        
        # 使用文档中的示例数据
        payload = {
            "jsonrpc": "2.0",
            "id": "bidding_data_retrieval_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "retrieve_tender_data",
                "input": {
                    "tender_id": "6846ab859c792c503ff05964",
                    "language": "chinese",
                    "include_metadata": True,
                    "fields": ["title", "content", "deadline", "budget"]
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': BIDDING_TEST_USER_ID,
            'X-Company-ID': BIDDING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/bidding-data-retrieval"
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        success, token_info = self._handle_a2a_response(response, "数据检索")
        if not success:
            pytest.fail("数据检索Agent测试失败")

    def test_requirement_analysis_agent(self):
        """测试需求分析搜索Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 需求分析搜索Agent测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": "requirement_analysis_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "analyze_and_search",
                "input": {
                    "summary_data": {
                        "project_type": "IT设备采购",
                        "key_requirements": ["高性能服务器", "网络设备", "安全设备"],
                        "budget_range": "500万元",
                        "technical_specs": "企业级、高可用性、3年质保"
                    },
                    "search_depth": "detailed",
                    "search_domains": ["technology", "market", "suppliers"],
                    "max_results": 10
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': BIDDING_TEST_USER_ID,
            'X-Company-ID': BIDDING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/requirement-analysis-search"
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        success, token_info = self._handle_a2a_response(response, "需求分析搜索")
        if not success:
            pytest.fail("需求分析搜索Agent测试失败")

    def test_report_generation_agent(self):
        """测试报告生成Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 报告生成Agent测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": "report_generation_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "generate_comprehensive_report",
                "input": {
                    "summary_data": {
                        "title": "某市政府人工智能服务平台建设项目",
                        "new_title": "政务AI服务平台：智能客服+文档处理+数据分析一体化解决方案",
                        "deadline": "2024-08-15",
                        "summary_text": "本项目为某市政府打造集成化AI政务服务平台，核心功能包括智能客服系统、自动化文档处理、政务数据智能分析等。",
                        "key_requirements": [
                            "AI算法自主研发能力",
                            "云原生微服务架构",
                            "等保三级安全认证",
                            "7x24小时运维保障"
                        ],
                        "budget_info": {
                            "amount": "500万元",
                            "type": "最高限价",
                            "payment_terms": "按里程碑分期支付"
                        }
                    },
                    "requirement_analysis": {
                        "technical_feasibility": "高",
                        "market_availability": "充足",
                        "cost_analysis": "合理"
                    },
                    "search_results": [
                        {
                            "title": "类似项目A的市场分析报告",
                            "url": "http://example.com/report_a",
                            "snippet": "项目A成功实施，市场反响良好...",
                            "source": "市场研究",
                            "relevance_score": 0.9,
                            "published_date": "2023-10-01"
                        },
                        {
                            "title": "技术供应商B的技术能力评估",
                            "url": "http://example.com/vendor_b",
                            "snippet": "供应商B在AI领域有深厚积累...",
                            "source": "技术评估报告",
                            "relevance_score": 0.85,
                            "published_date": "2024-01-15"
                        }
                    ],
                    "report_type": "comprehensive",
                    "language": "chinese"
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': BIDDING_TEST_USER_ID,
            'X-Company-ID': BIDDING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/report-generation"
        response = requests.post(url, json=payload, headers=headers, timeout=90)
        
        success, token_info = self._handle_a2a_response(response, "报告生成")
        if not success:
            pytest.fail("报告生成Agent测试失败")

    def test_token_consumption_verification(self):
        """测试招投标Agent的token消耗记录"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 招投标Agent Token消耗验证测试 ===")
        
        # 1. 先调用一个会消耗token的Agent（如AI摘要生成）
        print("调用AI摘要生成Agent以产生token消耗...")
        payload = {
            "jsonrpc": "2.0",
            "id": "token_test_summary",
            "method": "agent/execute",
            "params": {
                "skill_id": "generate_tender_summary",
                "input": {
                    "tender_data": {
                        "id": "TENDER_2024_AI_GOV_001",
                        "title": "某市政府人工智能服务平台建设项目",
                        "content": "项目旨在建设一套集成AI服务能力的政务平台，包括智能客服、文档处理、数据分析等功能。平台需要支持自然语言处理、机器学习模型部署、数据可视化等核心功能。",
                        "deadline": "2024-08-15T16:00:00Z",
                        "budget": {
                            "total": "5000000",
                            "currency": "CNY",
                            "budget_type": "maximum"
                        }
                    },
                    "language": "chinese",
                    "summary_type": "detailed",
                    "focus_areas": ["技术要求", "预算范围", "交付时间"]
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': BIDDING_TEST_USER_ID,
            'X-Company-ID': BIDDING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/ai-summary"
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        # 确保Agent调用成功
        success, token_info = self._handle_a2a_response(response, "Token消耗的前置AI摘要生成")
        if not success:
            pytest.fail("Token消耗测试失败，因为前置的AI摘要Agent调用失败。")

        # 检查从Agent直接返回的token信息
        assert token_info is not None, "Agent响应中应包含token信息"
        
        # 检查多种可能的token信息来源
        task_tokens = token_info.get("task_tokens", 0)
        output_total_tokens = token_info.get("output_tokens", {}).get('total_tokens', 0)
        consumed_tokens = token_info.get("consumed_tokens", 0)
        
        # 至少一种token信息源应该大于0
        assert task_tokens > 0 or output_total_tokens > 0 or consumed_tokens > 0, \
            f"Agent直接返回的token消耗量应大于0，但得到: task_tokens={task_tokens}, output_total_tokens={output_total_tokens}, consumed_tokens={consumed_tokens}"
        
        if consumed_tokens > 0:
            print(f"Agent成功返回了有效的token使用信息: {consumed_tokens} tokens")
        elif output_total_tokens > 0:
            print(f"Agent成功返回了有效的token使用信息: {output_total_tokens} tokens")
        else:
            print(f"Agent成功返回了有效的token使用信息: {task_tokens} tokens")
        
        # 2. 等待一下让token记录完成
        time.sleep(3)
        
        # 3. 检查Token消耗统计
        token_url = f"{base_url}/v1/tokens/stats/user/{BIDDING_TEST_USER_ID}"
        token_response = requests.get(token_url, timeout=30)
        
        print(f"Token统计API状态码: {token_response.status_code}")
        
        if token_response.status_code == 200:
            token_data = token_response.json()
            print(f"Token统计: {json.dumps(token_data, ensure_ascii=False, indent=2)}")
            
            # 验证基本字段
            assert token_data["user_id"] == BIDDING_TEST_USER_ID
            assert token_data["company_id"] == BIDDING_TEST_COMPANY_ID
            
            # 检查是否有token消耗记录
            last_consumed = token_data.get("last_consumed_at")
            monthly_tokens = token_data.get("monthly_usage", {}).get("total_tokens", 0)
            daily_tokens = token_data.get("daily_usage", {}).get("total_tokens", 0)

            assert last_consumed is not None, "数据库中应有最近消耗时间记录"
            assert monthly_tokens > 0, "月度累计token消耗应大于0"
            assert daily_tokens > 0, "每日累计token消耗应大于0"
            
            print("✅ 数据库Token消耗记录验证成功!")
        else:
            pytest.fail(f"Token统计API失败: {token_response.text}")

def test_bidding_agents_integration():
    """招投标Agent集成测试"""
    print("=== 招投标Agent集成测试 ===")
    
    test_suite = TestBiddingAgents()
    test_results = {}
    
    # 定义测试列表
    tests = [
        ("AI摘要生成", test_suite.test_ai_summary_agent),
        ("数据检索", test_suite.test_data_retrieval_agent),
        ("需求分析搜索", test_suite.test_requirement_analysis_agent),
        ("报告生成", test_suite.test_report_generation_agent),
        ("Token消耗验证", test_suite.test_token_consumption_verification)
    ]
    
    # 执行每个测试
    for test_name, test_func in tests:
        try:
            test_func()
            test_results[test_name] = "PASSED"
            print(f"{test_name}测试通过\n")
        except Exception as e:
            test_results[test_name] = f"FAILED: {str(e)}"
            print(f"{test_name}测试失败: {str(e)}\n")
        
        time.sleep(2)  # 避免请求过于频繁
    
    # 打印测试总结
    print("=== 招投标Agent测试总结 ===")
    passed_count = sum(1 for result in test_results.values() if result == "PASSED")
    total_count = len(test_results)
    
    for test_name, result in test_results.items():
        status = "PASS" if result == "PASSED" else "FAIL"
        print(f"[{status}] {test_name}: {result}")
    
    print(f"\n测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == 0:
        pytest.fail("所有招投标Agent测试都失败了")
    
    print("招投标Agent集成测试完成!")

if __name__ == "__main__":
    test_bidding_agents_integration()