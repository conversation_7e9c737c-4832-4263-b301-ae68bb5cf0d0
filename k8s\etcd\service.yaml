apiVersion: v1
kind: Service
metadata:
  name: etcd-service
  namespace: etcd
  labels:
    app: etcd
    component: database
spec:
  type: ClusterIP
  ports:
  - name: client
    port: 2379
    targetPort: 2379
    protocol: TCP
  - name: peer
    port: 2380
    targetPort: 2380
    protocol: TCP
  selector:
    app: etcd
---
# 如果需要外部访问etcd（调试用），可以创建NodePort服务
apiVersion: v1
kind: Service
metadata:
  name: etcd-nodeport
  namespace: etcd
  labels:
    app: etcd
    component: database
    purpose: external-access
spec:
  type: NodePort
  ports:
  - name: client
    port: 2379
    targetPort: 2379
    nodePort: 32379
    protocol: TCP
  selector:
    app: etcd
---