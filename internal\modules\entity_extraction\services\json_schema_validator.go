package services

import (
	"fmt"
	"reflect"
	"strings"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// jsonSchemaValidator JSON Schema验证器实现
type jsonSchemaValidator struct{}

// NewJSONSchemaValidator 创建JSON Schema验证器
func NewJSONSchemaValidator() JSONSchemaValidator {
	return &jsonSchemaValidator{}
}

// ValidateSchema 验证Schema格式
func (v *jsonSchemaValidator) ValidateSchema(schema models.JSONSchema) *models.ValidationResult {
	result := &models.ValidationResult{
		Valid:  true,
		Errors: []models.ValidationError{},
	}

	// 验证基本字段
	if schema.Name == "" {
		result.Valid = false
		result.Errors = append(result.Errors, models.NewValidationError(
			"name", "模型名称不能为空", models.ErrInvalidInput,
		))
	}

	if schema.Description == "" {
		result.Valid = false
		result.Errors = append(result.Errors, models.NewValidationError(
			"description", "模型描述不能为空", models.ErrInvalidInput,
		))
	}

	if schema.Type != "object" {
		result.Valid = false
		result.Errors = append(result.Errors, models.NewValidationError(
			"type", "根类型必须是object", models.ErrInvalidModelSchema,
		))
	}

	if len(schema.Properties) == 0 {
		result.Valid = false
		result.Errors = append(result.Errors, models.NewValidationError(
			"properties", "属性定义不能为空", models.ErrInvalidModelSchema,
		))
	}

	// 验证属性定义
	for propName, prop := range schema.Properties {
		if propErrors := v.validateProperty(propName, prop); len(propErrors) > 0 {
			result.Valid = false
			result.Errors = append(result.Errors, propErrors...)
		}
	}

	// 验证required字段
	for _, requiredField := range schema.Required {
		if _, exists := schema.Properties[requiredField]; !exists {
			result.Valid = false
			result.Errors = append(result.Errors, models.NewValidationError(
				"required", fmt.Sprintf("必填字段'%s'在properties中不存在", requiredField), models.ErrInvalidModelSchema,
			))
		}
	}

	return result
}

// validateProperty 验证单个属性
func (v *jsonSchemaValidator) validateProperty(propName string, prop models.Property) []models.ValidationError {
	var errors []models.ValidationError

	// 验证类型
	validTypes := []string{"string", "number", "integer", "boolean", "array", "object"}
	if prop.Type == "" {
		errors = append(errors, models.NewValidationError(
			propName+".type", "属性类型不能为空", models.ErrInvalidModelSchema,
		))
	} else if !v.isValidType(prop.Type, validTypes) {
		errors = append(errors, models.NewValidationError(
			propName+".type", fmt.Sprintf("无效的属性类型: %s", prop.Type), models.ErrInvalidModelSchema,
		))
	}

	// 验证描述
	if prop.Description == "" {
		errors = append(errors, models.NewValidationError(
			propName+".description", "属性描述不能为空", models.ErrInvalidModelSchema,
		))
	}

	// 验证数组类型的items
	if prop.Type == "array" {
		if prop.Items == nil {
			errors = append(errors, models.NewValidationError(
				propName+".items", "数组类型必须定义items", models.ErrInvalidModelSchema,
			))
		} else {
			if itemErrors := v.validateProperty(propName+".items", *prop.Items); len(itemErrors) > 0 {
				errors = append(errors, itemErrors...)
			}
		}
	}

	// 验证对象类型的properties
	if prop.Type == "object" && len(prop.Properties) > 0 {
		for subPropName, subProp := range prop.Properties {
			if subPropErrors := v.validateProperty(propName+"."+subPropName, subProp); len(subPropErrors) > 0 {
				errors = append(errors, subPropErrors...)
			}
		}
	}

	// 验证枚举值
	if len(prop.Enum) > 0 && prop.Type != "string" {
		errors = append(errors, models.NewValidationError(
			propName+".enum", "枚举类型只支持字符串", models.ErrInvalidModelSchema,
		))
	}

	// 验证格式
	if prop.Format != "" {
		validFormats := []string{"date-time", "date", "time", "email", "uri"}
		if !v.isValidType(prop.Format, validFormats) {
			errors = append(errors, models.NewValidationError(
				propName+".format", fmt.Sprintf("无效的格式: %s", prop.Format), models.ErrInvalidModelSchema,
			))
		}
	}

	return errors
}

// isValidType 检查类型是否有效
func (v *jsonSchemaValidator) isValidType(typeStr string, validTypes []string) bool {
	for _, validType := range validTypes {
		if typeStr == validType {
			return true
		}
	}
	return false
}

// ValidateData 验证数据是否符合Schema
func (v *jsonSchemaValidator) ValidateData(data map[string]interface{}, schema models.JSONSchema) *models.ValidationResult {
	result := &models.ValidationResult{
		Valid:          true,
		Errors:         []models.ValidationError{},
		RequiredFields: schema.Required,
	}

	// 验证必填字段
	for _, requiredField := range schema.Required {
		if _, exists := data[requiredField]; !exists {
			result.Valid = false
			result.Errors = append(result.Errors, models.NewValidationError(
				requiredField, fmt.Sprintf("必填字段'%s'缺失", requiredField), models.ErrSchemaValidation,
			))
		}
	}

	// 验证每个字段
	for fieldName, fieldValue := range data {
		if prop, exists := schema.Properties[fieldName]; exists {
			if fieldErrors := v.validateFieldValue(fieldName, fieldValue, prop); len(fieldErrors) > 0 {
				result.Valid = false
				result.Errors = append(result.Errors, fieldErrors...)
			}
		} else {
			// 允许额外字段，但记录警告
			result.Errors = append(result.Errors, models.NewValidationError(
				fieldName, fmt.Sprintf("字段'%s'不在Schema定义中", fieldName), "WARNING",
			))
		}
	}

	return result
}

// validateFieldValue 验证字段值
func (v *jsonSchemaValidator) validateFieldValue(fieldName string, value interface{}, prop models.Property) []models.ValidationError {
	var errors []models.ValidationError

	// 处理null值
	if value == nil {
		// 如果字段不是必填的，null值是允许的
		return errors
	}

	// 验证类型
	if !v.isValueTypeValid(value, prop.Type) {
		errors = append(errors, models.NewValidationError(
			fieldName, fmt.Sprintf("字段类型错误，期望%s，实际%s", prop.Type, v.getValueType(value)), models.ErrSchemaValidation,
		))
		return errors // 类型错误时不继续验证
	}

	// 验证枚举值
	if len(prop.Enum) > 0 {
		if strValue, ok := value.(string); ok {
			if !v.isInEnum(strValue, prop.Enum) {
				errors = append(errors, models.NewValidationError(
					fieldName, fmt.Sprintf("值'%s'不在枚举范围内: %v", strValue, prop.Enum), models.ErrSchemaValidation,
				))
			}
		}
	}

	// 验证数组类型
	if prop.Type == "array" && prop.Items != nil {
		if arrayValue, ok := value.([]interface{}); ok {
			for i, item := range arrayValue {
				itemFieldName := fmt.Sprintf("%s[%d]", fieldName, i)
				if itemErrors := v.validateFieldValue(itemFieldName, item, *prop.Items); len(itemErrors) > 0 {
					errors = append(errors, itemErrors...)
				}
			}
		}
	}

	// 验证对象类型
	if prop.Type == "object" && len(prop.Properties) > 0 {
		if objValue, ok := value.(map[string]interface{}); ok {
			for subFieldName, subFieldValue := range objValue {
				if subProp, exists := prop.Properties[subFieldName]; exists {
					fullFieldName := fmt.Sprintf("%s.%s", fieldName, subFieldName)
					if subErrors := v.validateFieldValue(fullFieldName, subFieldValue, subProp); len(subErrors) > 0 {
						errors = append(errors, subErrors...)
					}
				}
			}
		}
	}

	// 验证格式
	if prop.Format != "" && prop.Type == "string" {
		if strValue, ok := value.(string); ok {
			if !v.isValidFormat(strValue, prop.Format) {
				errors = append(errors, models.NewValidationError(
					fieldName, fmt.Sprintf("格式验证失败，期望格式: %s", prop.Format), models.ErrSchemaValidation,
				))
			}
		}
	}

	return errors
}

// isValueTypeValid 检查值类型是否有效
func (v *jsonSchemaValidator) isValueTypeValid(value interface{}, expectedType string) bool {
	actualType := v.getValueType(value)
	
	switch expectedType {
	case "string":
		return actualType == "string"
	case "number":
		return actualType == "number" || actualType == "integer"
	case "integer":
		return actualType == "integer"
	case "boolean":
		return actualType == "boolean"
	case "array":
		return actualType == "array"
	case "object":
		return actualType == "object"
	default:
		return false
	}
}

// getValueType 获取值的类型
func (v *jsonSchemaValidator) getValueType(value interface{}) string {
	if value == nil {
		return "null"
	}

	switch reflect.TypeOf(value).Kind() {
	case reflect.String:
		return "string"
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return "integer"
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return "integer"
	case reflect.Float32, reflect.Float64:
		return "number"
	case reflect.Bool:
		return "boolean"
	case reflect.Slice, reflect.Array:
		return "array"
	case reflect.Map:
		return "object"
	default:
		return "unknown"
	}
}

// isInEnum 检查值是否在枚举范围内
func (v *jsonSchemaValidator) isInEnum(value string, enum []string) bool {
	for _, enumValue := range enum {
		if value == enumValue {
			return true
		}
	}
	return false
}

// isValidFormat 验证格式
func (v *jsonSchemaValidator) isValidFormat(value, format string) bool {
	switch format {
	case "date-time":
		return v.isValidDateTime(value)
	case "date":
		return v.isValidDate(value)
	case "time":
		return v.isValidTime(value)
	case "email":
		return v.isValidEmail(value)
	case "uri":
		return v.isValidURI(value)
	default:
		return true // 未知格式默认通过
	}
}

// isValidDateTime 验证日期时间格式
func (v *jsonSchemaValidator) isValidDateTime(value string) bool {
	// 简单的ISO 8601格式检查
	return strings.Contains(value, "T") && (strings.Contains(value, "Z") || strings.Contains(value, "+") || strings.Contains(value, "-"))
}

// isValidDate 验证日期格式
func (v *jsonSchemaValidator) isValidDate(value string) bool {
	// 简单的日期格式检查 YYYY-MM-DD
	parts := strings.Split(value, "-")
	return len(parts) == 3 && len(parts[0]) == 4 && len(parts[1]) == 2 && len(parts[2]) == 2
}

// isValidTime 验证时间格式
func (v *jsonSchemaValidator) isValidTime(value string) bool {
	// 简单的时间格式检查 HH:MM:SS
	parts := strings.Split(value, ":")
	return len(parts) >= 2 && len(parts[0]) == 2 && len(parts[1]) == 2
}

// isValidEmail 验证邮箱格式
func (v *jsonSchemaValidator) isValidEmail(value string) bool {
	// 简单的邮箱格式检查
	return strings.Contains(value, "@") && strings.Contains(value, ".")
}

// isValidURI 验证URI格式
func (v *jsonSchemaValidator) isValidURI(value string) bool {
	// 简单的URI格式检查
	return strings.HasPrefix(value, "http://") || strings.HasPrefix(value, "https://") || strings.HasPrefix(value, "ftp://")
}

// GetRequiredFields 获取必填字段列表
func (v *jsonSchemaValidator) GetRequiredFields(schema models.JSONSchema) []string {
	return schema.Required
}