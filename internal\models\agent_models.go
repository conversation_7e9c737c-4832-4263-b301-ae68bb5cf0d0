package models

import (
	"time"
	"gitlab.com/specific-ai/taskd/internal/models/common"
)

// AgentType 定义Agent类型
type AgentType string

const (
	AgentTypeBidding   AgentType = "bidding"     // 招投标Agent
	AgentTypeSummary   AgentType = "summary"     // 摘要Agent
	AgentTypeAnalysis  AgentType = "analysis"    // 分析Agent
	AgentTypeSearch    AgentType = "search"      // 搜索Agent
	AgentTypeCustom    AgentType = "custom"      // 自定义Agent
)

// AgentStatus 定义Agent状态
type AgentStatus string

const (
	AgentStatusIdle       AgentStatus = "idle"       // 空闲
	AgentStatusProcessing AgentStatus = "processing" // 处理中
	AgentStatusError      AgentStatus = "error"      // 错误
	AgentStatusDisabled   AgentStatus = "disabled"   // 禁用
)

// AgentConfig Agent配置
type AgentConfig struct {
	ID          string            `json:"id" bson:"_id"`
	Name        string            `json:"name" bson:"name"`
	Type        AgentType         `json:"type" bson:"type"`
	Description string            `json:"description" bson:"description"`
	Version     string            `json:"version" bson:"version"`
	Enabled     bool              `json:"enabled" bson:"enabled"`
	Config      map[string]interface{} `json:"config" bson:"config"`
	CreatedAt   time.Time         `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at" bson:"updated_at"`
}

// AgentCapability Agent能力定义
type AgentCapability struct {
	Name        string                 `json:"name" bson:"name"`
	Description string                 `json:"description" bson:"description"`
	InputSchema map[string]interface{} `json:"input_schema" bson:"input_schema"`
	OutputSchema map[string]interface{} `json:"output_schema" bson:"output_schema"`
	Required    []string               `json:"required" bson:"required"`
}

// AgentCard Agent卡片（参考A2A协议）
type AgentCard struct {
	ID           string            `json:"id" bson:"_id"`
	Name         string            `json:"name" bson:"name"`
	Type         AgentType         `json:"type" bson:"type"`
	Description  string            `json:"description" bson:"description"`
	Version      string            `json:"version" bson:"version"`
	Author       string            `json:"author" bson:"author"`
	Capabilities []AgentCapability `json:"capabilities" bson:"capabilities"`
	Dependencies []string          `json:"dependencies" bson:"dependencies"`
	Metadata     map[string]interface{} `json:"metadata" bson:"metadata"`
	CreatedAt    time.Time         `json:"created_at" bson:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at" bson:"updated_at"`
}

// AgentInstance Agent实例
type AgentInstance struct {
	ID        string                 `json:"id" bson:"_id"`
	CardID    string                 `json:"card_id" bson:"card_id"`
	Status    AgentStatus            `json:"status" bson:"status"`
	Config    map[string]interface{} `json:"config" bson:"config"`
	StartedAt time.Time              `json:"started_at" bson:"started_at"`
	LastPing  time.Time              `json:"last_ping" bson:"last_ping"`
	ErrorMsg  string                 `json:"error_msg,omitempty" bson:"error_msg,omitempty"`
}

// AgentRequest Agent请求
type AgentRequest struct {
	ID           string                 `json:"id"`
	AgentID      string                 `json:"agent_id"`
	Capability   string                 `json:"capability"`
	Input        map[string]interface{} `json:"input"`
	Config       map[string]interface{} `json:"config,omitempty"`
	Priority     int                    `json:"priority,omitempty"`
	Timeout      int                    `json:"timeout,omitempty"`
	CallbackURL  string                 `json:"callback_url,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`
}

// AgentResponse Agent响应
type AgentResponse struct {
	ID        string                 `json:"id"`
	RequestID string                 `json:"request_id"`
	Success   bool                   `json:"success"`
	Output    map[string]interface{} `json:"output,omitempty"`
	Error     string                 `json:"error,omitempty"`
	Duration  int64                  `json:"duration"` // 毫秒
	CreatedAt time.Time              `json:"created_at"`
}

// AgentExecution Agent执行记录
type AgentExecution struct {
	ID            string                 `json:"id" bson:"_id"`
	AgentID       string                 `json:"agent_id" bson:"agent_id"`
	RequestID     string                 `json:"request_id" bson:"request_id"`
	Capability    string                 `json:"capability" bson:"capability"`
	Input         map[string]interface{} `json:"input" bson:"input"`
	Output        map[string]interface{} `json:"output" bson:"output"`
	Success       bool                   `json:"success" bson:"success"`
	Error         string                 `json:"error,omitempty" bson:"error,omitempty"`
	Duration      int64                  `json:"duration" bson:"duration"`
	TokenUsage    *common.TokenUsage     `json:"token_usage,omitempty" bson:"token_usage,omitempty"`
	CreatedAt     time.Time              `json:"created_at" bson:"created_at"`
}

// 注释：TokenUsage定义已移至common/base_models.go中，避免重复定义

// AgentTemplate Agent模板
type AgentTemplate struct {
	ID          string                 `json:"id" bson:"_id"`
	Name        string                 `json:"name" bson:"name"`
	Type        AgentType              `json:"type" bson:"type"`
	Description string                 `json:"description" bson:"description"`
	SystemPrompt string                `json:"system_prompt" bson:"system_prompt"`
	UserPromptTemplate string          `json:"user_prompt_template" bson:"user_prompt_template"`
	OutputSchema map[string]interface{} `json:"output_schema" bson:"output_schema"`
	Config      map[string]interface{} `json:"config" bson:"config"`
	CreatedAt   time.Time              `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at" bson:"updated_at"`
}

// AgentExecutionRequest Agent执行请求
type AgentExecutionRequest struct {
	AgentID    string                 `json:"agent_id" binding:"required"`
	Capability string                 `json:"capability" binding:"required"`
	Input      map[string]interface{} `json:"input" binding:"required"`
	Config     map[string]interface{} `json:"config,omitempty"`
	Priority   int                    `json:"priority,omitempty"`
	Timeout    int                    `json:"timeout,omitempty"`
}

// AgentListRequest Agent列表请求
type AgentListRequest struct {
	Type    AgentType `json:"type,omitempty" form:"type"`
	Status  AgentStatus `json:"status,omitempty" form:"status"`
	Page    int       `json:"page,omitempty" form:"page"`
	Limit   int       `json:"limit,omitempty" form:"limit"`
	Keyword string    `json:"keyword,omitempty" form:"keyword"`
}

// AgentListResponse Agent列表响应
type AgentListResponse struct {
	Total  int64           `json:"total"`
	Page   int             `json:"page"`
	Limit  int             `json:"limit"`
	Agents []AgentInstance `json:"agents"`
}

// AgentMetrics Agent指标
type AgentMetrics struct {
	AgentID        string  `json:"agent_id"`
	TotalRequests  int64   `json:"total_requests"`
	SuccessRequests int64   `json:"success_requests"`
	ErrorRequests  int64   `json:"error_requests"`
	SuccessRate    float64 `json:"success_rate"`
	AvgDuration    float64 `json:"avg_duration"`
	TotalTokens    int64   `json:"total_tokens"`
	LastExecution  *time.Time `json:"last_execution,omitempty"`
}

// AgentHealthCheck Agent健康检查
type AgentHealthCheck struct {
	AgentID   string    `json:"agent_id"`
	Status    AgentStatus `json:"status"`
	Healthy   bool      `json:"healthy"`
	Message   string    `json:"message,omitempty"`
	CheckedAt time.Time `json:"checked_at"`
}