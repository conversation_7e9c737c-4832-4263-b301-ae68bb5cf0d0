package services

import (
	biddingInterfaces "gitlab.com/specific-ai/taskd/internal/modules/bidding/interfaces"
)

// BiddingAgentManager manages all bidding-related agents
type BiddingAgentManager struct {
	registry biddingInterfaces.AgentRegistry
}

// NewBiddingAgentManager creates a new bidding agent manager
func NewBiddingAgentManager(registry biddingInterfaces.AgentRegistry) *BiddingAgentManager {
	return &BiddingAgentManager{
		registry: registry,
	}
}

// GetRegistry returns the agent registry
func (m *BiddingAgentManager) GetRegistry() biddingInterfaces.AgentRegistry {
	return m.registry
}

// HealthCheck returns the health status of all agents
func (m *BiddingAgentManager) HealthCheck() map[string]interface{} {
	return map[string]interface{}{
		"registry_stats": m.registry.GetAgentStats(),
	}
}
