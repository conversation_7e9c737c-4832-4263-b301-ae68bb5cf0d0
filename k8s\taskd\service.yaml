apiVersion: v1
kind: Service
metadata:
  name: taskd-service
  namespace: ovs
  labels:
    app: taskd
    app.kubernetes.io/name: taskd
    app.kubernetes.io/component: service
spec:
  type: ClusterIP
  ports:
  - port: 8601
    targetPort: 8601
    protocol: TCP
    name: http
    appProtocol: http
  selector:
    app: taskd

---
# NodePort服务（用于外部访问调试）
apiVersion: v1
kind: Service
metadata:
  name: taskd-nodeport
  namespace: ovs
  labels:
    app: taskd
    app.kubernetes.io/name: taskd
    app.kubernetes.io/component: service
    service-type: nodeport
spec:
  type: NodePort
  ports:
  - port: 8601
    targetPort: 8601
    protocol: TCP
    name: http
    appProtocol: http
    nodePort: 30601  # 外部访问端口，k3s环境下可通过此端口访问
  selector:
    app: taskd 