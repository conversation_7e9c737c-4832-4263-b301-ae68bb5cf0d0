package models

import "fmt"

// ProfileError 企业画像专用错误类型
type ProfileError struct {
	Code    string
	Message string
	Details map[string]interface{}
}

func (e *ProfileError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 错误代码常量
const (
	ErrCodeInvalidRequest     = "INVALID_REQUEST"
	ErrCodeLLMProcessing     = "LLM_PROCESSING_ERROR"
	ErrCodePromptFormatting  = "PROMPT_FORMATTING_ERROR"
	ErrCodeDataParsing       = "DATA_PARSING_ERROR"
	ErrCodeStorageError      = "STORAGE_ERROR"
	ErrCodeValidationError   = "VALIDATION_ERROR"
)

// NewProfileError 创建新的企业画像错误
func NewProfileError(code, message string, details map[string]interface{}) *ProfileError {
	return &ProfileError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewInvalidRequestError 创建无效请求错误
func NewInvalidRequestError(message string) *ProfileError {
	return NewProfileError(ErrCodeInvalidRequest, message, nil)
}

// NewLLMProcessingError 创建LLM处理错误
func NewLLMProcessingError(message string, details map[string]interface{}) *ProfileError {
	return NewProfileError(ErrCodeLLMProcessing, message, details)
}

// NewPromptFormattingError 创建提示词格式化错误
func NewPromptFormattingError(message string) *ProfileError {
	return NewProfileError(ErrCodePromptFormatting, message, nil)
}

// NewDataParsingError 创建数据解析错误  
func NewDataParsingError(message string, details map[string]interface{}) *ProfileError {
	return NewProfileError(ErrCodeDataParsing, message, details)
}

// NewStorageError 创建存储错误
func NewStorageError(message string, details map[string]interface{}) *ProfileError {
	return NewProfileError(ErrCodeStorageError, message, details)
}

// NewValidationError 创建验证错误
func NewValidationError(message string, details map[string]interface{}) *ProfileError {
	return NewProfileError(ErrCodeValidationError, message, details)
}