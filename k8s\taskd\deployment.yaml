apiVersion: apps/v1
kind: Deployment
metadata:
  name: taskd
  namespace: ovs
  labels:
    app: taskd
    app.kubernetes.io/name: taskd
    app.kubernetes.io/component: service
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1  # 调试模式使用单实例
  selector:
    matchLabels:
      app: taskd
  template:
    metadata:
      annotations:
        "sidecar.istio.io/inject": "false"
      labels:
        app: taskd
        app.kubernetes.io/name: taskd
        app.kubernetes.io/component: service
    spec:
      containers:
      - name: taskd
        image: 192.168.50.112/specific-ai/taskd:250729-1  # 生产镜像地址
        imagePullPolicy: IfNotPresent  # 本地不存在时才拉取
        ports:
        - containerPort: 8601
          name: http
          protocol: TCP
        env:
        # 从ConfigMap加载服务器配置
        - name: SERVER_PORT
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: SERVER_PORT
        - name: SERVER_MODE
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: SERVER_MODE
        - name: LOGGER_LEVEL
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: LOGGER_LEVEL
        
        # MongoDB配置
        - name: MONGODB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: MONGODB_DATABASE
        - name: MONGODB_TIMEOUT_SECONDS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: MONGODB_TIMEOUT_SECONDS
        - name: MONGODB_URI_ENV
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: MONGODB_URI
        
        # PostgreSQL配置 - 集群内PostgreSQL
        - name: POSTGRESQL_HOST_ENV
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_HOST
        - name: POSTGRESQL_PORT_ENV
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_PORT
        - name: POSTGRESQL_DATABASE_ENV
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_DATABASE
        - name: POSTGRESQL_SSL_MODE
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_SSL_MODE
        - name: POSTGRESQL_TIMEOUT_SECONDS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_TIMEOUT_SECONDS
        - name: POSTGRESQL_MAX_OPEN_CONNS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_MAX_OPEN_CONNS
        - name: POSTGRESQL_MAX_IDLE_CONNS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_MAX_IDLE_CONNS
        - name: POSTGRESQL_CONN_MAX_LIFETIME
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: POSTGRESQL_CONN_MAX_LIFETIME
        - name: POSTGRESQL_USER_ENV
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: POSTGRESQL_USER
        - name: POSTGRESQL_PASSWORD_ENV
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: POSTGRESQL_PASSWORD
        
        # Pulsar配置
        - name: PULSAR_SERVICE_URL
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: PULSAR_SERVICE_URL
        - name: PULSAR_CONSUMER_TOPIC_REPORT_SUMMARY
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: PULSAR_CONSUMER_TOPIC_REPORT_SUMMARY
        - name: PULSAR_CONSUMER_SUBSCRIPTION_NAME
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: PULSAR_CONSUMER_SUBSCRIPTION_NAME
        
        # LLM配置
        - name: LLM_DEFAULT_PROVIDER
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: LLM_DEFAULT_PROVIDER
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: LLM_PROVIDERS_VOLCENGINE_ARK_BASE_URL
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_DEFAULT_MODEL_ALIAS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: LLM_PROVIDERS_VOLCENGINE_ARK_DEFAULT_MODEL_ALIAS
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_REQUEST_TIMEOUT_SECONDS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: LLM_PROVIDERS_VOLCENGINE_ARK_REQUEST_TIMEOUT_SECONDS
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_MAX_CONCURRENT_REQUESTS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: LLM_PROVIDERS_VOLCENGINE_ARK_MAX_CONCURRENT_REQUESTS
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_API_KEY
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: LLM_PROVIDERS_VOLCENGINE_ARK_API_KEY
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_API_KEYS
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: LLM_PROVIDERS_VOLCENGINE_ARK_API_KEYS
        - name: LLM_PROVIDERS_VOLCENGINE_ARK_MODEL_REPORT_SUMMARIZER
          valueFrom:
            secretKeyRef:
              name: taskd-secret
              key: LLM_PROVIDERS_VOLCENGINE_ARK_MODEL_REPORT_SUMMARIZER
        
        # ETCD配置中心配置
        - name: ETCD_ENABLED
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: ETCD_ENABLED
        - name: ETCD_ENDPOINTS
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: ETCD_ENDPOINTS
        - name: ETCD_DIAL_TIMEOUT_SEC
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: ETCD_DIAL_TIMEOUT_SEC
        - name: ETCD_REQUEST_TIMEOUT_SEC
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: ETCD_REQUEST_TIMEOUT_SEC
        - name: ETCD_CONFIG_PREFIX
          valueFrom:
            configMapKeyRef:
              name: taskd-config
              key: ETCD_CONFIG_PREFIX
        
        # 调试模式：禁用健康检查避免无限重启
        # livenessProbe:
        #   httpGet:
        #     path: /healthz
        #     port: 8601
        #   initialDelaySeconds: 60
        #   periodSeconds: 20
        #   timeoutSeconds: 10
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /healthz
        #     port: 8601
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        
        # 资源限制
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        
        # 调试模式：允许root权限便于安装工具和编译
        securityContext:
          runAsNonRoot: false
          runAsUser: 0  # root用户便于调试
          allowPrivilegeEscalation: true
          readOnlyRootFilesystem: false
        
        # 调试模式：启动服务但保持调试能力
        command: ["/bin/bash"]
        args: ["-c", "./taskd || (echo 'Service failed, keeping container alive for debugging' && sleep infinity)"]
      
      # Pod安全上下文
      securityContext:
        fsGroup: 1000
      
      # 重启策略
      restartPolicy: Always
      
      # DNS策略
      dnsPolicy: ClusterFirst 