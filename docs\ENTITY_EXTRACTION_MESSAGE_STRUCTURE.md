# 实体提取Agent消息结构定义

## 概述

本文档详细定义了实体提取Agent的所有请求和响应消息结构，包括标准的A2A（Agent-to-Agent）通信协议格式以及各种业务场景的具体消息示例。

## 标准消息格式

### Agent执行请求格式

所有对实体提取Agent的调用都遵循标准的Agent执行请求格式：

```json
{
  "agent_id": "entity-extraction-agent-001",
  "capability": "extract_structured_data",
  "input": {
    // 具体的输入参数，见下文详细定义
  },
  "config": {
    "timeout": 30,
    "priority": 1,
    "retry_count": 3
  }
}
```

### Agent响应格式

```json
{
  "id": "resp_1234567890",
  "request_id": "req_1234567890",
  "success": true,
  "output": {
    // 具体的输出数据，见下文详细定义
  },
  "error": "错误信息（仅当success为false时存在）",
  "duration": 1250,
  "created_at": "2024-01-01T10:30:00Z"
}
```

## 输入消息结构

### 基础输入结构

```json
{
  "input_text": "string",
  "target_schema": {
    "name": "string",
    "description": "string", 
    "type": "object",
    "properties": {},
    "required": [],
    "examples": []
  },
  "language": "zh|en|mixed",
  "extraction_config": {
    "confidence_threshold": 0.7,
    "max_retries": 3,
    "timeout": 30,
    "enable_fallback": true
  }
}
```

### 字段详细说明

#### input_text
- **类型**: string
- **必填**: 是
- **描述**: 需要进行实体提取的输入文本
- **限制**: 长度1-50000字符
- **示例**: "苹果公司发布了新的iPhone 15"

#### target_schema
- **类型**: object
- **必填**: 是
- **描述**: 目标JSON Schema定义，描述期望提取的数据结构

##### target_schema.name
- **类型**: string
- **必填**: 是
- **描述**: 模型名称，用于标识数据模型
- **示例**: "ProductInfo", "OpportunityDimension"

##### target_schema.description
- **类型**: string
- **必填**: 是
- **描述**: 模型的详细描述，帮助LLM理解模型用途
- **示例**: "产品信息模型，用于提取产品相关的结构化数据"

##### target_schema.type
- **类型**: string
- **必填**: 是
- **枚举值**: ["object"]
- **描述**: 根数据类型，固定为"object"

##### target_schema.properties
- **类型**: object
- **必填**: 是
- **描述**: 字段属性定义，每个key代表一个字段名

###### 属性字段结构
```json
{
  "field_name": {
    "type": "string|number|integer|boolean|array|object",
    "description": "字段描述",
    "format": "date-time|date|time|email|uri",
    "enum": ["value1", "value2"],
    "items": {
      // 数组项类型定义（当type为array时）
    },
    "properties": {
      // 嵌套对象属性（当type为object时）
    },
    "required": [],
    "default": "默认值"
  }
}
```

##### target_schema.required
- **类型**: array of strings
- **必填**: 否
- **描述**: 必填字段列表
- **示例**: ["name", "price"]

##### target_schema.examples
- **类型**: array
- **必填**: 否
- **描述**: 示例数据，帮助LLM理解期望的输出格式
- **建议**: 提供1-3个典型示例

#### language
- **类型**: string
- **必填**: 否
- **默认值**: "zh"
- **枚举值**: ["zh", "en", "mixed"]
- **描述**: 输入文本的语言类型

#### extraction_config
- **类型**: object
- **必填**: 否
- **描述**: 提取配置参数

##### extraction_config.confidence_threshold
- **类型**: number
- **范围**: 0.0-1.0
- **默认值**: 0.7
- **描述**: 置信度阈值，低于此值的结果将被拒绝

##### extraction_config.max_retries
- **类型**: integer
- **范围**: 0-10
- **默认值**: 3
- **描述**: 最大重试次数

##### extraction_config.timeout
- **类型**: integer
- **范围**: 1-300
- **默认值**: 30
- **描述**: 超时时间（秒）

##### extraction_config.enable_fallback
- **类型**: boolean
- **默认值**: true
- **描述**: 是否启用降级策略

## 输出消息结构

### 基础输出结构

```json
{
  "success": true,
  "data": {},
  "confidence": 0.92,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "metadata": {
    "model_used": "deepseek-v3-250324",
    "processing_time_ms": 1250,
    "token_usage": {
      "input_tokens": 150,
      "output_tokens": 80,
      "total_tokens": 230
    },
    "language": "zh",
    "retry_count": 0,
    "processed_at": "2024-01-01T10:30:00Z"
  },
  "raw_output": "LLM原始输出（仅在解析失败时提供）"
}
```

### 字段详细说明

#### success
- **类型**: boolean
- **描述**: 提取是否成功
- **true**: 成功提取并通过置信度检查
- **false**: 提取失败或置信度过低

#### data
- **类型**: object
- **描述**: 提取的结构化数据，符合target_schema定义
- **仅在success为true时存在**

#### confidence
- **类型**: number
- **范围**: 0.0-1.0
- **描述**: 提取结果的置信度评分

#### error
- **类型**: object
- **描述**: 错误信息（仅当success为false时存在）

##### error.code
- **类型**: string
- **描述**: 标准化错误代码，参考错误码表

##### error.message
- **类型**: string
- **描述**: 人类可读的错误描述

##### error.details
- **类型**: object
- **描述**: 错误的详细信息和上下文

#### metadata
- **类型**: object
- **描述**: 处理元数据信息

##### metadata.model_used
- **类型**: string
- **描述**: 使用的LLM模型名称

##### metadata.processing_time_ms
- **类型**: integer
- **描述**: 处理时间（毫秒）

##### metadata.token_usage
- **类型**: object
- **描述**: Token使用统计

##### metadata.language
- **类型**: string
- **描述**: 检测到的输入文本语言

##### metadata.retry_count
- **类型**: integer
- **描述**: 实际重试次数

##### metadata.processed_at
- **类型**: string (ISO 8601)
- **描述**: 处理完成时间

#### raw_output
- **类型**: string
- **描述**: LLM的原始输出（仅在JSON解析失败时提供）

## 业务场景消息示例

### 1. 商机维度提取

#### 请求消息
```json
{
  "agent_id": "entity-extraction-agent-001",
  "capability": "extract_structured_data",
  "input": {
    "input_text": "请你帮我分析这条新闻的商业相关的机会",
    "target_schema": {
      "name": "OpportunityDimension",
      "description": "商机维度模型，用于分析商业机会的各个维度",
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "维度名称，如'商业机会'、'技术创新'等"
        },
        "description": {
          "type": "string", 
          "description": "维度的详细描述，解释该维度的含义和作用"
        },
        "keywords": {
          "type": "array",
          "description": "与该维度相关的关键字列表，支持中英文",
          "items": {"type": "string"}
        }
      },
      "required": ["name", "description"],
      "examples": [
        {
          "name": "商业机会",
          "description": "与企业增长或盈利潜力相关的机会。",
          "keywords": ["市场扩张", "新客户", "收入增长"]
        }
      ]
    },
    "language": "zh"
  },
  "config": {
    "timeout": 30,
    "priority": 1
  }
}
```

#### 响应消息
```json
{
  "id": "resp_1704096600123",
  "request_id": "req_1704096600123",
  "success": true,
  "output": {
    "success": true,
    "data": {
      "name": "商业机会",
      "description": "与企业增长或盈利潜力相关的机会。",
      "keywords": ["市场扩张", "新客户", "收入增长", "market expansion", "new clients", "revenue growth"]
    },
    "confidence": 0.92,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 1250,
      "token_usage": {
        "input_tokens": 150,
        "output_tokens": 80,
        "total_tokens": 230
      },
      "language": "zh",
      "retry_count": 0,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 1250,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### 2. 招标关键词信息提取

#### 请求消息
```json
{
  "agent_id": "entity-extraction-agent-001",
  "capability": "extract_structured_data",
  "input": {
    "input_text": "我想找德国2025年5月后发布的低平板挂车采购项目",
    "target_schema": {
      "name": "BiddingKeyInfo",
      "description": "招标关键词信息模型，用于提取招标项目的关键信息",
      "type": "object",
      "properties": {
        "subject": {
          "type": "string",
          "description": "标题或主要采购内容"
        },
        "industry": {
          "type": "string",
          "description": "所属行业"
        },
        "country": {
          "type": "string",
          "description": "国家或地区"
        },
        "publication_date_from": {
          "type": "string",
          "format": "date-time",
          "description": "发布日期起始时间"
        },
        "procurement_type": {
          "type": "string",
          "description": "采购类型，如物品采购、服务采购、工程采购"
        },
        "estimated_value_min": {
          "type": "number",
          "description": "预算下限"
        },
        "currency": {
          "type": "string",
          "description": "币种"
        }
      },
      "required": ["subject"],
      "examples": [
        {
          "subject": "低平板挂车",
          "country": "德国",
          "publication_date_from": "2025-05-01T00:00:00Z",
          "procurement_type": "物品采购"
        }
      ]
    },
    "language": "zh"
  }
}
```

#### 响应消息
```json
{
  "id": "resp_1704096600124",
  "request_id": "req_1704096600124",
  "success": true,
  "output": {
    "success": true,
    "data": {
      "subject": "低平板挂车",
      "industry": "交通运输设备",
      "country": "德国",
      "publication_date_from": "2025-05-01T00:00:00Z",
      "procurement_type": "物品采购"
    },
    "confidence": 0.95,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 950,
      "token_usage": {
        "input_tokens": 180,
        "output_tokens": 120,
        "total_tokens": 300
      },
      "language": "zh",
      "retry_count": 0,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 950,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### 3. 分析角度操作提取

#### 请求消息
```json
{
  "agent_id": "entity-extraction-agent-001",
  "capability": "extract_structured_data",
  "input": {
    "input_text": "增加一条竞争对手分析，减少摘要分析",
    "target_schema": {
      "name": "DirectionConditionsList",
      "description": "分析角度操作列表模型，用于解析用户对分析维度的操作需求",
      "type": "object",
      "properties": {
        "conditions_list": {
          "type": "array",
          "description": "分析角度操作列表",
          "items": {
            "type": "object",
            "properties": {
              "operator": {
                "type": "string",
                "enum": ["删除", "修改", "新增"],
                "description": "操作类型"
              },
              "name": {
                "type": "string",
                "description": "维度名称"
              },
              "description": {
                "type": "string",
                "description": "维度描述"
              }
            },
            "required": ["operator", "name", "description"]
          }
        }
      },
      "required": ["conditions_list"],
      "examples": [
        {
          "conditions_list": [
            {
              "operator": "新增",
              "name": "竞争对手分析",
              "description": "对主要竞争对手的市场表现、战略和优势进行分析。"
            }
          ]
        }
      ]
    },
    "language": "zh"
  }
}
```

#### 响应消息
```json
{
  "id": "resp_1704096600125",
  "request_id": "req_1704096600125",
  "success": true,
  "output": {
    "success": true,
    "data": {
      "conditions_list": [
        {
          "operator": "新增",
          "name": "竞争对手分析",
          "description": "对主要竞争对手的市场表现、战略和优势进行分析。"
        },
        {
          "operator": "删除",
          "name": "摘要分析",
          "description": "对新闻内容进行简要总结。"
        }
      ]
    },
    "confidence": 0.88,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 1100,
      "token_usage": {
        "input_tokens": 200,
        "output_tokens": 150,
        "total_tokens": 350
      },
      "language": "zh",
      "retry_count": 0,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 1100,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### 4. 查询条件提取

#### 请求消息
```json
{
  "agent_id": "entity-extraction-agent-001",
  "capability": "extract_structured_data",
  "input": {
    "input_text": "请你帮我查找中国的大于7月18号的新闻",
    "target_schema": {
      "name": "QueryConditionsList",
      "description": "查询条件列表模型，用于解析用户的查询需求",
      "type": "object",
      "properties": {
        "query_conditions_list": {
          "type": "array",
          "description": "查询条件列表",
          "items": {
            "type": "object",
            "properties": {
              "query_fields": {
                "type": "string",
                "description": "需要查询的字段名",
                "enum": ["country", "update_at", "title", "content"]
              },
              "query_value": {
                "type": "string",
                "description": "查询字段的值"
              },
              "query_operate": {
                "type": "string",
                "description": "查询操作符",
                "enum": ["eq", "ne", "gt", "gte", "lt", "lte"]
              }
            },
            "required": ["query_fields", "query_value", "query_operate"]
          }
        }
      },
      "required": ["query_conditions_list"],
      "examples": [
        {
          "query_conditions_list": [
            {
              "query_fields": "country",
              "query_value": "china",
              "query_operate": "eq"
            }
          ]
        }
      ]
    },
    "language": "zh"
  }
}
```

#### 响应消息
```json
{
  "id": "resp_1704096600126",
  "request_id": "req_1704096600126",
  "success": true,
  "output": {
    "success": true,
    "data": {
      "query_conditions_list": [
        {
          "query_fields": "country",
          "query_value": "china",
          "query_operate": "eq"
        },
        {
          "query_fields": "update_at",
          "query_value": "2025-7-18",
          "query_operate": "gt"
        }
      ]
    },
    "confidence": 0.95,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 950,
      "token_usage": {
        "input_tokens": 180,
        "output_tokens": 120,
        "total_tokens": 300
      },
      "language": "zh",
      "retry_count": 0,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 950,
  "created_at": "2024-01-01T10:30:00Z"
}
```

## 复杂数据类型处理

### 嵌套对象示例

#### Schema定义
```json
{
  "name": "UserProfile",
  "description": "用户档案模型",
  "type": "object",
  "properties": {
    "user": {
      "type": "object",
      "description": "用户基本信息",
      "properties": {
        "name": {"type": "string", "description": "姓名"},
        "email": {"type": "string", "format": "email", "description": "邮箱"},
        "profile": {
          "type": "object",
          "description": "用户档案",
          "properties": {
            "level": {
              "type": "string",
              "enum": ["basic", "premium", "vip"],
              "description": "用户等级"
            },
            "verified": {"type": "boolean", "description": "是否已验证"}
          }
        }
      },
      "required": ["name", "email"]
    },
    "tags": {
      "type": "array",
      "description": "用户标签",
      "items": {"type": "string"}
    }
  },
  "required": ["user"]
}
```

#### 输出示例
```json
{
  "user": {
    "name": "张三",
    "email": "<EMAIL>",
    "profile": {
      "level": "vip",
      "verified": true
    }
  },
  "tags": ["VIP客户", "技术专家"]
}
```

### 数组类型示例

#### Schema定义
```json
{
  "name": "ProductList",
  "description": "产品列表模型",
  "type": "object",
  "properties": {
    "products": {
      "type": "array",
      "description": "产品列表",
      "items": {
        "type": "object",
        "properties": {
          "name": {"type": "string", "description": "产品名称"},
          "price": {"type": "number", "description": "价格"},
          "category": {"type": "string", "description": "分类"}
        },
        "required": ["name", "price"]
      }
    }
  },
  "required": ["products"]
}
```

### 日期时间格式处理

#### 支持的日期格式
- **date-time**: ISO 8601格式，如 "2024-01-01T10:30:00Z"
- **date**: 日期格式，如 "2024-01-01"
- **time**: 时间格式，如 "10:30:00"

#### Schema示例
```json
{
  "created_at": {
    "type": "string",
    "format": "date-time",
    "description": "创建时间"
  },
  "birth_date": {
    "type": "string",
    "format": "date",
    "description": "出生日期"
  }
}
```

### 枚举类型处理

#### Schema示例
```json
{
  "status": {
    "type": "string",
    "enum": ["active", "inactive", "suspended"],
    "description": "用户状态"
  },
  "priority": {
    "type": "string",
    "enum": ["low", "medium", "high", "urgent"],
    "description": "优先级"
  }
}
```

## 错误响应示例

### 输入验证失败
```json
{
  "id": "resp_1704096600127",
  "request_id": "req_1704096600127",
  "success": false,
  "error": "输入验证失败: [input_text: 输入文本不能为空]",
  "duration": 50,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### Schema验证失败
```json
{
  "id": "resp_1704096600128",
  "request_id": "req_1704096600128",
  "success": false,
  "output": {
    "success": false,
    "error": {
      "code": "INVALID_MODEL_SCHEMA",
      "message": "Schema验证失败: name: 模型名称不能为空",
      "details": {
        "validation_errors": [
          {
            "field": "name",
            "message": "模型名称不能为空",
            "code": "INVALID_INPUT"
          }
        ]
      }
    },
    "confidence": 0.0,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 100,
      "token_usage": {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0
      },
      "language": "zh",
      "retry_count": 0,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 100,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### 置信度过低
```json
{
  "id": "resp_1704096600129",
  "request_id": "req_1704096600129",
  "success": true,
  "output": {
    "success": false,
    "error": {
      "code": "LOW_CONFIDENCE",
      "message": "提取结果置信度过低: 0.45 < 0.70",
      "details": {
        "confidence": 0.45,
        "confidence_threshold": 0.70,
        "validation_errors": []
      }
    },
    "data": {
      "name": "未知维度",
      "description": "无法确定具体维度"
    },
    "confidence": 0.45,
    "raw_output": "根据输入文本，无法准确识别出明确的商业机会维度...",
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 1500,
      "token_usage": {
        "input_tokens": 120,
        "output_tokens": 80,
        "total_tokens": 200
      },
      "language": "zh",
      "retry_count": 2,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 1500,
  "created_at": "2024-01-01T10:30:00Z"
}
```

### LLM调用失败
```json
{
  "id": "resp_1704096600130",
  "request_id": "req_1704096600130",
  "success": true,
  "output": {
    "success": false,
    "error": {
      "code": "LLM_CALL_FAILED",
      "message": "LLM调用失败，已重试3次: API调用超时",
      "details": {
        "original_error": "API调用超时",
        "retry_count": 3,
        "last_attempt_at": "2024-01-01T10:30:00Z"
      }
    },
    "confidence": 0.0,
    "metadata": {
      "model_used": "deepseek-v3-250324",
      "processing_time_ms": 30000,
      "token_usage": {
        "input_tokens": 0,
        "output_tokens": 0,
        "total_tokens": 0
      },
      "language": "zh",
      "retry_count": 3,
      "processed_at": "2024-01-01T10:30:00Z"
    }
  },
  "duration": 30000,
  "created_at": "2024-01-01T10:30:00Z"
}
```

## 最佳实践建议

### 1. 消息设计原则

- **明确性**: 字段名称和描述要清晰明确
- **一致性**: 保持消息结构的一致性
- **完整性**: 提供足够的信息用于处理
- **可扩展性**: 预留扩展空间

### 2. Schema设计建议

- **合理的层级**: 避免过深的嵌套结构
- **适当的示例**: 提供典型的示例数据
- **清晰的描述**: 为每个字段提供详细描述
- **正确的类型**: 选择最合适的数据类型

### 3. 错误处理建议

- **详细的错误信息**: 提供足够的错误上下文
- **标准化错误码**: 使用统一的错误码体系
- **用户友好**: 错误信息要便于理解和处理
- **可恢复性**: 提供错误恢复的建议

### 4. 性能优化建议

- **合理的超时**: 根据复杂度设置超时时间
- **批量处理**: 对于大量请求考虑批量处理
- **缓存策略**: 对相似请求实施缓存
- **监控指标**: 关注响应时间和成功率

## 版本兼容性

### 当前版本: v1.0.0

- 支持所有定义的消息结构
- 向后兼容保证
- 新增字段采用可选方式

### 升级指南

在升级到新版本时，请注意：

1. 检查新增的必填字段
2. 验证废弃字段的替代方案
3. 测试现有集成的兼容性
4. 更新错误处理逻辑

## 技术支持

如需技术支持或有疑问，请参考：

- **API文档**: 详细的接口说明
- **错误码表**: 完整的错误码定义
- **示例代码**: 各种语言的调用示例
- **FAQ**: 常见问题解答