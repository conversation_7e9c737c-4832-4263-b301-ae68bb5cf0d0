package messaging

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/apache/pulsar-client-go/pulsar"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/store" // 用于将结果保存到数据库
	"gitlab.com/specific-ai/taskd/internal/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// PulsarMessageProcessor 处理 Pulsar 消息
type PulsarMessageProcessor struct {
	reportSummaryService *services.ReportSummaryService
}

func NewPulsarMessageProcessor(rsService *services.ReportSummaryService) *PulsarMessageProcessor {
	return &PulsarMessageProcessor{
		reportSummaryService: rsService,
	}
}

// StartReportSummaryConsumer 启动报告总结的 Pulsar 消费者
func (pmp *PulsarMessageProcessor) StartReportSummaryConsumer(pulsarClient pulsar.Client, cfg config.PulsarConsumerConfig) error {
	if cfg.TopicReportSummary == "" {
		utils.Log.Warn("Pulsar 报告总结 Topic 未配置，消费者将不会启动。")
		return nil
	}

	consumer, err := pulsarClient.Subscribe(pulsar.ConsumerOptions{
		Topic:            cfg.TopicReportSummary,
		SubscriptionName: cfg.SubscriptionName,
		Type:             pulsar.Shared, // 或 Exclusive / Failover
	})
	if err != nil {
		return fmt.Errorf("订阅 Pulsar topic %s 失败: %w", cfg.TopicReportSummary, err)
	}
	defer consumer.Close()

	utils.Log.Infof("Pulsar 消费者已启动, Topic: %s, 订阅名: %s", cfg.TopicReportSummary, cfg.SubscriptionName)

	for { // 无限循环接收消息
		msg, err := consumer.Receive(context.Background()) // 阻塞调用
		if err != nil {
			utils.Log.Errorf("从 Pulsar 接收消息失败: %v", err)
			time.Sleep(1 * time.Second) // 简单退避
			continue
		}

		utils.Log.Infof("收到 Pulsar 消息 ID: %s", msg.ID())

		var reportReq models.ReportSummaryPulsarMessage
		if err := json.Unmarshal(msg.Payload(), &reportReq); err != nil {
			utils.Log.Errorf("解析 Pulsar 消息负载失败: %v. MessageID: %s", err, msg.ID())
			consumer.Nack(msg) // 负载格式错误，Nack
			continue
		}

		// 异步处理消息
		go func(m pulsar.Message, req models.ReportSummaryPulsarMessage) {
			summaryResp, err := pmp.reportSummaryService.Summarize(req.ReportSummaryRequest)
			if err != nil {
				utils.Log.Errorf("处理 Pulsar 消息 (ID: %s) 的报告总结时出错: %v", m.ID(), err)
				savePulsarProcessingResult(req, nil, err) // 保存错误结果
				consumer.Nack(m)                          // 处理错误，Nack
				return
			}

			utils.Log.Infof("成功处理 Pulsar 消息 (ID: %s). 摘要: %s", m.ID(), summaryResp.Result)
			savePulsarProcessingResult(req, summaryResp, nil) // 保存成功结果
			consumer.Ack(m)                                   // 确认成功处理
		}(msg, reportReq)
	}
}

// savePulsarProcessingResult 示例函数：将 Pulsar 消息处理结果保存到 MongoDB
func savePulsarProcessingResult(request models.ReportSummaryPulsarMessage, response *models.ReportSummaryResponse, procError error) {
	type ProcessedPulsarTask struct {
		ID              primitive.ObjectID `bson:"_id,omitempty"`
		PulsarMessageID string             `bson:"pulsar_message_id"`
		Request         interface{}        `bson:"request"` // 存储原始请求
		Response        interface{}        `bson:"response,omitempty"`
		Error           string             `bson:"error,omitempty"`
		Status          string             `bson:"status"` // "completed" 或 "failed"
		Timestamp       time.Time          `bson:"timestamp"`
	}

	record := ProcessedPulsarTask{
		PulsarMessageID: request.MessageID, // 假设 MessageID 在 Pulsar 消息负载中
		Request:         request.ReportSummaryRequest,
		Timestamp:       time.Now().UTC(),
	}
	if procError != nil {
		record.Error = procError.Error()
		record.Status = "failed"
	} else {
		record.Response = response
		record.Status = "completed"
	}

	collection := store.GetCollection("pulsar_processed_tasks")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := collection.InsertOne(ctx, record)
	if err != nil {
		utils.Log.Errorf("保存 Pulsar 处理结果到 MongoDB 失败 (MessageID: %s): %v", request.MessageID, err)
	} else {
		utils.Log.Infof("已将 Pulsar 处理结果保存到 MongoDB (MessageID: %s)", request.MessageID)
	}
}
