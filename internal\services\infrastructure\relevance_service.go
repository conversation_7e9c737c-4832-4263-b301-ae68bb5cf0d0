package infrastructure

import (
	"fmt"
	"math"
	"regexp"
	"sort"
	"strings"

	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// WordEmbedder 定义了获取单个词词向量的接口
type WordEmbedder interface {
	GetWordEmbedding(word string) ([]float32, error) // 返回词向量和错误
	GetEmbeddingDimension() int                      // 返回词向量维度
}

// SentenceEmbedder 定义了获取句子向量的接口
type SentenceEmbedder interface {
	GetSentenceEmbedding(sentence string) ([]float32, error)
	GetEmbeddingDimension() int
}

// --- Mock/TODO Implementations ---

// MockWordEmbedder 是一个示例性的词嵌入器实现
// TODO: 实现一个真正的 WordEmbedder，例如加载预训练的 Word2Vec, GloVe, or FastText 模型
type MockWordEmbedder struct {
	dimension int
	vocab     map[string][]float32 // 模拟词汇表和向量
}

func NewMockWordEmbedder(dimension int) *MockWordEmbedder {
	// 初始化一些模拟词汇，方便测试
	vocab := make(map[string][]float32)
	// 为了简单，所有词返回相同的向量或随机向量
	// 实际中，这里应该从文件加载模型
	// 示例： "特定AI公司", "新产品发布", "模型", "关注"
	mockVector := make([]float32, dimension)
	for i := 0; i < dimension; i++ {
		mockVector[i] = float32(i+1) * 0.01 // 随意生成一些值
	}
	vocab["特定ai公司"] = append([]float32(nil), mockVector...) // 使用小写存储
	vocab["新产品发布"] = append([]float32(nil), mockVector...)
	vocab["模型"] = append([]float32(nil), mockVector...)
	vocab["关注"] = append([]float32(nil), mockVector...)
	vocab["天气"] = append([]float32(nil), mockVector...)

	return &MockWordEmbedder{
		dimension: dimension,
		vocab:     vocab,
	}
}

func (m *MockWordEmbedder) GetWordEmbedding(word string) ([]float32, error) {
	// TODO: 实现真实的词向量查找逻辑
	// 1. 文本预处理 (小写化, 去标点等)
	// 2. 在加载的词向量模型中查找词
	// 3. 如果词不在词汇表中，可以返回零向量或特定UNK向量，或错误
	processedWord := strings.ToLower(strings.TrimSpace(word)) // 简单预处理
	if vec, ok := m.vocab[processedWord]; ok {
		return vec, nil
	}
	// 模拟 OOV (Out Of Vocabulary) 情况，返回零向量
	utils.Log.Warnf("MockWordEmbedder: Word '%s' (processed: '%s') not in mock vocab, returning zero vector.", word, processedWord)
	return make([]float32, m.dimension), nil
	// 或者返回错误: return nil, fmt.Errorf("word '%s' not in vocabulary", processedWord)
}

func (m *MockWordEmbedder) GetEmbeddingDimension() int {
	return m.dimension
}

// AverageWordEmbeddingsSentenceEmbedder 通过平均词向量来计算句子向量
type AverageWordEmbeddingsSentenceEmbedder struct {
	wordEmbedder WordEmbedder
	tokenizer    func(text string) []string // 简单的分词函数
}

func NewAverageWordEmbeddingsSentenceEmbedder(we WordEmbedder) *AverageWordEmbeddingsSentenceEmbedder {
	// 一个非常简单的基于空格的分词器
	// TODO: 替换为更健壮的分词逻辑，例如考虑中文分词库 (如 gojieba) 或特定于模型的 tokenizer
	simpleTokenizer := func(text string) []string {
		// 简单去除常见标点，然后按空格分割
		// 正则表达式可以更灵活
		text = strings.ToLower(text)
		text = regexp.MustCompile(`[^\p{L}\p{N}\s]+`).ReplaceAllString(text, "") // 保留字母、数字和空格
		return strings.Fields(text)
	}
	return &AverageWordEmbeddingsSentenceEmbedder{
		wordEmbedder: we,
		tokenizer:    simpleTokenizer,
	}
}

func (s *AverageWordEmbeddingsSentenceEmbedder) GetSentenceEmbedding(sentence string) ([]float32, error) {
	words := s.tokenizer(sentence)
	if len(words) == 0 {
		// 如果句子为空或分词后没有词，返回零向量
		return make([]float32, s.wordEmbedder.GetEmbeddingDimension()), nil
	}

	dimension := s.wordEmbedder.GetEmbeddingDimension()
	sentenceVector := make([]float32, dimension)
	validWordCount := 0

	for _, word := range words {
		wordVec, err := s.wordEmbedder.GetWordEmbedding(word)
		if err != nil {
			//可以选择忽略错误（例如，如果GetWordEmbedding对OOV返回错误）
			//或者将其视为严重错误
			utils.Log.Warnf("Error getting embedding for word '%s': %v. Skipping word.", word, err)
			continue
		}
		// 假设 GetWordEmbedding 对 OOV 返回零向量或有效向量
		isZeroVector := true
		for _, val := range wordVec {
			if val != 0 {
				isZeroVector = false
				break
			}
		}
		if isZeroVector && len(wordVec) > 0 { // 明确跳过零向量（通常代表OOV）
			// utils.Log.Debugf("Skipping zero vector for word: %s", word)
			continue
		}

		if len(wordVec) == dimension {
			for i := 0; i < dimension; i++ {
				sentenceVector[i] += wordVec[i]
			}
			validWordCount++
		} else if len(wordVec) > 0 { // 维度不匹配，是个问题
			return nil, fmt.Errorf("mismatched embedding dimension for word '%s': expected %d, got %d", word, dimension, len(wordVec))
		}
	}

	if validWordCount > 0 {
		for i := 0; i < dimension; i++ {
			sentenceVector[i] /= float32(validWordCount)
		}
	}
	// 如果所有词都是OOV且返回零向量，那么句子向量也会是零向量

	return sentenceVector, nil
}

func (s *AverageWordEmbeddingsSentenceEmbedder) GetEmbeddingDimension() int {
	return s.wordEmbedder.GetEmbeddingDimension()
}

// --- Relevance Service ---

// RelevanceService 提供相关性计算功能
type RelevanceService struct {
	sentenceEmbedder SentenceEmbedder
	// segmentLength 和 overlap 在这个简化版中可能不是必须的，
	// 因为我们现在是直接对整个 content_list 中的文本进行句子嵌入。
	// 如果未来要恢复基于更长文本分段的逻辑，可以再加回来。
}

// NewRelevanceService 创建 RelevanceService 实例
func NewRelevanceService(se SentenceEmbedder) *RelevanceService {
	return &RelevanceService{
		sentenceEmbedder: se,
	}
}

// cosineSimilarity 计算两个向量的余弦相似度
func cosineSimilarity(vecA, vecB []float32) (float64, error) {
	if len(vecA) != len(vecB) || len(vecA) == 0 {
		return 0, fmt.Errorf("vectors must have the same non-zero length (lenA: %d, lenB: %d)", len(vecA), len(vecB))
	}
	var dotProduct float64
	var normA float64
	var normB float64
	for i := 0; i < len(vecA); i++ {
		dotProduct += float64(vecA[i] * vecB[i])
		normA += float64(vecA[i] * vecA[i])
		normB += float64(vecB[i] * vecB[i])
	}
	if normA == 0 || normB == 0 {
		// 如果任一向量是零向量，根据上下文，相似度可以是0或未定义
		// 如果两个都是零向量，也可以认为是相似度为1或未定义
		// 这里简单处理为0，表示无相似性或无法计算
		return 0, nil
	}
	denominator := math.Sqrt(normA) * math.Sqrt(normB)
	if denominator == 0 { // 再次检查，理论上 normA/normB > 0 时不会为0
		return 0, nil
	}
	return dotProduct / denominator, nil
}

// CalculateRelevance 处理相关性计算请求
func (s *RelevanceService) CalculateRelevance(req models.RelevanceCalculationRequest) (*models.RelevanceCalculationResponse, error) {
	if len(req.TargetList) == 0 || len(req.ContentList) == 0 {
		return &models.RelevanceCalculationResponse{FilteredItems: []models.FilteredItem{}}, nil
	}

	// 1. 获取目标关键词的句子/短语嵌入
	// 注意：TargetList 中的元素也可能是短语，我们的 SentenceEmbedder 应该能处理
	targetEmbeddings := make([][]float32, len(req.TargetList))
	for i, targetText := range req.TargetList {
		emb, err := s.sentenceEmbedder.GetSentenceEmbedding(targetText)
		if err != nil {
			return nil, fmt.Errorf("failed to get embedding for target '%s': %w", targetText, err)
		}
		targetEmbeddings[i] = emb
	}

	// 2. 获取内容列表中文本的句子嵌入，并记录原始索引
	type ContentEmbeddingItem struct {
		OriginalIndex int
		Embedding     []float32
		// Text          string // 可选，用于调试或返回
	}
	contentEmbeddings := make([]ContentEmbeddingItem, 0, len(req.ContentList))
	for i, contentText := range req.ContentList {
		trimmedText := strings.TrimSpace(contentText)
		if trimmedText == "" { // 跳过空内容
			continue
		}
		emb, err := s.sentenceEmbedder.GetSentenceEmbedding(trimmedText)
		if err != nil {
			// 决定如何处理错误：是跳过这个内容，还是中止整个请求？
			utils.Log.Warnf("Failed to get embedding for content at index %d ('%s'): %v. Skipping.", i, utils.TruncateString(trimmedText, 50), err)
			continue
		}
		contentEmbeddings = append(contentEmbeddings, ContentEmbeddingItem{
			OriginalIndex: i,
			Embedding:     emb,
			// Text:          trimmedText,
		})
	}

	if len(contentEmbeddings) == 0 {
		return &models.RelevanceCalculationResponse{FilteredItems: []models.FilteredItem{}}, nil
	}

	// 3. 计算相似度并过滤
	filteredItems := make([]models.FilteredItem, 0)

	for _, contentItem := range contentEmbeddings {
		keywordScoresDetailed := make([]models.KeywordScore, len(req.TargetList))
		var sumOfScores float64
		validKeywordScores := 0

		for targetIdx, targetText := range req.TargetList {
			sim, err := cosineSimilarity(targetEmbeddings[targetIdx], contentItem.Embedding)
			if err != nil {
				utils.Log.Warnf("Error calculating cosine similarity for target '%s' and content (original index %d): %v", targetText, contentItem.OriginalIndex, err)
				keywordScoresDetailed[targetIdx] = models.KeywordScore{Keyword: targetText, Score: 0} // 出错则分数为0
				continue
			}
			keywordScoresDetailed[targetIdx] = models.KeywordScore{Keyword: targetText, Score: sim}
			sumOfScores += sim
			validKeywordScores++
		}

		if validKeywordScores == 0 { // 如果所有目标关键词计算都出错
			continue
		}

		averageScore := sumOfScores / float64(validKeywordScores)

		if averageScore >= req.Threshold {
			// 按分数降序排列 keywordScoresDetailed (可选)
			sort.Slice(keywordScoresDetailed, func(i, j int) bool {
				return keywordScoresDetailed[i].Score > keywordScoresDetailed[j].Score
			})

			filteredItems = append(filteredItems, models.FilteredItem{
				OriginalIndex: contentItem.OriginalIndex,
				AverageScore:  averageScore,
				KeywordScores: keywordScoresDetailed,
			})
		}
	}

	// 按 AverageScore 降序排列 filteredItems (可选)
	sort.Slice(filteredItems, func(i, j int) bool {
		return filteredItems[i].AverageScore > filteredItems[j].AverageScore
	})

	return &models.RelevanceCalculationResponse{FilteredItems: filteredItems}, nil
}
