#!/usr/bin/env python3
"""
测试用例设计
  1. 商机维度提取测试 (test_opportunity_dimension_extraction)
    - 基于文档中的OpportunityDimension示例
    - 测试从自然语言中提取商业分析维度
  2. 招标关键词信息提取测试 (test_bidding_key_info_extraction)
    - 基于文档中的BiddingKeyInfo示例
    - 测试从招标需求中提取结构化信息
  3. 查询条件提取测试 (test_query_conditions_extraction)
    - 基于文档中的QueryConditionsList示例
    - 测试从自然语言查询中提取数据库查询条件
  4. 分析角度操作提取测试 (test_direction_conditions_extraction)
    - 基于文档中的DirectionConditionsList示例
    - 测试对分析维度的操作指令解析
  5. 复杂嵌套结构提取测试 (test_complex_nested_structure)
    - 基于文档中的UserProfile示例
    - 测试多层嵌套对象的实体提取
  6. 错误处理测试 (test_error_handling)
    - 测试无效Schema的错误处理机制
  7. Token消耗验证测试 (test_token_consumption_verification)
    - 使用指定的测试用户 test_user_entity 和公司 test_company_entity
    - 验证实体提取Agent的token记录功能
"""
import json
import requests
import os
import time
import pytest

# 测试用户常量
TEST_USER_ID = "test_user_entity"
TEST_COMPANY_ID = "test_company_entity"

class TestEntityExtractionAgent:
    """实体提取Agent测试套件"""
    
    def _handle_a2a_response(self, response, test_name, expected_fields=None):
        """处理A2A响应的通用方法"""
        if response.status_code == 200:
            data = response.json()
            # A2A JSON-RPC 响应格式检查
            if "result" in data:
                result = data["result"]
                output = result.get("output", {})
                
                # 处理成功的响应 (status: "completed")
                if result.get("status") == "completed" and output.get("success"):
                    if expected_fields:
                        for field in expected_fields:
                            assert field in output, f"响应中应包含{field}字段"
                    if "confidence" in output:
                        assert output["confidence"] >= 0.0, "置信度应为有效数值"
                        print(f"✓ {test_name}成功，置信度: {output['confidence']:.3f}")
                    else:
                        print(f"✓ {test_name}成功")
                    return "success"
                
                # 处理失败的响应 (status: "failed" 或 success: false)
                elif result.get("status") == "failed" or not output.get("success"):
                    error_msg = output.get('error', '未知错误')
                    print(f"✗ 实体提取失败: {error_msg}")
                    return "failed"
                
                else:
                    print(f"未知任务状态: {result.get('status')}")
                    print(f"完整响应: {output}")
                    return "unknown"
            elif "error" in data:
                print(f"A2A协议错误: {data['error']}")
                return "error"
            else:
                print(f"未知响应格式: {data}")
                return "unknown"
        else:
            pytest.fail(f"{test_name}失败: {response.status_code} - {response.text}")
            return "http_error"
    
    def test_opportunity_dimension_extraction(self):
        """测试商机维度提取"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 商机维度提取测试 ===")
        
        # A2A JSON-RPC 格式请求
        payload = {
            "jsonrpc": "2.0",
            "id": "req_opportunity_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "请你帮我分析这条新闻的商业相关的机会",
                    "target_schema": {
                        "name": "OpportunityDimension",
                        "description": "商机维度模型，用于分析商业机会的各个维度",
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "维度名称，如'商业机会'、'技术创新'等"
                            },
                            "description": {
                                "type": "string", 
                                "description": "维度的详细描述，解释该维度的含义和作用"
                            },
                            "keywords": {
                                "type": "array",
                                "description": "与该维度相关的关键字列表，支持中英文",
                                "items": {
                                    "type": "string",
                                    "description": "关键字"
                                }
                            }
                        },
                        "required": ["name", "description"],
                        "examples": [
                            {
                                "name": "商业机会",
                                "description": "与企业增长或盈利潜力相关的机会。",
                                "keywords": ["市场扩张", "新客户", "收入增长"]
                            }
                        ]
                    },
                    "language": "zh",
                    "extraction_config": {
                        "confidence_threshold": 0.1,
                        "timeout": 30,
                        "max_retries": 3,
                        "enable_fallback": True
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        # 使用通用响应处理方法
        result = self._handle_a2a_response(response, "商机维度提取", ["data", "confidence"])
        if result != "success":
            pytest.fail(f"商机维度提取测试失败: {result}")
    
    def test_bidding_key_info_extraction(self):
        """测试招标关键词信息提取"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 招标关键词信息提取测试 ===")
        
        # A2A JSON-RPC 格式请求
        payload = {
            "jsonrpc": "2.0",
            "id": "req_bidding_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "我想找德国2025年5月后发布的低平板挂车采购项目",
                    "target_schema": {
                        "name": "BiddingKeyInfo",
                        "description": "招标关键词信息模型，用于提取招标项目的关键信息",
                        "type": "object",
                        "properties": {
                            "subject": {
                                "type": "string",
                                "description": "标题或主要采购内容"
                            },
                            "country": {
                                "type": "string",
                                "description": "国家或地区"
                            },
                            "publication_date_from": {
                                "type": "string",
                                "format": "date-time",
                                "description": "发布日期起始时间"
                            },
                            "procurement_type": {
                                "type": "string",
                                "description": "采购类型，如物品采购、服务采购、工程采购"
                            }
                        },
                        "required": ["subject"],
                        "examples": [
                            {
                                "subject": "低平板挂车",
                                "country": "德国",
                                "publication_date_from": "2025-05-01T00:00:00Z",
                                "procurement_type": "物品采购"
                            }
                        ]
                    },
                    "language": "zh",
                    "extraction_config": {
                        "confidence_threshold": 0.1,
                        "timeout": 30,
                        "max_retries": 3,
                        "enable_fallback": True
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        # 使用通用响应处理方法
        result = self._handle_a2a_response(response, "招标信息提取", ["data", "confidence"])
        if result == "success":
            # 验证提取的数据内容
            data = response.json()
            extracted_data = data["result"]["output"]["data"]
            assert "低平板挂车" in extracted_data["subject"], "应正确提取采购标题（包含关键词）"
            assert extracted_data.get("country") == "德国", "应正确提取国家信息"
        elif result != "success":
            pytest.fail(f"招标信息提取测试失败: {result}")
    
    def test_query_conditions_extraction(self):
        """测试查询条件提取"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 查询条件提取测试 ===")
        
        # A2A JSON-RPC 格式请求
        payload = {
            "jsonrpc": "2.0",
            "id": "req_query_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "请你帮我查找中国的大于7月18号的新闻",
                    "target_schema": {
                        "name": "QueryConditionsList",
                        "description": "查询条件列表模型，用于解析用户的查询需求",
                        "type": "object",
                        "properties": {
                            "query_conditions_list": {
                                "type": "array",
                                "description": "查询条件列表",
                                "items": {
                                    "type": "object",
                                    "description": "查询条件项",
                                    "properties": {
                                        "query_fields": {
                                            "type": "string",
                                            "description": "需要查询的字段名",
                                            "enum": ["country", "update_at"]
                                        },
                                        "query_value": {
                                            "type": "string",
                                            "description": "查询字段的值"
                                        },
                                        "query_operate": {
                                            "type": "string",
                                            "description": "查询操作符",
                                            "enum": ["eq", "ne", "gt", "gte", "lt", "lte"]
                                        }
                                    },
                                    "required": ["query_fields", "query_value", "query_operate"]
                                }
                            }
                        },
                        "required": ["query_conditions_list"],
                        "examples": [
                            {
                                "query_conditions_list": [
                                    {
                                        "query_fields": "country",
                                        "query_value": "china",
                                        "query_operate": "eq"
                                    }
                                ]
                            }
                        ]
                    },
                    "language": "zh",
                    "extraction_config": {
                        "confidence_threshold": 0.1,
                        "timeout": 30,
                        "max_retries": 3,
                        "enable_fallback": True
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        # 使用通用响应处理方法
        result = self._handle_a2a_response(response, "查询条件提取", ["data", "confidence"])
        if result == "success":
            # 验证提取的数据内容
            data = response.json()
            conditions = data["result"]["output"]["data"]["query_conditions_list"]
            assert len(conditions) >= 1, "应提取到至少一个查询条件"
            # 验证是否包含中国和日期条件
            found_country = any(c.get("query_fields") == "country" for c in conditions)
            found_date = any(c.get("query_fields") == "update_at" for c in conditions)
            assert found_country or found_date, "应提取到国家或日期条件"
        elif result != "success":
            pytest.fail(f"查询条件提取测试失败: {result}")
    
    def test_direction_conditions_extraction(self):
        """测试分析角度操作提取"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 分析角度操作提取测试 ===")
        
        # A2A JSON-RPC 格式请求
        payload = {
            "jsonrpc": "2.0",
            "id": "req_direction_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "增加一条竞争对手分析，减少摘要分析",
                    "target_schema": {
                        "name": "DirectionConditionsList",
                        "description": "分析角度操作列表模型，用于解析用户对分析维度的操作需求",
                        "type": "object",
                        "properties": {
                            "conditions_list": {
                                "type": "array",
                                "description": "分析角度操作列表",
                                "items": {
                                    "type": "object",
                                    "description": "分析角度操作项",
                                    "properties": {
                                        "operator": {
                                            "type": "string",
                                            "enum": ["删除", "修改", "新增", "替换", "调整"],
                                            "description": "操作类型"
                                        },
                                        "name": {
                                            "type": "string",
                                            "description": "维度名称"
                                        },
                                        "description": {
                                            "type": "string",
                                            "description": "维度描述或修改说明"
                                        }
                                    },
                                    "required": ["operator", "name", "description"]
                                }
                            }
                        },
                        "required": ["conditions_list"],
                        "examples": [
                            {
                                "conditions_list": [
                                    {
                                        "operator": "新增",
                                        "name": "竞争对手分析",
                                        "description": "对主要竞争对手的市场表现、战略和优势进行分析。"
                                    }
                                ]
                            }
                        ]
                    },
                    "language": "zh",
                    "extraction_config": {
                        "confidence_threshold": 0.1,
                        "timeout": 30,
                        "max_retries": 3,
                        "enable_fallback": True
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        # 使用通用响应处理方法
        result = self._handle_a2a_response(response, "分析角度操作提取", ["data", "confidence"])
        if result == "success":
            # 验证提取的数据内容
            data = response.json()
            conditions = data["result"]["output"]["data"]["conditions_list"]
            assert len(conditions) >= 2, "应提取到至少两个操作（新增和删除）"
            # 验证包含新增和删除操作
            operators = [c.get("operator") for c in conditions]
            assert "新增" in operators, "应包含新增操作"
            assert "删除" in operators or "减少" in str(operators), "应包含删除或减少操作"
        elif result != "success":
            pytest.fail(f"分析角度操作提取测试失败: {result}")
    
    def test_complex_nested_structure(self):
        """测试复杂嵌套结构提取"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 复杂嵌套结构提取测试 ===")
        
        # A2A JSON-RPC 格式请求
        payload = {
            "jsonrpc": "2.0",
            "id": "req_complex_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "用户张三，28岁男性，邮箱zhang<EMAIL>，住址北京市朝阳区，在科技公司担任软件工程师，擅长Python和Java",
                    "target_schema": {
                        "name": "UserProfile",
                        "description": "用户档案模型",
                        "type": "object",
                        "properties": {
                            "user": {
                                "type": "object",
                                "description": "用户信息",
                                "properties": {
                                    "personal": {
                                        "type": "object",
                                        "description": "个人信息",
                                        "properties": {
                                            "name": {"type": "string", "description": "姓名"},
                                            "age": {"type": "integer", "description": "年龄"},
                                            "email": {"type": "string", "description": "邮箱地址"}
                                        },
                                        "required": ["name"]
                                    },
                                    "work": {
                                        "type": "object", 
                                        "description": "工作信息",
                                        "properties": {
                                            "company": {"type": "string", "description": "公司名称"},
                                            "position": {"type": "string", "description": "职位"},
                                            "skills": {
                                                "type": "array",
                                                "description": "技能列表",
                                                "items": {
                                                    "type": "string",
                                                    "description": "技能名称"
                                                }
                                            }
                                        }
                                    }
                                },
                                "required": ["personal"]
                            }
                        },
                        "required": ["user"],
                        "examples": [
                            {
                                "user": {
                                    "personal": {
                                        "name": "张三",
                                        "age": 28,
                                        "email": "<EMAIL>"
                                    },
                                    "work": {
                                        "company": "科技公司",
                                        "position": "软件工程师",
                                        "skills": ["Python", "Java"]
                                    }
                                }
                            }
                        ]
                    },
                    "language": "zh",
                    "extraction_config": {
                        "confidence_threshold": 0.1,
                        "timeout": 30,
                        "max_retries": 3,
                        "enable_fallback": True
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        # 使用通用响应处理方法
        result = self._handle_a2a_response(response, "复杂嵌套结构提取", ["data", "confidence"])
        if result == "success":
            # 验证提取的数据内容 - 三层嵌套结构
            data = response.json()
            extracted_data = data["result"]["output"]["data"]
            assert "user" in extracted_data, "应包含user对象"
            
            user = extracted_data["user"]
            assert "personal" in user, "应包含personal对象"
            
            personal = user["personal"]
            assert "name" in personal, "应包含姓名"
            assert personal["name"] == "张三", "应正确提取姓名"
            
            if "age" in personal:
                assert personal["age"] == 28, "应正确提取年龄"
            
            if "work" in user:
                work = user["work"]
                if "company" in work:
                    assert "科技" in work["company"], "应正确提取公司信息"
                if "skills" in work:
                    assert isinstance(work["skills"], list), "技能应为列表"
        elif result != "success":
            pytest.fail(f"复杂嵌套结构提取测试失败: {result}")
    
    def test_error_handling(self):
        """测试错误处理"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 错误处理测试 ===")
        
        # 测试无效Schema - A2A JSON-RPC 格式请求
        payload = {
            "jsonrpc": "2.0",
            "id": "req_error_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "测试文本",
                    "target_schema": {
                        "name": "",  # 空名称，应该导致验证失败
                        # 缺少description字段，应该导致验证失败
                        "type": "object",
                        "properties": {}
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        # 使用通用响应处理方法验证错误处理
        result = self._handle_a2a_response(response, "错误处理测试", [])
        if result == "failed":
            # 期望的失败响应，错误处理正常
            print("错误处理测试通过：正确检测到无效Schema")
        elif result == "error":
            # A2A协议级别错误也是可接受的
            print("错误处理测试通过：A2A协议级别错误处理正常")
        else:
            print(f"警告: 预期应该失败的测试却返回了: {result}")
    
    def test_token_consumption_verification(self):
        """测试实体提取的token消耗记录"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 实体提取Token消耗验证测试 ===")
        
        # 1. 执行一个实体提取请求 - A2A JSON-RPC 格式
        payload = {
            "jsonrpc": "2.0",
            "id": "req_token_001",
            "method": "agent/execute",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "input_text": "苹果公司在2024年发布了新产品iPhone 15 Pro，价格999美元",
                    "target_schema": {
                        "name": "ProductInfo",
                        "description": "产品信息模型",
                        "type": "object",
                        "properties": {
                            "company": {"type": "string", "description": "公司名称"},
                            "year": {"type": "integer", "description": "发布年份"},
                            "product": {"type": "string", "description": "产品名称"},
                            "price": {"type": "number", "description": "价格"},
                            "currency": {"type": "string", "description": "货币"}
                        },
                        "required": ["company", "product"]
                    },
                    "language": "zh",
                    "extraction_config": {
                        "confidence_threshold": 0.1,
                        "timeout": 30,
                        "max_retries": 3,
                        "enable_fallback": True
                    }
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': TEST_USER_ID,
            'X-Company-ID': TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/execute/entity-extraction-agent-001"
        
        print(f"请求URL: {url}")
        print(f"请求头: {json.dumps(headers, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        # 使用通用响应处理方法
        result = self._handle_a2a_response(response, "Token消耗验证", ["data", "confidence"])
        if result == "success":
            # 检查响应中的token信息，参考simple_token_test.py的处理方式
            data = response.json()
            result_data = data["result"]
            
            # 检查任务级别的metadata（simple_token_test.py使用的路径）
            task_metadata = result_data.get("metadata", {})
            tokens_used = task_metadata.get("tokens_used", 0)
            
            # 检查output中的metadata（Agent实现的路径）
            output = result_data.get("output", {})
            output_metadata = output.get("metadata", {})
            token_usage = output_metadata.get("token_usage", {})
            
            print(f"任务级别token使用: {tokens_used}")
            if token_usage:
                print(f"输出级别Token信息: input={token_usage.get('input_tokens', 0)}, "
                      f"output={token_usage.get('output_tokens', 0)}, total={token_usage.get('total_tokens', 0)}")
            
            if tokens_used > 0 or token_usage.get('total_tokens', 0) > 0:
                print("实体提取Agent返回了token使用信息")
            else:
                print("注意: 实体提取Agent返回的token使用量为0")
        else:
            print(f"实体提取请求结果: {result}")
            if response.status_code not in [404, 501]:
                print(f"实体提取请求状态: {response.status_code} - {response.text}")
        
        # 3. 等待一下让token记录完成
        time.sleep(3)
        
        # 4. 检查Token消耗统计
        token_url = f"{base_url}/v1/tokens/stats/user/{TEST_USER_ID}"
        token_response = requests.get(token_url, timeout=30)
        
        print(f"Token统计API状态码: {token_response.status_code}")
        
        if token_response.status_code == 200:
            token_data = token_response.json()
            print(f"Token统计API响应: {json.dumps(token_data, ensure_ascii=False, indent=2)}")
            
            # 验证基本字段
            assert token_data["user_id"] == TEST_USER_ID
            assert token_data["company_id"] == TEST_COMPANY_ID
            
            print("实体提取Agent的token消耗记录功能测试完成!")
        else:
            print(f"Token统计API失败: {token_response.text}")
            print("Token统计服务可能暂时不可用，但实体提取功能正常")

def test_entity_extraction_integration():
    """集成测试：完整的实体提取Agent功能"""
    print("=== 实体提取Agent集成测试 ===")
    
    test_suite = TestEntityExtractionAgent()
    test_results = {}
    
    # 定义测试列表
    tests = [
        ("商机维度提取", test_suite.test_opportunity_dimension_extraction),
        ("招标信息提取", test_suite.test_bidding_key_info_extraction),
        ("查询条件提取", test_suite.test_query_conditions_extraction),
        ("分析角度操作提取", test_suite.test_direction_conditions_extraction),
        ("复杂嵌套结构提取", test_suite.test_complex_nested_structure),
        ("错误处理", test_suite.test_error_handling),
        ("Token消耗验证", test_suite.test_token_consumption_verification)
    ]
    
    # 执行每个测试，记录结果但不因单个失败而停止
    for test_name, test_func in tests:
        try:
            test_func()
            test_results[test_name] = "PASSED"
            print(f"✓ {test_name}测试通过")
        except Exception as e:
            test_results[test_name] = f"FAILED: {str(e)}"
            print(f"✗ {test_name}测试失败: {str(e)}")
        
        time.sleep(1)
    
    # 打印测试总结
    print("\n=== 实体提取Agent集成测试总结 ===")
    passed_count = sum(1 for result in test_results.values() if result == "PASSED")
    total_count = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✓" if result == "PASSED" else "✗"
        print(f"{status} {test_name}: {result}")
    
    print(f"\n测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    # 只有当所有测试都失败时才认为集成测试失败
    if passed_count == 0:
        pytest.fail("所有实体提取测试都失败了")
    
    print("实体提取Agent集成测试完成!")

if __name__ == "__main__":
    test_entity_extraction_integration()
    print("所有实体提取Agent测试完成!")