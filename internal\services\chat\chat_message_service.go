package chat

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ChatMessageService 聊天消息服务
type ChatMessageService struct {
	store  store.DBTX
	config *models.ChatConfig
}

// NewChatMessageService 创建聊天消息服务
func NewChatMessageService(store store.DBTX, config *models.ChatConfig) *ChatMessageService {
	return &ChatMessageService{
		store:  store,
		config: config,
	}
}

// SaveMessage 保存聊天消息
func (s *ChatMessageService) SaveMessage(ctx context.Context, sessionID, userID string, role models.MessageRole, content string, tokenCount int, metadata map[string]interface{}) (*models.ChatMessage, error) {
	messageID := uuid.New().String()
	now := time.Now()

	// 序列化metadata
	var metadataJSON []byte
	var err error
	if metadata != nil {
		metadataJSON, err = json.Marshal(metadata)
		if err != nil {
			utils.Log.Errorf("Failed to marshal metadata: %v", err)
			return nil, fmt.Errorf("failed to marshal metadata: %w", err)
		}
	}

	message := &models.ChatMessage{
		MessageID:  messageID,
		SessionID:  sessionID,
		UserID:     userID,
		Role:       role,
		Content:    content,
		TokenCount: tokenCount,
		Metadata:   metadata,
		CreatedAt:  now,
	}

	query := `
		INSERT INTO chat_messages (message_id, session_id, user_id, role, content, token_count, metadata, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id`

	err = s.store.QueryRowContext(ctx, query,
		message.MessageID,
		message.SessionID,
		message.UserID,
		message.Role,
		message.Content,
		message.TokenCount,
		metadataJSON,
		message.CreatedAt,
	).Scan(&message.ID)

	if err != nil {
		utils.Log.Errorf("Failed to save chat message: %v", err)
		return nil, fmt.Errorf("failed to save chat message: %w", err)
	}

	utils.Log.Debugf("Saved chat message: %s in session: %s", messageID, sessionID)
	return message, nil
}

// GetMessage 获取单个消息
func (s *ChatMessageService) GetMessage(ctx context.Context, messageID string) (*models.ChatMessage, error) {
	query := `
		SELECT id, message_id, session_id, user_id, role, content, token_count, metadata, created_at
		FROM chat_messages 
		WHERE message_id = $1`

	var message models.ChatMessage
	var metadataJSON []byte

	err := s.store.QueryRowContext(ctx, query, messageID).Scan(
		&message.ID,
		&message.MessageID,
		&message.SessionID,
		&message.UserID,
		&message.Role,
		&message.Content,
		&message.TokenCount,
		&metadataJSON,
		&message.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("message not found: %s", messageID)
		}
		utils.Log.Errorf("Failed to get chat message: %v", err)
		return nil, fmt.Errorf("failed to get chat message: %w", err)
	}

	// 反序列化metadata
	if metadataJSON != nil {
		err = json.Unmarshal(metadataJSON, &message.Metadata)
		if err != nil {
			utils.Log.Errorf("Failed to unmarshal metadata: %v", err)
			message.Metadata = nil
		}
	}

	return &message, nil
}

// GetSessionMessages 获取会话的消息历史
func (s *ChatMessageService) GetSessionMessages(ctx context.Context, sessionID string, limit, offset int) ([]models.ChatMessage, error) {
	if limit <= 0 {
		limit = 50 // 默认限制
	}

	query := `
		SELECT id, message_id, session_id, user_id, role, content, token_count, metadata, created_at
		FROM chat_messages 
		WHERE session_id = $1
		ORDER BY created_at ASC
		LIMIT $2 OFFSET $3`

	rows, err := s.store.QueryContext(ctx, query, sessionID, limit, offset)
	if err != nil {
		utils.Log.Errorf("Failed to get session messages: %v", err)
		return nil, fmt.Errorf("failed to get session messages: %w", err)
	}
	defer rows.Close()

	var messages []models.ChatMessage
	for rows.Next() {
		var message models.ChatMessage
		var metadataJSON []byte

		err := rows.Scan(
			&message.ID,
			&message.MessageID,
			&message.SessionID,
			&message.UserID,
			&message.Role,
			&message.Content,
			&message.TokenCount,
			&metadataJSON,
			&message.CreatedAt,
		)
		if err != nil {
			utils.Log.Errorf("Failed to scan message: %v", err)
			continue
		}

		// 反序列化metadata
		if metadataJSON != nil {
			err = json.Unmarshal(metadataJSON, &message.Metadata)
			if err != nil {
				utils.Log.Errorf("Failed to unmarshal metadata: %v", err)
				message.Metadata = nil
			}
		}

		messages = append(messages, message)
	}

	return messages, nil
}

// GetSessionMessageCount 获取会话消息总数
func (s *ChatMessageService) GetSessionMessageCount(ctx context.Context, sessionID string) (int, error) {
	query := `SELECT COUNT(*) FROM chat_messages WHERE session_id = $1`

	var count int
	err := s.store.QueryRowContext(ctx, query, sessionID).Scan(&count)
	if err != nil {
		utils.Log.Errorf("Failed to get session message count: %v", err)
		return 0, fmt.Errorf("failed to get session message count: %w", err)
	}

	return count, nil
}

// GetSessionTokenCount 获取会话当前token计数
func (s *ChatMessageService) GetSessionTokenCount(ctx context.Context, sessionID string) (int, error) {
	query := `SELECT COALESCE(SUM(token_count), 0) FROM chat_messages WHERE session_id = $1`

	var totalTokens int
	err := s.store.QueryRowContext(ctx, query, sessionID).Scan(&totalTokens)
	if err != nil {
		utils.Log.Errorf("Failed to get session token count: %v", err)
		return 0, fmt.Errorf("failed to get session token count: %w", err)
	}

	return totalTokens, nil
}

// TrimSessionContext 修剪会话上下文以保持在token限制内
func (s *ChatMessageService) TrimSessionContext(ctx context.Context, sessionID string, tokenLimit int) error {
	// 获取当前总token数
	totalTokens, err := s.GetSessionTokenCount(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("failed to get current token count: %w", err)
	}

	if totalTokens <= tokenLimit {
		return nil // 不需要修剪
	}

	// 保留最近的消息，删除最旧的消息直到符合限制
	// 注意：系统消息通常应该保留
	deleteQuery := `
		DELETE FROM chat_messages 
		WHERE session_id = $1 
		AND role != 'system'
		AND id IN (
			SELECT id FROM chat_messages 
			WHERE session_id = $1 
			AND role != 'system'
			ORDER BY created_at ASC
			LIMIT (
				SELECT COUNT(*) - (
					SELECT COUNT(*) FROM (
						SELECT id, SUM(token_count) OVER (ORDER BY created_at DESC) as running_total
						FROM chat_messages 
						WHERE session_id = $1
						ORDER BY created_at DESC
					) sub WHERE running_total <= $2
				)
				FROM chat_messages 
				WHERE session_id = $1 
				AND role != 'system'
			)
		)`

	result, err := s.store.ExecContext(ctx, deleteQuery, sessionID, tokenLimit)
	if err != nil {
		utils.Log.Errorf("Failed to trim session context: %v", err)
		return fmt.Errorf("failed to trim session context: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected > 0 {
		utils.Log.Infof("Trimmed %d messages from session %s to maintain token limit", rowsAffected, sessionID)
	}

	return nil
}

// DeleteMessage 删除消息
func (s *ChatMessageService) DeleteMessage(ctx context.Context, messageID string) error {
	query := `DELETE FROM chat_messages WHERE message_id = $1`

	result, err := s.store.ExecContext(ctx, query, messageID)
	if err != nil {
		utils.Log.Errorf("Failed to delete message: %v", err)
		return fmt.Errorf("failed to delete message: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("message not found: %s", messageID)
	}

	utils.Log.Debugf("Deleted message: %s", messageID)
	return nil
}

// DeleteSessionMessages 删除会话的所有消息
func (s *ChatMessageService) DeleteSessionMessages(ctx context.Context, sessionID string) error {
	query := `DELETE FROM chat_messages WHERE session_id = $1`

	result, err := s.store.ExecContext(ctx, query, sessionID)
	if err != nil {
		utils.Log.Errorf("Failed to delete session messages: %v", err)
		return fmt.Errorf("failed to delete session messages: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	utils.Log.Debugf("Deleted %d messages from session: %s", rowsAffected, sessionID)
	return nil
}

// GetRecentMessages 获取最近的消息（跨会话）
func (s *ChatMessageService) GetRecentMessages(ctx context.Context, userID string, hours int, limit int) ([]models.ChatMessage, error) {
	if limit <= 0 {
		limit = 100
	}
	if hours <= 0 {
		hours = 24
	}

	query := `
		SELECT id, message_id, session_id, user_id, role, content, token_count, metadata, created_at
		FROM chat_messages 
		WHERE user_id = $1 
		AND created_at >= (CURRENT_TIMESTAMP - INTERVAL '%d hours')
		ORDER BY created_at DESC
		LIMIT $2`

	rows, err := s.store.QueryContext(ctx, fmt.Sprintf(query, hours), userID, limit)
	if err != nil {
		utils.Log.Errorf("Failed to get recent messages: %v", err)
		return nil, fmt.Errorf("failed to get recent messages: %w", err)
	}
	defer rows.Close()

	var messages []models.ChatMessage
	for rows.Next() {
		var message models.ChatMessage
		var metadataJSON []byte

		err := rows.Scan(
			&message.ID,
			&message.MessageID,
			&message.SessionID,
			&message.UserID,
			&message.Role,
			&message.Content,
			&message.TokenCount,
			&metadataJSON,
			&message.CreatedAt,
		)
		if err != nil {
			utils.Log.Errorf("Failed to scan message: %v", err)
			continue
		}

		// 反序列化metadata
		if metadataJSON != nil {
			err = json.Unmarshal(metadataJSON, &message.Metadata)
			if err != nil {
				utils.Log.Errorf("Failed to unmarshal metadata: %v", err)
				message.Metadata = nil
			}
		}

		messages = append(messages, message)
	}

	return messages, nil
}
