package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// AgentHandler Agent处理器
type AgentHandler struct {
	agentManager core.AgentManager
	logger       *logrus.Logger
}

// NewAgentHandler 创建Agent处理器
func NewAgentHandler(agentManager core.AgentManager) *AgentHandler {
	return &AgentHandler{
		agentManager: agentManager,
		logger:       utils.Log, // 使用统一的logger
	}
}

// ExecuteAgent 执行Agent
func (ah *AgentHandler) ExecuteAgent(c *gin.Context) {
	var req models.AgentExecutionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ah.logger.WithError(err).Error("解析Agent执行请求失败")
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"code":    "INVALID_REQUEST",
			"message": "请求格式错误: " + err.Error(),
		})
		return
	}

	// 执行Agent
	response, err := ah.agentManager.ExecuteAgent(c.Request.Context(), &req)
	if err != nil {
		ah.logger.WithError(err).WithFields(logrus.Fields{
			"agent_id":   req.AgentID,
			"capability": req.Capability,
		}).Error("执行Agent失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    "EXECUTION_FAILED",
			"message": "执行Agent失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ListAgents 获取Agent列表
func (ah *AgentHandler) ListAgents(c *gin.Context) {
	var req models.AgentListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ah.logger.WithError(err).Error("解析Agent列表请求失败")
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_REQUEST",
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取Agent列表
	response, err := ah.agentManager.ListAgents(c.Request.Context(), req)
	if err != nil {
		ah.logger.WithError(err).Error("获取Agent列表失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    "LIST_FAILED",
			"message": "获取Agent列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetAgent 获取Agent详情
func (ah *AgentHandler) GetAgent(c *gin.Context) {
	agentID := c.Param("agent_id")
	if agentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_AGENT_ID",
			"message": "Agent ID不能为空",
		})
		return
	}

	// 获取Agent实例
	agent, err := ah.agentManager.GetAgent(c.Request.Context(), agentID)
	if err != nil {
		ah.logger.WithError(err).WithField("agent_id", agentID).Error("获取Agent失败")
		c.JSON(http.StatusNotFound, gin.H{
			"code":    "AGENT_NOT_FOUND",
			"message": "Agent不存在: " + err.Error(),
		})
		return
	}

	// 构建Agent详情响应
	agentDetails := gin.H{
		"id":      agent.GetID(),
		"card_id": agent.GetID(),
		"status":  "idle",
		"config":  make(map[string]interface{}),
	}

	c.JSON(http.StatusOK, agentDetails)
}

// GetAgentCapabilities 获取Agent能力列表
func (ah *AgentHandler) GetAgentCapabilities(c *gin.Context) {
	agentID := c.Param("agent_id")
	if agentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_AGENT_ID",
			"message": "Agent ID不能为空",
		})
		return
	}

	// 获取Agent实例
	agent, err := ah.agentManager.GetAgent(c.Request.Context(), agentID)
	if err != nil {
		ah.logger.WithError(err).WithField("agent_id", agentID).Error("获取Agent失败")
		c.JSON(http.StatusNotFound, gin.H{
			"code":    "AGENT_NOT_FOUND",
			"message": "Agent不存在: " + err.Error(),
		})
		return
	}

	// 获取Agent能力
	capabilities := agent.GetCapabilities()
	c.JSON(http.StatusOK, capabilities)
}

// GetAgentMetrics 获取Agent指标
func (ah *AgentHandler) GetAgentMetrics(c *gin.Context) {
	agentID := c.Param("agent_id")
	if agentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_AGENT_ID",
			"message": "Agent ID不能为空",
		})
		return
	}

	// 获取Agent指标
	metrics, err := ah.agentManager.GetAgentMetrics(c.Request.Context(), agentID)
	if err != nil {
		ah.logger.WithError(err).WithField("agent_id", agentID).Error("获取Agent指标失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    "METRICS_FAILED",
			"message": "获取Agent指标失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetAgentExecutionHistory 获取Agent执行历史
func (ah *AgentHandler) GetAgentExecutionHistory(c *gin.Context) {
	agentID := c.Param("agent_id")
	if agentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    "INVALID_AGENT_ID",
			"message": "Agent ID不能为空",
		})
		return
	}

	// 解析查询参数
	limit := 20
	offset := 0
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	_ = limit
	_ = offset

	// 这里应该调用monitor服务获取执行历史
	// 简化实现，返回空列表
	executions := []gin.H{}
	c.JSON(http.StatusOK, executions)
}

// RegisterRoutes 注册路由
func (ah *AgentHandler) RegisterRoutes(router *gin.RouterGroup) {
	agentRoutes := router.Group("/agents")
	{
		agentRoutes.POST("/execute", ah.ExecuteAgent)
		agentRoutes.GET("", ah.ListAgents)
		agentRoutes.GET("/:agent_id", ah.GetAgent)
		agentRoutes.GET("/:agent_id/capabilities", ah.GetAgentCapabilities)
		agentRoutes.GET("/:agent_id/metrics", ah.GetAgentMetrics)
		agentRoutes.GET("/:agent_id/executions", ah.GetAgentExecutionHistory)
	}
}
