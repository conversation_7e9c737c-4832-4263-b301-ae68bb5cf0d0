package prompts

import (
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v2"
)

// TenderPreprocessPromptManager 招投标预处理prompt管理器
type TenderPreprocessPromptManager struct {
	prompts map[string]*TenderPreprocessPromptTemplate
	baseDir string
}

// TenderPreprocessPromptTemplate 招投标预处理prompt模板
type TenderPreprocessPromptTemplate struct {
	Name               string              `yaml:"name"`
	Description        string              `yaml:"description"`
	Language           string              `yaml:"language"`
	Version            string              `yaml:"version"`
	SystemPrompt       string              `yaml:"system_prompt"`
	UserPromptTemplate string              `yaml:"user_prompt_template"`
	Variables          map[string]Variable `yaml:"variables"`
}

// NewTenderPreprocessPromptManager 创建招投标预处理prompt管理器
func NewTenderPreprocessPromptManager(baseDir string) (*TenderPreprocessPromptManager, error) {
	if baseDir == "" {
		baseDir = "internal/prompts/tender_preprocess"
	}

	pm := &TenderPreprocessPromptManager{
		prompts: make(map[string]*TenderPreprocessPromptTemplate),
		baseDir: baseDir,
	}

	// 加载所有prompt文件
	if err := pm.loadPrompts(); err != nil {
		return nil, fmt.Errorf("failed to load prompts: %v", err)
	}

	return pm, nil
}

// loadPrompts 加载prompt文件
func (pm *TenderPreprocessPromptManager) loadPrompts() error {
	// 直接扫描tender_preprocess目录下的所有yaml文件，不再使用子目录
	files, err := ioutil.ReadDir(pm.baseDir)
	if err != nil {
		return fmt.Errorf("failed to read directory %s: %v", pm.baseDir, err)
	}

	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), ".yaml") {
			continue
		}

		filePath := filepath.Join(pm.baseDir, file.Name())
		if err := pm.loadPromptFile(filePath); err != nil {
			log.Printf("Failed to load prompt file %s: %v", filePath, err)
			continue
		}
	}

	log.Printf("Loaded %d tender preprocess prompt templates", len(pm.prompts))
	return nil
}

// loadPromptFile 加载单个prompt文件
func (pm *TenderPreprocessPromptManager) loadPromptFile(filePath string) error {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %v", filePath, err)
	}

	var template TenderPreprocessPromptTemplate
	if err := yaml.Unmarshal(data, &template); err != nil {
		return fmt.Errorf("failed to parse YAML file %s: %v", filePath, err)
	}

	// 验证模板
	if template.Name == "" || template.SystemPrompt == "" {
		return fmt.Errorf("invalid template in file %s: missing name or system_prompt", filePath)
	}

	pm.prompts[template.Name] = &template
	log.Printf("Loaded tender preprocess prompt template: %s (%s)", template.Name, template.Language)

	return nil
}

// GetPromptTemplate 获取prompt模板
func (pm *TenderPreprocessPromptManager) GetPromptTemplate(name, language string) (*TenderPreprocessPromptTemplate, error) {
	// 构建模板名称：name_language
	templateName := fmt.Sprintf("%s_%s", name, language)

	template, exists := pm.prompts[templateName]
	if !exists {
		// 如果找不到指定语言的模板，尝试查找默认中文模板
		fallbackName := fmt.Sprintf("%s_zh", name)
		if template, exists = pm.prompts[fallbackName]; !exists {
			return nil, fmt.Errorf("tender preprocess prompt template not found: %s (language: %s)", name, language)
		}
		log.Printf("Using fallback template %s for requested %s", fallbackName, templateName)
	}

	return template, nil
}

// RenderPrompt 渲染prompt
func (pm *TenderPreprocessPromptManager) RenderPrompt(template *TenderPreprocessPromptTemplate, variables map[string]interface{}) (string, string, error) {
	// 验证必需变量
	for varName, varDef := range template.Variables {
		if varDef.Required {
			if _, exists := variables[varName]; !exists {
				return "", "", fmt.Errorf("required variable missing: %s", varName)
			}
		}
	}

	// 设置默认值
	for varName, varDef := range template.Variables {
		if _, exists := variables[varName]; !exists && varDef.Default != nil {
			variables[varName] = varDef.Default
		}
	}

	// 渲染系统prompt
	systemPrompt, err := pm.renderTemplate(template.SystemPrompt, variables)
	if err != nil {
		return "", "", fmt.Errorf("failed to render system prompt: %v", err)
	}

	// 渲染用户prompt
	userPrompt := ""
	if template.UserPromptTemplate != "" {
		userPrompt, err = pm.renderTemplate(template.UserPromptTemplate, variables)
		if err != nil {
			return "", "", fmt.Errorf("failed to render user prompt: %v", err)
		}
	}

	return systemPrompt, userPrompt, nil
}

// renderTemplate 渲染模板字符串
func (pm *TenderPreprocessPromptManager) renderTemplate(templateStr string, variables map[string]interface{}) (string, error) {
	// 简单的变量替换实现
	result := templateStr
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		valueStr := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, valueStr)
	}

	// 处理条件语句 {{#if variable}}...{{/if}}
	result = pm.processConditionals(result, variables)

	return result, nil
}

// processConditionals 处理条件语句
func (pm *TenderPreprocessPromptManager) processConditionals(template string, variables map[string]interface{}) string {
	// 简化的条件处理，支持 {{#if variable}}content{{/if}}
	result := template

	// 这是一个简化实现，实际项目中可能需要更复杂的模板引擎
	for varName, value := range variables {
		ifStart := fmt.Sprintf("{{#if %s}}", varName)
		ifEnd := "{{/if}}"

		for {
			startIdx := strings.Index(result, ifStart)
			if startIdx == -1 {
				break
			}

			endIdx := strings.Index(result[startIdx:], ifEnd)
			if endIdx == -1 {
				break
			}
			endIdx += startIdx

			// 提取条件内容
			content := result[startIdx+len(ifStart) : endIdx]

			// 判断变量是否为真
			replacement := ""
			if pm.isTruthy(value) {
				replacement = content
			}

			// 替换整个条件块
			result = result[:startIdx] + replacement + result[endIdx+len(ifEnd):]
		}
	}

	return result
}

// isTruthy 判断值是否为真
func (pm *TenderPreprocessPromptManager) isTruthy(value interface{}) bool {
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case string:
		return v != ""
	case int, int64, float64:
		return v != 0
	case []interface{}:
		return len(v) > 0
	case map[string]interface{}:
		return len(v) > 0
	default:
		return true
	}
}

// ListTemplates 列出所有可用模板
func (pm *TenderPreprocessPromptManager) ListTemplates() []string {
	var names []string
	for name := range pm.prompts {
		names = append(names, name)
	}
	return names
}

// GetAvailableModules 获取可用的预处理模块类型
func (pm *TenderPreprocessPromptManager) GetAvailableModules() map[string][]string {
	modules := make(map[string][]string)

	for templateName := range pm.prompts {
		// 从模板名称中提取模块类型（例如：classification_zh -> classification）
		parts := strings.Split(templateName, "_")
		if len(parts) >= 2 {
			moduleType := parts[0]
			if strings.Contains(templateName, "extraction") {
				if strings.Contains(templateName, "basic") {
					moduleType = "extraction_basic"
				} else if strings.Contains(templateName, "data") {
					moduleType = "extraction_data"
				}
			}

			language := parts[len(parts)-1]
			if modules[moduleType] == nil {
				modules[moduleType] = make([]string, 0)
			}

			// 避免重复语言
			found := false
			for _, lang := range modules[moduleType] {
				if lang == language {
					found = true
					break
				}
			}
			if !found {
				modules[moduleType] = append(modules[moduleType], language)
			}
		}
	}

	return modules
}
