# Agent API 参考文档

## 📋 概述

TaskD Agent模块提供了完整的RESTful API接口，支持Agent的注册、管理、执行和监控。本文档详细描述了所有可用的API接口、参数、响应格式和使用示例。

## 🌐 API基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json`
- **Authentication**: API Key (可选)
- **Rate Limiting**: 1000 requests/minute per IP

## 📚 API接口列表

### 1. Agent管理接口

#### 1.1 获取Agent列表

**接口描述**: 获取所有注册的Agent列表

```http
GET /api/v1/agents
```

**查询参数**:
- `type` (string, optional): Agent类型过滤 (`bidding`, `summary`, `analysis`, `search`, `custom`)
- `status` (string, optional): Agent状态过滤 (`idle`, `processing`, `error`, `disabled`)
- `page` (int, optional): 页码，默认1
- `limit` (int, optional): 每页数量，默认20，最大100
- `keyword` (string, optional): 关键词搜索

**响应示例**:
```json
{
  "total": 1,
  "page": 1,
  "limit": 20,
  "agents": [
    {
      "id": "bidding-agent-001",
      "card_id": "bidding-agent-001",
      "status": "idle",
      "config": {},
      "started_at": "2024-07-10T10:00:00Z",
      "last_ping": "2024-07-10T10:30:00Z"
    }
  ]
}
```

**错误响应**:
```json
{
  "code": "LIST_FAILED",
  "message": "获取Agent列表失败: 数据库连接错误"
}
```

#### 1.2 获取Agent详情

**接口描述**: 获取指定Agent的详细信息

```http
GET /api/v1/agents/{agent_id}
```

**路径参数**:
- `agent_id` (string, required): Agent唯一标识符

**响应示例**:
```json
{
  "id": "bidding-agent-001",
  "card_id": "bidding-agent-001",
  "status": "idle",
  "config": {
    "model": "deepseek-v3-250324",
    "temperature": 0.1,
    "max_tokens": 2000
  },
  "started_at": "2024-07-10T10:00:00Z",
  "last_ping": "2024-07-10T10:30:00Z"
}
```

#### 1.3 获取Agent能力列表

**接口描述**: 获取指定Agent支持的能力列表

```http
GET /api/v1/agents/{agent_id}/capabilities
```

**响应示例**:
```json
[
  {
    "name": "generate_summary",
    "description": "生成招投标项目摘要",
    "input_schema": {
      "type": "object",
      "properties": {
        "tender_data": {
          "type": "object",
          "description": "招投标原始数据"
        }
      },
      "required": ["tender_data"]
    },
    "output_schema": {
      "type": "object",
      "properties": {
        "project_overview": {
          "type": "string",
          "description": "项目概述"
        },
        "main_requirements": {
          "type": "string",
          "description": "主要需求"
        }
      }
    },
    "required": ["tender_data"]
  }
]
```

### 2. Agent执行接口

#### 2.1 执行Agent能力

**接口描述**: 执行指定Agent的指定能力

```http
POST /api/v1/agents/execute
```

**请求体**:
```json
{
  "agent_id": "bidding-agent-001",
  "capability": "generate_summary",
  "input": {
    "tender_data": {
      "project_name": "某市政府采购IT设备项目",
      "budget": "500万元",
      "deadline": "2024-12-31",
      "description": "采购服务器、网络设备等IT基础设施",
      "requirements": "需要具备相关资质，有类似项目经验"
    }
  },
  "config": {
    "model": "deepseek-v3-250324",
    "temperature": 0.1,
    "max_tokens": 2000
  },
  "priority": 1,
  "timeout": 300
}
```

**请求参数**:
- `agent_id` (string, required): Agent ID
- `capability` (string, required): 能力名称
- `input` (object, required): 输入数据
- `config` (object, optional): 执行配置
- `priority` (int, optional): 优先级 (1-10)
- `timeout` (int, optional): 超时时间(秒)

**响应示例**:
```json
{
  "id": "resp_1704888000123",
  "request_id": "req_1704888000123",
  "success": true,
  "output": {
    "project_overview": "该项目是某市政府主导的IT基础设施采购项目，预算500万元，截止2024年12月31日。",
    "main_requirements": "采购高性能服务器、企业级网络设备和分布式存储系统，要求供应商具备相关资质和项目经验。",
    "technical_requirements": "10台高性能服务器、企业级网络设备、分布式存储系统",
    "contract_info": "政府采购合同，需要相关资质证明",
    "deadline": "2024-12-31",
    "budget": "500万元"
  },
  "duration": 1250,
  "created_at": "2024-07-10T10:30:00Z"
}
```

**错误响应**:
```json
{
  "id": "resp_1704888000124",
  "request_id": "req_1704888000124",
  "success": false,
  "error": "输入验证失败: 必需参数 'tender_data' 缺失",
  "duration": 10,
  "created_at": "2024-07-10T10:30:00Z"
}
```

### 3. 监控和健康检查接口

#### 3.1 Agent健康检查

**接口描述**: 检查指定Agent的健康状态

```http
GET /api/v1/agents/{agent_id}/health
```

**响应示例**:
```json
{
  "agent_id": "bidding-agent-001",
  "status": "idle",
  "healthy": true,
  "message": "Agent is healthy",
  "checked_at": "2024-07-10T10:30:00Z"
}
```

#### 3.2 所有Agent健康检查

**接口描述**: 检查所有Agent的健康状态

```http
GET /api/v1/agents/health
```

**响应示例**:
```json
[
  {
    "agent_id": "bidding-agent-001",
    "status": "idle",
    "healthy": true,
    "message": "Agent is healthy",
    "checked_at": "2024-07-10T10:30:00Z"
  }
]
```

#### 3.3 获取Agent指标

**接口描述**: 获取指定Agent的性能指标

```http
GET /api/v1/agents/{agent_id}/metrics
```

**响应示例**:
```json
{
  "agent_id": "bidding-agent-001",
  "total_requests": 150,
  "success_requests": 145,
  "error_requests": 5,
  "success_rate": 96.67,
  "avg_duration": 1250.5,
  "total_tokens": 125000,
  "last_execution": "2024-07-10T10:25:00Z"
}
```

#### 3.4 获取Agent执行历史

**接口描述**: 获取指定Agent的执行历史记录

```http
GET /api/v1/agents/{agent_id}/executions?limit=20&offset=0
```

**查询参数**:
- `limit` (int, optional): 记录数量，默认20
- `offset` (int, optional): 偏移量，默认0

**响应示例**:
```json
[
  {
    "id": "exec_1704888000123",
    "agent_id": "bidding-agent-001",
    "request_id": "req_1704888000123",
    "capability": "generate_summary",
    "input": {
      "tender_data": {...}
    },
    "output": {
      "project_overview": "...",
      "main_requirements": "..."
    },
    "success": true,
    "duration": 1250,
    "token_usage": {
      "input_tokens": 500,
      "output_tokens": 800,
      "total_tokens": 1300
    },
    "created_at": "2024-07-10T10:25:00Z"
  }
]
```

## 🎯 使用示例

### 1. 招投标摘要生成

```bash
curl -X POST http://localhost:8080/api/v1/agents/execute \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "bidding-agent-001",
    "capability": "generate_summary",
    "input": {
      "tender_data": {
        "project_name": "智慧城市数据平台建设",
        "budget": "800万元",
        "deadline": "2024-12-31",
        "description": "建设统一的城市数据平台，整合各部门数据资源",
        "requirements": "需要大数据处理能力、云计算平台、AI算法支持"
      }
    },
    "config": {
      "model": "deepseek-v3-250324",
      "temperature": 0.1
    }
  }'
```

### 2. 获取Agent状态

```bash
# 获取所有Agent
curl http://localhost:8080/api/v1/agents

# 获取特定Agent详情
curl http://localhost:8080/api/v1/agents/bidding-agent-001

# 检查Agent健康状态
curl http://localhost:8080/api/v1/agents/bidding-agent-001/health

# 获取Agent能力列表
curl http://localhost:8080/api/v1/agents/bidding-agent-001/capabilities
```

## 📊 错误码参考

### 通用错误码

| 错误码 | HTTP状态码 | 描述 |
|--------|-----------|------|
| INVALID_REQUEST | 400 | 请求格式错误 |
| AGENT_NOT_FOUND | 404 | Agent不存在 |
| CAPABILITY_NOT_FOUND | 404 | Agent能力不存在 |
| INVALID_AGENT_ID | 400 | Agent ID格式错误 |
| INVALID_CAPABILITY | 400 | 能力名称格式错误 |
| VALIDATION_FAILED | 400 | 参数验证失败 |
| EXECUTION_FAILED | 500 | Agent执行失败 |
| TIMEOUT | 408 | 执行超时 |
| RATE_LIMITED | 429 | 请求频率限制 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

### Agent特定错误码

| 错误码 | 描述 |
|--------|------|
| AGENT_BUSY | Agent正在处理其他请求 |
| AGENT_UNHEALTHY | Agent健康状态异常 |
| LLM_ERROR | LLM调用失败 |
| TOKEN_EXCEEDED | Token使用量超限 |
| OUTPUT_PARSE_ERROR | 输出解析失败 |
| TEMPLATE_ERROR | 模板渲染失败 |

## 🔧 配置参数

### Agent执行配置

```json
{
  "config": {
    "model": "deepseek-v3-250324",           // LLM模型名称
    "temperature": 0.1,               // 温度参数 (0.0-2.0)
    "max_tokens": 2000,               // 最大token数
    "timeout": 300,                   // 超时时间(秒)
    "retry_count": 3,                 // 重试次数
    "stream": false,                  // 是否流式输出
    "format": "json"                  // 输出格式
  }
}
```

### 请求优先级

| 优先级 | 描述 | 使用场景 |
|--------|------|----------|
| 1 | 最高优先级 | 紧急任务 |
| 2-3 | 高优先级 | 重要业务 |
| 4-6 | 中优先级 | 常规任务 |
| 7-9 | 低优先级 | 批量处理 |
| 10 | 最低优先级 | 后台任务 |

## 📈 性能优化建议

### 1. 请求优化

- **批量处理**: 合并相似请求减少网络开销
- **异步调用**: 长时间任务使用异步接口
- **缓存结果**: 相同输入可复用之前结果
- **参数压缩**: 减少不必要的输入参数

### 2. 并发控制

- **限制并发数**: 避免过多并发请求
- **优先级设置**: 合理设置请求优先级
- **超时管理**: 设置合适的超时时间
- **重试策略**: 使用指数退避重试

### 3. 监控告警

- **响应时间监控**: 设置响应时间告警阈值
- **错误率监控**: 监控Agent错误率变化
- **资源使用监控**: 关注CPU、内存使用情况
- **业务指标监控**: 跟踪业务相关的关键指标

## 🛡️ 安全最佳实践

### 1. 认证授权

```bash
# 使用API Key认证
curl -H "Authorization: Bearer YOUR_API_KEY" \
     http://localhost:8080/api/v1/agents
```

### 2. 输入验证

- **参数校验**: 严格验证所有输入参数
- **数据清洗**: 过滤潜在的恶意输入
- **大小限制**: 限制请求体大小
- **格式检查**: 验证数据格式合法性

### 3. 输出过滤

- **敏感信息过滤**: 移除响应中的敏感数据
- **格式标准化**: 确保输出格式一致
- **错误信息脱敏**: 避免暴露内部错误详情

## 📚 SDK和客户端

### 1. Go客户端

```go
import "gitlab.com/specific-ai/taskd/client"

client := client.NewAgentClient("http://localhost:8080")
response, err := client.ExecuteAgent(ctx, &client.ExecuteRequest{
    AgentID:    "bidding-agent-001",
    Capability: "generate_summary",
    Input:      input,
})
```

### 2. Python客户端

```python
import requests

client = requests.Session()
response = client.post(
    "http://localhost:8080/api/v1/agents/execute",
    json={
        "agent_id": "bidding-agent-001",
        "capability": "generate_summary",
        "input": input_data
    }
)
```

### 3. JavaScript客户端

```javascript
const response = await fetch('/api/v1/agents/execute', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        agent_id: 'bidding-agent-001',
        capability: 'generate_summary',
        input: inputData
    })
});
```

## 🔄 版本兼容性

### API版本策略

- **v1**: 当前稳定版本
- **向后兼容**: 保证向后兼容性
- **废弃通知**: 提前通知API废弃计划
- **迁移指南**: 提供版本迁移文档

### 版本头部

```http
Accept: application/vnd.taskd.v1+json
API-Version: v1
```

## 📞 技术支持

### 错误排查

1. **检查Agent状态**: 确认Agent健康状态
2. **验证参数格式**: 检查输入参数是否正确
3. **查看错误日志**: 分析详细错误信息
4. **测试网络连接**: 确认服务可访问性

### 联系方式

- **文档**: [TaskD文档中心](https://docs.taskd.ai)
- **问题反馈**: [GitHub Issues](https://github.com/taskd/taskd/issues)
- **技术支持**: <EMAIL>