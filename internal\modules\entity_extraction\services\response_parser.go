package services

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// responseParser 响应解析器实现
type responseParser struct {
	validator JSONSchemaValidator
}

// NewResponseParser 创建响应解析器
func NewResponseParser(validator JSONSchemaValidator) ResponseParser {
	return &responseParser{
		validator: validator,
	}
}

// ParseResponse 解析LLM响应
func (rp *responseParser) ParseResponse(ctx context.Context, responseText string, schema models.JSONSchema) (*models.ParseResult, error) {
	// 尝试解析JSON
	var data map[string]interface{}

	// 首先尝试直接解析
	if err := json.Unmarshal([]byte(responseText), &data); err != nil {
		// 如果直接解析失败，尝试提取JSON部分
		jsonStr := rp.ExtractJSON(responseText)
		if jsonStr == "" {
			// 如果仍然无法提取，尝试更宽松的解析
			jsonStr = rp.ExtractJSONLenient(responseText)
		}

		if jsonStr == "" {
			return nil, fmt.Errorf("无法从响应中提取有效JSON: %s", responseText)
		}

		if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
			return nil, fmt.Errorf("JSON解析失败: %v, 提取的JSON: %s", err, jsonStr)
		}
	}

	// 检查是否为无法提取的情况
	if impossible, exists := data["extraction_impossible"]; exists {
		if impossibleBool, ok := impossible.(bool); ok && impossibleBool {
			reason := "输入文本与模型无关联"
			if reasonStr, exists := data["reason"]; exists {
				if reasonString, ok := reasonStr.(string); ok {
					reason = reasonString
				}
			}

			return &models.ParseResult{
				Data:       nil,
				Confidence: 0.0,
				Valid:      false,
				Errors: []models.ValidationError{
					models.NewValidationError("extraction", reason, models.ErrExtractionImpossible),
				},
			}, nil
		}
	}

	// 验证数据结构
	validationResult := rp.ValidateStructure(data, schema)

	// 计算置信度
	confidence := rp.CalculateConfidence(data, validationResult)

	return &models.ParseResult{
		Data:       data,
		Confidence: confidence,
		Valid:      validationResult.Valid,
		Errors:     validationResult.Errors,
	}, nil
}

// ExtractJSON 从文本中提取JSON
func (rp *responseParser) ExtractJSON(text string) string {
	// 清理文本
	text = strings.TrimSpace(text)

	// 方法1: 查找JSON代码块
	jsonBlockRegex := regexp.MustCompile("```json\\s*([\\s\\S]*?)\\s*```")
	matches := jsonBlockRegex.FindStringSubmatch(text)
	if len(matches) > 1 {
		jsonStr := strings.TrimSpace(matches[1])
		if rp.isValidJSON(jsonStr) {
			return jsonStr
		}
	}

	// 方法2: 查找普通代码块
	codeBlockRegex := regexp.MustCompile("```\\s*([\\s\\S]*?)\\s*```")
	matches = codeBlockRegex.FindStringSubmatch(text)
	if len(matches) > 1 {
		jsonStr := strings.TrimSpace(matches[1])
		if rp.isValidJSON(jsonStr) {
			return jsonStr
		}
	}

	// 方法3: 查找花括号包围的JSON
	braceRegex := regexp.MustCompile("\\{[\\s\\S]*\\}")
	match := braceRegex.FindString(text)
	if match != "" && rp.isValidJSON(match) {
		return match
	}

	// 方法4: 尝试从第一个{到最后一个}
	firstBrace := strings.Index(text, "{")
	lastBrace := strings.LastIndex(text, "}")
	if firstBrace != -1 && lastBrace != -1 && lastBrace > firstBrace {
		jsonStr := text[firstBrace : lastBrace+1]
		if rp.isValidJSON(jsonStr) {
			return jsonStr
		}
	}

	// 方法5: 尝试多行JSON匹配
	lines := strings.Split(text, "\n")
	var jsonLines []string
	inJSON := false
	braceCount := 0

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 检查是否开始JSON
		if strings.HasPrefix(line, "{") {
			inJSON = true
			braceCount = 0
		}

		if inJSON {
			jsonLines = append(jsonLines, line)

			// 计算花括号数量
			braceCount += strings.Count(line, "{") - strings.Count(line, "}")

			// 如果花括号平衡，可能是JSON结束
			if braceCount == 0 && strings.HasSuffix(line, "}") {
				jsonStr := strings.Join(jsonLines, "\n")
				if rp.isValidJSON(jsonStr) {
					return jsonStr
				}
				// 重置状态继续寻找
				jsonLines = []string{}
				inJSON = false
			}
		}
	}

	return ""
}

// isValidJSON 检查字符串是否为有效JSON
func (rp *responseParser) isValidJSON(str string) bool {
	var js map[string]interface{}
	return json.Unmarshal([]byte(str), &js) == nil
}

// CalculateConfidence 计算置信度
func (rp *responseParser) CalculateConfidence(data map[string]interface{}, validation *models.ValidationResult) float64 {
	if !validation.Valid {
		// 如果验证失败，根据错误类型给予不同的置信度
		errorCount := len(validation.Errors)
		if errorCount == 0 {
			return 0.0
		}

		// 根据错误严重程度调整置信度
		criticalErrors := 0
		warningErrors := 0

		for _, err := range validation.Errors {
			if err.Code == models.ErrSchemaValidation || err.Code == models.ErrInvalidInput {
				criticalErrors++
			} else if err.Code == "WARNING" {
				warningErrors++
			}
		}

		// 如果只有警告错误，给予较高置信度
		if criticalErrors == 0 && warningErrors > 0 {
			return 0.8 - float64(warningErrors)*0.1
		}

		// 有严重错误时，置信度较低
		return 0.3 - float64(criticalErrors)*0.1
	}

	// 基础置信度
	confidence := 0.8

	// 根据字段完整性调整
	if len(validation.RequiredFields) > 0 {
		presentRequiredFields := 0
		for _, field := range validation.RequiredFields {
			if value, exists := data[field]; exists && value != nil {
				presentRequiredFields++
			}
		}

		fieldCompleteness := float64(presentRequiredFields) / float64(len(validation.RequiredFields))
		confidence *= fieldCompleteness
	}

	// 根据数据质量调整
	qualityBonus := rp.assessDataQuality(data)
	confidence += qualityBonus

	// 根据数据丰富度调整
	richnessBonus := rp.assessDataRichness(data)
	confidence += richnessBonus

	// 确保置信度在0-1范围内
	if confidence > 1.0 {
		confidence = 1.0
	}
	if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// assessDataQuality 评估数据质量
func (rp *responseParser) assessDataQuality(data map[string]interface{}) float64 {
	bonus := 0.0
	totalFields := len(data)

	if totalFields == 0 {
		return -0.2
	}

	nonNullFields := 0
	meaningfulFields := 0

	for _, value := range data {
		if value != nil {
			nonNullFields++

			// 检查是否为有意义的值
			if rp.isMeaningfulValue(value) {
				meaningfulFields++
			}
		}
	}

	// 非空字段比例
	nonNullRatio := float64(nonNullFields) / float64(totalFields)
	bonus += (nonNullRatio - 0.5) * 0.1

	// 有意义字段比例
	meaningfulRatio := float64(meaningfulFields) / float64(totalFields)
	bonus += (meaningfulRatio - 0.5) * 0.1

	return bonus
}

// assessDataRichness 评估数据丰富度
func (rp *responseParser) assessDataRichness(data map[string]interface{}) float64 {
	bonus := 0.0

	// 检查是否有复杂结构
	for _, value := range data {
		switch v := value.(type) {
		case []interface{}:
			if len(v) > 0 {
				bonus += 0.05 // 非空数组
			}
		case map[string]interface{}:
			if len(v) > 0 {
				bonus += 0.05 // 非空对象
			}
		case string:
			if len(v) > 10 {
				bonus += 0.02 // 较长的字符串
			}
		}
	}

	// 字段数量奖励
	fieldCount := len(data)
	if fieldCount > 3 {
		bonus += 0.05
	}
	if fieldCount > 5 {
		bonus += 0.05
	}

	return bonus
}

// isMeaningfulValue 检查值是否有意义
func (rp *responseParser) isMeaningfulValue(value interface{}) bool {
	switch v := value.(type) {
	case string:
		trimmed := strings.TrimSpace(v)
		// 排除空字符串、占位符等
		meaninglessValues := []string{"", "null", "undefined", "N/A", "n/a", "无", "未知", "unknown"}
		for _, meaningless := range meaninglessValues {
			if strings.EqualFold(trimmed, meaningless) {
				return false
			}
		}
		return len(trimmed) > 0
	case []interface{}:
		return len(v) > 0
	case map[string]interface{}:
		return len(v) > 0
	case nil:
		return false
	default:
		return true
	}
}

// ExtractJSONLenient 更宽松的JSON提取方法
func (rp *responseParser) ExtractJSONLenient(text string) string {
	// 清理文本
	text = strings.TrimSpace(text)

	// 尝试查找任何可能的JSON结构
	// 方法1: 查找所有可能的JSON模式，包括不完整的
	patterns := []string{
		`\{[^{}]*\}`,                                 // 单层对象
		`\{[^{}]*\{[^{}]*\}[^{}]*\}`,                 // 两层嵌套
		`\{[^{}]*\{[^{}]*\{[^{}]*\}[^{}]*\}[^{}]*\}`, // 三层嵌套
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		matches := regex.FindAllString(text, -1)
		for _, match := range matches {
			if rp.isValidJSON(match) {
				return match
			}
		}
	}

	// 方法2: 尝试修复常见的JSON格式问题
	return rp.fixCommonJSONIssues(text)
}

// fixCommonJSONIssues 修复常见的JSON格式问题
func (rp *responseParser) fixCommonJSONIssues(text string) string {
	// 查找花括号范围
	firstBrace := strings.Index(text, "{")
	lastBrace := strings.LastIndex(text, "}")

	if firstBrace == -1 || lastBrace == -1 || lastBrace <= firstBrace {
		return ""
	}

	jsonStr := text[firstBrace : lastBrace+1]

	// 尝试修复常见问题
	fixes := []func(string) string{
		rp.fixTrailingCommas,
		rp.fixUnquotedKeys,
		rp.fixSingleQuotes,
		rp.fixMissingCommas,
		rp.fixNestedObjectIssues,
		rp.fixArrayFormatIssues,
	}

	for _, fix := range fixes {
		fixed := fix(jsonStr)
		if rp.isValidJSON(fixed) {
			return fixed
		}
		jsonStr = fixed // 尝试累积修复
	}

	return ""
}

// fixTrailingCommas 修复尾随逗号
func (rp *responseParser) fixTrailingCommas(jsonStr string) string {
	// 移除对象或数组中的尾随逗号
	re := regexp.MustCompile(`,(\s*[}\]])`)
	return re.ReplaceAllString(jsonStr, "$1")
}

// fixUnquotedKeys 修复未加引号的键
func (rp *responseParser) fixUnquotedKeys(jsonStr string) string {
	// 为未加引号的键添加引号
	re := regexp.MustCompile(`(\w+)(\s*:)`)
	return re.ReplaceAllString(jsonStr, `"$1"$2`)
}

// fixSingleQuotes 修复单引号
func (rp *responseParser) fixSingleQuotes(jsonStr string) string {
	// 将单引号替换为双引号（简单实现）
	return strings.ReplaceAll(jsonStr, "'", "\"")
}

// fixMissingCommas 修复缺失的逗号
func (rp *responseParser) fixMissingCommas(jsonStr string) string {
	// 在对象属性之间添加缺失的逗号
	re := regexp.MustCompile(`"(\s*}?\s*)"(\s*\w+)`)
	return re.ReplaceAllString(jsonStr, `"$1","$2`)
}

// fixNestedObjectIssues 修复嵌套对象格式问题
func (rp *responseParser) fixNestedObjectIssues(jsonStr string) string {
	// 修复嵌套对象中的常见问题
	
	// 1. 修复缺失的花括号结束符
	openBraces := strings.Count(jsonStr, "{")
	closeBraces := strings.Count(jsonStr, "}")
	if openBraces > closeBraces {
		jsonStr = jsonStr + strings.Repeat("}", openBraces-closeBraces)
	}
	
	// 2. 修复嵌套对象属性缺少引号的问题
	re := regexp.MustCompile(`(\w+)(\s*:\s*\{)`)
	jsonStr = re.ReplaceAllString(jsonStr, `"$1"$2`)
	
	// 3. 修复嵌套对象中的字段分隔问题
	re2 := regexp.MustCompile(`\}(\s*)(["\w])`)
	jsonStr = re2.ReplaceAllString(jsonStr, `},$1$2`)
	
	return jsonStr
}

// fixArrayFormatIssues 修复数组格式问题
func (rp *responseParser) fixArrayFormatIssues(jsonStr string) string {
	// 1. 修复数组中缺失的逗号
	re := regexp.MustCompile(`"(\s*)"(\s*"[^"]*")`)
	jsonStr = re.ReplaceAllString(jsonStr, `"$1",$2`)
	
	// 2. 修复数组中的多余逗号
	re2 := regexp.MustCompile(`,(\s*\])`)
	jsonStr = re2.ReplaceAllString(jsonStr, `$1`)
	
	// 3. 修复数组括号平衡问题
	openBrackets := strings.Count(jsonStr, "[")
	closeBrackets := strings.Count(jsonStr, "]")
	if openBrackets > closeBrackets {
		jsonStr = jsonStr + strings.Repeat("]", openBrackets-closeBrackets)
	}
	
	return jsonStr
}

// ValidateStructure 验证数据结构
func (rp *responseParser) ValidateStructure(data map[string]interface{}, schema models.JSONSchema) *models.ValidationResult {
	return rp.validator.ValidateData(data, schema)
}
