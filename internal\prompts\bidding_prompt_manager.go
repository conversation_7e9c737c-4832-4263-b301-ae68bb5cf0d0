package prompts

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/spf13/viper"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// LanguagePrompt represents a prompt in a specific language
type LanguagePrompt struct {
	System   string `mapstructure:"system"`
	Template string `mapstructure:"template"`
}

// MultiLanguagePromptSet contains prompts in multiple languages
type MultiLanguagePromptSet struct {
	Chinese LanguagePrompt `mapstructure:"chinese"`
	English LanguagePrompt `mapstructure:"english"`
}

// BiddingPromptManager manages multi-language prompt templates for bidding agents
type BiddingPromptManager struct {
	prompts map[string]MultiLanguagePromptSet
}

// NewBiddingPromptManager creates a new bidding prompt manager
func NewBiddingPromptManager(promptsDir string) (*BiddingPromptManager, error) {
	pm := &BiddingPromptManager{
		prompts: make(map[string]MultiLanguagePromptSet),
	}

	if promptsDir == "" {
		promptsDir = "internal/prompts"
	}

	// Check if directory exists
	if _, err := os.Stat(promptsDir); os.IsNotExist(err) {
		utils.Log.Warnf("BiddingPromptManager: Prompts directory '%s' not found", promptsDir)
		return pm, nil
	}

	files, err := os.ReadDir(promptsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read prompts directory '%s': %w", promptsDir, err)
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fileName := file.Name()
		ext := filepath.Ext(fileName)
		if ext != ".yaml" && ext != ".yml" {
			continue
		}

		// Only load bidding-related prompts
		if !strings.HasPrefix(fileName, "bidding_") {
			continue
		}

		promptName := strings.TrimSuffix(fileName, ext)
		filePath := filepath.Join(promptsDir, fileName)

		// Use Viper to read the prompt file
		vp := viper.New()
		vp.SetConfigFile(filePath)
		if err := vp.ReadInConfig(); err != nil {
			utils.Log.Warnf("BiddingPromptManager: Failed to read prompt file '%s': %v", filePath, err)
			continue
		}

		var promptSet MultiLanguagePromptSet
		if err := vp.Unmarshal(&promptSet); err != nil {
			utils.Log.Warnf("BiddingPromptManager: Failed to parse prompt file '%s': %v", filePath, err)
			continue
		}

		// Validate the prompt set
		if promptSet.Chinese.System == "" && promptSet.Chinese.Template == "" &&
			promptSet.English.System == "" && promptSet.English.Template == "" {
			utils.Log.Warnf("BiddingPromptManager: Prompt set in file '%s' is empty", filePath)
			continue
		}

		pm.prompts[promptName] = promptSet
		utils.Log.Infof("BiddingPromptManager: Loaded prompt set '%s' from file '%s'", promptName, filePath)
	}

	return pm, nil
}

// GetPrompt returns the prompt for a specific language
func (pm *BiddingPromptManager) GetPrompt(promptName, language string) (LanguagePrompt, error) {
	promptSet, ok := pm.prompts[promptName]
	if !ok {
		return LanguagePrompt{}, fmt.Errorf("prompt set not found: '%s'", promptName)
	}

	switch strings.ToLower(language) {
	case "chinese", "zh", "cn":
		if promptSet.Chinese.System == "" && promptSet.Chinese.Template == "" {
			return LanguagePrompt{}, fmt.Errorf("Chinese prompt not found for '%s'", promptName)
		}
		return promptSet.Chinese, nil
	case "english", "en":
		if promptSet.English.System == "" && promptSet.English.Template == "" {
			return LanguagePrompt{}, fmt.Errorf("English prompt not found for '%s'", promptName)
		}
		return promptSet.English, nil
	default:
		// Default to Chinese if language not specified or unknown
		if promptSet.Chinese.System != "" || promptSet.Chinese.Template != "" {
			return promptSet.Chinese, nil
		}
		return promptSet.English, nil
	}
}

// FormatPrompt formats a prompt template with the given data
func (pm *BiddingPromptManager) FormatPrompt(promptTemplate string, data interface{}) (string, error) {
	// Create custom template functions
	funcMap := template.FuncMap{
		"join": func(arr []string, sep string) string {
			return strings.Join(arr, sep)
		},
		"toJSON": func(v interface{}) string {
			// Simple JSON representation for templates
			return fmt.Sprintf("%+v", v)
		},
	}

	tmpl, err := template.New("prompt").Funcs(funcMap).Parse(promptTemplate)
	if err != nil {
		return "", fmt.Errorf("failed to parse prompt template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute prompt template: %w", err)
	}
	return buf.String(), nil
}

// BuildPrompt builds a complete prompt with system message and user message
func (pm *BiddingPromptManager) BuildPrompt(promptName, language string, data interface{}) (string, string, error) {
	prompt, err := pm.GetPrompt(promptName, language)
	if err != nil {
		return "", "", err
	}

	systemMsg := prompt.System
	if systemMsg != "" {
		systemMsg, err = pm.FormatPrompt(systemMsg, data)
		if err != nil {
			return "", "", fmt.Errorf("failed to format system prompt: %w", err)
		}
	}

	userMsg := prompt.Template
	if userMsg != "" {
		userMsg, err = pm.FormatPrompt(userMsg, data)
		if err != nil {
			return "", "", fmt.Errorf("failed to format user prompt: %w", err)
		}
	}

	return systemMsg, userMsg, nil
}

// ListPrompts returns all available prompt names
func (pm *BiddingPromptManager) ListPrompts() []string {
	var names []string
	for name := range pm.prompts {
		names = append(names, name)
	}
	return names
}

// HasPrompt checks if a prompt exists
func (pm *BiddingPromptManager) HasPrompt(promptName string) bool {
	_, exists := pm.prompts[promptName]
	return exists
}