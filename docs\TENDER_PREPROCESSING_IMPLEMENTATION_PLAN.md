# 招投标预处理Agent拆分实施计划

## 目标概述

将bidding-crawler项目中的招投标预处理单体流水线拆分为6个独立的Agent，基于taskd的A2A架构实现，支持独立调用和编排调用。

## 实施目标

### 主要目标
1. **模块化**：将单体预处理流程拆分为独立的、可复用的Agent
2. **可扩展性**：每个Agent可独立部署和扩展
3. **容错性**：单个Agent失败不影响整体服务
4. **性能优化**：支持并发处理和批量操作
5. **标准化**：统一的A2A协议和Agent规范

### 技术目标
1. 复用现有taskd的BaseAgent架构
2. 实现标准的A2A协议通信
3. 支持中英文双语prompt
4. 集成现有的LLM服务统一出口
5. 实现批量推理能力

## 实施阶段

### 阶段1：基础架构准备 (1-2天)
1. **目录结构规划**
   - 在`internal/modules/bidding/agents/`下创建子目录
   - 设计prompt目录结构
   - 规划配置文件组织

2. **Prompt准备**
   - 迁移现有Python prompt到Go项目
   - 实现中英文双语版本
   - 优化prompt模板

3. **LLM服务增强**
   - 扩展现有LLM服务支持批量推理
   - 优化令牌管理和使用统计

### 阶段2：核心Agent实现 (3-5天)
按优先级顺序实现各个Agent：

#### 高优先级Agent
1. **数据提取与验证Agent** (1天)
   - 实现结构化信息提取
   - 数据验证和完整性检查
   - 错误处理和重试机制

2. **智能分类Agent** (1天)
   - 行业分类实现
   - 采购类型分类
   - 业务领域识别

3. **内容增强Agent** (1天)
   - 关键词提取
   - 标题优化
   - 质量评分算法

#### 中优先级Agent
4. **多语言处理Agent** (1天)
   - 批量翻译功能
   - 翻译质量评估
   - 本地化处理

5. **结果聚合Agent** (0.5天)
   - 结果整合逻辑
   - 格式化输出
   - 数据持久化

#### 编排Agent
6. **编排Agent** (1.5天)
   - 工作流编排引擎
   - 并发控制
   - 错误处理和重试
   - 进度监控

### 阶段3：集成测试 (1-2天)
1. **单元测试**
   - 每个Agent的独立测试
   - Mock数据测试
   - 错误场景测试

2. **集成测试**
   - Agent间通信测试
   - 端到端流程测试
   - 性能压力测试

3. **兼容性测试**
   - 与现有API的兼容性
   - 数据格式兼容性
   - 向后兼容验证

### 阶段4：部署和优化 (1天)
1. **容器化部署**
   - Docker镜像构建
   - K8s部署配置
   - 服务发现配置

2. **监控配置**
   - 日志配置
   - 指标监控
   - 告警规则

3. **性能优化**
   - 并发参数调优
   - 内存使用优化
   - 响应时间优化

## 详细任务分解

### Task 1: 目录结构和基础设施
```
internal/modules/bidding/agents/
├── preprocessing/                    # 预处理相关Agent
│   ├── extraction/                  # 数据提取Agent
│   ├── classification/              # 分类Agent
│   ├── enhancement/                 # 内容增强Agent
│   ├── multilingual/               # 多语言Agent
│   ├── aggregation/                # 聚合Agent
│   └── orchestration/              # 编排Agent
└── common/                         # 共享组件
    ├── types.go                    # 共享数据类型
    ├── utils.go                    # 工具函数
    └── constants.go                # 常量定义

internal/prompts/preprocessing/
├── extraction/
│   ├── basic_info_zh.yaml         # 基本信息提取-中文
│   ├── basic_info_en.yaml         # 基本信息提取-英文
│   ├── organization_zh.yaml       # 机构信息提取-中文
│   └── organization_en.yaml       # 机构信息提取-英文
├── classification/
│   ├── industry_zh.yaml           # 行业分类-中文
│   └── industry_en.yaml           # 行业分类-英文
├── enhancement/
│   ├── keywords_zh.yaml           # 关键词提取-中文
│   ├── keywords_en.yaml           # 关键词提取-英文
│   ├── scoring_zh.yaml            # 质量评分-中文
│   └── scoring_en.yaml            # 质量评分-英文
└── multilingual/
    ├── translation_zh.yaml        # 翻译-中文源
    └── translation_en.yaml        # 翻译-英文源
```

### Task 2: LLM服务增强
- 扩展`internal/services/llm_service_impl.go`
- 新增批量推理接口
- 优化令牌计算和统计
- 实现并发控制和限流

### Task 3-8: Agent实现
每个Agent包含以下标准组件：
- Agent结构体定义
- Skill实现
- Agent Card配置
- 单元测试
- 集成测试

### Task 9: 编排Agent特殊实现
- 工作流状态管理
- 并发goroutine控制
- Agent间通信协调
- 错误恢复机制

### Task 10: 测试覆盖
- 单元测试覆盖率 > 80%
- 集成测试覆盖主要场景
- 性能基准测试
- 错误场景测试

## 技术规范

### 代码规范
1. **命名约定**
   - Agent名称：`{功能}Agent`，如`ExtractionAgent`
   - Skill名称：动词+名词，如`ExtractStructuredData`
   - 文件名：snake_case

2. **错误处理**
   - 统一使用Go error约定
   - 区分AI失败和业务逻辑失败
   - 实现重试机制

3. **日志规范**
   - 使用结构化日志
   - 包含trace_id进行链路追踪
   - 记录关键性能指标

### 性能要求
1. **响应时间**
   - 单个Agent处理时间 < 30秒
   - 编排Agent总处理时间 < 120秒

2. **并发能力**
   - 支持至少10个并发请求
   - 编排Agent支持最多50条数据并发处理

3. **资源限制**
   - 单个Agent内存使用 < 1GB
   - CPU使用率 < 80%

### 质量保证
1. **代码审查**
   - 所有代码需要Code Review
   - 遵循Go最佳实践
   - 确保测试覆盖率

2. **文档完整性**
   - Agent Card规范文档
   - API使用示例
   - 部署运维文档

## 风险评估和缓解

### 技术风险
1. **性能风险**
   - 风险：拆分后性能下降
   - 缓解：实现缓存机制，优化并发处理

2. **稳定性风险**
   - 风险：Agent间通信失败
   - 缓解：实现重试机制和降级策略

3. **兼容性风险**
   - 风险：与现有系统不兼容
   - 缓解：保留原有接口，实现适配器模式

### 业务风险
1. **迁移风险**
   - 风险：迁移过程中服务中断
   - 缓解：蓝绿部署，渐进式切换

2. **数据一致性风险**
   - 风险：处理结果不一致
   - 缓解：实现对比测试，确保结果一致性

## 成功标准

### 功能标准
- [x] 6个Agent全部实现并通过测试
- [ ] 编排Agent支持并发处理
- [ ] 与现有API完全兼容
- [ ] 支持中英文双语处理

### 性能标准
- [ ] 处理速度不低于现有系统
- [ ] 支持10倍并发扩展
- [ ] 错误率 < 1%

### 质量标准
- [ ] 代码覆盖率 > 80%
- [ ] 文档完整性 100%
- [ ] 通过所有集成测试

## 后续优化计划

### 短期优化（1个月内）
1. 性能调优和监控优化
2. 错误处理机制完善
3. 缓存策略优化

### 中期扩展（3个月内）
1. 支持更多语言翻译
2. 实现智能路由和负载均衡
3. 增加更多业务规则引擎

### 长期规划（6个月内）
1. 机器学习模型优化
2. 自动化运维和自愈能力
3. 跨地域部署支持