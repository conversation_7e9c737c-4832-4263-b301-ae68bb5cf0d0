#!/bin/bash
# TaskD 测试数据初始化脚本
# 使用SQL脚本初始化数据库表结构和测试数据

set -e

# 默认数据库连接参数 - 使用dev环境配置
DB_HOST=${DB_HOST:-"***********"}
DB_PORT=${DB_PORT:-"5433"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"SecurePass123!"}
DB_NAME=${DB_NAME:-"overseas"}

echo "Initializing TaskD test data..."
echo "Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo "User: $DB_USER"

# 设置PGPASSWORD环境变量
export PGPASSWORD="$DB_PASSWORD"

# 执行初始化SQL脚本
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f init_test_data.sql

if [ $? -eq 0 ]; then
    echo ""
    echo "Test data initialization completed successfully!"
else
    echo ""
    echo "Test data initialization failed!"
    exit 1
fi