package entity_extraction

import (
	"encoding/json"

	"gitlab.com/specific-ai/taskd/internal/llm"
)

// Factory 实体提取Agent工厂
type Factory struct{}

// NewFactory 创建工厂实例
func NewFactory() *Factory {
	return &Factory{}
}

// CreateEntityExtractionAgent 创建实体提取Agent
func (f *Factory) CreateEntityExtractionAgent(id string, llmClient llm.LLMClient) *EntityExtractionAgent {
	// 使用默认配置创建Agent
	config := DefaultAgentConfig()
	return NewEntityExtractionAgent(llmClient, config)
}

// CreateWithConfig 使用配置创建Agent
func (f *Factory) CreateWithConfig(id string, llmClient llm.LLMClient, configMap map[string]interface{}) *EntityExtractionAgent {
	// 转换配置
	config := f.convertMapToConfig(configMap)
	if config == nil {
		config = DefaultAgentConfig()
	}
	
	// 创建Agent
	agent := NewEntityExtractionAgent(llmClient, config)
	
	return agent
}

// convertMapToConfig 转换map配置为AgentConfig
func (f *Factory) convertMapToConfig(configMap map[string]interface{}) *AgentConfig {
	if configMap == nil {
		return nil
	}
	
	// 序列化为JSON再反序列化，确保类型转换正确
	configJSON, err := json.Marshal(configMap)
	if err != nil {
		return nil
	}
	
	var config AgentConfig
	if err := json.Unmarshal(configJSON, &config); err != nil {
		return nil
	}
	
	// 设置默认值
	if config.LLMModel == "" {
		config.LLMModel = "deepseek-v3-250324"
	}
	if config.Temperature == 0 {
		config.Temperature = 0.1
	}
	if config.MaxTokens == 0 {
		config.MaxTokens = 4000
	}
	if config.Timeout == 0 {
		config.Timeout = 30
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.ConfidenceThreshold == 0 {
		config.ConfidenceThreshold = 0.7
	}
	
	return &config
}