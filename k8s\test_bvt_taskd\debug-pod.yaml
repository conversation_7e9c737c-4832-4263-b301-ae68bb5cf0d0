apiVersion: v1
kind: Pod
metadata:
  name: taskd-bvt-debugger
  namespace: ovs
  annotations:
    # Disable Istio sidecar injection for this pod to ensure direct network access
    "sidecar.istio.io/inject": "false"
spec:
  containers:
  - name: debugger
    image: 192.168.50.112/specific-ai/taskd-bvt-runner:latest # Ensure this image is rebuilt with new Dockerfile
    imagePullPolicy: Always
    # Keep the pod running indefinitely for debugging
    command: ["/bin/bash", "-c", "sleep infinity"]
    envFrom:
    - configMapRef:
        name: taskd-bvt-runner-config
  restartPolicy: Never 