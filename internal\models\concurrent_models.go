package models

import (
	"context"
	"time"
	"gitlab.com/specific-ai/taskd/internal/models/common"
)

// UserPriority 用户优先级类型
type UserPriority string

const (
	PriorityVIP    UserPriority = "vip"
	PriorityNormal UserPriority = "normal"
	PriorityFree   UserPriority = "free"
)

// RequestPriority 请求优先级（数字越小优先级越高）
type RequestPriority int

const (
	PriorityHigh   RequestPriority = 1 // VIP用户
	PriorityMedium RequestPriority = 2 // 普通用户
	PriorityLow    RequestPriority = 3 // 免费用户
)

// LLMRequestMessage 封装LLM请求的消息
type LLMRequestMessage struct {
	ID           string                             `json:"id"`           // 请求唯一ID
	UserID       string                             `json:"user_id"`      // 用户ID
	CompanyID    string                             `json:"company_id"`   // 公司ID
	Priority     RequestPriority                    `json:"priority"`     // 请求优先级
	UserPriority UserPriority                       `json:"user_priority"` // 用户优先级
	Params       OpenAICompatibleRequestParams      `json:"params"`       // 原始LLM请求参数
	CreatedAt    time.Time                          `json:"created_at"`   // 创建时间
	MaxWaitTime  time.Duration                      `json:"max_wait_time"` // 最大等待时间
	RetryCount   int                                `json:"retry_count"`  // 重试次数
	MaxRetries   int                                `json:"max_retries"`  // 最大重试次数
	Context      context.Context                    `json:"-"`            // 请求上下文
	ResponseChan chan *LLMResponseMessage           `json:"-"`            // 响应通道
}

// LLMResponseMessage LLM响应消息
type LLMResponseMessage struct {
	ID        string    `json:"id"`         // 对应请求ID
	Success   bool      `json:"success"`    // 是否成功
	Result    string    `json:"result"`     // 响应内容
	Error     string    `json:"error"`      // 错误信息
	TokenUsage common.TokenUsage `json:"token_usage"` // Token使用情况
	Duration  time.Duration `json:"duration"`  // 处理时长
	RetryCount int       `json:"retry_count"` // 实际重试次数
}

// 注释：TokenUsage定义已移至agent_models.go中，避免重复定义

// QueueStats 队列统计信息
type QueueStats struct {
	MainQueueSize    int                        `json:"main_queue_size"`    // 主队列大小
	RetryQueueSize   int                        `json:"retry_queue_size"`   // 重试队列大小
	ProcessingCount  int                        `json:"processing_count"`   // 正在处理的请求数
	ActiveWorkers    int                        `json:"active_workers"`     // 活跃工作协程数
	PriorityStats    map[RequestPriority]int    `json:"priority_stats"`     // 各优先级队列统计
	UserPriorityStats map[UserPriority]int      `json:"user_priority_stats"` // 各用户优先级统计
	TotalProcessed   int64                      `json:"total_processed"`    // 总处理数
	TotalErrors      int64                      `json:"total_errors"`       // 总错误数
	TotalRetries     int64                      `json:"total_retries"`      // 总重试数
	AverageWaitTime  time.Duration              `json:"average_wait_time"`  // 平均等待时间
	AverageProcessTime time.Duration            `json:"average_process_time"` // 平均处理时间
}

// ConcurrentConfig 并发控制配置
type ConcurrentConfig struct {
	MaxConcurrentRequests int           `json:"max_concurrent_requests"` // 最大并发请求数
	MainQueueSize        int           `json:"main_queue_size"`         // 主队列大小
	RetryQueueSize       int           `json:"retry_queue_size"`        // 重试队列大小
	MaxWaitTime          time.Duration `json:"max_wait_time"`           // 最大等待时间
	MaxRetries           int           `json:"max_retries"`             // 最大重试次数
	RetryBaseDelay       time.Duration `json:"retry_base_delay"`        // 重试基础延迟
	WorkerCount          int           `json:"worker_count"`            // 工作协程数
	PulsarTopic          string        `json:"pulsar_topic"`            // Pulsar主题
	PulsarRetryTopic     string        `json:"pulsar_retry_topic"`      // Pulsar重试主题
	PulsarSubscription   string        `json:"pulsar_subscription"`     // Pulsar订阅名
	EnablePriorityQueue  bool          `json:"enable_priority_queue"`   // 是否启用优先级队列
	RejectFreeUserWhenBusy bool        `json:"reject_free_user_when_busy"` // 繁忙时拒绝免费用户
}

// DefaultConcurrentConfig 默认并发控制配置
func DefaultConcurrentConfig() *ConcurrentConfig {
	return &ConcurrentConfig{
		MaxConcurrentRequests: 1000,
		MainQueueSize:        1000,
		RetryQueueSize:       200,
		MaxWaitTime:          180 * time.Second,
		MaxRetries:           3,
		RetryBaseDelay:       2 * time.Second,
		WorkerCount:          20,
		PulsarTopic:          "persistent://public/default/llm-requests",
		PulsarRetryTopic:     "persistent://public/default/llm-retries",
		PulsarSubscription:   "llm-processor",
		EnablePriorityQueue:  true,
		RejectFreeUserWhenBusy: true,
	}
}

// GetPriorityFromUser 根据用户优先级获取请求优先级
func GetPriorityFromUser(userPriority UserPriority) RequestPriority {
	switch userPriority {
	case PriorityVIP:
		return PriorityHigh
	case PriorityNormal:
		return PriorityMedium
	case PriorityFree:
		return PriorityLow
	default:
		return PriorityLow
	}
}

// ShouldRejectRequest 是否应该拒绝请求
func (c *ConcurrentConfig) ShouldRejectRequest(userPriority UserPriority, currentQueueSize int) bool {
	if !c.RejectFreeUserWhenBusy {
		return false
	}
	
	// 当队列使用率超过80%时，拒绝免费用户请求
	queueUtilization := float64(currentQueueSize) / float64(c.MainQueueSize)
	return userPriority == PriorityFree && queueUtilization > 0.8
}