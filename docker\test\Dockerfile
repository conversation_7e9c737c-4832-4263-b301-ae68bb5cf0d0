# TaskD 简单测试环境
# 基于 Ubuntu 22.04，包含 Python 3.11 和 uv 包管理器

FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    python3.11 \
    python3.11-dev \
    python3.11-venv \
    python3-pip \
    netcat-openbsd \
    telnet \
    iputils-ping \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建 python3 软链接
RUN ln -sf /usr/bin/python3.11 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.11 /usr/bin/python

# 安装 uv 包管理器
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# 复制项目文件
COPY pyproject.toml ./
COPY test/ ./test/
COPY docker/test/docker-entrypoint.sh ./entrypoint.sh
COPY docker/test/.env.docker ./

# 创建日志和报告目录
RUN mkdir -p test/logs test/reports

# 初始化 Python 环境
RUN uv sync --all-extras

# 设置执行权限
RUN chmod +x entrypoint.sh

# 入口点
ENTRYPOINT ["./entrypoint.sh"]

# 默认命令
CMD ["test"]