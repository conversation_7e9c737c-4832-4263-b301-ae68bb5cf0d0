package setup

import (
	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/services/bidding"
	"gitlab.com/specific-ai/taskd/internal/services/infrastructure"
)

// LLMModule包含了与LLM相关的服务和处理器
type LLMModule struct {
	ReportSummaryService *bidding.ReportSummaryService
	SentimentService     *infrastructure.SentimentService
	LLMHandler           *handlers.LLMHandler
}

// NewLLMModule 初始化LLM相关的服务和处理器
func NewLLMModule(deps AppDependencies) (*LLMModule, error) {
	reportSummaryService := bidding.NewReportSummaryService(deps.LLMClient, deps.PromptManager)
	sentimentService := infrastructure.NewSentimentService(deps.LLMClient, deps.PromptManager)
	llmHandler := handlers.NewLLMHandler(reportSummaryService, sentimentService)
	return &LLMModule{
		ReportSummaryService: reportSummaryService,
		SentimentService:     sentimentService,
		LLMHandler:           llmHandler,
	}, nil
}
