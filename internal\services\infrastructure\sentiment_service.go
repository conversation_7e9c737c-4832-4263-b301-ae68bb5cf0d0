package infrastructure

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

const (
	PromptKeySentimentAnalysis = "sentiment_analysis" // 确保与 prompts.yaml 中的 key 一致
)

// llmSentimentOutput 是 LLM 可能直接返回的结构，字段名可能不完全一致或类型可能更宽泛
type llmSentimentOutput struct {
	TextID      interface{} `json:"text_id"` // LLM 可能返回数字或字符串
	Sentiment   string      `json:"sentiment"`
	Confidence  interface{} `json:"confidence"` // LLM 可能返回数字或字符串形式的数字
	Explanation string      `json:"explanation"`
}

// llmSentimentResponse 对应 LLM 返回的整体 JSON 结构
type llmSentimentResponse struct {
	Result []llmSentimentOutput `json:"result"`
}

// SentimentService 提供情感分析功能
type SentimentService struct {
	llmClient     llm.LLMClient
	promptManager *prompts.PromptManager
}

// NewSentimentService 创建 SentimentService 实例
func NewSentimentService(llmClient llm.LLMClient, pm *prompts.PromptManager) *SentimentService {
	return &SentimentService{
		llmClient:     llmClient,
		promptManager: pm,
	}
}

// AnalyzeSentiment 执行情感分析
func (s *SentimentService) AnalyzeSentiment(req models.SentimentAnalysisRequest) (*models.SentimentAnalysisResponse, error) {
	promptSet, err := s.promptManager.GetPromptSet(PromptKeySentimentAnalysis)
	if err != nil {
		return nil, fmt.Errorf("获取 '%s' prompts 失败: %w", PromptKeySentimentAnalysis, err)
	}

	promptData := map[string]interface{}{"text": req.Text}

	sysContent, err := s.promptManager.FormatPrompt(promptSet.SysPrompt, promptData)
	if err != nil {
		return nil, fmt.Errorf("格式化 system prompt 失败: %w", err)
	}
	userContent, err := s.promptManager.FormatPrompt(promptSet.UserPrompt, promptData)
	if err != nil {
		return nil, fmt.Errorf("格式化 user prompt 失败: %w", err)
	}

	llmReqParams := models.OpenAICompatibleRequestParams{
		Model:       req.ModelAlias,
		Messages:    []common.LLMMessage{{Role: "system", Content: sysContent}, {Role: "user", Content: userContent}},
		Temperature: req.Temperature,
		MaxTokens:   req.MaxTokens,
		ProviderSpecificParams: map[string]interface{}{
			"response_format": map[string]interface{}{"type": "json_object"}, // 请求 JSON 输出
		},
	}

	llmResultText, err := s.llmClient.ChatCompletions(llmReqParams)
	if err != nil {
		utils.Log.Errorf("LLM ChatCompletions 调用失败 (Sentiment): %v", err)
		return nil, fmt.Errorf("LLM 交互失败: %w", err)
	}
	utils.Log.Debugf("LLM Raw Response (Sentiment): %s", llmResultText)

	// 尝试从 LLM 返回的文本中提取 JSON 部分
	jsonStr := utils.ExtractJSONSubstring(llmResultText)
	if jsonStr == "" {
		errMsg := "LLM 响应中未找到有效的JSON内容"
		utils.Log.Error(errMsg + ": " + llmResultText)
		return &models.SentimentAnalysisResponse{Result: []models.SentimentOutput{
			{Sentiment: "neutral", Explanation: errMsg}, // 提供一个默认的回退
		}}, fmt.Errorf(errMsg)
	}

	var llmResp llmSentimentResponse
	if err := json.Unmarshal([]byte(jsonStr), &llmResp); err != nil {
		utils.Log.Errorf("解析 LLM JSON 响应失败 (Sentiment): %v. JSON String: %s", err, jsonStr)

		return &models.SentimentAnalysisResponse{Result: []models.SentimentOutput{
			{Sentiment: "neutral", Explanation: "无法解析LLM的JSON响应: " + err.Error()},
		}}, fmt.Errorf("无法反序列化LLM JSON响应: %w. Raw JSON: %s", err, jsonStr)
	}

	// 转换并验证 llmResp.Result 到 API 标准的 []models.SentimentOutput
	apiResult := make([]models.SentimentOutput, 0, len(llmResp.Result))
	for _, item := range llmResp.Result {
		apiItem := models.SentimentOutput{Explanation: item.Explanation}

		// 处理 TextID (LLM 可能返回数字或字符串)
		switch v := item.TextID.(type) {
		case string:
			apiItem.TextID = v
		case float64: // JSON 数字默认为 float64
			apiItem.TextID = fmt.Sprintf("%.0f", v) // 转为无小数的字符串
		case int, int32, int64:
			apiItem.TextID = fmt.Sprintf("%d", v)
		default:
			apiItem.TextID = "unknown_id" // 或者记录警告
			utils.Log.Warnf("未知的 text_id 类型: %T, value: %v", item.TextID, item.TextID)
		}

		// 处理 Sentiment 并校验
		sentimentLower := strings.ToLower(strings.TrimSpace(item.Sentiment))
		switch sentimentLower {
		case "positive", "negative", "neutral":
			apiItem.Sentiment = sentimentLower
		default:
			utils.Log.Warnf("LLM 返回了无效的情感值 '%s' (原始: '%s') for text_id '%s', 将默认为 'neutral'", sentimentLower, item.Sentiment, apiItem.TextID)
			apiItem.Sentiment = "neutral"
		}

		// 处理 Confidence (LLM 可能返回数字或字符串形式的数字)
		switch v := item.Confidence.(type) {
		case float64:
			apiItem.Confidence = v
		case string:
			// 尝试将字符串转换为 float64
			// 在实际应用中可能需要更健壮的转换库
			var f float64
			_, scanErr := fmt.Sscanf(v, "%f", &f)
			if scanErr == nil {
				apiItem.Confidence = f
			} else {
				utils.Log.Warnf("无法将 confidence 字符串 '%s' 转换为 float64 for text_id '%s'", v, apiItem.TextID)
			}
		case nil: // Confidence 是可选的
		default:
			utils.Log.Warnf("未知的 confidence 类型: %T, value: %v for text_id '%s'", item.Confidence, item.Confidence, apiItem.TextID)
		}
		// 可以在这里添加对 Confidence 范围 (0-1) 的校验

		apiResult = append(apiResult, apiItem)
	}

	return &models.SentimentAnalysisResponse{Result: apiResult}, nil
}
