package services

import (
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// schemaProcessor Schema处理服务实现
type schemaProcessor struct {
	validator JSONSchemaValidator
}

// NewSchemaProcessor 创建Schema处理服务
func NewSchemaProcessor(validator JSONSchemaValidator) SchemaProcessor {
	return &schemaProcessor{
		validator: validator,
	}
}

// ProcessSchema 处理和验证Schema
func (sp *schemaProcessor) ProcessSchema(schemaData map[string]interface{}) (*models.ProcessedSchema, error) {
	// 将map转换为JSONSchema结构
	schema, err := sp.convertToJSONSchema(schemaData)
	if err != nil {
		return nil, fmt.Errorf("Schema转换失败: %v", err)
	}

	// 验证Schema格式
	validationResult := sp.validator.ValidateSchema(*schema)
	if !validationResult.Valid {
		return nil, fmt.Errorf("Schema验证失败: %v", sp.formatValidationErrors(validationResult.Errors))
	}

	// 处理Schema，生成额外信息
	processedSchema := &models.ProcessedSchema{
		Schema:           *schema,
		ValidationResult: *validationResult,
		FieldTypes:       sp.extractFieldTypes(*schema),
		RequiredFields:   schema.Required,
		OptionalFields:   sp.extractOptionalFields(*schema),
		NestedStructures: sp.extractNestedStructures(*schema),
		EnumFields:       sp.extractEnumFields(*schema),
		FormatFields:     sp.extractFormatFields(*schema),
		Complexity:       sp.calculateComplexity(*schema),
	}

	return processedSchema, nil
}

// convertToJSONSchema 将map转换为JSONSchema结构
func (sp *schemaProcessor) convertToJSONSchema(data map[string]interface{}) (*models.JSONSchema, error) {
	// 将map序列化为JSON，然后反序列化为结构体
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化失败: %v", err)
	}

	var schema models.JSONSchema
	if err := json.Unmarshal(jsonData, &schema); err != nil {
		return nil, fmt.Errorf("反序列化失败: %v", err)
	}

	return &schema, nil
}

// formatValidationErrors 格式化验证错误
func (sp *schemaProcessor) formatValidationErrors(errors []models.ValidationError) string {
	var errorMessages []string
	for _, err := range errors {
		errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(errorMessages, "; ")
}

// extractFieldTypes 提取字段类型映射
func (sp *schemaProcessor) extractFieldTypes(schema models.JSONSchema) map[string]string {
	fieldTypes := make(map[string]string)
	
	for fieldName, prop := range schema.Properties {
		fieldTypes[fieldName] = prop.Type
		
		// 递归处理嵌套对象
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedTypes := sp.extractNestedFieldTypes(fieldName, prop.Properties)
			for k, v := range nestedTypes {
				fieldTypes[k] = v
			}
		}
		
		// 处理数组项类型
		if prop.Type == "array" && prop.Items != nil {
			fieldTypes[fieldName+"[]"] = prop.Items.Type
		}
	}
	
	return fieldTypes
}

// extractNestedFieldTypes 提取嵌套字段类型
func (sp *schemaProcessor) extractNestedFieldTypes(parentName string, properties map[string]models.Property) map[string]string {
	fieldTypes := make(map[string]string)
	
	for fieldName, prop := range properties {
		fullName := parentName + "." + fieldName
		fieldTypes[fullName] = prop.Type
		
		// 递归处理更深层的嵌套
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedTypes := sp.extractNestedFieldTypes(fullName, prop.Properties)
			for k, v := range nestedTypes {
				fieldTypes[k] = v
			}
		}
	}
	
	return fieldTypes
}

// extractOptionalFields 提取可选字段
func (sp *schemaProcessor) extractOptionalFields(schema models.JSONSchema) []string {
	var optionalFields []string
	requiredMap := make(map[string]bool)
	
	// 创建必填字段映射
	for _, field := range schema.Required {
		requiredMap[field] = true
	}
	
	// 找出所有非必填字段
	for fieldName := range schema.Properties {
		if !requiredMap[fieldName] {
			optionalFields = append(optionalFields, fieldName)
		}
	}
	
	return optionalFields
}

// extractNestedStructures 提取嵌套结构信息
func (sp *schemaProcessor) extractNestedStructures(schema models.JSONSchema) []models.NestedStructure {
	var nestedStructures []models.NestedStructure
	
	for fieldName, prop := range schema.Properties {
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedStructures = append(nestedStructures, models.NestedStructure{
				FieldName:  fieldName,
				Type:       "object",
				Properties: prop.Properties,
				Required:   prop.Required,
			})
		}
		
		if prop.Type == "array" && prop.Items != nil && prop.Items.Type == "object" && len(prop.Items.Properties) > 0 {
			nestedStructures = append(nestedStructures, models.NestedStructure{
				FieldName:  fieldName,
				Type:       "array_of_objects",
				Properties: prop.Items.Properties,
				Required:   prop.Items.Required,
			})
		}
	}
	
	return nestedStructures
}

// extractEnumFields 提取枚举字段信息
func (sp *schemaProcessor) extractEnumFields(schema models.JSONSchema) map[string][]string {
	enumFields := make(map[string][]string)
	
	for fieldName, prop := range schema.Properties {
		if len(prop.Enum) > 0 {
			enumFields[fieldName] = prop.Enum
		}
		
		// 递归处理嵌套对象中的枚举
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedEnums := sp.extractNestedEnumFields(fieldName, prop.Properties)
			for k, v := range nestedEnums {
				enumFields[k] = v
			}
		}
		
		// 处理数组项中的枚举
		if prop.Type == "array" && prop.Items != nil && len(prop.Items.Enum) > 0 {
			enumFields[fieldName+"[]"] = prop.Items.Enum
		}
	}
	
	return enumFields
}

// extractNestedEnumFields 提取嵌套枚举字段
func (sp *schemaProcessor) extractNestedEnumFields(parentName string, properties map[string]models.Property) map[string][]string {
	enumFields := make(map[string][]string)
	
	for fieldName, prop := range properties {
		fullName := parentName + "." + fieldName
		
		if len(prop.Enum) > 0 {
			enumFields[fullName] = prop.Enum
		}
		
		// 递归处理更深层的嵌套
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedEnums := sp.extractNestedEnumFields(fullName, prop.Properties)
			for k, v := range nestedEnums {
				enumFields[k] = v
			}
		}
	}
	
	return enumFields
}

// extractFormatFields 提取格式字段信息
func (sp *schemaProcessor) extractFormatFields(schema models.JSONSchema) map[string]string {
	formatFields := make(map[string]string)
	
	for fieldName, prop := range schema.Properties {
		if prop.Format != "" {
			formatFields[fieldName] = prop.Format
		}
		
		// 递归处理嵌套对象中的格式
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedFormats := sp.extractNestedFormatFields(fieldName, prop.Properties)
			for k, v := range nestedFormats {
				formatFields[k] = v
			}
		}
		
		// 处理数组项中的格式
		if prop.Type == "array" && prop.Items != nil && prop.Items.Format != "" {
			formatFields[fieldName+"[]"] = prop.Items.Format
		}
	}
	
	return formatFields
}

// extractNestedFormatFields 提取嵌套格式字段
func (sp *schemaProcessor) extractNestedFormatFields(parentName string, properties map[string]models.Property) map[string]string {
	formatFields := make(map[string]string)
	
	for fieldName, prop := range properties {
		fullName := parentName + "." + fieldName
		
		if prop.Format != "" {
			formatFields[fullName] = prop.Format
		}
		
		// 递归处理更深层的嵌套
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedFormats := sp.extractNestedFormatFields(fullName, prop.Properties)
			for k, v := range nestedFormats {
				formatFields[k] = v
			}
		}
	}
	
	return formatFields
}

// calculateComplexity 计算Schema复杂度
func (sp *schemaProcessor) calculateComplexity(schema models.JSONSchema) models.SchemaComplexity {
	complexity := models.SchemaComplexity{
		TotalFields:    0,
		RequiredFields: len(schema.Required),
		OptionalFields: 0,
		NestedLevels:   0,
		ArrayFields:    0,
		ObjectFields:   0,
		EnumFields:     0,
		FormatFields:   0,
	}
	
	complexity.TotalFields = len(schema.Properties)
	complexity.OptionalFields = complexity.TotalFields - complexity.RequiredFields
	
	// 计算各种字段类型的数量和嵌套层级
	maxNestingLevel := sp.calculateNestingLevel(schema.Properties, 1)
	complexity.NestedLevels = maxNestingLevel
	
	for _, prop := range schema.Properties {
		switch prop.Type {
		case "array":
			complexity.ArrayFields++
		case "object":
			complexity.ObjectFields++
		}
		
		if len(prop.Enum) > 0 {
			complexity.EnumFields++
		}
		
		if prop.Format != "" {
			complexity.FormatFields++
		}
	}
	
	return complexity
}

// calculateNestingLevel 计算嵌套层级
func (sp *schemaProcessor) calculateNestingLevel(properties map[string]models.Property, currentLevel int) int {
	maxLevel := currentLevel
	
	for _, prop := range properties {
		if prop.Type == "object" && len(prop.Properties) > 0 {
			nestedLevel := sp.calculateNestingLevel(prop.Properties, currentLevel+1)
			if nestedLevel > maxLevel {
				maxLevel = nestedLevel
			}
		}
		
		if prop.Type == "array" && prop.Items != nil && prop.Items.Type == "object" && len(prop.Items.Properties) > 0 {
			nestedLevel := sp.calculateNestingLevel(prop.Items.Properties, currentLevel+1)
			if nestedLevel > maxLevel {
				maxLevel = nestedLevel
			}
		}
	}
	
	return maxLevel
}

// ValidateSchemaData 验证Schema数据
func (sp *schemaProcessor) ValidateSchemaData(data map[string]interface{}, schema models.JSONSchema) (*models.ValidationResult, error) {
	return sp.validator.ValidateData(data, schema), nil
}

// GetSchemaInfo 获取Schema信息摘要
func (sp *schemaProcessor) GetSchemaInfo(schema models.JSONSchema) *models.SchemaInfo {
	return &models.SchemaInfo{
		Name:           schema.Name,
		Description:    schema.Description,
		TotalFields:    len(schema.Properties),
		RequiredFields: len(schema.Required),
		OptionalFields: len(schema.Properties) - len(schema.Required),
		HasNestedObjects: sp.hasNestedObjects(schema.Properties),
		HasArrays:      sp.hasArrays(schema.Properties),
		HasEnums:       sp.hasEnums(schema.Properties),
		HasFormats:     sp.hasFormats(schema.Properties),
		ExampleCount:   len(schema.Examples),
	}
}

// hasNestedObjects 检查是否有嵌套对象
func (sp *schemaProcessor) hasNestedObjects(properties map[string]models.Property) bool {
	for _, prop := range properties {
		if prop.Type == "object" && len(prop.Properties) > 0 {
			return true
		}
		if prop.Type == "array" && prop.Items != nil && prop.Items.Type == "object" {
			return true
		}
	}
	return false
}

// hasArrays 检查是否有数组字段
func (sp *schemaProcessor) hasArrays(properties map[string]models.Property) bool {
	for _, prop := range properties {
		if prop.Type == "array" {
			return true
		}
	}
	return false
}

// hasEnums 检查是否有枚举字段
func (sp *schemaProcessor) hasEnums(properties map[string]models.Property) bool {
	for _, prop := range properties {
		if len(prop.Enum) > 0 {
			return true
		}
		// 递归检查嵌套对象
		if prop.Type == "object" && sp.hasEnums(prop.Properties) {
			return true
		}
		// 检查数组项
		if prop.Type == "array" && prop.Items != nil && len(prop.Items.Enum) > 0 {
			return true
		}
	}
	return false
}

// hasFormats 检查是否有格式字段
func (sp *schemaProcessor) hasFormats(properties map[string]models.Property) bool {
	for _, prop := range properties {
		if prop.Format != "" {
			return true
		}
		// 递归检查嵌套对象
		if prop.Type == "object" && sp.hasFormats(prop.Properties) {
			return true
		}
		// 检查数组项
		if prop.Type == "array" && prop.Items != nil && prop.Items.Format != "" {
			return true
		}
	}
	return false
}

// GenerateSchemaExample 生成Schema示例数据
func (sp *schemaProcessor) GenerateSchemaExample(schema models.JSONSchema) map[string]interface{} {
	example := make(map[string]interface{})
	
	for fieldName, prop := range schema.Properties {
		example[fieldName] = sp.generateFieldExample(prop)
	}
	
	return example
}

// generateFieldExample 生成字段示例值
func (sp *schemaProcessor) generateFieldExample(prop models.Property) interface{} {
	// 如果有枚举值，使用第一个
	if len(prop.Enum) > 0 {
		return prop.Enum[0]
	}
	
	// 如果有默认值，使用默认值
	if prop.Default != nil {
		return prop.Default
	}
	
	// 根据类型生成示例值
	switch prop.Type {
	case "string":
		if prop.Format == "date-time" {
			return "2024-01-01T10:30:00Z"
		} else if prop.Format == "date" {
			return "2024-01-01"
		} else if prop.Format == "time" {
			return "10:30:00"
		} else if prop.Format == "email" {
			return "<EMAIL>"
		} else if prop.Format == "uri" {
			return "https://example.com"
		}
		return "示例文本"
	case "number":
		return 123.45
	case "integer":
		return 123
	case "boolean":
		return true
	case "array":
		if prop.Items != nil {
			return []interface{}{sp.generateFieldExample(*prop.Items)}
		}
		return []interface{}{}
	case "object":
		if len(prop.Properties) > 0 {
			obj := make(map[string]interface{})
			for subFieldName, subProp := range prop.Properties {
				obj[subFieldName] = sp.generateFieldExample(subProp)
			}
			return obj
		}
		return map[string]interface{}{}
	default:
		return nil
	}
}