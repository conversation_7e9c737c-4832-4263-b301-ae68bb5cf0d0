server:
  port: "8601"
  mode: "debug"

logger:
  level: "debug"

mongodb:
  uri: "************************************************************************"
  database: "overseas"
  timeout_seconds: 10

postgresql:
  host: "***********"
  port: 5433
  user: "admin"
  password: "SecurePass123!"
  database: "overseas"
  ssl_mode: "disable"
  timeout_seconds: 10
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300

pulsar:
  service_url: "pulsar://*************:30164"  #pulsar-proxy dev-es k8s服务 namespace=pulsar
  consumer:
    topic_report_summary: "persistent://public/default/taskd-report-summary-requests"
    subscription_name: "taskd-summary-sub"

llm:
  default_provider: "volcengine_ark" # 默认使用的 LLM 提供商名称
  providers:
    volcengine_ark: # 提供商的唯一标识名 (例如火山方舟)
      api_key: "cdb5ba17-1758-4d11-be9b-379c9453fb16"
      api_keys: # API Key池，支持多个Key轮询
        - "cdb5ba17-1758-4d11-be9b-379c9453fb16"
        - "08eb4227-fd5c-49cc-a0e2-a9c176a4dfa2"
        - "ce2ded6b-dc20-478b-b138-657b33419a96"
        - "7bdf1086-334c-46c7-9511-e4ebeb1ad168"
        - "5d0268a3-f710-4a97-ad7d-834584ffeb92"
      base_url: "https://ark.cn-beijing.volces.com/api/v3" # 火山方舟 OpenAI 兼容 API 地址
      models: # 模型别名到实际模型 ID 的映射
        "doubao-1-5-pro-32k-250115": "doubao-1-5-pro-32k-250115"
        "report-summarizer": "doubao-1-5-pro-32k-250115" # 示例，请使用火山方舟上正确的模型ID
        "deepseek-v3-250324": "deepseek-v3-250324" # Entity Extraction Agent使用的正确模型ID
        # 例如： "report-summarizer": "ep-20240123150019-g21er" (方舟上的模型发布ID)
      default_model_alias: "report-summarizer" # 如果请求中未指定模型，则使用此别名对应的模型
      request_timeout_seconds: 300
      max_concurrent_requests: 10
    # openai_official: # 另一个示例提供商 (例如官方 OpenAI)
    #   # api_key: "YOUR_OPENAI_API_KEY" # 环境变量: LLM_PROVIDERS_OPENAI_OFFICIAL_API_KEY
    #   base_url: "https://api.openai.com/v1"
    #   models:
    #     "gpt-3.5-turbo": "gpt-3.5-turbo"
    #     "gpt-4": "gpt-4"
    #   default_model_alias: "gpt-3.5-turbo"
    #   request_timeout_seconds: 60
    #   max_concurrent_requests: 5

prompts:
#  report_summary:
#    sys_prompt: |
#      你是一位报告分析专家。请对给定的报告内容进行客观总结，要求：
#      - 提取报告核心内容和关键结论
#      - 保持客观、准确
#      - 总结长度控制在50字以内
#      - 仅做内容总结，不加入任何建议或评价
#      输出语言：{{.language}}
#    user_prompt: |
#      #### 企业名称
#      {{.target_company}}
#
#      需要总结的报告内容如下：
#      {{.text}}