package store

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// DBTX is an interface for working with sqlx.DB and sqlx.Tx
type DBTX interface {
	sqlx.ExtContext
	sqlx.PreparerContext
	QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row
}

var PostgresDB *sqlx.DB
var ProfileDB *sqlx.DB // Dedicated connection for profile services using ovs-profile schema

// PostgresConfig represents PostgreSQL configuration
type PostgresConfig struct {
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	User            string `mapstructure:"user"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	SSLMode         string `mapstructure:"ssl_mode"`
	TimeoutSeconds  int    `mapstructure:"timeout_seconds"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// GetDSN returns the PostgreSQL connection string
func (cfg *PostgresConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Host, cfg.Port, cfg.User, cfg.Password, cfg.Database, cfg.SSLMode)
}

// GetDBTimeout returns the database timeout duration
func (cfg *PostgresConfig) GetDBTimeout() time.Duration {
	return time.Duration(cfg.TimeoutSeconds) * time.Second
}

// InitPostgres initializes PostgreSQL connection
func InitPostgres(cfg PostgresConfig) error {
	if cfg.Host == "" {
		utils.Log.Error("PostgreSQL host is not configured")
		return fmt.Errorf("PostgreSQL host is not configured")
	}

	// Set defaults
	if cfg.Port == 0 {
		cfg.Port = 5432
	}
	if cfg.SSLMode == "" {
		cfg.SSLMode = "disable"
	}
	if cfg.TimeoutSeconds == 0 {
		cfg.TimeoutSeconds = 10
	}
	if cfg.MaxOpenConns == 0 {
		cfg.MaxOpenConns = 25
	}
	if cfg.MaxIdleConns == 0 {
		cfg.MaxIdleConns = 5
	}
	if cfg.ConnMaxLifetime == 0 {
		cfg.ConnMaxLifetime = 300 // 5 minutes
	}

	dsn := cfg.GetDSN()
	utils.Log.Infof("Connecting to PostgreSQL: %s:%d/%s", cfg.Host, cfg.Port, cfg.Database)

	db, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		utils.Log.Errorf("Failed to connect to PostgreSQL: %v", err)
		return fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// Test connection
	if err := db.Ping(); err != nil {
		utils.Log.Errorf("Failed to ping PostgreSQL: %v", err)
		return fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	// Set default schema to taskd
	if _, err := db.Exec("SET search_path TO taskd, public"); err != nil {
		utils.Log.Warnf("Failed to set search_path to taskd schema: %v", err)
	}

	PostgresDB = db
	utils.Log.Infof("Successfully connected to PostgreSQL database: %s", cfg.Database)

	// Initialize dedicated profile connection with ovs-profile schema
	if err := initProfileConnection(cfg); err != nil {
		utils.Log.Warnf("Failed to initialize profile database connection: %v", err)
	}

	return nil
}

// initProfileConnection initializes a dedicated connection for profile services
func initProfileConnection(cfg PostgresConfig) error {
	dsn := cfg.GetDSN()
	
	db, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		return fmt.Errorf("failed to connect to PostgreSQL for profile services: %w", err)
	}

	// Configure connection pool (smaller pool for profile services)
	db.SetMaxOpenConns(cfg.MaxOpenConns / 2) // Use half the connections for profile services
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// Test connection
	if err := db.Ping(); err != nil {
		db.Close()
		return fmt.Errorf("failed to ping PostgreSQL for profile services: %w", err)
	}

	// Set search_path to ovs-profile schema for profile operations
	if _, err := db.Exec(`SET search_path TO "ovs-profile", public`); err != nil {
		utils.Log.Errorf("Failed to set search_path to ovs-profile schema: %v", err)
		db.Close()
		return fmt.Errorf("failed to set ovs-profile search_path: %w", err)
	}

	ProfileDB = db
	utils.Log.Info("Successfully initialized profile database connection with ovs-profile schema")
	
	return nil
}

// ClosePostgres closes the PostgreSQL connection
func ClosePostgres() {
	if PostgresDB != nil {
		if err := PostgresDB.Close(); err != nil {
			utils.Log.Errorf("Failed to close PostgreSQL connection: %v", err)
		} else {
			utils.Log.Info("PostgreSQL connection closed")
		}
	}
	
	if ProfileDB != nil {
		if err := ProfileDB.Close(); err != nil {
			utils.Log.Errorf("Failed to close Profile PostgreSQL connection: %v", err)
		} else {
			utils.Log.Info("Profile PostgreSQL connection closed")
		}
	}
}

// RunMigration runs database migrations from schema.sql
func RunMigration() error {
	if PostgresDB == nil {
		return fmt.Errorf("PostgreSQL connection is not initialized")
	}

	// Read schema file
	schemaPath := filepath.Join("internal", "store", "schema.sql")
	schemaSQL, err := os.ReadFile(schemaPath)
	if err != nil {
		utils.Log.Errorf("Failed to read schema file: %v", err)
		return fmt.Errorf("failed to read schema file: %w", err)
	}

	// Execute schema
	if _, err := PostgresDB.Exec(string(schemaSQL)); err != nil {
		utils.Log.Errorf("Failed to execute schema: %v", err)
		return fmt.Errorf("failed to execute schema: %w", err)
	}

	utils.Log.Info("Database migration completed successfully")
	return nil
}

// GetDB returns the PostgreSQL database instance
func GetDB() *sqlx.DB {
	if PostgresDB == nil {
		utils.Log.Fatal("PostgreSQL connection is not initialized. Please call InitPostgres first.")
		return nil
	}
	return PostgresDB
}

// GetProfileDB returns the PostgreSQL database instance configured for profile services
func GetProfileDB() *sqlx.DB {
	if ProfileDB == nil {
		utils.Log.Warn("Profile PostgreSQL connection is not initialized. Falling back to main connection.")
		return GetDB()
	}
	return ProfileDB
}

// CheckConnection checks if the database connection is healthy
func CheckConnection() error {
	if PostgresDB == nil {
		return fmt.Errorf("PostgreSQL connection is not initialized")
	}

	if err := PostgresDB.Ping(); err != nil {
		return fmt.Errorf("PostgreSQL connection is not healthy: %w", err)
	}

	return nil
}
