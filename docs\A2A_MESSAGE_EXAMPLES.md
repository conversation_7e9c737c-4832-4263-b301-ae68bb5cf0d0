# A2A协议消息示例

## 概述

本文档提供招投标Agent间A2A协议通信的完整消息示例，包括请求、响应、错误处理等场景。

## Agent 1: 数据获取Agent 消息示例

### 1.1 获取招投标数据

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_data_001",
  "method": "agent/execute",
  "params": {
    "skill_id": "retrieve_tender_data",
    "input": {
      "tender_id": "TENDER_2024_AI_GOV_001",
      "language": "chinese",
      "include_metadata": true,
      "fields": ["title", "content", "deadline", "budget", "requirements"]
    },
    "context": {
      "user_id": "user_12345",
      "company_id": "comp_abc",
      "trace_id": "trace_xyz789",
      "language": "chinese"
    }
  }
}
```

#### 成功响应示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_data_001",
  "result": {
    "task_id": "task_1721373600123",
    "status": "completed",
    "output": {
      "tender_data": {
        "id": "TENDER_2024_AI_GOV_001",
        "title": "某市政府人工智能服务平台建设项目",
        "content": "项目旨在建设一套集成AI服务能力的政务平台，包括智能客服、文档处理、数据分析等功能...",
        "deadline": "2024-08-15T16:00:00Z",
        "budget": {
          "total": "5000000",
          "currency": "CNY",
          "budget_type": "maximum"
        },
        "requirements": {
          "technical": ["AI算法能力", "云原生架构", "高可用性设计"],
          "business": ["7x24小时服务", "用户培训", "运维保障"],
          "compliance": ["等保三级", "信创要求", "数据安全"]
        },
        "contact_info": {
          "organization": "某市政务服务局",
          "contact_person": "张先生",
          "phone": "010-12345678"
        }
      },
      "validation_status": "valid",
      "metadata": {
        "source": "mongodb://cluster.example.com:27017/overseas",
        "timestamp": "2024-07-19T10:30:00Z",
        "data_quality": 0.95,
        "field_count": 8,
        "schema": "taskd"
      }
    },
    "metadata": {
      "execution_time": 850,
      "tokens_used": 0,
      "model_version": null,
      "agent_version": "1.0.0"
    }
  }
}
```

#### 错误响应示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_data_001",
  "error": {
    "code": -40003,
    "message": "招投标数据不存在",
    "data": {
      "error_type": "业务错误",
      "tender_id": "TENDER_2024_INVALID",
      "retry_after": null,
      "agent": "bidding-data-retrieval",
      "timestamp": "2024-07-19T10:30:00Z",
      "suggestions": ["检查tender_id格式", "确认数据是否已导入"]
    }
  }
}
```

## Agent 2: AI摘要Agent 消息示例

### 2.1 生成招投标摘要

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_summary_001",
  "method": "agent/execute",
  "params": {
    "skill_id": "generate_tender_summary",
    "input": {
      "tender_data": {
        "id": "TENDER_2024_AI_GOV_001",
        "title": "某市政府人工智能服务平台建设项目",
        "content": "项目旨在建设一套集成AI服务能力的政务平台...",
        "deadline": "2024-08-15T16:00:00Z"
      },
      "language": "chinese",
      "summary_type": "detailed",
      "focus_areas": ["技术要求", "预算范围", "交付时间", "评分标准"]
    },
    "context": {
      "user_id": "user_12345",
      "trace_id": "trace_xyz789"
    }
  }
}
```

#### 成功响应示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_summary_001",
  "result": {
    "task_id": "task_1721373650456",
    "status": "completed",
    "output": {
      "summary": {
        "title": "某市政府人工智能服务平台建设项目",
        "new_title": "政务AI服务平台：智能客服+文档处理+数据分析一体化解决方案",
        "deadline": "2024-08-15",
        "summary_text": "本项目为某市政府打造集成化AI政务服务平台，核心功能包括智能客服系统、自动化文档处理、政务数据智能分析等。项目预算500万元，要求7x24小时稳定运行，需满足等保三级及信创要求。技术路线需采用云原生架构，具备高可用性设计。投标方需具备AI算法自研能力和政务项目经验。",
        "key_requirements": [
          "AI算法自主研发能力",
          "云原生微服务架构",
          "等保三级安全认证",
          "信创产品兼容性",
          "7x24小时运维保障",
          "用户培训和技术支持"
        ],
        "budget_info": {
          "amount": "500万元",
          "type": "最高限价",
          "payment_terms": "按里程碑分期支付"
        },
        "timeline": {
          "bidding_deadline": "2024-08-15",
          "project_duration": "12个月",
          "key_milestones": ["需求确认", "系统开发", "测试验收", "上线运行"]
        }
      },
      "confidence_score": 0.92,
      "processing_metadata": {
        "model_used": "deepseek-v3-250324",
        "tokens_consumed": 1250,
        "processing_time": 3.2
      }
    },
    "metadata": {
      "execution_time": 3250,
      "tokens_used": 1250,
      "model_version": "deepseek-v3-250324",
      "agent_version": "1.0.0"
    }
  }
}
```

## Agent 3: 需求分析与搜索Agent 消息示例

### 3.1 分析需求并搜索背景信息

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_analysis_001",
  "method": "agent/execute",
  "params": {
    "skill_id": "analyze_and_search",
    "input": {
      "summary_data": {
        "title": "政务AI服务平台：智能客服+文档处理+数据分析一体化解决方案",
        "summary_text": "本项目为某市政府打造集成化AI政务服务平台...",
        "key_requirements": ["AI算法自主研发能力", "云原生微服务架构"]
      },
      "search_depth": "normal",
      "search_domains": ["technology", "market", "government"],
      "max_results": 15
    },
    "context": {
      "user_id": "user_12345",
      "trace_id": "trace_xyz789"
    }
  }
}
```

#### 成功响应示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_analysis_001",
  "result": {
    "task_id": "task_1721373700789",
    "status": "completed",
    "output": {
      "requirement_analysis": {
        "key_requirements": [
          "人工智能服务平台",
          "智能客服系统",
          "文档智能处理",
          "政务数据分析",
          "云原生架构"
        ],
        "technical_keywords": [
          "AI算法", "自然语言处理", "机器学习", "微服务架构",
          "容器化部署", "DevOps", "API网关", "服务网格"
        ],
        "business_keywords": [
          "政务服务", "数字政府", "政府采购", "信创产品",
          "等保三级", "运维保障", "用户培训"
        ]
      },
      "search_results": [
        {
          "title": "2024年中国政务AI服务市场发展报告",
          "url": "https://www.example-research.com/gov-ai-2024",
          "snippet": "2024年政务AI服务市场规模达到150亿元，智能客服、文档处理、数据分析成为三大核心应用场景...",
          "source": "行业研究报告",
          "relevance_score": 0.95,
          "published_date": "2024-06-15"
        },
        {
          "title": "政府AI采购指南：技术要求与评估标准",
          "url": "https://www.gov-procurement.org/ai-guidelines",
          "snippet": "政府AI项目采购需重点关注算法透明度、数据安全、服务可用性等关键指标...",
          "source": "政府采购网",
          "relevance_score": 0.88,
          "published_date": "2024-05-20"
        },
        {
          "title": "云原生政务系统架构设计最佳实践",
          "url": "https://tech.example.com/cloud-native-gov",
          "snippet": "基于Kubernetes的政务系统需要考虑安全性、可扩展性、多租户隔离等特殊要求...",
          "source": "技术博客",
          "relevance_score": 0.82,
          "published_date": "2024-04-10"
        }
      ],
      "search_summary": "通过分析15个相关资源，发现政务AI服务平台建设是当前热点，市场需求旺盛。技术路线以云原生+AI算法为主流，重点关注安全合规和服务稳定性。主要厂商包括阿里云、腾讯云、华为云等，竞争激烈。项目成功关键在于技术实力、政务经验和服务保障能力。",
      "search_metadata": {
        "total_results": 15,
        "search_time": 12.5,
        "search_engines_used": ["exa", "bing", "google"]
      }
    },
    "metadata": {
      "execution_time": 15000,
      "tokens_used": 800,
      "model_version": "deepseek-v3-250324",
      "agent_version": "1.0.0"
    }
  }
}
```

## Agent 4: 报告生成Agent 消息示例

### 4.1 生成最终分析报告

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_report_001",
  "method": "agent/execute",
  "params": {
    "skill_id": "generate_final_report",
    "input": {
      "search_results": [
        {
          "title": "2024年中国政务AI服务市场发展报告",
          "snippet": "2024年政务AI服务市场规模达到150亿元..."
        }
      ],
      "summary_data": {
        "title": "政务AI服务平台：智能客服+文档处理+数据分析一体化解决方案",
        "budget_info": {"amount": "500万元"}
      },
      "requirement_analysis": {
        "key_requirements": ["人工智能服务平台", "智能客服系统"],
        "technical_keywords": ["AI算法", "云原生架构"]
      },
      "report_template": "standard",
      "target_audience": "business"
    },
    "context": {
      "user_id": "user_12345",
      "trace_id": "trace_xyz789"
    }
  }
}
```

#### 成功响应示例
```json
{
  "jsonrpc": "2.0",
  "id": "req_report_001",
  "result": {
    "task_id": "task_1721373750123",
    "status": "completed",
    "output": {
      "final_report": {
        "executive_summary": "某市政府AI服务平台建设项目预算500万元，旨在构建集智能客服、文档处理、数据分析于一体的政务服务平台。项目技术要求高，需满足等保三级和信创要求，市场竞争激烈但机会较大。建议重点关注技术实力展示和政务项目经验证明。",
        
        "project_overview": "本项目是某市政府数字化转型的重点工程，通过AI技术提升政务服务效率和用户体验。项目包含三大核心模块：1) 智能客服系统，提供7x24小时在线服务；2) 文档智能处理，实现自动化审批流程；3) 政务数据分析，支持决策支持和趋势预测。项目采用云原生架构，要求高可用性和安全性。",
        
        "market_analysis": "根据最新市场调研，2024年中国政务AI服务市场规模达150亿元，年增长率超30%。智能客服、文档处理、数据分析是三大核心应用场景，占据市场份额的60%以上。政府对AI技术的接受度不断提高，相关政策支持力度加大，为项目成功提供了良好的外部环境。",
        
        "technical_background": "政务AI平台建设需要融合多种前沿技术：1) 自然语言处理技术用于智能客服和文档理解；2) 机器学习算法实现数据分析和预测；3) 云原生架构保障系统弹性和可扩展性；4) 微服务设计支持模块化部署和维护。技术难点在于政务场景的特殊性，需要平衡功能性、安全性和合规性。",
        
        "competitive_landscape": "市场主要竞争者包括：1) 云服务厂商：阿里云、腾讯云、华为云，具备完整的云+AI解决方案；2) AI算法公司：百度、商汤、旷视等，在AI技术方面有优势；3) 系统集成商：浪潮、东软、太极等，在政务项目经验丰富。竞争关键在于技术实力、项目经验、服务保障三个维度。",
        
        "recommendations": [
          "重点突出AI算法的自主研发能力和技术先进性",
          "详细展示云原生架构设计和高可用性保障方案",
          "充分证明政务项目实施经验和成功案例",
          "明确等保三级和信创要求的具体落实措施",
          "提供详细的7x24小时运维保障和用户培训方案",
          "制定分阶段实施计划，降低项目风险",
          "准备完整的技术文档和演示系统"
        ],
        
        "risk_assessment": [
          {
            "risk": "技术实现难度高，AI算法效果不达预期",
            "impact": "高",
            "mitigation": "提前进行POC验证，选择成熟的AI技术栈，建立技术评估机制"
          },
          {
            "risk": "安全合规要求严格，可能影响功能实现",
            "impact": "中",
            "mitigation": "早期引入安全专家，采用安全by design理念，与监管部门提前沟通"
          },
          {
            "risk": "竞争激烈，价格压力大",
            "impact": "中",
            "mitigation": "差异化技术方案，强调长期价值，建立成本优势"
          }
        ]
      },
      "report_metadata": {
        "generation_time": "2024-07-19T10:45:00Z",
        "word_count": 1580,
        "confidence_score": 0.89,
        "sources_count": 15,
        "report_version": "1.0"
      },
      "appendices": {
        "source_references": [
          {
            "id": 1,
            "title": "2024年中国政务AI服务市场发展报告",
            "url": "https://www.example-research.com/gov-ai-2024",
            "relevance": "高"
          }
        ],
        "data_tables": [
          {
            "title": "政务AI市场规模趋势",
            "data": {
              "2022": "100亿元",
              "2023": "125亿元", 
              "2024": "150亿元"
            }
          }
        ]
      }
    },
    "metadata": {
      "execution_time": 8500,
      "tokens_used": 2100,
      "model_version": "deepseek-v3-250324",
      "agent_version": "1.0.0"
    }
  }
}
```

## 错误处理示例

### 5.1 AI服务错误
```json
{
  "jsonrpc": "2.0",
  "id": "req_summary_001",
  "error": {
    "code": -40002,
    "message": "AI服务暂时不可用",
    "data": {
      "error_type": "AI错误",
      "retry_after": 60,
      "agent": "ai-summary",
      "timestamp": "2024-07-19T10:30:00Z",
      "details": {
        "service": "deepseek-v3-250324",
        "error_code": "rate_limit_exceeded",
        "suggested_action": "请稍后重试或联系管理员"
      }
    }
  }
}
```

### 5.2 参数验证错误
```json
{
  "jsonrpc": "2.0",
  "id": "req_data_001",
  "error": {
    "code": -40004,
    "message": "参数验证失败",
    "data": {
      "error_type": "业务错误",
      "agent": "bidding-data-retrieval",
      "timestamp": "2024-07-19T10:30:00Z",
      "validation_errors": [
        {
          "field": "tender_id",
          "error": "格式不正确，应为TENDER_YYYY_XXX格式"
        },
        {
          "field": "language",
          "error": "不支持的语言类型，仅支持chinese或english"
        }
      ]
    }
  }
}
```

## Agent间协作流程示例

### 6.1 完整处理流程
```bash
# 步骤1: 数据获取
curl -X POST http://taskd-service:8601/agents/bidding-data-retrieval \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_001",
    "method": "agent/execute",
    "params": {
      "skill_id": "retrieve_tender_data",
      "input": {"tender_id": "TENDER_2024_AI_GOV_001"}
    }
  }'

# 步骤2: AI摘要生成 (使用步骤1的输出)
curl -X POST http://taskd-service:8601/agents/ai-summary \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_002", 
    "method": "agent/execute",
    "params": {
      "skill_id": "generate_tender_summary",
      "input": {
        "tender_data": { /* 步骤1的输出 */ }
      }
    }
  }'

# 步骤3: 需求分析与搜索 (使用步骤2的输出)
curl -X POST http://taskd-service:8601/agents/requirement-analysis-search \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_003",
    "method": "agent/execute", 
    "params": {
      "skill_id": "analyze_and_search",
      "input": {
        "summary_data": { /* 步骤2的输出 */ }
      }
    }
  }'

# 步骤4: 报告生成 (使用前面所有步骤的输出)
curl -X POST http://taskd-service:8601/agents/report-generation \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_004",
    "method": "agent/execute",
    "params": {
      "skill_id": "generate_final_report",
      "input": {
        "search_results": { /* 步骤3的搜索结果 */ },
        "summary_data": { /* 步骤2的摘要数据 */ },
        "requirement_analysis": { /* 步骤3的需求分析 */ }
      }
    }
  }'
```

## 健康检查和Agent Card示例

### 7.1 健康检查
```bash
curl http://taskd-service:8601/agents/bidding-data-retrieval/health
```

响应：
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 3600,
  "dependencies": {
    "mongodb": "connected",
    "ai_service": "available"
  },
  "performance": {
    "avg_response_time": 850,
    "requests_per_minute": 45,
    "error_rate": 0.02
  }
}
```

### 7.2 获取Agent Card
```bash
curl http://taskd-service:8601/agents/bidding-data-retrieval/.well-known/agent.json
```

响应即为对应Agent的Agent Card JSON定义。