# 招投标Agent API使用示例

## 概述

本文档提供招投标Agent系统的完整API使用示例，包括所有4个Agent的调用方法和响应格式。

## 快速开始

### 1. 查看可用Agent列表

```bash
curl -X GET http://localhost:8601/agents
```

**响应示例：**
```json
{
  "agents": [
    {
      "id": "bidding-data-retrieval",
      "agent_card": {
        "name": "Bidding Data Retrieval Agent",
        "description": "获取和预处理招投标数据",
        "version": "1.0.0"
      },
      "status": {
        "status": "healthy",
        "uptime": 3600
      }
    }
  ],
  "count": 4,
  "timestamp": "2024-07-19T10:30:00Z"
}
```

### 2. 访问Agent文档界面

在浏览器中访问：
```
http://localhost:8601/agents/docs
```

这将显示一个可视化的Agent文档界面，包含所有Agent的详细信息。

## Agent详细使用示例

### Agent 1: 数据获取Agent

#### 获取招投标数据

**请求：**
```bash
curl -X POST http://localhost:8601/agents/bidding-data-retrieval \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_123" \
  -H "X-Company-ID: company_456" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_001",
    "method": "agent/execute",
    "params": {
      "skill_id": "retrieve_tender_data",
      "input": {
        "tender_id": "TENDER_2024_AI_GOV_001",
        "language": "chinese",
        "include_metadata": true,
        "fields": ["title", "content", "deadline", "budget"]
      }
    }
  }'
```

**响应：**
```json
{
  "jsonrpc": "2.0",
  "id": "req_001",
  "result": {
    "task_id": "task_1721373600123",
    "status": "completed",
    "output": {
      "tender_data": {
        "id": "TENDER_2024_AI_GOV_001",
        "title": "某市政府人工智能服务平台建设项目",
        "content": "项目旨在建设一套集成AI服务能力的政务平台...",
        "deadline": "2024-08-15T16:00:00Z",
        "budget": {
          "total": "5000000",
          "currency": "CNY",
          "budget_type": "maximum"
        }
      },
      "validation_status": "valid",
      "metadata": {
        "source": "mongodb://cluster.example.com:27017/overseas",
        "timestamp": "2024-07-19T10:30:00Z",
        "data_quality": 0.95,
        "field_count": 8
      }
    },
    "metadata": {
      "execution_time": 850,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  }
}
```

#### 获取Agent Card

**请求：**
```bash
curl -X GET http://localhost:8601/agents/bidding-data-retrieval/.well-known/agent.json
```

#### 健康检查

**请求：**
```bash
curl -X GET http://localhost:8601/agents/bidding-data-retrieval/health
```

### Agent 2: AI摘要Agent

#### 生成招投标摘要

**请求：**
```bash
curl -X POST http://localhost:8601/agents/ai-summary \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_123" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_002",
    "method": "agent/execute",
    "params": {
      "skill_id": "generate_tender_summary",
      "input": {
        "tender_data": {
          "id": "TENDER_2024_AI_GOV_001",
          "title": "某市政府人工智能服务平台建设项目",
          "content": "项目旨在建设一套集成AI服务能力的政务平台，包括智能客服、文档处理、数据分析等功能...",
          "deadline": "2024-08-15T16:00:00Z",
          "budget": {
            "total": "5000000",
            "currency": "CNY"
          }
        },
        "language": "chinese",
        "summary_type": "detailed",
        "focus_areas": ["技术要求", "预算范围", "交付时间"]
      }
    }
  }'
```

**响应：**
```json
{
  "jsonrpc": "2.0",
  "id": "req_002",
  "result": {
    "task_id": "task_1721373650456",
    "status": "completed",
    "output": {
      "summary": {
        "title": "某市政府人工智能服务平台建设项目",
        "new_title": "政务AI服务平台：智能客服+文档处理+数据分析一体化解决方案",
        "deadline": "2024-08-15",
        "summary_text": "本项目为某市政府打造集成化AI政务服务平台，核心功能包括智能客服系统、自动化文档处理、政务数据智能分析等...",
        "key_requirements": [
          "AI算法自主研发能力",
          "云原生微服务架构",
          "等保三级安全认证",
          "7x24小时运维保障"
        ],
        "budget_info": {
          "amount": "500万元",
          "type": "最高限价",
          "payment_terms": "按里程碑分期支付"
        }
      },
      "confidence_score": 0.92,
      "processing_metadata": {
        "model_used": "deepseek-v3-250324",
        "tokens_consumed": 1250,
        "processing_time": 3.2
      }
    },
    "metadata": {
      "execution_time": 3250,
      "tokens_used": 1250,
      "agent_version": "1.0.0"
    }
  }
}
```

### Agent 3: 需求分析与搜索Agent

#### 分析需求并搜索背景信息

**请求：**
```bash
curl -X POST http://localhost:8601/agents/requirement-analysis-search \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_123" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_003",
    "method": "agent/execute",
    "params": {
      "skill_id": "analyze_and_search",
      "input": {
        "summary_data": {
          "title": "政务AI服务平台：智能客服+文档处理+数据分析一体化解决方案",
          "summary_text": "本项目为某市政府打造集成化AI政务服务平台...",
          "key_requirements": ["AI算法自主研发能力", "云原生微服务架构"]
        },
        "search_depth": "normal",
        "search_domains": ["technology", "market", "government"],
        "max_results": 10
      }
    }
  }'
```

**响应：**
```json
{
  "jsonrpc": "2.0",
  "id": "req_003",
  "result": {
    "task_id": "task_1721373700789",
    "status": "completed",
    "output": {
      "requirement_analysis": {
        "key_requirements": [
          "人工智能服务平台",
          "智能客服系统",
          "文档智能处理",
          "政务数据分析"
        ],
        "technical_keywords": [
          "AI算法", "自然语言处理", "机器学习", "微服务架构",
          "容器化部署", "API网关"
        ],
        "business_keywords": [
          "政务服务", "数字政府", "政府采购", "信创产品"
        ]
      },
      "search_results": [
        {
          "title": "2024年中国政务AI服务市场发展报告",
          "url": "https://www.example-research.com/gov-ai-2024",
          "snippet": "2024年政务AI服务市场规模达到150亿元，智能客服、文档处理、数据分析成为三大核心应用场景...",
          "source": "行业研究报告",
          "relevance_score": 0.95,
          "published_date": "2024-06-15"
        }
      ],
      "search_summary": "通过分析10个相关资源，发现政务AI服务平台建设是当前热点，市场需求旺盛。技术路线以云原生+AI算法为主流...",
      "search_metadata": {
        "total_results": 10,
        "search_time": 12.5,
        "search_engines_used": ["mock_search_engine"]
      }
    },
    "metadata": {
      "execution_time": 15000,
      "tokens_used": 800,
      "agent_version": "1.0.0"
    }
  }
}
```

### Agent 4: 报告生成Agent

#### 生成最终分析报告

**请求：**
```bash
curl -X POST http://localhost:8601/agents/report-generation \
  -H "Content-Type: application/json" \
  -H "X-User-ID: user_123" \
  -d '{
    "jsonrpc": "2.0",
    "id": "req_004",
    "method": "agent/execute",
    "params": {
      "skill_id": "generate_final_report",
      "input": {
        "search_results": [
          {
            "title": "2024年中国政务AI服务市场发展报告",
            "snippet": "2024年政务AI服务市场规模达到150亿元...",
            "relevance_score": 0.95
          }
        ],
        "summary_data": {
          "title": "政务AI服务平台建设项目",
          "summary_text": "本项目为政府打造AI服务平台...",
          "budget_info": {"amount": "500万元"}
        },
        "requirement_analysis": {
          "key_requirements": ["人工智能服务平台", "智能客服系统"],
          "technical_keywords": ["AI算法", "云原生架构"]
        },
        "report_template": "standard",
        "target_audience": "business"
      }
    }
  }'
```

**响应：**
```json
{
  "jsonrpc": "2.0",
  "id": "req_004",
  "result": {
    "task_id": "task_1721373750123",
    "status": "completed",
    "output": {
      "final_report": {
        "executive_summary": "某市政府AI服务平台建设项目预算500万元，旨在构建集智能客服、文档处理、数据分析于一体的政务服务平台...",
        "project_overview": "本项目是某市政府数字化转型的重点工程...",
        "market_analysis": "根据最新市场调研，2024年中国政务AI服务市场规模达150亿元...",
        "technical_background": "政务AI平台建设需要融合多种前沿技术...",
        "competitive_landscape": "市场主要竞争者包括阿里云、腾讯云、华为云等...",
        "recommendations": [
          "重点突出AI算法的自主研发能力和技术先进性",
          "详细展示云原生架构设计和高可用性保障方案",
          "充分证明政务项目实施经验和成功案例"
        ],
        "risk_assessment": [
          {
            "risk": "技术实现难度高，AI算法效果不达预期",
            "impact": "高",
            "mitigation": "提前进行POC验证，选择成熟的AI技术栈"
          }
        ]
      },
      "report_metadata": {
        "generation_time": "2024-07-19T10:45:00Z",
        "word_count": 1580,
        "confidence_score": 0.89,
        "sources_count": 10,
        "report_version": "1.0"
      },
      "appendices": {
        "source_references": [
          {
            "id": 1,
            "title": "2024年中国政务AI服务市场发展报告",
            "url": "https://www.example-research.com/gov-ai-2024",
            "relevance": "95%"
          }
        ]
      }
    },
    "metadata": {
      "execution_time": 8500,
      "tokens_used": 2100,
      "agent_version": "1.0.0"
    }
  }
}
```

## 完整工作流程示例

### 步骤1: 执行完整的招投标分析流程

```bash
#!/bin/bash

# 1. 获取招投标数据
echo "步骤1: 获取招投标数据"
DATA_RESULT=$(curl -s -X POST http://localhost:8601/agents/bidding-data-retrieval \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "flow_001",
    "method": "agent/execute",
    "params": {
      "skill_id": "retrieve_tender_data",
      "input": {
        "tender_id": "TENDER_2024_AI_GOV_001",
        "language": "chinese"
      }
    }
  }')

# 2. 生成AI摘要
echo "步骤2: 生成AI摘要"
SUMMARY_RESULT=$(curl -s -X POST http://localhost:8601/agents/ai-summary \
  -H "Content-Type: application/json" \
  -d "{
    \"jsonrpc\": \"2.0\",
    \"id\": \"flow_002\",
    \"method\": \"agent/execute\",
    \"params\": {
      \"skill_id\": \"generate_tender_summary\",
      \"input\": {
        \"tender_data\": $(echo $DATA_RESULT | jq '.result.output.tender_data'),
        \"language\": \"chinese\"
      }
    }
  }")

# 3. 需求分析与搜索
echo "步骤3: 需求分析与搜索"
ANALYSIS_RESULT=$(curl -s -X POST http://localhost:8601/agents/requirement-analysis-search \
  -H "Content-Type: application/json" \
  -d "{
    \"jsonrpc\": \"2.0\",
    \"id\": \"flow_003\",
    \"method\": \"agent/execute\",
    \"params\": {
      \"skill_id\": \"analyze_and_search\",
      \"input\": {
        \"summary_data\": $(echo $SUMMARY_RESULT | jq '.result.output.summary')
      }
    }
  }")

# 4. 生成最终报告
echo "步骤4: 生成最终报告"
REPORT_RESULT=$(curl -s -X POST http://localhost:8601/agents/report-generation \
  -H "Content-Type: application/json" \
  -d "{
    \"jsonrpc\": \"2.0\",
    \"id\": \"flow_004\",
    \"method\": \"agent/execute\",
    \"params\": {
      \"skill_id\": \"generate_final_report\",
      \"input\": {
        \"search_results\": $(echo $ANALYSIS_RESULT | jq '.result.output.search_results'),
        \"summary_data\": $(echo $SUMMARY_RESULT | jq '.result.output.summary'),
        \"requirement_analysis\": $(echo $ANALYSIS_RESULT | jq '.result.output.requirement_analysis')
      }
    }
  }")

echo "工作流程完成！"
echo "最终报告: $REPORT_RESULT"
```

## 错误处理示例

### 常见错误响应

#### 1. Agent不存在
```json
{
  "jsonrpc": "2.0",
  "id": "req_001",
  "error": {
    "code": -32601,
    "message": "Agent not found: non-existent-agent",
    "data": {
      "error_type": "业务错误",
      "timestamp": "2024-07-19T10:30:00Z"
    }
  }
}
```

#### 2. 参数验证失败
```json
{
  "jsonrpc": "2.0",
  "id": "req_001",
  "error": {
    "code": -40004,
    "message": "参数验证失败",
    "data": {
      "error_type": "业务错误",
      "validation_errors": [
        {
          "field": "tender_id",
          "error": "格式不正确，应为TENDER_YYYY_XXX格式"
        }
      ]
    }
  }
}
```

#### 3. AI服务错误
```json
{
  "jsonrpc": "2.0",
  "id": "req_001",
  "error": {
    "code": -40002,
    "message": "AI服务暂时不可用",
    "data": {
      "error_type": "AI错误",
      "retry_after": 60,
      "details": {
        "service": "deepseek-v3-250324",
        "error_code": "rate_limit_exceeded"
      }
    }
  }
}
```

## 监控和调试

### 1. 检查Agent状态
```bash
# 获取所有Agent状态
curl -X GET http://localhost:8601/agents

# 检查特定Agent健康状态
curl -X GET http://localhost:8601/agents/bidding-data-retrieval/health
```

### 2. 查看Agent性能指标

访问Agent文档界面查看实时性能指标：
```
http://localhost:8601/agents/docs
```

### 3. 日志追踪

在请求头中添加追踪ID：
```bash
curl -X POST http://localhost:8601/agents/ai-summary \
  -H "X-Trace-ID: trace_12345" \
  -H "X-Request-ID: req_67890" \
  ...
```

## Python客户端示例

```python
import requests
import json

class BiddingAgentClient:
    def __init__(self, base_url="http://localhost:8601"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'X-User-ID': 'your_user_id',
            'X-Company-ID': 'your_company_id'
        })
    
    def retrieve_tender_data(self, tender_id, language="chinese"):
        """获取招投标数据"""
        request = {
            "jsonrpc": "2.0",
            "id": f"req_{int(time.time())}",
            "method": "agent/execute",
            "params": {
                "skill_id": "retrieve_tender_data",
                "input": {
                    "tender_id": tender_id,
                    "language": language,
                    "include_metadata": True
                }
            }
        }
        
        response = self.session.post(
            f"{self.base_url}/agents/bidding-data-retrieval",
            json=request
        )
        
        return response.json()
    
    def generate_summary(self, tender_data, language="chinese"):
        """生成AI摘要"""
        request = {
            "jsonrpc": "2.0",
            "id": f"req_{int(time.time())}",
            "method": "agent/execute",
            "params": {
                "skill_id": "generate_tender_summary",
                "input": {
                    "tender_data": tender_data,
                    "language": language,
                    "summary_type": "detailed"
                }
            }
        }
        
        response = self.session.post(
            f"{self.base_url}/agents/ai-summary",
            json=request
        )
        
        return response.json()

# 使用示例
client = BiddingAgentClient()
data_result = client.retrieve_tender_data("TENDER_2024_001")
if 'result' in data_result:
    summary_result = client.generate_summary(data_result['result']['output']['tender_data'])
    print(json.dumps(summary_result, indent=2, ensure_ascii=False))
```

## 注意事项

1. **超时设置**: AI相关的Agent处理时间较长，建议设置至少60秒的超时时间
2. **并发控制**: 系统内置了并发控制，高并发请求会自动排队处理
3. **Token管理**: 系统会自动统计和管理AI模型的token使用量
4. **错误重试**: 对于网络错误和临时性错误，建议实现重试机制
5. **数据格式**: 确保输入数据格式符合Agent的输入schema要求

## 获取帮助

- 查看Agent文档界面: `http://localhost:8601/agents/docs`
- 获取Agent Card: `http://localhost:8601/agents/{agent-id}/.well-known/agent.json`
- 健康检查: `http://localhost:8601/agents/{agent-id}/health`
- 运行BVT测试: `./test/run_bidding_tests.sh`