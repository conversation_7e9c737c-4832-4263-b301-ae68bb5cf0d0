# 数据库查询Agent API调用示例

## 概述

本文档提供数据库查询Agent的详细API调用示例，涵盖常见的业务场景和使用模式。

## 通用请求格式

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "技能ID",
    "input": {
      // 具体的查询参数
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_query_001",
      "timestamp": "2024-01-15T10:00:00Z"
    }
  },
  "id": "query_request_001"
}
```

## Skill 1: 查询招投标数据 (query_bidding_data)

### 接口信息
- **URL**: `http://taskd-service:8601/agents/database-query`
- **技能ID**: `query_bidding_data`

### 示例1: 基本时间范围查询

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "query_bidding_data",
    "input": {
      "collection": "bidding_result",
      "query_conditions": {
        "time_range": {
          "start_time": "2024-01-01T00:00:00Z",
          "end_time": "2024-01-15T23:59:59Z",
          "time_field": "update_time"
        },
        "has_raw_content": true
      },
      "pagination": {
        "page": 1,
        "limit": 50
      },
      "sorting": {
        "field": "update_time",
        "order": "desc"
      },
      "fields": {
        "include": ["_id", "title", "crawler_name", "raw_content", "update_time", "status"],
        "exclude": ["metadata", "files"]
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_query_time_range",
      "timestamp": "2024-01-15T10:00:00Z"
    }
  },
  "id": "query_time_range_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_query_1642240000_query_bidding_data",
    "status": "completed",
    "output": {
      "query_result": {
        "data": [
          {
            "_id": "65a123456789abcdef123456",
            "title": "某市教育局2024年度计算机设备采购项目",
            "crawler_name": "government_procurement",
            "raw_content": "【政府采购项目】某市教育局2024年度计算机设备采购项目。项目编号：EDU-2024-001...",
            "update_time": "2024-01-15T09:30:00Z",
            "status": "pending"
          },
          {
            "_id": "65a123456789abcdef123457",
            "title": "某科技公司服务器设备采购",
            "crawler_name": "enterprise_procurement",
            "raw_content": "【企业采购】某科技公司为扩大业务需要采购服务器设备...",
            "update_time": "2024-01-14T16:20:00Z",
            "status": "pending"
          }
        ],
        "pagination": {
          "current_page": 1,
          "total_pages": 23,
          "page_size": 50,
          "total_count": 1127,
          "has_next": true,
          "has_prev": false
        }
      },
      "query_metadata": {
        "executed_query": {
          "update_time": {
            "$gte": "2024-01-01T00:00:00Z",
            "$lt": "2024-01-15T23:59:59Z"
          },
          "raw_content": {
            "$exists": true,
            "$ne": null,
            "$ne": ""
          }
        },
        "execution_time": 125.6,
        "result_count": 50,
        "total_scanned": 1127,
        "index_used": "update_time_1"
      },
      "data_summary": {
        "crawler_stats": {
          "government_procurement": 28,
          "enterprise_procurement": 15,
          "international_tender": 7
        },
        "time_distribution": {
          "2024-01-15": 12,
          "2024-01-14": 18,
          "2024-01-13": 20
        },
        "status_distribution": {
          "pending": 42,
          "processing": 5,
          "failed": 3
        }
      }
    },
    "metadata": {
      "execution_time": 126,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "query_time_range_001"
}
```

### 示例2: 多条件复合查询

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "query_bidding_data",
    "input": {
      "collection": "bidding_result",
      "query_conditions": {
        "crawler_names": ["ungm", "world_bank", "ted_europa"],
        "crawl_modes": ["automation_robot", "drone"],
        "status": ["pending", "failed"],
        "error_threshold": 3,
        "has_raw_content": true,
        "custom_filters": {
          "title": {
            "$regex": "IT|计算机|software",
            "$options": "i"
          }
        }
      },
      "pagination": {
        "limit": 100
      },
      "sorting": {
        "field": "crawl_time",
        "order": "asc"
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456", 
      "trace_id": "trace_query_complex",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  },
  "id": "query_complex_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_query_1642241800_query_bidding_data",
    "status": "completed",
    "output": {
      "query_result": {
        "data": [
          {
            "_id": "65a123456789abcdef123458",
            "title": "UN Global Marketplace - IT Services Procurement",
            "crawler_name": "ungm",
            "crawl_mode": "automation_robot",
            "status": "pending",
            "error_times": 0,
            "raw_content": "The United Nations is seeking IT services...",
            "crawl_time": "2024-01-10T08:15:00Z"
          },
          {
            "_id": "65a123456789abcdef123459", 
            "title": "World Bank Software Development Contract",
            "crawler_name": "world_bank",
            "crawl_mode": "drone",
            "status": "failed",
            "error_times": 2,
            "raw_content": "The World Bank Group invites proposals for software development...",
            "crawl_time": "2024-01-11T14:30:00Z"
          }
        ],
        "pagination": {
          "current_page": 1,
          "total_pages": 3,
          "page_size": 100,
          "total_count": 267,
          "has_next": true,
          "has_prev": false
        }
      },
      "query_metadata": {
        "executed_query": {
          "crawler_name": {"$in": ["ungm", "world_bank", "ted_europa"]},
          "crawl_mode": {"$in": ["automation_robot", "drone"]},
          "status": {"$in": ["pending", "failed"]},
          "error_times": {"$lt": 3},
          "raw_content": {"$exists": true, "$ne": null, "$ne": ""},
          "title": {"$regex": "IT|计算机|software", "$options": "i"}
        },
        "execution_time": 89.3,
        "result_count": 100,
        "total_scanned": 267,
        "index_used": "crawler_name_1_crawl_mode_1"
      }
    },
    "metadata": {
      "execution_time": 90,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "query_complex_001"
}
```

### 示例3: 聚合统计查询

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "query_bidding_data",
    "input": {
      "collection": "bidding_result",
      "query_conditions": {
        "time_range": {
          "start_time": "2024-01-01T00:00:00Z",
          "end_time": "2024-01-31T23:59:59Z"
        }
      },
      "aggregation": {
        "group_by": ["crawler_name", "status"],
        "statistics": ["count", "distinct_count"]
      },
      "pagination": {
        "limit": 0
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_query_aggregate",
      "timestamp": "2024-01-15T11:00:00Z"
    }
  },
  "id": "query_aggregate_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_query_1642243200_query_bidding_data",
    "status": "completed", 
    "output": {
      "query_result": {
        "data": [],
        "aggregation_result": {
          "group_stats": {
            "ungm_pending": {"count": 1245, "distinct_count": 1245},
            "ungm_completed": {"count": 3421, "distinct_count": 3421},
            "ungm_failed": {"count": 156, "distinct_count": 156},
            "world_bank_pending": {"count": 892, "distinct_count": 892},
            "world_bank_completed": {"count": 2108, "distinct_count": 2108},
            "world_bank_failed": {"count": 97, "distinct_count": 97},
            "ted_europa_pending": {"count": 567, "distinct_count": 567},
            "ted_europa_completed": {"count": 1834, "distinct_count": 1834},
            "ted_europa_failed": {"count": 89, "distinct_count": 89}
          },
          "summary": {
            "total_records": 10409,
            "total_crawlers": 15,
            "status_breakdown": {
              "pending": 2704,
              "completed": 7363,
              "failed": 342
            }
          }
        }
      },
      "query_metadata": {
        "execution_time": 245.8,
        "result_count": 0,
        "total_scanned": 10409,
        "index_used": "compound_crawler_status_time"
      }
    },
    "metadata": {
      "execution_time": 246,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "query_aggregate_001"
}
```

## Skill 2: 获取预处理候选数据 (get_preprocessing_candidates)

### 接口信息
- **技能ID**: `get_preprocessing_candidates`

### 示例1: 获取高质量待处理数据

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "get_preprocessing_candidates",
    "input": {
      "candidate_criteria": {
        "processing_status": "unprocessed",
        "priority": "high",
        "content_completeness": "complete",
        "max_error_count": 2,
        "min_content_length": 500
      },
      "batch_config": {
        "batch_size": 50,
        "randomize": false,
        "diversity_factor": 0.3
      },
      "time_constraints": {
        "min_age_hours": 2,
        "max_age_days": 7
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_get_candidates",
      "timestamp": "2024-01-15T12:00:00Z"
    }
  },
  "id": "get_candidates_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_candidates_1642246800_get_preprocessing_candidates",
    "status": "completed",
    "output": {
      "candidates": [
        {
          "_id": "65a123456789abcdef12345a",
          "title": "某市智慧城市建设项目招标公告",
          "raw_content": "【政府招标】某市为推进智慧城市建设，现公开招标智慧城市系统集成项目。项目预算2000万元，要求投标人具备智慧城市建设经验...",
          "crawler_name": "government_procurement",
          "crawl_time": "2024-01-13T14:30:00Z",
          "priority_score": 8.5,
          "content_quality": {
            "length": 2847,
            "completeness": 0.92,
            "structure_score": 0.85,
            "readability": 0.78
          }
        },
        {
          "_id": "65a123456789abcdef12345b",
          "title": "International Software Development Services",
          "raw_content": "The European Commission invites tenders for software development services to support digital transformation initiatives...",
          "crawler_name": "ted_europa",
          "crawl_time": "2024-01-12T09:15:00Z",
          "priority_score": 8.2,
          "content_quality": {
            "length": 3156,
            "completeness": 0.88,
            "structure_score": 0.91,
            "readability": 0.82
          }
        }
      ],
      "selection_metadata": {
        "total_candidates": 1342,
        "selected_count": 50,
        "selection_criteria": {
          "content_quality_threshold": 0.8,
          "priority_threshold": 7.0,
          "diversity_applied": true
        },
        "quality_distribution": {
          "high_quality": 35,
          "medium_quality": 15,
          "government_sources": 28,
          "international_sources": 22
        }
      }
    },
    "metadata": {
      "execution_time": 187,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "get_candidates_001"
}
```

### 示例2: 获取失败重试数据

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "get_preprocessing_candidates",
    "input": {
      "candidate_criteria": {
        "processing_status": "failed",
        "priority": "all",
        "content_completeness": "partial",
        "max_error_count": 5,
        "min_content_length": 100
      },
      "batch_config": {
        "batch_size": 20,
        "randomize": true,
        "diversity_factor": 0.5
      },
      "time_constraints": {
        "min_age_hours": 24,
        "max_age_days": 30
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_retry_candidates",
      "timestamp": "2024-01-15T13:00:00Z"
    }
  },
  "id": "get_retry_candidates_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_candidates_1642250400_get_preprocessing_candidates",
    "status": "completed",
    "output": {
      "candidates": [
        {
          "_id": "65a123456789abcdef12345c",
          "title": "Construction Project Tender - Partial Content",
          "raw_content": "Construction tender for highway development project. Budget information missing...",
          "crawler_name": "construction_tenders",
          "crawl_time": "2024-01-05T11:20:00Z",
          "priority_score": 5.2,
          "content_quality": {
            "length": 456,
            "completeness": 0.45,
            "structure_score": 0.38,
            "readability": 0.65,
            "issues": ["incomplete_budget", "missing_contact_info"]
          }
        }
      ],
      "selection_metadata": {
        "total_candidates": 234,
        "selected_count": 20,
        "selection_criteria": {
          "retry_priority": true,
          "error_count_filter": "<=5",
          "age_range": "1-30_days"
        },
        "quality_distribution": {
          "partial_content": 20,
          "error_categories": {
            "ai_extraction_failed": 12,
            "content_parsing_error": 5,
            "timeout_error": 3
          }
        }
      }
    },
    "metadata": {
      "execution_time": 98,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "get_retry_candidates_001"
}
```

## Skill 3: 更新处理状态 (update_processing_status)

### 接口信息
- **技能ID**: `update_processing_status`

### 示例1: 批量更新处理状态

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "update_processing_status",
    "input": {
      "record_ids": [
        "65a123456789abcdef12345a",
        "65a123456789abcdef12345b",
        "65a123456789abcdef12345c"
      ],
      "updates": {
        "processing_status": "processing",
        "last_processing_time": "2024-01-15T14:00:00Z",
        "processing_result": {
          "agent_id": "tender-preprocessing-orchestration",
          "workflow_id": "workflow_1642251600"
        },
        "custom_fields": {
          "processing_attempt": 1,
          "assigned_worker": "worker_node_03"
        }
      },
      "batch_operation": true
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_update_status",
      "timestamp": "2024-01-15T14:00:00Z"
    }
  },
  "id": "update_status_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_update_1642251600_update_processing_status",
    "status": "completed",
    "output": {
      "update_result": {
        "matched_count": 3,
        "modified_count": 3,
        "failed_updates": []
      }
    },
    "metadata": {
      "execution_time": 23,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "update_status_001"
}
```

### 示例2: 更新处理完成状态

#### 请求示例
```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "update_processing_status",
    "input": {
      "record_ids": ["65a123456789abcdef12345a"],
      "updates": {
        "processing_status": "completed",
        "last_processing_time": "2024-01-15T14:30:00Z",
        "processing_result": {
          "agent_id": "tender-preprocessing-orchestration",
          "workflow_id": "workflow_1642251600",
          "result_id": "tender_processed_1642253800",
          "quality_score": 8.5,
          "processing_duration": 1800
        },
        "error_count": 0,
        "custom_fields": {
          "processing_attempt": 1,
          "success": true,
          "result_quality": "high"
        }
      },
      "batch_operation": false
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_complete_status",
      "timestamp": "2024-01-15T14:30:00Z"
    }
  },
  "id": "complete_status_001"
}
```

#### 响应示例
```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_update_1642253400_update_processing_status",
    "status": "completed",
    "output": {
      "update_result": {
        "matched_count": 1,
        "modified_count": 1,
        "failed_updates": []
      }
    },
    "metadata": {
      "execution_time": 12,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "complete_status_001"
}
```

## 错误处理示例

### 查询超时错误
```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32003,
    "message": "查询执行超时",
    "data": {
      "error_type": "timeout",
      "retry_after": 30,
      "context": {
        "query_timeout": 30000,
        "execution_time": 30124,
        "suggestion": "请简化查询条件或增加timeout参数"
      }
    }
  },
  "id": "query_timeout_001"
}
```

### 数据库连接错误
```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32004,
    "message": "数据库连接失败",
    "data": {
      "error_type": "database_error",
      "retry_after": 60,
      "context": {
        "database": "bidding",
        "error_detail": "Connection timeout to MongoDB server",
        "suggestion": "请检查数据库连接状态"
      }
    }
  },
  "id": "db_connection_001"
}
```

### 查询参数验证错误
```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32001,
    "message": "查询参数验证失败",
    "data": {
      "error_type": "validation_error",
      "validation_errors": [
        "pagination.limit不能超过1000",
        "time_range.start_time必须小于end_time"
      ],
      "context": {
        "invalid_params": ["pagination.limit", "time_range"],
        "suggestion": "请检查并修正查询参数"
      }
    }
  },
  "id": "validation_001"
}
```

## 性能监控示例

### 健康检查
```bash
curl -X GET http://taskd-service:8601/agents/database-query/health
```

#### 健康检查响应
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 172800000,
  "dependencies": {
    "mongodb": "ok",
    "connection_pool": "ok",
    "validator": "ok"
  },
  "performance": {
    "avg_response_time": 156,
    "requests_per_minute": 23,
    "error_rate": 0.01,
    "success_rate": 0.99,
    "active_connections": 5,
    "pool_size": 10
  },
  "statistics": {
    "total_queries": 15640,
    "queries_today": 892,
    "slow_queries": 12,
    "cache_hit_rate": 0.34
  },
  "timestamp": "2024-01-15T15:00:00Z"
}
```

## 使用建议

### 1. 查询优化
- **使用索引字段排序**：优先使用crawler_name、update_time等已索引字段
- **合理设置分页**：单次查询不超过1000条记录
- **字段投影**：只查询需要的字段，排除大文本字段
- **时间范围限制**：避免查询过长时间范围的数据

### 2. 预处理数据获取
- **批量大小控制**：建议50-200条记录per batch
- **多样性平衡**：设置合理的diversity_factor避免数据偏差
- **质量阈值设置**：根据处理能力设置合适的质量要求
- **错误重试策略**：失败数据重试前适当延迟

### 3. 状态管理
- **及时更新状态**：开始处理时立即更新为processing状态
- **记录详细信息**：保存处理结果和元数据便于排查
- **批量操作优化**：多条记录更新时使用batch_operation
- **错误计数管理**：合理设置error_count避免无限重试

### 4. 监控和维护
- **慢查询监控**：关注execution_time超过5秒的查询
- **索引使用监控**：确保查询使用了合适的索引
- **连接池监控**：保持合理的数据库连接数
- **缓存策略优化**：对重复查询启用查询缓存