[project]
name = "taskd-test-suite"
version = "0.1.0"
description = "Test suite for TaskD service using uv package manager"
readme = "test/README.md"
requires-python = ">=3.8"

dependencies = [
    "requests>=2.31.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.1",
    "pytest-xdist>=3.3.1",
    "faker>=19.6.2",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "websocket-client>=1.6.0",
    "python-socketio[client]>=5.8.0",
    "colorlog>=6.7.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.1",
    "black>=23.7.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["test"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "p0: Priority 0 - Critical (service unavailable level)",
    "p1: Priority 1 - High (core business functionality)",
    "p2: Priority 2 - Medium (preprocessing functionality)",
    "p3: Priority 3 - Medium-Low (support functionality)",
    "p4: Priority 4 - Low (new features)",
    "e2e: End-to-end integration tests",
]

[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true

[[tool.mypy.overrides]]
module = [
    "faker.*",
    "socketio.*",
    "websocket.*",
]
ignore_missing_imports = true