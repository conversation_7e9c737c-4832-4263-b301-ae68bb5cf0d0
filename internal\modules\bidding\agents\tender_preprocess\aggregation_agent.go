package tender_preprocess

import (
	"encoding/json"
	"fmt"

	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// AggregationAgent 结果聚合Agent
type AggregationAgent struct {
	*agents.BaseAgent
}

// NewAggregationAgent 创建结果聚合Agent
func NewAggregationAgent() *AggregationAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentAggregationName,
		Description: "聚合各个预处理Agent的结果，生成最终的综合处理结果",
		URL:         "http://taskd-service:8601/agents/tender-result-aggregation",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillAggregateResults,
				Name:        "结果聚合",
				Description: "聚合提取、分类、增强和多语言处理的结果",
				Tags:        []string{"aggregation", "integration", "final"},
				Examples: []string{
					"整合所有预处理步骤的输出结果",
					"生成完整的招投标处理报告",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentAggregationName, agentCard)

	return &AggregationAgent{
		BaseAgent: baseAgent,
	}
}

// ExecuteSkill 执行技能
func (a *AggregationAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillAggregateResults:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.aggregateResultsHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// aggregateResultsHandler 结果聚合处理器
func (a *AggregationAgent) aggregateResultsHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseAggregationParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting result aggregation")

	// 执行结果聚合
	aggregatedResult, metadata, err := a.performAggregation(params)
	if err != nil {
		return nil, fmt.Errorf("result aggregation failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"final_result":        aggregatedResult,
		"processing_metadata": metadata,
	}

	return output, nil
}

// AggregationParams 聚合参数
type AggregationParams struct {
	ExtractedData      *agentCommon.TenderData                 `json:"extracted_data"`
	ClassificationData *agentCommon.ClassificationResult       `json:"classification_data"`
	EnhancementData    *agentCommon.EnhancementResult          `json:"enhancement_data"`
	TranslationData    map[string]*agentCommon.TranslationData `json:"translation_data"`
	OriginalText       string                                  `json:"original_text"`
	ProcessingOptions  *AggregationOptions                     `json:"processing_options"`
}

// AggregationOptions 聚合选项
type AggregationOptions struct {
	IncludeTranslations bool   `json:"include_translations"`
	IncludeMetadata     bool   `json:"include_metadata"`
	OutputFormat        string `json:"output_format"`
	GenerateReport      bool   `json:"generate_report"`
}

// parseAggregationParams 解析聚合参数
func (a *AggregationAgent) parseAggregationParams(input map[string]interface{}) (*AggregationParams, error) {
	// 解析extracted_data
	extractedDataInterface, ok := input["extracted_data"]
	if !ok {
		return nil, fmt.Errorf("extracted_data is required")
	}

	extractedDataBytes, err := json.Marshal(extractedDataInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal extracted_data: %v", err)
	}

	var extractedData agentCommon.TenderData
	if err := json.Unmarshal(extractedDataBytes, &extractedData); err != nil {
		return nil, fmt.Errorf("failed to parse extracted_data: %v", err)
	}

	// 解析classification_data
	classificationDataInterface, ok := input["classification_data"]
	if !ok {
		return nil, fmt.Errorf("classification_data is required")
	}

	classificationDataBytes, err := json.Marshal(classificationDataInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal classification_data: %v", err)
	}

	var classificationData agentCommon.ClassificationResult
	if err := json.Unmarshal(classificationDataBytes, &classificationData); err != nil {
		return nil, fmt.Errorf("failed to parse classification_data: %v", err)
	}

	// 解析enhancement_data（可选）
	var enhancementData *agentCommon.EnhancementResult
	if enhancementDataInterface, ok := input["enhancement_data"]; ok {
		enhancementDataBytes, err := json.Marshal(enhancementDataInterface)
		if err == nil {
			var enhancement agentCommon.EnhancementResult
			if json.Unmarshal(enhancementDataBytes, &enhancement) == nil {
				enhancementData = &enhancement
			}
		}
	}

	// 解析translation_data（可选）
	var translationData map[string]*agentCommon.TranslationData
	if translationDataInterface, ok := input["translation_data"]; ok {
		translationDataBytes, err := json.Marshal(translationDataInterface)
		if err == nil {
			json.Unmarshal(translationDataBytes, &translationData)
		}
	}

	// 解析processing_options（可选）
	var processingOptions AggregationOptions
	if optionsInterface, ok := input["processing_options"]; ok {
		optionsBytes, err := json.Marshal(optionsInterface)
		if err == nil {
			json.Unmarshal(optionsBytes, &processingOptions)
		}
	} else {
		// 默认选项
		processingOptions = AggregationOptions{
			IncludeTranslations: true,
			IncludeMetadata:     true,
			OutputFormat:        agentCommon.OutputFormatJSON,
			GenerateReport:      true,
		}
	}

	originalText, _ := input["original_text"].(string)

	return &AggregationParams{
		ExtractedData:      &extractedData,
		ClassificationData: &classificationData,
		EnhancementData:    enhancementData,
		TranslationData:    translationData,
		OriginalText:       originalText,
		ProcessingOptions:  &processingOptions,
	}, nil
}

// performAggregation 执行聚合逻辑
func (a *AggregationAgent) performAggregation(params *AggregationParams) (*agentCommon.PreprocessingResult, *agentCommon.ProcessingMetadata, error) {
	// 初始化结果对象
	result := &agentCommon.PreprocessingResult{
		ExtractedData:  params.ExtractedData,
		Classification: params.ClassificationData,
		Enhancement:    params.EnhancementData,
		Multilingual:   params.TranslationData,
		ProcessingSummary: &agentCommon.ProcessingSummary{
			AgentsInvolved: []string{
				agentCommon.AgentExtractionName,
				agentCommon.AgentClassificationName,
				agentCommon.AgentEnhancementName,
				agentCommon.AgentMultilingualName,
				agentCommon.AgentAggregationName,
			},
		},
	}

	// 计算处理元数据
	metadata := a.calculateMetadata(params)
	result.ProcessingSummary.TotalProcessingTime = metadata.ProcessingTime
	result.ProcessingSummary.OverallConfidence = metadata.ConfidenceScore

	return result, metadata, nil
}

// calculateMetadata 计算元数据
func (a *AggregationAgent) calculateMetadata(params *AggregationParams) *agentCommon.ProcessingMetadata {
	var totalTime float64
	var totalTokens int
	var weightedConfidence float64
	var totalWeight float64

	// 提取元数据
	if params.ExtractedData != nil && params.ClassificationData != nil && params.ClassificationData.ProcessingMetadata != nil {
		totalTime += params.ClassificationData.ProcessingMetadata.ProcessingTime
		totalTokens += params.ClassificationData.ProcessingMetadata.TokensConsumed
		weightedConfidence += params.ClassificationData.ProcessingMetadata.ConfidenceScore * 0.4 // 权重40%
		totalWeight += 0.4
	}

	// 增强元数据
	if params.EnhancementData != nil && params.EnhancementData.ProcessingMetadata != nil {
		totalTime += params.EnhancementData.ProcessingMetadata.ProcessingTime
		totalTokens += params.EnhancementData.ProcessingMetadata.TokensConsumed
		weightedConfidence += params.EnhancementData.ProcessingMetadata.ConfidenceScore * 0.6 // 权重60%
		totalWeight += 0.6
	}

	var overallConfidence float64
	if totalWeight > 0 {
		overallConfidence = weightedConfidence / totalWeight
	}

	return &agentCommon.ProcessingMetadata{
		ModelUsed:       "aggregation-v1.0",
		TokensConsumed:  totalTokens,
		ProcessingTime:  totalTime,
		ConfidenceScore: overallConfidence,
	}
}
