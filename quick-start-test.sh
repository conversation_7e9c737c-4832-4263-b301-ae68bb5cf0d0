#!/bin/bash

# TaskD 简单测试快速启动脚本
# 使用 docker run 直接启动测试容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${BLUE}=== TaskD 简单测试环境快速启动 ===${NC}"
echo "项目目录: $PROJECT_ROOT"
echo "测试方式: Ubuntu容器 -> Windows TaskD服务"
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装${NC}"
    echo "请先安装Docker: https://docs.docker.com/engine/install/ubuntu/"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查Windows上的TaskD服务是否可访问
echo -e "${BLUE}🔍 检查Windows TaskD服务连通性...${NC}"
TASKD_URL="http://**************:8601"
if timeout 10 curl -sf "$TASKD_URL/healthz" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ TaskD服务连通性检查通过${NC}"
else
    echo -e "${RED}❌ 无法连接到TaskD服务: $TASKD_URL${NC}"
    echo -e "${YELLOW}请确保:${NC}"
    echo "1. TaskD服务在Windows机器上正常运行"
    echo "2. Windows防火墙允许8601端口访问"
    echo "3. IP地址**************正确"
    
    # 显示更多诊断信息
    echo ""
    echo -e "${BLUE}🔧 网络诊断:${NC}"
    if timeout 5 nc -z ************** 8601; then
        echo -e "${GREEN}✓ 端口8601可达${NC}"
    else
        echo -e "${RED}✗ 端口8601不可达${NC}"
    fi
    
    exit 1
fi

echo -e "${BLUE}🔨 构建并运行测试...${NC}"

# 运行P0测试
echo -e "${BLUE}🧪 运行P0级别测试 (最关键)...${NC}"
if ./run-tests.sh test p0; then
    echo -e "${GREEN}✅ P0测试通过${NC}"
    
    echo -e "${BLUE}🧪 运行P1级别测试 (核心功能)...${NC}"
    if ./run-tests.sh test p1; then
        echo -e "${GREEN}✅ P1测试通过${NC}"
        
        echo -e "${BLUE}🧪 运行完整测试套件...${NC}"
        ./run-tests.sh test
    else
        echo -e "${YELLOW}⚠️ P1测试部分失败，但P0通过，服务基本可用${NC}"
    fi
else
    echo -e "${RED}❌ P0测试失败，服务存在严重问题${NC}"
    echo ""
    echo -e "${YELLOW}📋 故障排除建议:${NC}"
    echo "1. 进入容器调试: ./run-tests.sh shell"
    echo "2. 查看测试日志: ls -la test/logs/"
    echo "3. 手动检查连接: curl -v http://**************:8601/healthz"
fi

echo ""
echo -e "${BLUE}=== 常用命令 ===${NC}"
echo "运行所有测试:     ./run-tests.sh test"
echo "运行特定测试:     ./run-tests.sh test bidding"
echo "进入测试容器:     ./run-tests.sh shell"
echo "清理环境:         ./run-tests.sh clean"
echo "查看测试日志:     ls -la test/logs/"
echo ""
echo -e "${GREEN}🎉 TaskD简单测试环境已准备就绪！${NC}"