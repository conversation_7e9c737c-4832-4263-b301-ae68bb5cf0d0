package handlers

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	socketio "github.com/googollee/go-socket.io"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services/chat"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ChatHandler 聊天处理器
type ChatHandler struct {
	chatAgentService  *chat.ChatAgentService
	sessionService    *chat.ChatSessionService
	connectionService *chat.SocketConnectionService
	cleanupService    *chat.ChatCleanupService
	socketIOServer    *socketio.Server
	config            *models.ChatConfig
}

// NewChatHandler 创建聊天处理器
func NewChatHandler(
	chatAgentService *chat.ChatAgentService,
	sessionService *chat.ChatSessionService,
	connectionService *chat.SocketConnectionService,
	cleanupService *chat.ChatCleanupService,
	config *models.ChatConfig,
) *Chat<PERSON>andler {
	handler := &ChatHandler{
		chatAgentService:  chatAgentService,
		sessionService:    sessionService,
		connectionService: connectionService,
		cleanupService:    cleanupService,
		config:            config,
	}

	// 初始化SocketIO服务器
	handler.initSocketIOServer()

	return handler
}

// initSocketIOServer 初始化SocketIO服务器
func (h *ChatHandler) initSocketIOServer() {
	server := socketio.NewServer(nil)

	h.socketIOServer = server

	// 连接事件
	server.OnConnect("/", func(s socketio.Conn) error {
		utils.Log.Infof("Socket connected: %s", s.ID())
		return nil
	})

	// 断开连接事件
	server.OnDisconnect("/", func(s socketio.Conn, reason string) {
		utils.Log.Infof("Socket disconnected: %s, reason: %s", s.ID(), reason)

		ctx := context.Background()
		err := h.connectionService.DisconnectSocket(ctx, s.ID())
		if err != nil {
			utils.Log.Errorf("Failed to update socket disconnection: %v", err)
		}
	})

	// 加入会话事件
	server.OnEvent("/", "join_session", func(s socketio.Conn, data map[string]interface{}) {
		h.handleJoinSession(s, data)
	})

	// 聊天消息事件
	server.OnEvent("/", "chat_message", func(s socketio.Conn, data map[string]interface{}) {
		h.handleChatMessage(s, data)
	})

	// Ping事件
	server.OnEvent("/", "ping", func(s socketio.Conn, data map[string]interface{}) {
		h.handlePing(s, data)
	})

	// 错误处理
	server.OnError("/", func(s socketio.Conn, e error) {
		utils.Log.Errorf("SocketIO error for %s: %v", s.ID(), e)
	})

	go server.Serve()
}

// handleJoinSession 处理加入会话请求
func (h *ChatHandler) handleJoinSession(s socketio.Conn, data map[string]interface{}) {
	ctx := context.Background()

	// 解析请求数据
	userID, ok := data["user_id"].(string)
	if !ok || userID == "" {
		h.emitError(s, "missing or invalid user_id")
		return
	}

	companyID, ok := data["company_id"].(string)
	if !ok || companyID == "" {
		h.emitError(s, "missing or invalid company_id")
		return
	}

	// 获取客户端信息
	clientIP := h.getClientIP(data)
	userAgent := h.getUserAgent(data)

	// 创建Socket连接记录
	connectionReq := &models.SocketConnectionRequest{
		UserID:    userID,
		CompanyID: companyID,
		ClientIP:  clientIP,
		UserAgent: userAgent,
	}

	connection, err := h.connectionService.CreateConnection(ctx, connectionReq)
	if err != nil {
		utils.Log.Errorf("Failed to create socket connection: %v", err)
		h.emitError(s, "failed to create connection")
		return
	}

	// 设置socket ID
	s.SetContext(map[string]interface{}{
		"socket_id":  connection.SocketID,
		"session_id": connection.SessionID,
		"user_id":    userID,
		"company_id": companyID,
	})

	// 加入会话房间
	s.Join(connection.SessionID)

	// 发送连接成功消息
	h.emitResponse(s, models.SocketEventJoinSession, map[string]interface{}{
		"socket_id":  connection.SocketID,
		"session_id": connection.SessionID,
		"status":     "connected",
	})

	utils.Log.Infof("Socket %s joined session %s for user %s", s.ID(), connection.SessionID, userID)
}

// handleChatMessage 处理聊天消息
func (h *ChatHandler) handleChatMessage(s socketio.Conn, data map[string]interface{}) {
	ctx := context.Background()

	// 获取连接上下文
	socketCtx := s.Context()
	if socketCtx == nil {
		h.emitError(s, "invalid socket context")
		return
	}

	ctxMap, ok := socketCtx.(map[string]interface{})
	if !ok {
		h.emitError(s, "invalid socket context type")
		return
	}

	sessionID, ok := ctxMap["session_id"].(string)
	if !ok {
		h.emitError(s, "session not found")
		return
	}

	userID, ok := ctxMap["user_id"].(string)
	if !ok {
		h.emitError(s, "user not found")
		return
	}
	utils.Log.Debugf("Handling chat message for user %s", userID)

	// 解析消息内容
	content, ok := data["content"].(string)
	if !ok || content == "" {
		h.emitError(s, "missing or invalid content")
		return
	}

	// 获取元数据
	metadata, _ := data["metadata"].(map[string]interface{})

	// 更新连接ping时间
	err := h.connectionService.UpdateLastPing(ctx, s.ID())
	if err != nil {
		utils.Log.Errorf("Failed to update last ping: %v", err)
	}

	// 处理聊天消息
	messageReq := &models.ChatMessageRequest{
		SessionID: sessionID,
		Content:   content,
		Metadata:  metadata,
	}

	response, err := h.chatAgentService.ProcessChatMessage(ctx, messageReq)
	if err != nil {
		utils.Log.Errorf("Failed to process chat message: %v", err)
		h.emitError(s, fmt.Sprintf("failed to process message: %v", err))
		return
	}

	// 发送AI回复
	h.emitResponse(s, models.SocketEventResponse, map[string]interface{}{
		"message_id":  response.MessageID,
		"content":     response.Content,
		"token_count": response.TokenCount,
		"metadata":    response.Metadata,
		"created_at":  response.CreatedAt,
	})

	utils.Log.Debugf("Processed chat message for session %s", sessionID)
}

// handlePing 处理ping请求
func (h *ChatHandler) handlePing(s socketio.Conn, data map[string]interface{}) {
	ctx := context.Background()

	// 更新连接ping时间
	err := h.connectionService.UpdateLastPing(ctx, s.ID())
	if err != nil {
		utils.Log.Errorf("Failed to update last ping: %v", err)
	}

	// 发送pong响应
	h.emitResponse(s, models.SocketEventPong, map[string]interface{}{
		"timestamp": time.Now(),
	})
}

// emitError 发送错误消息
func (h *ChatHandler) emitError(s socketio.Conn, errorMsg string) {
	errStr := errorMsg
	data := models.SocketMessageData{
		Event:     models.SocketEventError,
		Error:     &errStr,
		Timestamp: time.Now(),
	}
	s.Emit("error", data)
}

// emitResponse 发送响应消息
func (h *ChatHandler) emitResponse(s socketio.Conn, event models.SocketEvent, data map[string]interface{}) {
	response := models.SocketMessageData{
		Event:     event,
		Data:      data,
		Timestamp: time.Now(),
	}
	s.Emit(string(event), response)
}

// getClientIP 获取客户端IP
func (h *ChatHandler) getClientIP(data map[string]interface{}) *string {
	if ip, ok := data["client_ip"].(string); ok && ip != "" {
		return &ip
	}
	return nil
}

// getUserAgent 获取用户代理
func (h *ChatHandler) getUserAgent(data map[string]interface{}) *string {
	if ua, ok := data["user_agent"].(string); ok && ua != "" {
		return &ua
	}
	return nil
}

// REST API 处理器

// CreateChatSession 创建聊天会话 REST API
func (h *ChatHandler) CreateChatSession(c *gin.Context) {
	var req models.ChatSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	session, err := h.chatAgentService.CreateChatSession(c.Request.Context(), &req)
	if err != nil {
		utils.Log.Errorf("Failed to create chat session: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, session)
}

// GetChatHistory 获取聊天历史 REST API
func (h *ChatHandler) GetChatHistory(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "session_id is required"})
		return
	}

	limit := 50
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	offset := 0
	if o := c.Query("offset"); o != "" {
		if parsed, err := strconv.Atoi(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}

	req := &models.ChatHistoryRequest{
		SessionID: sessionID,
		Limit:     &limit,
		Offset:    &offset,
	}

	history, err := h.chatAgentService.GetChatHistory(c.Request.Context(), req)
	if err != nil {
		utils.Log.Errorf("Failed to get chat history: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, history)
}

// CloseChatSession 关闭聊天会话 REST API
func (h *ChatHandler) CloseChatSession(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "session_id is required"})
		return
	}

	err := h.chatAgentService.CloseChatSession(c.Request.Context(), sessionID)
	if err != nil {
		utils.Log.Errorf("Failed to close chat session: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "session closed"})
}

// GetChatStats 获取聊天统计信息 REST API
func (h *ChatHandler) GetChatStats(c *gin.Context) {
	stats, err := h.cleanupService.GetCleanupStats(c.Request.Context())
	if err != nil {
		utils.Log.Errorf("Failed to get chat stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// ForceCleanup 强制执行清理 REST API
func (h *ChatHandler) ForceCleanup(c *gin.Context) {
	err := h.cleanupService.ForceCleanup(c.Request.Context())
	if err != nil {
		utils.Log.Errorf("Failed to force cleanup: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "cleanup completed"})
}

// GetSocketIOHandler 获取SocketIO处理器（用于路由注册）
func (h *ChatHandler) GetSocketIOHandler() gin.HandlerFunc {
	return gin.WrapH(h.socketIOServer)
}
