package services

// SchemaService JSON Schema管理服务实现
type SchemaService struct {
}

// NewSchemaService 创建新的Schema服务
func NewSchemaService() *SchemaService {
	return &SchemaService{}
}

// GetCompanyProfileSchema 获取企业画像的JSON Schema
func (s *SchemaService) GetCompanyProfileSchema() map[string]interface{} {
	return getCompanyProfileSchema()
}

// ValidateProfileData 验证企业画像数据结构（暂时返回nil，可根据需要实现）
func (s *SchemaService) ValidateProfileData(data interface{}) error {
	// TODO: 可以在这里实现JSON Schema验证逻辑
	return nil
}

// getCompanyProfileSchema 返回企业画像的JSON Schema定义
// 用于OpenAI Structured Outputs功能，确保LLM返回严格符合结构的JSON
func getCompanyProfileSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"business_capabilities": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"core_business_areas": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "核心业务领域列表",
					},
					"product_service_types": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "产品服务类型列表",
					},
					"technical_capabilities": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"name": map[string]interface{}{
									"type": "string",
									"description": "技术能力名称",
								},
								"level": map[string]interface{}{
									"type": "string",
									"enum": []string{"advanced", "intermediate", "basic"},
									"description": "技术水平",
								},
								"description": map[string]interface{}{
									"type": "string",
									"description": "技术能力描述",
								},
							},
							"required": []string{"name", "level", "description"},
							"additionalProperties": false,
						},
					},
					"business_scale": map[string]interface{}{
						"type": "string",
						"enum": []string{"大型企业", "中型企业", "小型企业"},
						"description": "企业规模",
					},
					"project_experience": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"project_type": map[string]interface{}{
									"type": "string",
									"description": "项目类型",
								},
								"scale": map[string]interface{}{
									"type": "string",
									"description": "项目规模",
								},
								"count": map[string]interface{}{
									"type": "integer",
									"minimum": 0,
									"description": "项目数量",
								},
								"description": map[string]interface{}{
									"type": "string",
									"description": "项目描述",
								},
							},
							"required": []string{"project_type", "scale", "count", "description"},
							"additionalProperties": false,
						},
					},
					"certifications": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "资质认证列表",
					},
				},
				"required": []string{"core_business_areas", "product_service_types", "technical_capabilities", "business_scale", "project_experience", "certifications"},
				"additionalProperties": false,
			},
			"tender_matching": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"project_types": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "适合的项目类型",
					},
					"project_scale": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
							"enum": []string{"小型项目", "中型项目", "大型项目"},
						},
						"description": "适合的项目规模",
					},
					"geographic_coverage": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "地域覆盖能力",
					},
					"qualification_match": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"type": map[string]interface{}{
									"type": "string",
									"description": "资质类型",
								},
								"level": map[string]interface{}{
									"type": "string",
									"description": "资质等级",
								},
								"description": map[string]interface{}{
									"type": "string",
									"description": "资质描述",
								},
							},
							"required": []string{"type", "level", "description"},
							"additionalProperties": false,
						},
					},
					"bidding_strategy": map[string]interface{}{
						"type": "string",
						"description": "投标策略描述",
					},
					"historical_win_rate": map[string]interface{}{
						"type": "number",
						"minimum": 0.0,
						"maximum": 1.0,
						"description": "历史中标率",
					},
				},
				"required": []string{"project_types", "project_scale", "geographic_coverage", "qualification_match", "bidding_strategy", "historical_win_rate"},
				"additionalProperties": false,
			},
			"competitive_profile": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"market_position": map[string]interface{}{
						"type": "string",
						"description": "市场地位描述",
					},
					"core_advantages": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "核心优势列表",
					},
					"differentiators": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "差异化特点",
					},
					"main_competitors": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"name": map[string]interface{}{
									"type": "string",
									"description": "竞争对手名称",
								},
								"relationship": map[string]interface{}{
									"type": "string",
									"enum": []string{"direct", "indirect"},
									"description": "竞争关系",
								},
								"advantage": map[string]interface{}{
									"type": "string",
									"description": "相对优势",
								},
							},
							"required": []string{"name", "relationship", "advantage"},
							"additionalProperties": false,
						},
					},
					"price_competitiveness": map[string]interface{}{
						"type": "string",
						"description": "价格竞争力描述",
					},
					"market_influence": map[string]interface{}{
						"type": "string",
						"description": "市场影响力描述",
					},
				},
				"required": []string{"market_position", "core_advantages", "differentiators", "main_competitors", "price_competitiveness", "market_influence"},
				"additionalProperties": false,
			},
			"international_capabilities": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"overseas_markets": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"region": map[string]interface{}{
									"type": "string",
									"description": "海外区域",
								},
								"experience": map[string]interface{}{
									"type": "string",
									"description": "经验描述",
								},
								"market_share": map[string]interface{}{
									"type": "string",
									"description": "市场份额",
								},
							},
							"required": []string{"region", "experience", "market_share"},
							"additionalProperties": false,
						},
					},
					"cross_border_experience": map[string]interface{}{
						"type": "string",
						"description": "跨境业务经验",
					},
					"international_certifications": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "国际认证列表",
					},
					"partner_network": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"name": map[string]interface{}{
									"type": "string",
									"description": "合作伙伴名称",
								},
								"region": map[string]interface{}{
									"type": "string",
									"description": "合作区域",
								},
								"partnership": map[string]interface{}{
									"type": "string",
									"description": "合作关系",
								},
							},
							"required": []string{"name", "region", "partnership"},
							"additionalProperties": false,
						},
					},
					"language_capabilities": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "语言能力",
					},
					"cultural_adaptability": map[string]interface{}{
						"type": "string",
						"description": "文化适应性",
					},
				},
				"required": []string{"overseas_markets", "cross_border_experience", "international_certifications", "partner_network", "language_capabilities", "cultural_adaptability"},
				"additionalProperties": false,
			},
			"risk_tolerance": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"policy_risk_sensitivity": map[string]interface{}{
						"type": "string",
						"enum": []string{"高", "中", "低"},
						"description": "政策风险敏感度",
					},
					"exchange_rate_risk": map[string]interface{}{
						"type": "string",
						"enum": []string{"高", "中", "低"},
						"description": "汇率风险承受能力",
					},
					"project_cycle_preference": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
							"enum": []string{"短期", "中期", "长期"},
						},
						"description": "项目周期偏好",
					},
					"funding_capability": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"capital_scale": map[string]interface{}{
								"type": "string",
								"description": "资金规模",
							},
							"funding_sources": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
								},
								"description": "资金来源",
							},
							"cash_flow_stability": map[string]interface{}{
								"type": "string",
								"description": "现金流稳定性",
							},
						},
						"required": []string{"capital_scale", "funding_sources", "cash_flow_stability"},
						"additionalProperties": false,
					},
					"risk_control_mechanisms": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "风险控制机制",
					},
					"insurance_coverage": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "保险覆盖",
					},
				},
				"required": []string{"policy_risk_sensitivity", "exchange_rate_risk", "project_cycle_preference", "funding_capability", "risk_control_mechanisms", "insurance_coverage"},
				"additionalProperties": false,
			},
			"confidence_score": map[string]interface{}{
				"type": "integer",
				"minimum": 0,
				"maximum": 100,
				"description": "置信度分数",
			},
		},
		"required": []string{"business_capabilities", "tender_matching", "competitive_profile", "international_capabilities", "risk_tolerance", "confidence_score"},
		"additionalProperties": false,
	}
}