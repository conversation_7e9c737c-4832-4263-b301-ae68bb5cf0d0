#!/bin/bash

# etcd配置初始化脚本
# 用途：将taskd的配置数据预装到etcd中

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查etcd连接
check_etcd() {
    log_info "检查etcd连接状态..."
    
    # 获取etcd Pod名称
    ETCD_POD=$(kubectl get pods -n etcd -l app=etcd -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -z "$ETCD_POD" ]; then
        log_error "未找到etcd Pod，请先部署etcd"
        log_info "运行命令: ./deploy-etcd.sh"
        exit 1
    fi
    
    # 测试etcd健康状态
    if ! kubectl exec -n etcd "$ETCD_POD" -- etcdctl endpoint health >/dev/null 2>&1; then
        log_error "etcd健康检查失败"
        exit 1
    fi
    
    log_success "etcd连接正常"
}

# 初始化配置数据
init_config_data() {
    log_info "初始化taskd配置数据到etcd..."
    
    # 配置前缀
    PREFIX="/config/taskd"
    
    # MongoDB配置
    log_info "设置MongoDB配置..."
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/mongodb/uri" "mongodb://formula:<EMAIL>:27017/overseas?authSource=admin"
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/mongodb/database" "taskd_prod_db"
    
    # PostgreSQL配置
    log_info "设置PostgreSQL配置..."
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/postgresql/host" "pg-server-prod-service.pg.svc.cluster.local"
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/postgresql/user" "admin"
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/postgresql/password" "SecurePass123!"
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/postgresql/database" "overseas"
    
    # Pulsar配置
    log_info "设置Pulsar配置..."
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/pulsar/service_url" "pulsar://*************:30164"
    
    # LLM配置
    log_info "设置LLM配置..."
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/llm/providers/volcengine_ark/api_key" "cdb5ba17-1758-4d11-be9b-379c9453fb16"
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put "${PREFIX}/llm/providers/volcengine_ark/base_url" "https://ark.cn-beijing.volces.com/api/v3"
    
    log_success "配置数据初始化完成"
}

# 验证配置数据
verify_config_data() {
    log_info "验证配置数据..."
    
    PREFIX="/config/taskd"
    
    # 列出所有配置
    log_info "当前etcd中的taskd配置："
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl get "${PREFIX}" --prefix --keys-only | sed "s|^${PREFIX}/||" | sort
    
    # 统计配置数量
    CONFIG_COUNT=$(kubectl exec -n etcd "$ETCD_POD" -- etcdctl get "${PREFIX}" --prefix --keys-only | wc -l)
    log_success "共有 ${CONFIG_COUNT} 个配置项"
}

# 显示配置使用说明
show_usage_info() {
    log_info "配置使用说明："
    echo ""
    echo "1. 查看所有配置："
    echo "   kubectl exec -n etcd <etcd-pod> -- etcdctl get /config/taskd --prefix"
    echo ""
    echo "2. 修改配置示例："
    echo "   kubectl exec -n etcd <etcd-pod> -- etcdctl put /config/taskd/mongodb/database \"new_database\""
    echo ""
    echo "3. 删除配置："
    echo "   kubectl exec -n etcd <etcd-pod> -- etcdctl del /config/taskd/mongodb/uri"
    echo ""
    echo "4. 重启taskd以加载新配置："
    echo "   kubectl delete pod -n ovs -l app=taskd"
    echo ""
    log_warn "注意：修改配置后需要重启taskd服务才能生效"
}

# 主函数
main() {
    log_info "开始初始化etcd配置数据..."
    echo "========================================"
    
    check_etcd
    init_config_data
    verify_config_data
    
    echo "========================================"
    log_success "etcd配置初始化完成！"
    show_usage_info
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi