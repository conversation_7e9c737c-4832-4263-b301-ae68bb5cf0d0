# 招投标预处理Agent API调用示例

## 概述

本文档提供所有招投标预处理Agent的详细API调用示例，包括请求参数、响应格式和错误处理示例。

## 通用请求格式

所有Agent都遵循A2A协议的标准请求格式：

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "技能ID",
    "input": {
      // 具体的输入参数
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456", 
      "trace_id": "trace_789",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  },
  "id": "request_001"
}
```

## Agent 1: 数据提取与验证Agent

### 接口信息
- **URL**: `http://taskd-service:8601/agents/tender-data-extraction`
- **技能ID**: `extract_structured_data`

### 请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "extract_structured_data",
    "input": {
      "raw_text": "【政府采购项目】某市教育局2024年度计算机设备采购项目。项目编号：EDU-2024-001。预算金额：500万元。投标截止时间：2024年2月15日下午3点。采购人：某市教育局，地址：某市中心路123号。联系人：张老师，电话：010-12345678。本项目采购台式计算机200台，笔记本电脑100台，投影仪50台。投标人需具备计算机信息系统集成资质，注册资金不少于1000万元，近三年有类似项目经验。",
      "language": "chinese",
      "extraction_mode": "standard",
      "fields": ["basic_info", "organization", "tender_requirements", "supplier_requirements"]
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_001",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  },
  "id": "extract_001"
}
```

### 响应示例

```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_extract_1642234200_extract_structured_data",
    "status": "completed",
    "output": {
      "extracted_data": {
        "basic_info": {
          "project_name": "某市教育局2024年度计算机设备采购项目",
          "project_number": "EDU-2024-001",
          "deadline": "2024年2月15日下午3点",
          "budget": "500万元",
          "contact_info": "张老师，电话：010-12345678"
        },
        "organization": {
          "purchaser_name": "某市教育局",
          "purchaser_address": "某市中心路123号",
          "agent_name": "",
          "agent_contact": ""
        },
        "tender_requirements": {
          "technical_requirements": [
            "台式计算机200台",
            "笔记本电脑100台",
            "投影仪50台"
          ],
          "commercial_requirements": [
            "预算金额500万元"
          ],
          "delivery_requirements": "按合同约定时间交付",
          "performance_requirements": "设备需符合国家相关标准"
        },
        "supplier_requirements": {
          "qualification_requirements": [
            "具备计算机信息系统集成资质"
          ],
          "financial_requirements": "注册资金不少于1000万元",
          "experience_requirements": "近三年有类似项目经验",
          "certification_requirements": []
        }
      },
      "validation_result": {
        "status": "valid",
        "completeness_score": 0.85,
        "missing_fields": ["agent_name", "agent_contact"],
        "validation_errors": []
      },
      "processing_metadata": {
        "model_used": "gpt-4",
        "tokens_consumed": 1250,
        "processing_time": 3.2,
        "confidence_score": 0.92
      }
    },
    "metadata": {
      "execution_time": 3200,
      "tokens_used": 1250,
      "agent_version": "1.0.0"
    }
  },
  "id": "extract_001"
}
```

### 错误响应示例

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32000,
    "message": "AI模型调用失败",
    "data": {
      "error_type": "ai_failure",
      "retry_after": 5,
      "context": {
        "model_error": "模型服务暂时不可用",
        "suggestion": "请稍后重试"
      }
    }
  },
  "id": "extract_001"
}
```

## Agent 2: 智能分类Agent

### 接口信息
- **URL**: `http://taskd-service:8601/agents/tender-classification`
- **技能ID**: `classify_tender`

### 请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "classify_tender",
    "input": {
      "extracted_data": {
        "basic_info": {
          "project_name": "某市教育局2024年度计算机设备采购项目",
          "project_number": "EDU-2024-001"
        },
        "tender_requirements": {
          "technical_requirements": [
            "台式计算机200台",
            "笔记本电脑100台", 
            "投影仪50台"
          ]
        }
      },
      "classification_depth": 3,
      "language": "chinese"
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_002",
      "timestamp": "2024-01-15T10:35:00Z"
    }
  },
  "id": "classify_001"
}
```

### 响应示例

```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_classify_1642234500_classify_tender",
    "status": "completed",
    "output": {
      "industry_classification": {
        "level1": "制造业",
        "level2": "计算机、通信和其他电子设备制造业",
        "level3": "计算机整机制造",
        "confidence_scores": {
          "level1": 0.95,
          "level2": 0.89,
          "level3": 0.82
        }
      },
      "procurement_type": {
        "main_type": "product",
        "sub_types": ["硬件设备", "办公设备", "IT设备"],
        "confidence_score": 0.93
      },
      "business_domain": {
        "primary_domain": "教育装备",
        "secondary_domains": ["政府采购", "IT设备采购"],
        "domain_tags": ["教育", "计算机", "硬件", "批量采购"]
      },
      "processing_metadata": {
        "model_used": "gpt-4",
        "tokens_consumed": 680,
        "processing_time": 2.1
      }
    },
    "metadata": {
      "execution_time": 2100,
      "tokens_used": 680,
      "agent_version": "1.0.0"
    }
  },
  "id": "classify_001"
}
```

## Agent 3: 内容增强Agent

### 接口信息
- **URL**: `http://taskd-service:8601/agents/tender-content-enhancement`
- **技能ID**: `enhance_content`

### 请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "enhance_content",
    "input": {
      "extracted_data": {
        "basic_info": {
          "project_name": "某市教育局2024年度计算机设备采购项目",
          "budget": "500万元"
        },
        "tender_requirements": {
          "technical_requirements": ["台式计算机200台", "笔记本电脑100台", "投影仪50台"]
        }
      },
      "classification_data": {
        "industry_classification": {
          "level1": "制造业",
          "level2": "计算机、通信和其他电子设备制造业",
          "level3": "计算机整机制造"
        },
        "procurement_type": {
          "main_type": "product"
        }
      },
      "enhancement_options": {
        "generate_keywords": true,
        "optimize_title": true,
        "calculate_score": true,
        "generate_summary": true
      },
      "language": "chinese"
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_003",
      "timestamp": "2024-01-15T10:40:00Z"
    }
  },
  "id": "enhance_001"
}
```

### 响应示例

```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_enhance_1642234800_enhance_content",
    "status": "completed",
    "output": {
      "keywords": {
        "technical_keywords": ["计算机", "台式机", "笔记本电脑", "投影仪", "IT设备"],
        "business_keywords": ["政府采购", "教育局", "批量采购", "设备采购", "公开招标"],
        "core_products": ["台式计算机", "笔记本电脑", "投影仪"]
      },
      "optimized_title": {
        "original_title": "某市教育局2024年度计算机设备采购项目",
        "new_title": "某市教育局IT设备批量采购：台式机200台+笔记本100台+投影仪50台（预算500万）",
        "optimization_reason": "增加了具体数量信息和预算金额，提高标题的信息密度和检索性"
      },
      "quality_score": {
        "overall_score": 7.2,
        "dimension_scores": {
          "project_value": 7.5,
          "supplier_preference": 6.8,
          "client_authority": 8.5,
          "cooperation_potential": 7.0,
          "information_completeness": 6.2
        },
        "scoring_reasons": [
          "项目金额适中（500万），具有一定吸引力",
          "政府教育部门采购，权威性高",
          "信息相对完整，但缺少部分技术细节",
          "批量采购，有利于成本控制"
        ]
      },
      "summary": {
        "executive_summary": "某市教育局发起的计算机设备批量采购项目，总预算500万元，包含台式机、笔记本电脑和投影仪等教育装备。项目具有较高的政府权威性和稳定的付款保障。",
        "key_points": [
          "政府教育部门采购，权威性高",
          "批量采购降低单价，规模效应明显",
          "预算充足，500万元资金保障",
          "设备种类明确，需求清晰"
        ],
        "risk_factors": [
          "竞争可能较为激烈",
          "政府采购流程相对复杂",
          "交付时间要求可能较紧"
        ]
      },
      "processing_metadata": {
        "model_used": "gpt-4",
        "tokens_consumed": 1420,
        "processing_time": 4.3
      }
    },
    "metadata": {
      "execution_time": 4300,
      "tokens_used": 1420,
      "agent_version": "1.0.0"
    }
  },
  "id": "enhance_001"
}
```

## Agent 4: 多语言处理Agent

### 接口信息
- **URL**: `http://taskd-service:8601/agents/tender-multilingual`
- **技能ID**: `process_multilingual`

### 请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "process_multilingual",
    "input": {
      "chinese_data": {
        "optimized_title": {
          "new_title": "某市教育局IT设备批量采购：台式机200台+笔记本100台+投影仪50台（预算500万）"
        },
        "keywords": {
          "technical_keywords": ["计算机", "台式机", "笔记本电脑", "投影仪", "IT设备"],
          "business_keywords": ["政府采购", "教育局", "批量采购"]
        },
        "summary": {
          "executive_summary": "某市教育局发起的计算机设备批量采购项目，总预算500万元。"
        }
      },
      "target_languages": ["english"],
      "translation_mode": "batch",
      "fields_to_translate": ["optimized_title", "keywords", "summary"]
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_004",
      "timestamp": "2024-01-15T10:45:00Z"
    }
  },
  "id": "multilingual_001"
}
```

### 响应示例

```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_multilingual_1642235100_process_multilingual",
    "status": "completed",
    "output": {
      "translated_data": {
        "english": {
          "optimized_title": {
            "new_title": "Municipal Education Bureau IT Equipment Bulk Procurement: 200 Desktop Computers + 100 Laptops + 50 Projectors (Budget: 5 Million RMB)"
          },
          "keywords": {
            "technical_keywords": ["computer", "desktop", "laptop", "projector", "IT equipment"],
            "business_keywords": ["government procurement", "education bureau", "bulk purchase"]
          },
          "summary": {
            "executive_summary": "A computer equipment bulk procurement project initiated by the Municipal Education Bureau with a total budget of 5 million RMB."
          }
        }
      },
      "translation_quality": {
        "overall_quality": 0.92,
        "field_quality": {
          "optimized_title": 0.95,
          "keywords": 0.88,
          "summary": 0.93
        },
        "quality_issues": []
      },
      "processing_metadata": {
        "model_used": "gpt-4",
        "tokens_consumed": 890,
        "processing_time": 2.8,
        "batch_size": 3
      }
    },
    "metadata": {
      "execution_time": 2800,
      "tokens_used": 890,
      "agent_version": "1.0.0"
    }
  },
  "id": "multilingual_001"
}
```

## Agent 5: 结果聚合Agent

### 接口信息
- **URL**: `http://taskd-service:8601/agents/tender-result-aggregation`
- **技能ID**: `aggregate_results`

### 请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "aggregate_results",
    "input": {
      "extraction_result": {
        "extracted_data": {
          "basic_info": {
            "project_name": "某市教育局2024年度计算机设备采购项目",
            "project_number": "EDU-2024-001",
            "budget": "500万元"
          }
        },
        "validation_result": {
          "status": "valid",
          "completeness_score": 0.85
        }
      },
      "classification_result": {
        "industry_classification": {
          "level1": "制造业",
          "level2": "计算机、通信和其他电子设备制造业",
          "level3": "计算机整机制造"
        },
        "procurement_type": {
          "main_type": "product"
        }
      },
      "enhancement_result": {
        "keywords": {
          "technical_keywords": ["计算机", "台式机", "笔记本电脑"],
          "business_keywords": ["政府采购", "教育局"]
        },
        "quality_score": {
          "overall_score": 7.2
        }
      },
      "multilingual_result": {
        "translated_data": {
          "english": {
            "optimized_title": {
              "new_title": "Municipal Education Bureau IT Equipment Bulk Procurement"
            }
          }
        }
      },
      "output_format": "standard",
      "persistence_options": {
        "save_to_db": true,
        "generate_report": false,
        "notify_subscribers": false
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_005",
      "timestamp": "2024-01-15T10:50:00Z"
    }
  },
  "id": "aggregate_001"
}
```

### 响应示例

```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_aggregate_1642235400_aggregate_results",
    "status": "completed",
    "output": {
      "final_result": {
        "id": "tender_processed_1642235400",
        "original_text": "【政府采购项目】某市教育局2024年度计算机设备采购项目...",
        "extracted_data": {
          "basic_info": {
            "project_name": "某市教育局2024年度计算机设备采购项目",
            "project_number": "EDU-2024-001",
            "budget": "500万元"
          }
        },
        "classification": {
          "industry_classification": {
            "level1": "制造业",
            "level2": "计算机、通信和其他电子设备制造业",
            "level3": "计算机整机制造"
          }
        },
        "enhancement": {
          "keywords": {
            "technical_keywords": ["计算机", "台式机", "笔记本电脑"],
            "business_keywords": ["政府采购", "教育局"]
          },
          "quality_score": {
            "overall_score": 7.2
          }
        },
        "multilingual": {
          "english": {
            "title": "Municipal Education Bureau IT Equipment Bulk Procurement"
          }
        },
        "processing_summary": {
          "total_processing_time": 12.4,
          "agents_involved": ["extraction", "classification", "enhancement", "multilingual"],
          "success_rate": 1.0,
          "overall_confidence": 0.89
        }
      },
      "persistence_result": {
        "database_id": "tender_db_1642235400",
        "save_status": "success",
        "report_url": null,
        "notification_status": "skipped"
      },
      "metadata": {
        "processing_timestamp": "2024-01-15T10:50:30Z",
        "version": "1.0.0",
        "quality_metrics": {
          "data_completeness": 0.85,
          "processing_accuracy": 0.92,
          "translation_quality": 0.92
        }
      }
    },
    "metadata": {
      "execution_time": 500,
      "tokens_used": 0,
      "agent_version": "1.0.0"
    }
  },
  "id": "aggregate_001"
}
```

## Agent 6: 编排Agent

### 接口信息
- **URL**: `http://taskd-service:8601/agents/tender-preprocessing-orchestration`
- **技能ID**: `orchestrate_preprocessing`

### 单条数据处理请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "orchestrate_preprocessing",
    "input": {
      "input_data": {
        "raw_text": "【政府采购项目】某市教育局2024年度计算机设备采购项目。项目编号：EDU-2024-001。预算金额：500万元...",
        "metadata": {
          "source": "government_website",
          "crawl_time": "2024-01-15T09:00:00Z",
          "priority": "normal"
        }
      },
      "processing_options": {
        "concurrent_limit": 10,
        "enable_caching": true,
        "timeout": 300,
        "retry_attempts": 3,
        "skip_steps": []
      },
      "notification_config": {
        "webhook_url": "https://api.example.com/webhook",
        "notify_on_completion": true,
        "notify_on_error": true
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_orchestration_001",
      "timestamp": "2024-01-15T11:00:00Z"
    }
  },
  "id": "orchestrate_001"
}
```

### 多条数据并发处理请求示例

```json
{
  "jsonrpc": "2.0",
  "method": "execute_skill",
  "params": {
    "skill_id": "orchestrate_preprocessing",
    "input": {
      "input_data": [
        {
          "raw_text": "【政府采购项目】某市教育局2024年度计算机设备采购项目...",
          "metadata": {
            "source_id": "tender_001",
            "priority": "high"
          }
        },
        {
          "raw_text": "【企业采购公告】某科技公司服务器设备采购项目...",
          "metadata": {
            "source_id": "tender_002",
            "priority": "normal"
          }
        },
        {
          "raw_text": "【工程招标】某市道路建设工程招标公告...",
          "metadata": {
            "source_id": "tender_003",
            "priority": "low"
          }
        }
      ],
      "processing_options": {
        "concurrent_limit": 5,
        "enable_caching": true,
        "timeout": 600,
        "retry_attempts": 2,
        "skip_steps": ["multilingual"]
      }
    },
    "context": {
      "user_id": "user_123",
      "company_id": "company_456",
      "trace_id": "trace_orchestration_batch_001",
      "timestamp": "2024-01-15T11:00:00Z"
    }
  },
  "id": "orchestrate_batch_001"
}
```

### 编排Agent响应示例

```json
{
  "jsonrpc": "2.0",
  "result": {
    "task_id": "task_orchestrate_1642236000_orchestrate_preprocessing",
    "status": "completed",
    "output": {
      "orchestration_result": {
        "total_items": 3,
        "processed_items": 3,
        "successful_items": 2,
        "failed_items": 1,
        "processing_time": 45.6,
        "results": [
          {
            "input_id": "tender_001",
            "status": "success",
            "result": {
              "id": "tender_processed_1642236001",
              "extracted_data": {
                "basic_info": {
                  "project_name": "某市教育局2024年度计算机设备采购项目"
                }
              },
              "classification": {
                "industry_classification": {
                  "level1": "制造业"
                }
              },
              "quality_score": 7.2
            },
            "processing_steps": [
              {
                "agent": "tender-data-extraction",
                "skill": "extract_structured_data",
                "status": "success",
                "duration": 3.2
              },
              {
                "agent": "tender-classification",
                "skill": "classify_tender",
                "status": "success",
                "duration": 2.1
              },
              {
                "agent": "tender-content-enhancement",
                "skill": "enhance_content",
                "status": "success",
                "duration": 4.3
              },
              {
                "agent": "tender-result-aggregation",
                "skill": "aggregate_results",
                "status": "success",
                "duration": 0.5
              }
            ]
          },
          {
            "input_id": "tender_002",
            "status": "success",
            "result": {
              "id": "tender_processed_1642236002",
              "quality_score": 6.8
            },
            "processing_steps": [
              {
                "agent": "tender-data-extraction",
                "skill": "extract_structured_data",
                "status": "success",
                "duration": 2.8
              }
            ]
          },
          {
            "input_id": "tender_003",
            "status": "failed",
            "error": "数据提取失败：文本格式不支持",
            "processing_steps": [
              {
                "agent": "tender-data-extraction",
                "skill": "extract_structured_data",
                "status": "failed",
                "duration": 1.2,
                "error": "Invalid text format"
              }
            ]
          }
        ]
      },
      "orchestration_metadata": {
        "workflow_id": "workflow_1642236000",
        "started_at": "2024-01-15T11:00:00Z",
        "completed_at": "2024-01-15T11:00:45Z",
        "agents_used": [
          "tender-data-extraction",
          "tender-classification", 
          "tender-content-enhancement",
          "tender-result-aggregation"
        ],
        "resource_usage": {
          "total_tokens": 8560,
          "total_api_calls": 8,
          "peak_memory": 256.7
        }
      }
    },
    "metadata": {
      "execution_time": 45600,
      "tokens_used": 8560,
      "agent_version": "1.0.0"
    }
  },
  "id": "orchestrate_batch_001"
}
```

## 错误处理示例

### 通用错误码

| 错误码 | 错误类型 | 描述 |
|--------|----------|------|
| -32001 | `validation_error` | 输入参数验证失败 |
| -32002 | `ai_failure` | AI模型调用失败 |
| -32003 | `timeout` | 处理超时 |
| -32004 | `internal_error` | 内部服务错误 |
| -32005 | `agent_unavailable` | Agent服务不可用 |

### 参数验证错误

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32001,
    "message": "输入参数验证失败",
    "data": {
      "error_type": "validation_error",
      "validation_errors": [
        "raw_text字段不能为空",
        "language字段必须是chinese或english"
      ],
      "context": {
        "field": "raw_text",
        "expected": "non-empty string",
        "received": ""
      }
    }
  },
  "id": "request_001"
}
```

### 超时错误

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32003,
    "message": "处理超时",
    "data": {
      "error_type": "timeout",
      "retry_after": 30,
      "context": {
        "timeout_duration": 300,
        "processing_time": 305,
        "suggestion": "可以增加timeout参数或稍后重试"
      }
    }
  },
  "id": "request_001"
}
```

### Agent不可用错误

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32005,
    "message": "Agent服务暂时不可用",
    "data": {
      "error_type": "agent_unavailable",
      "retry_after": 60,
      "context": {
        "agent": "tender-classification",
        "last_health_check": "2024-01-15T10:55:00Z",
        "suggestion": "请稍后重试或联系系统管理员"
      }
    }
  },
  "id": "request_001"
}
```

## 性能监控示例

### 健康检查请求

```bash
curl -X GET http://taskd-service:8601/agents/tender-data-extraction/health
```

### 健康检查响应

```json
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 86400000,
  "dependencies": {
    "llm_service": "ok",
    "database": "ok",
    "validator": "ok"
  },
  "performance": {
    "avg_response_time": 2500,
    "requests_per_minute": 45,
    "error_rate": 0.02,
    "success_rate": 0.98
  },
  "timestamp": "2024-01-15T11:30:00Z"
}
```

### Agent Card获取

```bash
curl -X GET http://taskd-service:8601/agents/tender-data-extraction/.well-known/agent.json
```

返回完整的Agent Card JSON配置信息。

## 使用建议

1. **并发处理**：使用编排Agent处理多条数据时，建议设置合理的`concurrent_limit`避免资源过载
2. **错误重试**：对于AI调用失败，建议设置2-3次重试
3. **超时设置**：根据数据复杂度调整`timeout`参数，建议300-600秒
4. **缓存使用**：启用`enable_caching`可以提高相似数据的处理速度
5. **监控跟踪**：使用`trace_id`进行请求链路追踪和问题定位