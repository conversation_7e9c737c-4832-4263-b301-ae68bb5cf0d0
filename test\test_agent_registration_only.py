#!/usr/bin/env python3

import json
import requests
import os
import pytest

class TestAgentRegistration:
    """P0级别: Agent注册和列表测试"""
    
    def test_agent_list(self):
        """测试Agent列表获取"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        agent_url = f"{base_url}/agents"
        
        print("=== P0: Agent注册列表测试 ===")
        
        response = requests.get(agent_url, timeout=30)
        print(f"请求方法: GET")  
        print(f"请求URL: {agent_url}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            agent_count = data.get('count', 0)
            print(f"注册数量: {agent_count} 个Agent")
            assert agent_count > 0, "Agent数量应该大于0"
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            pytest.fail(f"获取Agent列表失败: {response.status_code}")

if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 