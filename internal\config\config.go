package config

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/viper"
	clientv3 "go.etcd.io/etcd/client/v3"
)

// TODO API-KEY 等配置能否由k8s加密管理？而不是硬编码写死在taskd

// 应用总配置
type Config struct {
	Server     ServerConfig
	Logger     LoggerConfig
	MongoDB    MongoDBConfig
	PostgreSQL PostgreSQLConfig
	Pulsar     PulsarConfig
	LLM        LLMConfigManager
	Prompts    map[string]PromptSet
	Chat       ChatConfig
	ETCD       ETCDConfig
}

// 服务器配置
type ServerConfig struct {
	Port string
	Mode string
}

// 日志配置
type LoggerConfig struct {
	Level string
}

// MongoDB 配置
type MongoDBConfig struct {
	URI            string `mapstructure:"uri"`
	Database       string
	TimeoutSeconds int `mapstructure:"timeout_seconds"`
}

// PostgreSQL 配置
type PostgreSQLConfig struct {
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	User            string `mapstructure:"user"`
	Password        string `mapstructure:"password"`
	Database        string `mapstructure:"database"`
	SSLMode         string `mapstructure:"ssl_mode"`
	TimeoutSeconds  int    `mapstructure:"timeout_seconds"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// Pulsar 配置
type PulsarConfig struct {
	ServiceURL string `mapstructure:"service_url"`
	Consumer   PulsarConsumerConfig
}

type PulsarConsumerConfig struct {
	TopicReportSummary string `mapstructure:"topic_report_summary"`
	SubscriptionName   string `mapstructure:"subscription_name"`
}

// LLMConfigManager 管理所有 LLM 提供商的配置
type LLMConfigManager struct {
	DefaultProvider string                          `mapstructure:"default_provider"`
	Providers       map[string]OpenAIProviderConfig `mapstructure:"providers"`
}

// OpenAIProviderConfig 单个 OpenAI 兼容提供商的配置
type OpenAIProviderConfig struct {
	APIKey                string            `mapstructure:"api_key"`  // 单个API Key (向后兼容)
	APIKeys               []string          `mapstructure:"api_keys"` // API Key池
	BaseURL               string            `mapstructure:"base_url"`
	Models                map[string]string `mapstructure:"models"` // 别名 -> 实际模型 ID
	DefaultModelAlias     string            `mapstructure:"default_model_alias"`
	DefaultModelID        string            // 解析后的实际默认模型 ID (代码中填充)
	RequestTimeoutSeconds int               `mapstructure:"request_timeout_seconds"`
	MaxConcurrentRequests int               `mapstructure:"max_concurrent_requests"`
}

// Prompt 定义
type PromptSet struct {
	SysPrompt  string `mapstructure:"sys_prompt"`
	UserPrompt string `mapstructure:"user_prompt"`
}

// ChatConfig 闲聊配置
type ChatConfig struct {
	MaxSocketConnections  int     `mapstructure:"max_socket_connections"`
	SessionTimeoutMinutes int     `mapstructure:"session_timeout_minutes"`
	DefaultTokenLimit     int     `mapstructure:"default_token_limit"`
	CleanupIntervalHours  int     `mapstructure:"cleanup_interval_hours"`
	PingIntervalSeconds   int     `mapstructure:"ping_interval_seconds"`
	SystemPrompt          string  `mapstructure:"system_prompt"`
	ModelProvider         string  `mapstructure:"model_provider"`
	ModelAlias            string  `mapstructure:"model_alias"`
	Temperature           float64 `mapstructure:"temperature"`
	MaxTokens             int     `mapstructure:"max_tokens"`
	EnableTokenTracking   bool    `mapstructure:"enable_token_tracking"`
}

// ETCDConfig etcd配置
type ETCDConfig struct {
	Enabled           bool     `mapstructure:"enabled"`
	Endpoints         []string `mapstructure:"endpoints"`
	Username          string   `mapstructure:"username"`
	Password          string   `mapstructure:"password"`
	DialTimeoutSec    int      `mapstructure:"dial_timeout_sec"`
	RequestTimeoutSec int      `mapstructure:"request_timeout_sec"`
	ConfigPrefix      string   `mapstructure:"config_prefix"`
	RequiredForK8s    bool     `mapstructure:"required_for_k8s"`
}

var AppConfig Config // 全局应用配置实例

// LoadConfig 加载配置
func LoadConfig(configPath ...string) error {
	v := viper.New()
	if len(configPath) > 0 && configPath[0] != "" {
		v.SetConfigFile(configPath[0])
	} else {
		v.SetConfigName("config")
		v.SetConfigType("yaml")
		v.AddConfigPath("./configs")
		v.AddConfigPath(".")
	}

	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Println("配置文件未找到，将依赖环境变量和默认值。")
		} else {
			return fmt.Errorf("读取配置文件失败: %w", err)
		}
	}

	v.AutomaticEnv()
	// SERVER_PORT, MONGODB_URI, LLM_PROVIDERS_VOLCENGINE_ARK_API_KEY
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_", "-", "_"))

	// 设置默认值
	v.SetDefault("server.port", "8601")
	v.SetDefault("server.mode", "release")
	v.SetDefault("logger.level", "info")
	v.SetDefault("mongodb.timeout_seconds", 10)
	v.SetDefault("postgresql.host", "localhost")
	v.SetDefault("postgresql.port", 5432)
	v.SetDefault("postgresql.user", "admin")
	v.SetDefault("postgresql.password", "SecurePass123!")
	v.SetDefault("postgresql.database", "taskd_tokens")
	v.SetDefault("postgresql.ssl_mode", "disable")
	v.SetDefault("postgresql.timeout_seconds", 10)
	v.SetDefault("postgresql.max_open_conns", 25)
	v.SetDefault("postgresql.max_idle_conns", 5)
	v.SetDefault("postgresql.conn_max_lifetime", 300)
	v.SetDefault("llm.default_provider", "volcengine_ark") // 默认提供商

	// 聊天默认配置
	v.SetDefault("chat.max_socket_connections", 100)
	v.SetDefault("chat.session_timeout_minutes", 60)
	v.SetDefault("chat.default_token_limit", 4000)
	v.SetDefault("chat.cleanup_interval_hours", 1)
	v.SetDefault("chat.ping_interval_seconds", 30)
	v.SetDefault("chat.system_prompt", "你是一个友善、有帮助的AI助手，喜欢与用户进行轻松愉快的对话。请保持对话自然流畅，并根据用户的问题给出恰当的回应。")
	v.SetDefault("chat.model_provider", "volcengine_ark")
	v.SetDefault("chat.model_alias", "doubao-1-5-pro-32k-250115")
	v.SetDefault("chat.temperature", 0.7)
	v.SetDefault("chat.max_tokens", 1000)
	v.SetDefault("chat.enable_token_tracking", true)

	// ETCD默认配置
	v.SetDefault("etcd.enabled", false)
	v.SetDefault("etcd.endpoints", []string{"localhost:2379"})
	v.SetDefault("etcd.dial_timeout_sec", 5)
	v.SetDefault("etcd.request_timeout_sec", 10)
	v.SetDefault("etcd.config_prefix", "/config/taskd")
	v.SetDefault("etcd.required_for_k8s", false)

	// 设置LLM默认配置
	v.SetDefault("llm.providers.volcengine_ark.base_url", "https://ark.cn-beijing.volces.com/api/v3")
	v.SetDefault("llm.providers.volcengine_ark.models.doubao-1-5-pro-32k-250115", "doubao-1-5-pro-32k-250115")
	v.SetDefault("llm.providers.volcengine_ark.models.report-summarizer", "doubao-1-5-pro-32k-250115")
	v.SetDefault("llm.providers.volcengine_ark.default_model_alias", "report-summarizer")
	v.SetDefault("llm.providers.volcengine_ark.request_timeout_seconds", 300)
	v.SetDefault("llm.providers.volcengine_ark.max_concurrent_requests", 10)

	if err := v.Unmarshal(&AppConfig); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	// 后处理 LLM 提供商配置：从环境变量加载 API Key，并解析默认模型 ID
	for name, providerCfg := range AppConfig.LLM.Providers {
		// 处理单个API Key (向后兼容)
		envKeyForAPIKey := strings.ToUpper(fmt.Sprintf("LLM_PROVIDERS_%s_API_KEY", strings.ReplaceAll(name, "-", "_")))
		if apiKeyFromEnv := v.GetString(envKeyForAPIKey); apiKeyFromEnv != "" {
			providerCfg.APIKey = apiKeyFromEnv
		}

		// 处理API Key池
		envKeyForAPIKeys := strings.ToUpper(fmt.Sprintf("LLM_PROVIDERS_%s_API_KEYS", strings.ReplaceAll(name, "-", "_")))
		if apiKeysFromEnv := v.GetString(envKeyForAPIKeys); apiKeysFromEnv != "" {
			// 支持逗号分隔的多个API Keys
			keys := strings.Split(apiKeysFromEnv, ",")
			for i, key := range keys {
				keys[i] = strings.TrimSpace(key)
			}
			providerCfg.APIKeys = keys
		}

		// 合并API Keys: 如果同时配置了单个和多个，将单个添加到池中
		allKeys := make([]string, 0)
		if len(providerCfg.APIKeys) > 0 {
			allKeys = append(allKeys, providerCfg.APIKeys...)
		}
		if providerCfg.APIKey != "" && !contains(allKeys, providerCfg.APIKey) {
			allKeys = append(allKeys, providerCfg.APIKey)
		}

		if len(allKeys) == 0 {
			fmt.Printf("警告: LLM 提供商 '%s' 的 API Key 未在环境变量 %s/%s 或配置文件中找到。\n",
				name, envKeyForAPIKey, envKeyForAPIKeys)
		} else {
			providerCfg.APIKeys = allKeys
			// 设置默认API Key为第一个
			if providerCfg.APIKey == "" {
				providerCfg.APIKey = allKeys[0]
			}
		}

		if actualModelID, ok := providerCfg.Models[providerCfg.DefaultModelAlias]; ok {
			providerCfg.DefaultModelID = actualModelID
		} else if providerCfg.DefaultModelAlias != "" {
			fmt.Printf("警告: LLM 提供商 '%s' 的默认模型别名 '%s' 在其模型映射中未找到。\n", name, providerCfg.DefaultModelAlias)
			providerCfg.DefaultModelID = "" // 或者设置为一个明确的空值
		}
		AppConfig.LLM.Providers[name] = providerCfg // 更新map中的配置
	}

	// 从环境变量覆盖敏感信息 (主要用于 K8s Secrets)
	if mongoURI := v.GetString("MONGODB_URI_ENV"); mongoURI != "" {
		AppConfig.MongoDB.URI = mongoURI
	}
	if pulsarURL := v.GetString("PULSAR_SERVICE_URL_ENV"); pulsarURL != "" {
		AppConfig.Pulsar.ServiceURL = pulsarURL
	}

	// PostgreSQL 环境变量覆盖
	if pgHost := v.GetString("POSTGRESQL_HOST_ENV"); pgHost != "" {
		AppConfig.PostgreSQL.Host = pgHost
	}
	if pgPort := v.GetInt("POSTGRESQL_PORT_ENV"); pgPort != 0 {
		AppConfig.PostgreSQL.Port = pgPort
	}
	if pgUser := v.GetString("POSTGRESQL_USER_ENV"); pgUser != "" {
		AppConfig.PostgreSQL.User = pgUser
	}
	if pgPassword := v.GetString("POSTGRESQL_PASSWORD_ENV"); pgPassword != "" {
		AppConfig.PostgreSQL.Password = pgPassword
	}
	if pgDatabase := v.GetString("POSTGRESQL_DATABASE_ENV"); pgDatabase != "" {
		AppConfig.PostgreSQL.Database = pgDatabase
	}

	// 处理etcd配置加载
	if err := loadETCDConfig(v); err != nil {
		return fmt.Errorf("加载etcd配置失败: %w", err)
	}

	return nil
}

func (c *MongoDBConfig) GetDBTimeout() time.Duration {
	return time.Duration(c.TimeoutSeconds) * time.Second
}

func (c *OpenAIProviderConfig) GetRequestTimeout() time.Duration {
	return time.Duration(c.RequestTimeoutSeconds) * time.Second
}

// GetAPIKeys 获取所有可用的API Keys
func (c *OpenAIProviderConfig) GetAPIKeys() []string {
	if len(c.APIKeys) > 0 {
		return c.APIKeys
	}
	if c.APIKey != "" {
		return []string{c.APIKey}
	}
	return []string{}
}

// contains 检查slice中是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// loadETCDConfig 加载etcd配置并进行强依赖检查
func loadETCDConfig(v *viper.Viper) error {
	// 检查是否在k8s环境（通过环境变量判断）
	isK8sEnv := v.GetString("KUBERNETES_SERVICE_HOST") != ""

	// 从环境变量获取etcd配置
	if etcdEnabled := v.GetBool("ETCD_ENABLED"); etcdEnabled {
		AppConfig.ETCD.Enabled = etcdEnabled
	}

	if etcdEndpoints := v.GetString("ETCD_ENDPOINTS"); etcdEndpoints != "" {
		AppConfig.ETCD.Endpoints = strings.Split(etcdEndpoints, ",")
		// 清理空格
		for i, endpoint := range AppConfig.ETCD.Endpoints {
			AppConfig.ETCD.Endpoints[i] = strings.TrimSpace(endpoint)
		}
	}

	if etcdUsername := v.GetString("ETCD_USERNAME"); etcdUsername != "" {
		AppConfig.ETCD.Username = etcdUsername
	}

	if etcdPassword := v.GetString("ETCD_PASSWORD"); etcdPassword != "" {
		AppConfig.ETCD.Password = etcdPassword
	}

	// k8s环境强依赖检查
	if isK8sEnv {
		AppConfig.ETCD.RequiredForK8s = true

		if !AppConfig.ETCD.Enabled {
			return fmt.Errorf("在k8s环境中，etcd配置中心是必需的，请设置 ETCD_ENABLED=true")
		}

		if len(AppConfig.ETCD.Endpoints) == 0 {
			return fmt.Errorf("在k8s环境中，必须配置etcd端点，请设置 ETCD_ENDPOINTS")
		}

		// 测试etcd连接
		if err := testETCDConnection(); err != nil {
			return fmt.Errorf("无法连接到etcd配置中心: %w", err)
		}

		fmt.Println("✅ etcd配置中心连接成功")

		// 从etcd加载配置
		if err := loadConfigFromETCD(); err != nil {
			return fmt.Errorf("从etcd加载配置失败: %w", err)
		}

		fmt.Println("✅ 从etcd成功加载配置")
	}

	return nil
}

// testETCDConnection 测试etcd连接
func testETCDConnection() error {
	_, cancel := context.WithTimeout(context.Background(),
		time.Duration(AppConfig.ETCD.DialTimeoutSec)*time.Second)
	defer cancel()

	config := clientv3.Config{
		Endpoints:   AppConfig.ETCD.Endpoints,
		DialTimeout: time.Duration(AppConfig.ETCD.DialTimeoutSec) * time.Second,
	}

	if AppConfig.ETCD.Username != "" {
		config.Username = AppConfig.ETCD.Username
		config.Password = AppConfig.ETCD.Password
	}

	client, err := clientv3.New(config)
	if err != nil {
		return fmt.Errorf("创建etcd客户端失败: %w", err)
	}
	defer client.Close()

	// 执行健康检查
	ctx2, cancel2 := context.WithTimeout(context.Background(),
		time.Duration(AppConfig.ETCD.RequestTimeoutSec)*time.Second)
	defer cancel2()

	_, err = client.Status(ctx2, AppConfig.ETCD.Endpoints[0])
	if err != nil {
		return fmt.Errorf("etcd健康检查失败: %w", err)
	}

	return nil
}

// loadConfigFromETCD 从etcd加载配置
func loadConfigFromETCD() error {
	ctx, cancel := context.WithTimeout(context.Background(),
		time.Duration(AppConfig.ETCD.RequestTimeoutSec)*time.Second)
	defer cancel()

	config := clientv3.Config{
		Endpoints:   AppConfig.ETCD.Endpoints,
		DialTimeout: time.Duration(AppConfig.ETCD.DialTimeoutSec) * time.Second,
	}

	if AppConfig.ETCD.Username != "" {
		config.Username = AppConfig.ETCD.Username
		config.Password = AppConfig.ETCD.Password
	}

	client, err := clientv3.New(config)
	if err != nil {
		return err
	}
	defer client.Close()

	prefix := AppConfig.ETCD.ConfigPrefix
	if prefix == "" {
		prefix = "/config/taskd"
	}

	// 获取所有配置
	resp, err := client.Get(ctx, prefix, clientv3.WithPrefix())
	if err != nil {
		return err
	}

	// 解析配置
	configCount := 0
	for _, kv := range resp.Kvs {
		key := string(kv.Key)
		value := string(kv.Value)

		// 解析配置键并应用到对应的配置项
		if err := applyETCDConfig(key, value); err != nil {
			fmt.Printf("警告: 解析etcd配置失败 %s=%s: %v\n", key, value, err)
		} else {
			configCount++
		}
	}

	fmt.Printf("从etcd加载了 %d 个配置项\n", configCount)
	return nil
}

// applyETCDConfig 应用etcd中的配置到AppConfig
func applyETCDConfig(key, value string) error {
	// 移除前缀
	prefix := AppConfig.ETCD.ConfigPrefix
	if prefix == "" {
		prefix = "/config/taskd"
	}

	if !strings.HasPrefix(key, prefix) {
		return nil // 跳过不属于此服务的配置
	}

	configKey := strings.TrimPrefix(key, prefix+"/")

	// 根据配置键设置对应的配置值
	switch {
	case strings.HasPrefix(configKey, "mongodb/"):
		return applyMongoDBConfig(configKey, value)
	case strings.HasPrefix(configKey, "postgresql/"):
		return applyPostgreSQLConfig(configKey, value)
	case strings.HasPrefix(configKey, "pulsar/"):
		return applyPulsarConfig(configKey, value)
	case strings.HasPrefix(configKey, "llm/"):
		return applyLLMConfig(configKey, value)
	default:
		// 其他配置项
		return nil
	}
}

// applyMongoDBConfig 应用MongoDB配置
func applyMongoDBConfig(key, value string) error {
	switch {
	case strings.HasSuffix(key, "uri"):
		AppConfig.MongoDB.URI = value
	case strings.HasSuffix(key, "database"):
		AppConfig.MongoDB.Database = value
	}
	return nil
}

// applyPostgreSQLConfig 应用PostgreSQL配置
func applyPostgreSQLConfig(key, value string) error {
	switch {
	case strings.HasSuffix(key, "host"):
		AppConfig.PostgreSQL.Host = value
	case strings.HasSuffix(key, "user"):
		AppConfig.PostgreSQL.User = value
	case strings.HasSuffix(key, "password"):
		AppConfig.PostgreSQL.Password = value
	case strings.HasSuffix(key, "database"):
		AppConfig.PostgreSQL.Database = value
	}
	return nil
}

// applyPulsarConfig 应用Pulsar配置
func applyPulsarConfig(key, value string) error {
	switch {
	case strings.HasSuffix(key, "service_url"):
		AppConfig.Pulsar.ServiceURL = value
	}
	return nil
}

// applyLLMConfig 应用LLM配置
func applyLLMConfig(key, value string) error {
	// 解析LLM配置路径，如: llm/providers/volcengine_ark/api_key
	parts := strings.Split(key, "/")
	if len(parts) >= 4 && parts[1] == "providers" {
		providerName := parts[2]
		configName := parts[3]

		if provider, exists := AppConfig.LLM.Providers[providerName]; exists {
			switch configName {
			case "api_key":
				provider.APIKey = value
			case "base_url":
				provider.BaseURL = value
			}
			AppConfig.LLM.Providers[providerName] = provider
		}
	}
	return nil
}
