#!/usr/bin/env python3

import json
import requests
import os
import pytest
import time

# 测试用户常量 - 与 init_test_data.sql 中的值保持一致
TEST_USER_ID = "test_user_intent"
TEST_COMPANY_ID = "test_company_intent"

class TestIntentRecognition:
    """P0级别: 意图识别测试"""
    
    def test_basic_intent_recognition(self):
        """测试基本意图识别功能"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 基本意图识别测试 ===")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "我想查看最新的招投标信息"
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_001",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        
        response = requests.post(url, json=payload, timeout=30)
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # 验证响应结构
            assert 'intent' in data, "响应中应包含intent字段"
            assert 'confidence' in data, "响应中应包含confidence字段"
            
            confidence = data.get('confidence', 0)
            assert confidence >= 0.0, f"置信度应该是有效数值: {confidence}"
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            # P0级别测试，非预期错误应该失败
            if response.status_code not in [404, 501]:
                pytest.fail(f"意图识别API调用失败: {response.status_code} - {response.text}")
    
    def test_bidding_analysis_intent(self):
        """测试招投标分析意图识别"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 招投标分析意图识别 ===")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "我想查看最新的政府采购项目和招投标信息"
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_001",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            assert 'intent' in data, "响应中应包含intent字段"
            assert data['intent'] == 'bidding_analysis', f"应识别为招投标分析意图，实际: {data['intent']}"
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            if response.status_code not in [404, 501]:
                pytest.fail(f"招投标分析意图识别失败: {response.status_code} - {response.text}")
    
    def test_business_news_intent(self):
        """测试商机新闻分析意图识别"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 商机新闻分析意图识别 ===")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "帮我分析一下最近的科技行业新闻和市场机会"
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_business",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            assert 'intent' in data, "响应中应包含intent字段"
            assert data['intent'] == 'business_news', f"应识别为商机新闻意图，实际: {data['intent']}"
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            if response.status_code not in [404, 501]:
                pytest.fail(f"商机新闻意图识别失败: {response.status_code} - {response.text}")
    
    def test_casual_chat_intent(self):
        """测试闲聊意图识别"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 闲聊意图识别 ===")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "你好，今天天气怎么样？"
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_casual",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            assert 'intent' in data, "响应中应包含intent字段"
            assert data['intent'] == 'casual_chat', f"应识别为闲聊意图，实际: {data['intent']}"
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            if response.status_code not in [404, 501]:
                pytest.fail(f"闲聊意图识别失败: {response.status_code} - {response.text}")
    
    def test_chat_summary_intent(self):
        """测试聊天记录总结意图识别"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 聊天记录总结意图识别 ===")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "请帮我总结一下之前的对话内容"
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_summary",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            assert 'intent' in data, "响应中应包含intent字段"
            assert data['intent'] == 'chat_summary', f"应识别为聊天总结意图，实际: {data['intent']}"
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            if response.status_code not in [404, 501]:
                pytest.fail(f"聊天总结意图识别失败: {response.status_code} - {response.text}")
    
    def test_invalid_request_format(self):
        """测试无效请求格式异常处理"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 无效请求格式异常测试 ===")
        
        # 缺少必要字段的请求
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "测试消息"
                }
            ]
            # 缺少 user_id, session_id, organization_id
        }
        
        url = f"{base_url}/v1/intent/recognize"
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        print(f"响应状态: {'SUCCESS' if response.status_code == 200 else 'FAILED'}")
        print(f"响应内容: {response.text}")
        
        # 应该返回400错误
        assert response.status_code == 400, f"无效请求应该返回400状态码，实际: {response.status_code}"
        
        if response.status_code == 400:
            data = response.json()
            assert 'code' in data, "错误响应中应包含code字段"
            assert 'message' in data, "错误响应中应包含message字段"
    
    def test_empty_message_content(self):
        """测试空消息内容异常处理"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 空消息内容异常测试 ===")
        
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": ""  # 空内容
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_empty",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"请求方法: POST")
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"HTTP状态码: {response.status_code}")
        
        print(f"响应状态: {'SUCCESS' if response.status_code == 200 else 'FAILED'}")
        print(f"响应内容: {response.text}")
        
        # 空内容应该返回400错误或者能正常处理
        if response.status_code == 400:
            data = response.json()
            assert 'code' in data, "错误响应中应包含code字段"
        elif response.status_code == 200:
            data = response.json()
            assert 'intent' in data, "响应中应包含intent字段"
        else:
            pytest.fail(f"空内容请求返回非预期状态码: {response.status_code}")
    
    def test_supported_intents(self):
        """测试获取支持的意图类型"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 支持的意图类型 ===")
        
        url = f"{base_url}/v1/intent/supported"
        
        response = requests.get(url, timeout=30)
        print(f"请求方法: GET")
        print(f"请求URL: {url}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"支持的意图: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # 验证支持的意图类型
            assert 'intents' in data or 'supported_intents' in data, "响应中应包含支持的意图列表"
            
            # 验证必要的意图类型存在
            supported_intents = data.get('supported_intents', data.get('intents', {}))
            expected_intents = ['bidding_analysis', 'business_news', 'casual_chat', 'chat_summary']
            
            for intent in expected_intents:
                assert intent in supported_intents, f"应支持{intent}意图类型"
                
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            # P0级别测试，非预期错误应该失败
            if response.status_code not in [404, 501]:
                pytest.fail(f"支持意图API调用失败: {response.status_code} - {response.text}")
    
    def test_intent_with_token_consumption(self):
        """测试意图识别的token消耗记录"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 意图识别Token消耗测试 ===")
        
        # 1. 调用意图识别API
        payload = {
            "messages": [
                {
                    "role": "user",
                    "content": "我想查看最新的招投标信息和政府采购项目"
                }
            ],
            "user_id": TEST_USER_ID,
            "session_id": "test_session_token",
            "organization_id": TEST_COMPANY_ID
        }
        
        url = f"{base_url}/v1/intent/recognize"
        print(f"请求URL: {url}")
        print(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=payload, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code != 200:
            pytest.fail(f"意图识别API调用失败: {response.status_code} - {response.text}")
        
        response_data = response.json()
        
        # 2. 验证响应中的token信息
        if "metadata" in response_data and "tokens_used" in response_data["metadata"]:
            token_info = response_data["metadata"]["tokens_used"]
            print(f"响应中的Token信息: input={token_info.get('input_tokens', 0)}, "
                  f"output={token_info.get('output_tokens', 0)}, total={token_info.get('total_tokens', 0)}")
            
            if token_info.get('total_tokens', 0) > 0:
                print("意图识别API返回了token使用信息")
            else:
                print("警告: 意图识别API返回的token使用量为0")
        else:
            print("响应中没有token使用信息")
        
        # 3. 等待一下让token记录完成
        time.sleep(2)
        
        # 4. 检查Token消耗统计
        token_url = f"{base_url}/v1/tokens/stats/user/{TEST_USER_ID}"
        token_response = requests.get(token_url, timeout=30)
        
        print(f"Token统计API状态码: {token_response.status_code}")
        
        if token_response.status_code == 200:
            token_data = token_response.json()
            print(f"Token统计API响应: {json.dumps(token_data, ensure_ascii=False, indent=2)}")
            
            # 验证基本字段
            assert token_data["user_id"] == TEST_USER_ID
            assert token_data["company_id"] == TEST_COMPANY_ID
            assert "intent" in response_data, "响应中缺少intent字段"
            assert response_data["intent"] == "bidding_analysis", f"意图识别结果不符合预期: {response_data['intent']}"
            
            print("意图识别和token记录功能测试通过!")
        else:
            print(f"Token统计API失败: {response.text}")
            # 不强制失败，允许token服务暂时不可用
            print("Token统计服务可能暂时不可用，但意图识别功能正常")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])