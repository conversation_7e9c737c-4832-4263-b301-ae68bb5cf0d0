package models

// SentimentAnalysisRequest 定义了情感分析API的请求体
type SentimentAnalysisRequest struct {
	Text        string   `json:"text" binding:"required"`
	ModelAlias  string   `json:"model_alias,omitempty"`
	Temperature *float64 `json:"temperature,omitempty"`
	MaxTokens   *int     `json:"max_tokens,omitempty"`
}

// SentimentOutput 单条文本的情感分析结果 (API 对外暴露的结构)
type SentimentOutput struct {
	TextID      string  `json:"text_id"`
	Sentiment   string  `json:"sentiment"` // "positive", "negative", "neutral"
	Confidence  float64 `json:"confidence,omitempty"`
	Explanation string  `json:"explanation,omitempty"`
}

// SentimentAnalysisResponse 定义了情感分析API的响应体
type SentimentAnalysisResponse struct {
	Result []SentimentOutput `json:"result"`
}
