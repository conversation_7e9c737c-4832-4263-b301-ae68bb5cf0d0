package agents

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// RequirementAnalysisAgent handles requirement analysis and background search
type RequirementAnalysisAgent struct {
	*BaseAgent
	llmService    services.LLMService
	httpClient    *http.Client
	promptManager *prompts.BiddingPromptManager
	modelID       string
}

// NewRequirementAnalysisAgent creates a new requirement analysis agent
func NewRequirementAnalysisAgent(llmService services.LLMService, promptManager *prompts.BiddingPromptManager, modelID string) *RequirementAnalysisAgent {
	agentCard := biddingModels.AgentCard{
		Name:        "Requirement Analysis and Search Agent",
		Description: "分析招投标需求并执行智能背景信息搜索",
		URL:         "http://taskd-service:8601/agents/requirement-analysis-search",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version:          "1.0.0",
		DocumentationURL: "https://docs.taskd.platform/agents/requirement-analysis-search",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          "analyze_and_search",
				Name:        "需求分析与背景搜索",
				Description: "分析招投标需求并搜索相关背景信息、市场情况、技术趋势等",
				Tags:        []string{"analysis", "search", "mcp", "background"},
				Examples: []string{
					"分析AI项目需求并搜索相关技术背景",
					"提取政府采购关键词并搜索市场信息",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
				InputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"summary_data": map[string]interface{}{
							"type":        "object",
							"description": "AI摘要Agent输出的摘要数据",
						},
						"search_depth": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"shallow", "normal", "deep"},
							"default":     "normal",
							"description": "搜索深度",
						},
						"search_domains": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "搜索领域限制",
							"example":     []string{"technology", "market", "regulations"},
						},
						"max_results": map[string]interface{}{
							"type":        "integer",
							"default":     10,
							"minimum":     1,
							"maximum":     50,
							"description": "最大搜索结果数量",
						},
						"language": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"chinese", "english"},
							"default":     "chinese",
							"description": "分析语言偏好 / Analysis language preference",
						},
					},
					"required": []string{"summary_data"},
				},
				OutputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"requirement_analysis": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"key_requirements":   map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
								"technical_keywords": map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
								"business_keywords":  map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
							},
						},
						"search_results": map[string]interface{}{
							"type": "array",
							"items": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"title":           map[string]interface{}{"type": "string"},
									"url":             map[string]interface{}{"type": "string"},
									"snippet":         map[string]interface{}{"type": "string"},
									"source":          map[string]interface{}{"type": "string"},
									"relevance_score": map[string]interface{}{"type": "number"},
									"published_date":  map[string]interface{}{"type": "string"},
								},
							},
						},
						"search_summary": map[string]interface{}{
							"type":        "string",
							"description": "搜索结果的简要总结",
						},
						"search_metadata": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"total_results":       map[string]interface{}{"type": "integer"},
								"search_time":         map[string]interface{}{"type": "number"},
								"search_engines_used": map[string]interface{}{"type": "array", "items": map[string]interface{}{"type": "string"}},
							},
						},
					},
				},
			},
		},
	}

	baseAgent := NewBaseAgent("requirement-analysis-search", agentCard)

	return &RequirementAnalysisAgent{
		BaseAgent:     baseAgent,
		llmService:    llmService,
		promptManager: promptManager,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		modelID: modelID,
	}
}

// ExecuteSkill executes the specified skill
func (agent *RequirementAnalysisAgent) ExecuteSkill(skillID string, input map[string]interface{},
	context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {

	switch skillID {
	case "analyze_and_search":
		return agent.ExecuteSkillWithHandler(skillID, input, context, agent.analyzeAndSearch)
	default:
		return nil, fmt.Errorf("unknown skill: %s", skillID)
	}
}

// analyzeAndSearch handles the requirement analysis and search logic
func (agent *RequirementAnalysisAgent) analyzeAndSearch(input map[string]interface{},
	a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {

	// Parse input parameters
	summaryDataRaw, ok := input["summary_data"]
	if !ok {
		return nil, fmt.Errorf("summary_data is required")
	}

	// Convert summary data to proper structure
	summaryData, err := agent.parseSummaryData(summaryDataRaw)
	if err != nil {
		return nil, fmt.Errorf("failed to parse summary data: %w", err)
	}

	searchDepth := "normal"
	if depth, exists := input["search_depth"]; exists {
		if depthStr, ok := depth.(string); ok {
			searchDepth = depthStr
		}
	}

	var searchDomains []string
	if domains, exists := input["search_domains"]; exists {
		if domainsArray, ok := domains.([]interface{}); ok {
			for _, domain := range domainsArray {
				if domainStr, ok := domain.(string); ok {
					searchDomains = append(searchDomains, domainStr)
				}
			}
		}
	}

	maxResults := 10
	if max, exists := input["max_results"]; exists {
		if maxInt, ok := max.(float64); ok {
			maxResults = int(maxInt)
		}
	}

	utils.Log.Infof("Starting requirement analysis and search: Depth=%s, Domains=%v, MaxResults=%d",
		searchDepth, searchDomains, maxResults)

	// Step 1: Analyze requirements
	var requirementAnalysis biddingModels.RequirementAnalysis
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	err = agent.analyzeRequirements(ctx, summaryData, &requirementAnalysis, a2aContext)

	if err != nil {
		return nil, fmt.Errorf("failed to analyze requirements: %w", err)
	}

	// Step 2: Perform background search
	var searchResults []biddingModels.SearchResult
	var searchMetadata biddingModels.SearchMetadata

	ctx, cancel = context.WithTimeout(context.Background(), 180*time.Second)
	defer cancel()
	err = agent.performBackgroundSearch(ctx, requirementAnalysis, searchDepth,
		searchDomains, maxResults, &searchResults, &searchMetadata)

	if err != nil {
		return nil, fmt.Errorf("failed to perform background search: %w", err)
	}

	// Step 3: Generate search summary
	searchSummary, err := agent.generateSearchSummary(searchResults, requirementAnalysis, a2aContext)
	if err != nil {
		utils.Log.Warnf("Failed to generate search summary: %v", err)
		searchSummary = fmt.Sprintf("通过分析%d个相关资源，找到了与项目需求相关的背景信息。", len(searchResults))
	}

	// Build response
	response := map[string]interface{}{
		"requirement_analysis": requirementAnalysis,
		"search_results":       searchResults,
		"search_summary":       searchSummary,
		"search_metadata":      searchMetadata,
	}

	utils.Log.Infof("Successfully completed analysis and search (results: %d, time: %.2fs)",
		len(searchResults), searchMetadata.SearchTime)

	return response, nil
}

// parseSummaryData converts raw summary data to structured format
func (agent *RequirementAnalysisAgent) parseSummaryData(raw interface{}) (biddingModels.TenderSummary, error) {
	var summaryData biddingModels.TenderSummary

	// Convert to JSON and back to properly structure the data
	jsonData, err := json.Marshal(raw)
	if err != nil {
		return summaryData, fmt.Errorf("failed to marshal summary data: %w", err)
	}

	err = json.Unmarshal(jsonData, &summaryData)
	if err != nil {
		return summaryData, fmt.Errorf("failed to unmarshal summary data: %w", err)
	}

	return summaryData, nil
}

// analyzeRequirements uses LLM to analyze requirements and extract keywords
func (agent *RequirementAnalysisAgent) analyzeRequirements(ctx context.Context,
	summaryData biddingModels.TenderSummary, analysis *biddingModels.RequirementAnalysis,
	a2aContext biddingModels.A2AContext) error {

	// Build the analysis prompt
	prompt := agent.buildAnalysisPrompt(summaryData)

	// Prepare LLM request
	maxTokens := 2000
	temperature := 0.2
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model:    agent.modelID, // Or get from config
		Messages: []common.LLMMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   &maxTokens,
		Temperature: &temperature,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	// Call LLM service
	response, err := agent.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return fmt.Errorf("LLM service error: %w", err)
	}

	// Parse analysis response
	err = agent.parseAnalysisResponse(response.Result, analysis)
	if err != nil {
		return fmt.Errorf("failed to parse analysis response: %w", err)
	}

	// Update metrics
	agent.UpdateMetrics(response.TokenUsage.TotalTokens)

	// 不再需要手动记录token消耗，因为LLM服务会自动记录
	// 如果需要额外的日志记录，可以保留以下代码
	utils.Log.Debugf("需求分析Agent %s 消耗tokens: input=%d, output=%d, total=%d",
		llmRequest.Model, response.TokenUsage.InputTokens, response.TokenUsage.OutputTokens, response.TokenUsage.TotalTokens)

	return nil
}

// buildAnalysisPrompt constructs the prompt for requirement analysis
func (agent *RequirementAnalysisAgent) buildAnalysisPrompt(summaryData biddingModels.TenderSummary) string {
	var promptBuilder strings.Builder

	promptBuilder.WriteString("你是招投标需求分析专家。请分析以下招投标摘要，提取关键需求和搜索关键词。\n\n")

	promptBuilder.WriteString(fmt.Sprintf("项目标题：%s\n", summaryData.Title))
	promptBuilder.WriteString(fmt.Sprintf("优化标题：%s\n", summaryData.NewTitle))
	promptBuilder.WriteString(fmt.Sprintf("项目摘要：%s\n", summaryData.SummaryText))

	if len(summaryData.KeyRequirements) > 0 {
		promptBuilder.WriteString(fmt.Sprintf("关键要求：%s\n", strings.Join(summaryData.KeyRequirements, "、")))
	}

	promptBuilder.WriteString(`
请按以下JSON格式提供分析结果：
{
  "key_requirements": ["核心需求1", "核心需求2", ...],
  "technical_keywords": ["技术关键词1", "技术关键词2", ...],
  "business_keywords": ["商务关键词1", "商务关键词2", ...]
}

要求：
1. key_requirements: 提取3-5个最核心的项目需求
2. technical_keywords: 提取5-8个技术相关的搜索关键词
3. business_keywords: 提取3-5个商务相关的搜索关键词
4. 关键词要具体且有搜索价值
5. 确保返回有效的JSON格式`)

	return promptBuilder.String()
}

// parseAnalysisResponse parses the LLM response into requirement analysis
func (agent *RequirementAnalysisAgent) parseAnalysisResponse(content string,
	analysis *biddingModels.RequirementAnalysis) error {

	// Clean and extract JSON
	content = strings.TrimSpace(content)
	start := strings.Index(content, "{")
	end := strings.LastIndex(content, "}") + 1

	if start == -1 || end <= start {
		return fmt.Errorf("no valid JSON found in analysis response")
	}

	jsonContent := content[start:end]

	// Parse JSON response
	var response map[string]interface{}
	err := json.Unmarshal([]byte(jsonContent), &response)
	if err != nil {
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// Extract key requirements
	if requirements, ok := response["key_requirements"].([]interface{}); ok {
		for _, req := range requirements {
			if reqStr, ok := req.(string); ok {
				analysis.KeyRequirements = append(analysis.KeyRequirements, reqStr)
			}
		}
	}

	// Extract technical keywords
	if keywords, ok := response["technical_keywords"].([]interface{}); ok {
		for _, keyword := range keywords {
			if keywordStr, ok := keyword.(string); ok {
				analysis.TechnicalKeywords = append(analysis.TechnicalKeywords, keywordStr)
			}
		}
	}

	// Extract business keywords
	if keywords, ok := response["business_keywords"].([]interface{}); ok {
		for _, keyword := range keywords {
			if keywordStr, ok := keyword.(string); ok {
				analysis.BusinessKeywords = append(analysis.BusinessKeywords, keywordStr)
			}
		}
	}

	return nil
}

// performBackgroundSearch performs the actual background search
func (agent *RequirementAnalysisAgent) performBackgroundSearch(ctx context.Context,
	analysis biddingModels.RequirementAnalysis, searchDepth string, searchDomains []string,
	maxResults int, results *[]biddingModels.SearchResult, metadata *biddingModels.SearchMetadata) error {

	startTime := time.Now()

	// Combine all keywords for search
	allKeywords := append(analysis.TechnicalKeywords, analysis.BusinessKeywords...)

	// Generate search queries
	searchQueries := agent.generateSearchQueries(allKeywords, searchDepth)

	utils.Log.Infof("Performing search with %d queries for %d keywords",
		len(searchQueries), len(allKeywords))

	// Perform searches (simulated - in real implementation, integrate with actual search engines)
	var allResults []biddingModels.SearchResult
	var searchEnginesUsed []string

	// For demonstration, we'll create mock search results
	// In real implementation, this would call actual search APIs
	allResults = agent.generateMockSearchResults(searchQueries, maxResults)
	searchEnginesUsed = []string{"mock_search_engine"}

	// Filter and rank results
	filteredResults := agent.filterAndRankResults(allResults, analysis, maxResults)

	*results = filteredResults
	*metadata = biddingModels.SearchMetadata{
		TotalResults:      len(allResults),
		SearchTime:        time.Since(startTime).Seconds(),
		SearchEnginesUsed: searchEnginesUsed,
	}

	return nil
}

// generateSearchQueries creates search queries based on keywords and depth
func (agent *RequirementAnalysisAgent) generateSearchQueries(keywords []string, depth string) []string {
	var queries []string

	switch depth {
	case "shallow":
		// Simple keyword search
		if len(keywords) > 0 {
			queries = append(queries, strings.Join(keywords[:min(3, len(keywords))], " "))
		}
	case "deep":
		// Multiple specific queries
		for _, keyword := range keywords {
			queries = append(queries, keyword)
			queries = append(queries, keyword+" 技术趋势")
			queries = append(queries, keyword+" 市场分析")
		}
	default: // normal
		// Balanced approach
		for i := 0; i < len(keywords); i += 2 {
			end := min(i+2, len(keywords))
			queries = append(queries, strings.Join(keywords[i:end], " "))
		}
	}

	return queries
}

// generateMockSearchResults creates mock search results for demonstration
func (agent *RequirementAnalysisAgent) generateMockSearchResults(queries []string, maxResults int) []biddingModels.SearchResult {
	mockResults := []biddingModels.SearchResult{
		{
			Title:          "2024年中国政务AI服务市场发展报告",
			URL:            "https://www.example-research.com/gov-ai-2024",
			Snippet:        "2024年政务AI服务市场规模达到150亿元，智能客服、文档处理、数据分析成为三大核心应用场景...",
			Source:         "行业研究报告",
			RelevanceScore: 0.95,
			PublishedDate:  "2024-06-15",
		},
		{
			Title:          "政府AI采购指南：技术要求与评估标准",
			URL:            "https://www.gov-procurement.org/ai-guidelines",
			Snippet:        "政府AI项目采购需重点关注算法透明度、数据安全、服务可用性等关键指标...",
			Source:         "政府采购网",
			RelevanceScore: 0.88,
			PublishedDate:  "2024-05-20",
		},
		{
			Title:          "云原生政务系统架构设计最佳实践",
			URL:            "https://tech.example.com/cloud-native-gov",
			Snippet:        "基于Kubernetes的政务系统需要考虑安全性、可扩展性、多租户隔离等特殊要求...",
			Source:         "技术博客",
			RelevanceScore: 0.82,
			PublishedDate:  "2024-04-10",
		},
	}

	// Limit to maxResults
	if len(mockResults) > maxResults {
		mockResults = mockResults[:maxResults]
	}

	return mockResults
}

// filterAndRankResults filters and ranks search results by relevance
func (agent *RequirementAnalysisAgent) filterAndRankResults(results []biddingModels.SearchResult,
	analysis biddingModels.RequirementAnalysis, maxResults int) []biddingModels.SearchResult {

	// For now, just return the results as-is (already ranked by RelevanceScore)
	// In a real implementation, you might re-rank based on keywords match

	if len(results) > maxResults {
		return results[:maxResults]
	}

	return results
}

// generateSearchSummary creates a summary of search results
func (agent *RequirementAnalysisAgent) generateSearchSummary(results []biddingModels.SearchResult,
	analysis biddingModels.RequirementAnalysis, a2aContext biddingModels.A2AContext) (string, error) {

	if len(results) == 0 {
		return "未找到相关的背景信息。", nil
	}

	// Build summary prompt
	var promptBuilder strings.Builder
	promptBuilder.WriteString("请基于以下搜索结果，生成一个简洁的背景信息摘要（100-200字）：\n\n")

	for i, result := range results {
		promptBuilder.WriteString(fmt.Sprintf("%d. %s\n   %s\n\n", i+1, result.Title, result.Snippet))
	}

	promptBuilder.WriteString("请总结这些信息的关键发现和趋势，用中文回答。")

	// Use LLM to generate summary
	maxTokens := 500
	temperature := 0.3
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model:    agent.modelID, // Or get from config
		Messages: []common.LLMMessage{
			{
				Role:    "user",
				Content: promptBuilder.String(),
			},
		},
		MaxTokens:   &maxTokens,
		Temperature: &temperature,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := agent.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return "", fmt.Errorf("failed to generate search summary: %w", err)
	}

	agent.UpdateMetrics(response.TokenUsage.TotalTokens)
	return strings.TrimSpace(response.Result), nil
}

// HealthCheck returns enhanced health status for this agent
func (agent *RequirementAnalysisAgent) HealthCheck() biddingModels.HealthStatus {
	status := agent.BaseAgent.HealthCheck()

	// Check LLM service availability
	if agent.llmService == nil {
		status.Status = biddingModels.AgentStatusUnhealthy
		status.Dependencies["llm_service"] = "unavailable"
	} else {
		status.Dependencies["llm_service"] = "available"
	}

	// Check HTTP client
	if agent.httpClient == nil {
		status.Dependencies["http_client"] = "unavailable"
	} else {
		status.Dependencies["http_client"] = "available"
	}

	return status
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
