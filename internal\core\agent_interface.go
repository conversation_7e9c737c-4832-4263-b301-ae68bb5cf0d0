package core

import (
	"context"
	"gitlab.com/specific-ai/taskd/internal/models"
)

// Agent 核心Agent接口
type Agent interface {
	// GetID 获取Agent ID
	GetID() string
	
	// GetType 获取Agent类型
	GetType() models.AgentType
	
	// GetCapabilities 获取Agent能力列表
	GetCapabilities() []models.AgentCapability
	
	// Execute 执行Agent能力
	Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error)
	
	// HealthCheck 健康检查
	HealthCheck(ctx context.Context) (*models.AgentHealthCheck, error)
	
	// Initialize 初始化Agent
	Initialize(ctx context.Context, config map[string]interface{}) error
	
	// Shutdown 关闭Agent
	Shutdown(ctx context.Context) error
}

// AgentManager Agent管理器接口
type AgentManager interface {
	// RegisterAgent 注册Agent
	RegisterAgent(ctx context.Context, agent Agent) error
	
	// UnregisterAgent 注销Agent
	UnregisterAgent(ctx context.Context, agentID string) error
	
	// GetAgent 获取Agent实例
	GetAgent(ctx context.Context, agentID string) (Agent, error)
	
	// ListAgents 列出所有Agent
	ListAgents(ctx context.Context, filter models.AgentListRequest) (*models.AgentListResponse, error)
	
	// ExecuteAgent 执行Agent
	ExecuteAgent(ctx context.Context, req *models.AgentExecutionRequest) (*models.AgentResponse, error)
	
	// GetAgentMetrics 获取Agent指标
	GetAgentMetrics(ctx context.Context, agentID string) (*models.AgentMetrics, error)
	
	// HealthCheckAll 检查所有Agent健康状态
	HealthCheckAll(ctx context.Context) ([]models.AgentHealthCheck, error)
}

// AgentTemplate Agent模板接口
type AgentTemplate interface {
	// GetTemplate 获取模板
	GetTemplate(ctx context.Context, templateID string) (*models.AgentTemplate, error)
	
	// CreateTemplate 创建模板
	CreateTemplate(ctx context.Context, template *models.AgentTemplate) error
	
	// UpdateTemplate 更新模板
	UpdateTemplate(ctx context.Context, template *models.AgentTemplate) error
	
	// DeleteTemplate 删除模板
	DeleteTemplate(ctx context.Context, templateID string) error
	
	// ListTemplates 列出模板
	ListTemplates(ctx context.Context, agentType models.AgentType) ([]models.AgentTemplate, error)
	
	// RenderPrompt 渲染提示词
	RenderPrompt(ctx context.Context, templateID string, params map[string]interface{}) (string, string, error)
}

// AgentRegistry Agent注册表接口
type AgentRegistry interface {
	// RegisterCard 注册Agent卡片
	RegisterCard(ctx context.Context, card *models.AgentCard) error
	
	// GetCard 获取Agent卡片
	GetCard(ctx context.Context, cardID string) (*models.AgentCard, error)
	
	// ListCards 列出Agent卡片
	ListCards(ctx context.Context, agentType models.AgentType) ([]models.AgentCard, error)
	
	// UpdateCard 更新Agent卡片
	UpdateCard(ctx context.Context, card *models.AgentCard) error
	
	// DeleteCard 删除Agent卡片
	DeleteCard(ctx context.Context, cardID string) error
	
	// DiscoverCapabilities 发现Agent能力
	DiscoverCapabilities(ctx context.Context, agentType models.AgentType) ([]models.AgentCapability, error)
}

// AgentExecutor Agent执行器接口
type AgentExecutor interface {
	// Execute 执行Agent任务
	Execute(ctx context.Context, req *models.AgentExecutionRequest) (*models.AgentResponse, error)
	
	// ExecuteAsync 异步执行Agent任务
	ExecuteAsync(ctx context.Context, req *models.AgentExecutionRequest) (string, error)
	
	// GetExecutionResult 获取执行结果
	GetExecutionResult(ctx context.Context, executionID string) (*models.AgentExecution, error)
	
	// ListExecutions 列出执行记录
	ListExecutions(ctx context.Context, agentID string, limit int, offset int) ([]models.AgentExecution, error)
	
	// CancelExecution 取消执行
	CancelExecution(ctx context.Context, executionID string) error
}

// AgentMonitor Agent监控接口
type AgentMonitor interface {
	// RecordExecution 记录执行
	RecordExecution(ctx context.Context, execution *models.AgentExecution) error
	
	// GetMetrics 获取指标
	GetMetrics(ctx context.Context, agentID string) (*models.AgentMetrics, error)
	
	// GetAllMetrics 获取所有Agent指标
	GetAllMetrics(ctx context.Context) ([]models.AgentMetrics, error)
	
	// GetExecutionHistory 获取执行历史
	GetExecutionHistory(ctx context.Context, agentID string, limit int) ([]models.AgentExecution, error)
	
	// CleanupOldExecutions 清理旧的执行记录
	CleanupOldExecutions(ctx context.Context, retentionDays int) error
}