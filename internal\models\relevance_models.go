package models

// RelevanceCalculationRequest 定义了相关性计算API的请求体
type RelevanceCalculationRequest struct {
	TargetList  []string `json:"target_list" binding:"required,dive,required"`
	ContentList []string `json:"content_list" binding:"required,dive,required"`
	Threshold   float64  `json:"threshold" binding:"required,gte=0,lte=1"`
}

// KeywordScore 存储单个关键词及其对应的分数
type KeywordScore struct {
	Keyword string  `json:"keyword"`
	Score   float64 `json:"score"`
}

// FilteredItem 代表一个通过阈值过滤的内容项及其相关分数
type FilteredItem struct {
	OriginalIndex int            `json:"original_index"`
	AverageScore  float64        `json:"average_score"`
	KeywordScores []KeywordScore `json:"keyword_scores"`
	// Text          string         `json:"text"` // 可选
}

// RelevanceCalculationResponse 定义了相关性计算API的响应体
type RelevanceCalculationResponse struct {
	FilteredItems []FilteredItem `json:"filtered_items"`
}
