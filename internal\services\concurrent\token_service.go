package concurrent

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/jmoiron/sqlx"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// TokenService handles token consumption tracking and management
type TokenService struct {
	db        *sqlx.DB
	jwtSecret []byte
}

// NewTokenService creates a new token service instance
func NewTokenService() *TokenService {
	return &TokenService{
		db:        store.GetDB(),
		jwtSecret: []byte("your-secret-key"), // TODO: a from config
	}
}

// GenerateToken generates a new JWT token
func (s *TokenService) GenerateToken(userID string, companyID string) (string, error) {
	expirationTime := time.Now().Add(24 * time.Hour)
	claims := &models.AuthClaims{
		UserID:    userID,
		CompanyID: companyID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.jwtSecret)
}

// ValidateToken validates a JWT token
func (s *TokenService) ValidateToken(tokenString string) (*models.AuthClaims, error) {
	claims := &models.AuthClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return s.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}

// RefreshToken refreshes a JWT token
func (s *TokenService) RefreshToken(tokenString string) (string, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	// Check if the token is within the refresh window (e.g., 30 minutes before expiry)
	if time.Until(claims.ExpiresAt.Time) > 30*time.Minute {
		return "", fmt.Errorf("token is not within refresh window")
	}

	return s.GenerateToken(claims.UserID, claims.CompanyID)
}

// LogTokenConsumption logs token consumption for a user
func (s *TokenService) LogTokenConsumption(req *models.TokenConsumptionRequest) (*models.TokenConsumption, error) {
	// Validate that user exists - we should NOT create users here
	var userExists bool
	err := s.db.Get(&userExists, "SELECT EXISTS(SELECT 1 FROM taskd.users WHERE user_id = $1)", req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check user existence: %w", err)
	}
	
	if !userExists {
		return nil, fmt.Errorf("user %s does not exist, cannot log token consumption", req.UserID)
	}

	// Validate that company exists - we should NOT create companies here
	var companyExists bool
	err = s.db.Get(&companyExists, "SELECT EXISTS(SELECT 1 FROM taskd.companies WHERE company_id = $1)", req.CompanyID)
	if err != nil {
		return nil, fmt.Errorf("failed to check company existence: %w", err)
	}
	
	if !companyExists {
		return nil, fmt.Errorf("company %s does not exist, cannot log token consumption", req.CompanyID)
	}

	// Calculate total tokens
	totalTokens := req.InputTokens + req.OutputTokens

	// Create consumption record
	consumption := &models.TokenConsumption{
		UserID:        req.UserID,
		CompanyID:     req.CompanyID,
		ModelProvider: req.ModelProvider,
		ModelName:     req.ModelName,
		InputTokens:   req.InputTokens,
		OutputTokens:  req.OutputTokens,
		TotalTokens:   totalTokens,
		CostCents:     sql.NullInt32{Int32: int32(s.calculateCost(req.ModelProvider, req.ModelName, req.InputTokens, req.OutputTokens)), Valid: true},
		RequestID:     sql.NullString{String: req.RequestID, Valid: req.RequestID != ""},
		APIEndpoint:   sql.NullString{String: req.APIEndpoint, Valid: req.APIEndpoint != ""},
		ConsumedAt:    time.Now(),
		CreatedAt:     time.Now(),
	}

	// Insert consumption record
	query := `
		INSERT INTO taskd.token_consumption (
			user_id, company_id, model_provider, model_name, 
			input_tokens, output_tokens, total_tokens, cost_cents, 
			request_id, api_endpoint, consumed_at, created_at
		) VALUES (
			:user_id, :company_id, :model_provider, :model_name, 
			:input_tokens, :output_tokens, :total_tokens, :cost_cents, 
			:request_id, :api_endpoint, :consumed_at, :created_at
		) RETURNING id`

	rows, err := s.db.NamedQuery(query, consumption)
	if err != nil {
		return nil, fmt.Errorf("failed to insert token consumption: %w", err)
	}
	defer rows.Close()

	if rows.Next() {
		if err := rows.Scan(&consumption.ID); err != nil {
			return nil, fmt.Errorf("failed to scan inserted ID: %w", err)
		}
	}

	costCents := int32(0)
	if consumption.CostCents.Valid {
		costCents = consumption.CostCents.Int32
	}
	utils.Log.Infof("[TOKEN_CONSUMED] user=%s, company=%s, model=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d, cost_cents=%d, request_id=%s",
		req.UserID, req.CompanyID, req.ModelName, req.InputTokens, req.OutputTokens, totalTokens, costCents, req.RequestID)

	return consumption, nil
}

// CheckTokenLimits checks if a user can consume the requested tokens
func (s *TokenService) CheckTokenLimits(userID string, requestedTokens int) (*models.TokenLimitCheck, error) {
	// Get user info
	var user models.User
	err := s.db.Get(&user, "SELECT * FROM taskd.users WHERE user_id = $1", userID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user: %w", err)
	}

	result := &models.TokenLimitCheck{
		UserID:          userID,
		CompanyID:       user.CompanyID,
		RequestedTokens: requestedTokens,
		CanProceed:      true,
	}

	// Check monthly limit
	monthlyCheck, err := s.checkPeriodLimit(userID, user.SubscriptionType, "monthly", requestedTokens)
	if err != nil {
		return nil, fmt.Errorf("failed to check monthly limit: %w", err)
	}
	result.MonthlyCheck = *monthlyCheck

	// Check weekly limit
	weeklyCheck, err := s.checkPeriodLimit(userID, user.SubscriptionType, "weekly", requestedTokens)
	if err != nil {
		return nil, fmt.Errorf("failed to check weekly limit: %w", err)
	}
	result.WeeklyCheck = *weeklyCheck

	// Check daily limit
	dailyCheck, err := s.checkPeriodLimit(userID, user.SubscriptionType, "daily", requestedTokens)
	if err != nil {
		return nil, fmt.Errorf("failed to check daily limit: %w", err)
	}
	result.DailyCheck = *dailyCheck

	// Determine if request can proceed
	if monthlyCheck.WouldExceed || weeklyCheck.WouldExceed || dailyCheck.WouldExceed {
		result.CanProceed = false
		if monthlyCheck.WouldExceed {
			result.Reason = "Monthly token limit exceeded"
		} else if weeklyCheck.WouldExceed {
			result.Reason = "Weekly token limit exceeded"
		} else {
			result.Reason = "Daily token limit exceeded"
		}
	}

	return result, nil
}

// checkPeriodLimit checks token limit for a specific period
func (s *TokenService) checkPeriodLimit(userID string, subscriptionType models.SubscriptionType, periodType string, requestedTokens int) (*models.LimitStatus, error) {
	// Get limit for this subscription type and period, with defaults
	var limit models.TokenLimit
	err := s.db.Get(&limit, "SELECT * FROM taskd.token_limits WHERE subscription_type = $1 AND limit_type = $2", subscriptionType, periodType)
	if err != nil {
		if err == sql.ErrNoRows {
			// Provide default limits if not configured
			defaultLimits := map[string]int{
				"daily":   10000,
				"weekly":  50000,
				"monthly": 200000,
			}
			limit = models.TokenLimit{
				SubscriptionType: subscriptionType,
				LimitType:        periodType,
				TokenLimit:       defaultLimits[periodType],
			}
		} else {
			return nil, fmt.Errorf("failed to fetch token limit: %w", err)
		}
	}

	// Calculate period start
	var periodStart time.Time
	now := time.Now()
	switch periodType {
	case "monthly":
		periodStart = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "weekly":
		// Start of current week (Monday)
		weekday := int(now.Weekday())
		if weekday == 0 { // Sunday
			weekday = 7
		}
		periodStart = now.AddDate(0, 0, -weekday+1)
		periodStart = time.Date(periodStart.Year(), periodStart.Month(), periodStart.Day(), 0, 0, 0, 0, periodStart.Location())
	case "daily":
		periodStart = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	}

	// Get current usage for this period
	var currentUsage int
	err = s.db.Get(&currentUsage, `
		SELECT COALESCE(total_tokens, 0) 
		FROM taskd.token_usage_summary 
		WHERE user_id = $1 AND period_type = $2 AND period_start = $3`,
		userID, periodType, periodStart)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to fetch current usage: %w", err)
	}

	// Calculate remaining tokens
	remaining := limit.TokenLimit - currentUsage
	if remaining < 0 {
		remaining = 0
	}

	// Calculate usage percentage
	usagePercent := float64(currentUsage) / float64(limit.TokenLimit) * 100
	if usagePercent > 100 {
		usagePercent = 100
	}

	return &models.LimitStatus{
		PeriodType:   periodType,
		CurrentUsage: currentUsage,
		Limit:        limit.TokenLimit,
		Remaining:    remaining,
		AfterRequest: currentUsage + requestedTokens,
		WouldExceed:  (currentUsage + requestedTokens) > limit.TokenLimit,
		UsagePercent: usagePercent,
	}, nil
}

// GetTokenUsageStats returns token usage statistics for a user
func (s *TokenService) GetTokenUsageStats(userID string) (*models.TokenUsageStats, error) {
	// Get user info
	var user models.User
	err := s.db.Get(&user, "SELECT * FROM taskd.users WHERE user_id = $1", userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %s, error: %w", userID, err)
	}

	stats := &models.TokenUsageStats{
		UserID:           userID,
		CompanyID:        user.CompanyID,
		SubscriptionType: user.SubscriptionType,
	}

	// Get total tokens consumed and last consumed timestamp
	var totalTokens sql.NullInt64
	var lastConsumed sql.NullTime
	err = s.db.QueryRow(`
		SELECT COALESCE(SUM(total_tokens), 0), MAX(consumed_at) 
		FROM taskd.token_consumption 
		WHERE user_id = $1
	`, userID).Scan(&totalTokens, &lastConsumed)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to fetch token statistics: %w", err)
	}

	if totalTokens.Valid {
		stats.TotalUsage.TotalTokens = int(totalTokens.Int64)
	}
	if lastConsumed.Valid {
		stats.LastConsumedAt = &lastConsumed.Time
	}

	// Get usage for different periods
	periods := []string{"monthly", "weekly", "daily"}
	for _, period := range periods {
		usage, err := s.getUsagePeriodStats(userID, user.SubscriptionType, period)
		if err != nil {
			return nil, fmt.Errorf("failed to get %s usage: %w", period, err)
		}
		
		switch period {
		case "monthly":
			stats.MonthlyUsage = *usage
		case "weekly":
			stats.WeeklyUsage = *usage
		case "daily":
			stats.DailyUsage = *usage
		}
	}

	return stats, nil
}

// getUsagePeriodStats gets usage statistics for a specific period
func (s *TokenService) getUsagePeriodStats(userID string, subscriptionType models.SubscriptionType, periodType string) (*models.UsagePeriod, error) {
	// Get limit for this period, with default values if not found
	var limit models.TokenLimit
	err := s.db.Get(&limit, "SELECT * FROM taskd.token_limits WHERE subscription_type = $1 AND limit_type = $2", subscriptionType, periodType)
	if err != nil {
		if err == sql.ErrNoRows {
			// Provide default limits if not configured
			defaultLimits := map[string]int{
				"daily":   10000,
				"weekly":  50000,
				"monthly": 200000,
			}
			limit = models.TokenLimit{
				SubscriptionType: subscriptionType,
				LimitType:        periodType,
				TokenLimit:       defaultLimits[periodType],
			}
			utils.Log.Warnf("[TOKEN_LIMIT_DEFAULT] subscription=%s, period=%s, limit=%d", subscriptionType, periodType, limit.TokenLimit)
		} else {
			return nil, fmt.Errorf("failed to fetch token limit: %w", err)
		}
	}

	// Calculate period start and end
	var periodStart, periodEnd time.Time
	now := time.Now()
	switch periodType {
	case "monthly":
		periodStart = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		periodEnd = periodStart.AddDate(0, 1, 0).Add(-time.Second)
	case "weekly":
		weekday := int(now.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		periodStart = now.AddDate(0, 0, -weekday+1)
		periodStart = time.Date(periodStart.Year(), periodStart.Month(), periodStart.Day(), 0, 0, 0, 0, periodStart.Location())
		periodEnd = periodStart.AddDate(0, 0, 7).Add(-time.Second)
	case "daily":
		periodStart = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		periodEnd = periodStart.AddDate(0, 0, 1).Add(-time.Second)
	}

	// Get current usage
	var summary models.TokenUsageSummary
	err = s.db.Get(&summary, `
		SELECT COALESCE(total_tokens, 0) as total_tokens, 
			   COALESCE(total_cost_cents, 0) as total_cost_cents,
			   COALESCE(request_count, 0) as request_count
		FROM taskd.token_usage_summary 
		WHERE user_id = $1 AND period_type = $2 AND period_start = $3`,
		userID, periodType, periodStart)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to fetch usage summary: %w", err)
	}

	// Calculate remaining and usage percentage
	remaining := limit.TokenLimit - summary.TotalTokens
	if remaining < 0 {
		remaining = 0
	}

	usagePercent := float64(summary.TotalTokens) / float64(limit.TokenLimit) * 100
	if usagePercent > 100 {
		usagePercent = 100
	}

	return &models.UsagePeriod{
		PeriodStart:    periodStart,
		PeriodEnd:      periodEnd,
		TotalTokens:    summary.TotalTokens,
		TotalCostCents: summary.TotalCostCents,
		RequestCount:   summary.RequestCount,
		Limit:          limit.TokenLimit,
		Remaining:      remaining,
		UsagePercent:   usagePercent,
	}, nil
}

// GetCompanyTokenStats returns token usage statistics for a company
func (s *TokenService) GetCompanyTokenStats(companyID string) (*models.CompanyTokenStats, error) {
	// Get company info
	var company models.Company
	err := s.db.Get(&company, "SELECT * FROM taskd.companies WHERE company_id = $1", companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch company: %w", err)
	}

	stats := &models.CompanyTokenStats{
		CompanyID:        companyID,
		CompanyName:      company.CompanyName,
		SubscriptionType: company.SubscriptionType,
	}

	// Get total users count
	err = s.db.Get(&stats.TotalUsers, "SELECT COUNT(*) FROM taskd.users WHERE company_id = $1", companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch total users: %w", err)
	}

	// Get aggregate usage for different periods
	periods := []string{"monthly", "weekly", "daily"}
	for _, period := range periods {
		usage, err := s.getCompanyUsagePeriodStats(companyID, company.SubscriptionType, period)
		if err != nil {
			return nil, fmt.Errorf("failed to get company %s usage: %w", period, err)
		}
		
		switch period {
		case "monthly":
			stats.MonthlyAggregate = *usage
		case "weekly":
			stats.WeeklyAggregate = *usage
		case "daily":
			stats.DailyAggregate = *usage
		}
	}

	// Get top users
	topUsers, err := s.getTopUsers(companyID, 5)
	if err != nil {
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}
	stats.TopUsers = topUsers

	// Get model breakdown
	modelBreakdown, err := s.getModelBreakdown(companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get model breakdown: %w", err)
	}
	stats.ModelBreakdown = modelBreakdown

	return stats, nil
}

// getCompanyUsagePeriodStats gets company usage statistics for a specific period
func (s *TokenService) getCompanyUsagePeriodStats(companyID string, subscriptionType models.SubscriptionType, periodType string) (*models.UsagePeriod, error) {
	// Get limit for this period
	var limit models.TokenLimit
	err := s.db.Get(&limit, "SELECT * FROM taskd.token_limits WHERE subscription_type = $1 AND limit_type = $2", subscriptionType, periodType)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token limit: %w", err)
	}

	// Calculate period start and end
	var periodStart, periodEnd time.Time
	now := time.Now()
	switch periodType {
	case "monthly":
		periodStart = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		periodEnd = periodStart.AddDate(0, 1, 0).Add(-time.Second)
	case "weekly":
		weekday := int(now.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		periodStart = now.AddDate(0, 0, -weekday+1)
		periodStart = time.Date(periodStart.Year(), periodStart.Month(), periodStart.Day(), 0, 0, 0, 0, periodStart.Location())
		periodEnd = periodStart.AddDate(0, 0, 7).Add(-time.Second)
	case "daily":
		periodStart = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		periodEnd = periodStart.AddDate(0, 0, 1).Add(-time.Second)
	}

	// Get aggregate usage for the company
	var totalTokens, totalCostCents, requestCount int
	err = s.db.Get(&totalTokens, `
		SELECT COALESCE(SUM(total_tokens), 0) 
		FROM taskd.token_usage_summary 
		WHERE company_id = $1 AND period_type = $2 AND period_start = $3`,
		companyID, periodType, periodStart)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to fetch total tokens: %w", err)
	}

	err = s.db.Get(&totalCostCents, `
		SELECT COALESCE(SUM(total_cost_cents), 0) 
		FROM taskd.token_usage_summary 
		WHERE company_id = $1 AND period_type = $2 AND period_start = $3`,
		companyID, periodType, periodStart)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to fetch total cost: %w", err)
	}

	err = s.db.Get(&requestCount, `
		SELECT COALESCE(SUM(request_count), 0) 
		FROM taskd.token_usage_summary 
		WHERE company_id = $1 AND period_type = $2 AND period_start = $3`,
		companyID, periodType, periodStart)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to fetch request count: %w", err)
	}

	// Calculate remaining and usage percentage
	remaining := limit.TokenLimit - totalTokens
	if remaining < 0 {
		remaining = 0
	}

	usagePercent := float64(totalTokens) / float64(limit.TokenLimit) * 100
	if usagePercent > 100 {
		usagePercent = 100
	}

	return &models.UsagePeriod{
		PeriodStart:    periodStart,
		PeriodEnd:      periodEnd,
		TotalTokens:    totalTokens,
		TotalCostCents: totalCostCents,
		RequestCount:   requestCount,
		Limit:          limit.TokenLimit,
		Remaining:      remaining,
		UsagePercent:   usagePercent,
	}, nil
}

// getTopUsers returns top users by token consumption
func (s *TokenService) getTopUsers(companyID string, limit int) ([]models.UserTokenUsage, error) {
	query := `
		SELECT u.user_id, u.username, 
			   COALESCE(SUM(tc.total_tokens), 0) as total_tokens,
			   COALESCE(SUM(tc.cost_cents), 0) as total_costs,
			   COALESCE(COUNT(tc.id), 0) as request_count
		FROM taskd.users u
		LEFT JOIN taskd.token_consumption tc ON u.user_id = tc.user_id
		WHERE u.company_id = $1 
		GROUP BY u.user_id, u.username
		ORDER BY total_tokens DESC
		LIMIT $2`

	var users []models.UserTokenUsage
	err := s.db.Select(&users, query, companyID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch top users: %w", err)
	}

	// Ensure we return an empty slice instead of nil if no results
	if users == nil {
		users = []models.UserTokenUsage{}
	}

	return users, nil
}

// getModelBreakdown returns model usage breakdown
func (s *TokenService) getModelBreakdown(companyID string) ([]models.ModelUsageBreakdown, error) {
	query := `
		SELECT model_provider, model_name,
			   COALESCE(SUM(total_tokens), 0) as total_tokens,
			   COALESCE(SUM(cost_cents), 0) as total_costs,
			   COALESCE(COUNT(id), 0) as request_count
		FROM taskd.token_consumption
		WHERE company_id = $1
		GROUP BY model_provider, model_name
		ORDER BY total_tokens DESC`

	var breakdown []models.ModelUsageBreakdown
	err := s.db.Select(&breakdown, query, companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch model breakdown: %w", err)
	}

	// Ensure we return an empty slice instead of nil if no results
	if breakdown == nil {
		breakdown = []models.ModelUsageBreakdown{}
	}

	return breakdown, nil
}

// CreateUser creates a new user
func (s *TokenService) CreateUser(user *models.User) error {
	query := `
		INSERT INTO taskd.users (user_id, username, email, company_id, subscription_type, created_at, updated_at)
		VALUES (:user_id, :username, :email, :company_id, :subscription_type, :created_at, :updated_at)
		RETURNING id`

	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	rows, err := s.db.NamedQuery(query, user)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}
	defer rows.Close()

	if rows.Next() {
		if err := rows.Scan(&user.ID); err != nil {
			return fmt.Errorf("failed to scan user ID: %w", err)
		}
	}

	return nil
}

// CreateCompany creates a new company
func (s *TokenService) CreateCompany(company *models.Company) error {
	query := `
		INSERT INTO taskd.companies (company_id, company_name, subscription_type, created_at, updated_at)
		VALUES (:company_id, :company_name, :subscription_type, :created_at, :updated_at)
		RETURNING id`

	company.CreatedAt = time.Now()
	company.UpdatedAt = time.Now()

	rows, err := s.db.NamedQuery(query, company)
	if err != nil {
		return fmt.Errorf("failed to create company: %w", err)
	}
	defer rows.Close()

	if rows.Next() {
		if err := rows.Scan(&company.ID); err != nil {
			return fmt.Errorf("failed to scan company ID: %w", err)
		}
	}

	return nil
}

// CheckConnection checks database connection health
func (s *TokenService) CheckConnection() error {
	return store.CheckConnection()
}

// GetTokenConsumptionHistory returns paginated token consumption history for a user
func (s *TokenService) GetTokenConsumptionHistory(userID string, limit, offset int) ([]models.TokenConsumption, int, error) {
	// Get total count
	var total int
	err := s.db.Get(&total, "SELECT COUNT(*) FROM taskd.token_consumption WHERE user_id = $1", userID)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}

	// Get paginated history
	var history []models.TokenConsumption
	query := `
		SELECT * FROM taskd.token_consumption 
		WHERE user_id = $1 
		ORDER BY consumed_at DESC 
		LIMIT $2 OFFSET $3`
	
	err = s.db.Select(&history, query, userID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to fetch token consumption history: %w", err)
	}

	// Ensure we return an empty slice instead of nil if no results
	if history == nil {
		history = []models.TokenConsumption{}
	}

	return history, total, nil
}

// calculateCost calculates the cost of token consumption in cents
func (s *TokenService) calculateCost(provider, model string, inputTokens, outputTokens int) int {
	// This is a simplified cost calculation
	// In a real implementation, you would have a more sophisticated pricing model
	// based on the provider and model
	
	// Example pricing (in cents per 1000 tokens):
	// - Input tokens: 0.5 cents per 1000 tokens
	// - Output tokens: 1.5 cents per 1000 tokens
	
	inputCost := float64(inputTokens) * 0.5 / 1000
	outputCost := float64(outputTokens) * 1.5 / 1000
	
	return int(inputCost + outputCost)
}

// CheckTokenAvailability checks if user has sufficient tokens for a specific operation
func (s *TokenService) CheckTokenAvailability(ctx context.Context, userID string, organizationID string, requestType string) (bool, error) {
	// Estimate tokens required based on request type
	var estimatedTokens int
	switch requestType {
	case "intent_recognition":
		estimatedTokens = 500 // Intent recognition typically needs ~500 tokens
	case "chat_completion":
		estimatedTokens = 2000 // Chat completion may need more tokens
	case "summary":
		estimatedTokens = 1500 // Summary operations
	default:
		estimatedTokens = 1000 // Default estimation
	}

	// Check token limits
	limitCheck, err := s.CheckTokenLimits(userID, estimatedTokens)
	if err != nil {
		return false, fmt.Errorf("failed to check token limits: %w", err)
	}

	return limitCheck.CanProceed, nil
}

// RecordTokenUsage records token usage for a specific request
func (s *TokenService) RecordTokenUsage(ctx context.Context, record *models.TokenRecord) error {
	// Create a token consumption request from the record
	consumptionReq := &models.TokenConsumptionRequest{
		UserID:        record.UserID,
		CompanyID:     record.OrganizationID, // Use OrganizationID as CompanyID
		ModelProvider: "volcengine_ark",      // Default provider
		ModelName:     record.ModelName,
		InputTokens:   record.TokenUsage.InputTokens,
		OutputTokens:  record.TokenUsage.OutputTokens,
		RequestID:     fmt.Sprintf("%s_%d", record.RequestType, record.Timestamp.Unix()),
		APIEndpoint:   "/api/v1/intent/recognize", // Intent recognition endpoint
	}

	// Log the consumption
	_, err := s.LogTokenConsumption(consumptionReq)
	if err != nil {
		return fmt.Errorf("failed to log token consumption: %w", err)
	}

	utils.Log.Infof("Token usage recorded: user=%s, org=%s, request_type=%s, tokens=%d",
		record.UserID, record.OrganizationID, record.RequestType, 
		record.TokenUsage.InputTokens+record.TokenUsage.OutputTokens)

	return nil
}