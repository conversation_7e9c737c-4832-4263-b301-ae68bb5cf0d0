#!/usr/bin/env python3

import json
import requests
import os
import pytest

class TestConcurrentControl:
    """P3级别: 并发控制测试"""
    
    def test_concurrent_health(self):
        """测试并发控制健康状态"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P3: 并发控制状态测试 ===")
        
        url = f"{base_url}/api/v1/concurrent/health"
        
        response = requests.get(url, timeout=30)
        print(f"请求方法: GET")
        print(f"请求URL: {url}")
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: SUCCESS")
            print(f"响应内容: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"响应状态: FAILED ({response.status_code})")
            print(f"错误信息: {response.text}")
            # 并发控制可能未实现，404/501是可接受的
            if response.status_code not in [404, 501]:
                pytest.fail(f"并发控制请求失败: {response.status_code}")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])