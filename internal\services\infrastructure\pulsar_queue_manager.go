package infrastructure

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/apache/pulsar-client-go/pulsar"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// PulsarQueueManager Pulsar队列管理器接口
type PulsarQueueManager interface {
	// SendRequest 发送请求到队列
	SendRequest(ctx context.Context, req *models.LLMRequestMessage) error

	// StartConsumer 启动消费者
	StartConsumer(ctx context.Context, handler RequestHandler) error

	// StartRetryConsumer 启动重试消费者
	StartRetryConsumer(ctx context.Context, handler RequestHandler) error

	// SendToRetryQueue 发送到重试队列
	SendToRetryQueue(ctx context.Context, req *models.LLMRequestMessage, delay time.Duration) error

	// GetQueueStats 获取队列统计
	GetQueueStats() *models.QueueStats

	// Close 关闭连接
	Close() error
}

// RequestHandler 请求处理器接口
type RequestHandler interface {
	HandleRequest(ctx context.Context, req *models.LLMRequestMessage) error
}

// pulsarQueueManagerImpl Pulsar队列管理器实现
type pulsarQueueManagerImpl struct {
	client       pulsar.Client
	config       *models.ConcurrentConfig
	pulsarConfig config.PulsarConsumerConfig

	// 生产者
	mainProducer  pulsar.Producer
	retryProducer pulsar.Producer

	// 消费者
	mainConsumer  pulsar.Consumer
	retryConsumer pulsar.Consumer

	// 统计
	stats      *models.QueueStats
	statsMutex sync.RWMutex

	// 控制
	wg      sync.WaitGroup
	ctx     context.Context
	cancel  context.CancelFunc
	started bool
	mu      sync.Mutex
}

// NewPulsarQueueManager 创建Pulsar队列管理器
func NewPulsarQueueManager(pulsarClient pulsar.Client, config *models.ConcurrentConfig, pulsarConfig config.PulsarConsumerConfig) (PulsarQueueManager, error) {
	if config == nil {
		config = models.DefaultConcurrentConfig()
	}

	manager := &pulsarQueueManagerImpl{
		client:       pulsarClient,
		config:       config,
		pulsarConfig: pulsarConfig,
		stats: &models.QueueStats{
			PriorityStats:     make(map[models.RequestPriority]int),
			UserPriorityStats: make(map[models.UserPriority]int),
		},
	}

	// 创建生产者
	if err := manager.createProducers(); err != nil {
		return nil, fmt.Errorf("failed to create producers: %w", err)
	}

	utils.Log.Info("Pulsar queue manager created successfully")
	return manager, nil
}

// createProducers 创建生产者
func (p *pulsarQueueManagerImpl) createProducers() error {
	// 主队列生产者
	mainProducer, err := p.client.CreateProducer(pulsar.ProducerOptions{
		Topic:                   p.config.PulsarTopic,
		SendTimeout:             30 * time.Second,
		CompressionType:         pulsar.LZ4,
		BatchingMaxMessages:     100,
		BatchingMaxPublishDelay: 10 * time.Millisecond,
	})
	if err != nil {
		return fmt.Errorf("failed to create main producer: %w", err)
	}
	p.mainProducer = mainProducer

	// 重试队列生产者
	retryProducer, err := p.client.CreateProducer(pulsar.ProducerOptions{
		Topic:           p.config.PulsarRetryTopic,
		SendTimeout:     30 * time.Second,
		CompressionType: pulsar.LZ4,
	})
	if err != nil {
		return fmt.Errorf("failed to create retry producer: %w", err)
	}
	p.retryProducer = retryProducer

	utils.Log.Info("Pulsar producers created successfully")
	return nil
}

// SendRequest 发送请求到主队列
func (p *pulsarQueueManagerImpl) SendRequest(ctx context.Context, req *models.LLMRequestMessage) error {
	// 序列化请求
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建消息
	msg := &pulsar.ProducerMessage{
		Payload: data,
		Key:     req.ID,
		Properties: map[string]string{
			"user_id":       req.UserID,
			"company_id":    req.CompanyID,
			"priority":      fmt.Sprintf("%d", int(req.Priority)),
			"user_priority": string(req.UserPriority),
			"created_at":    req.CreatedAt.Format(time.RFC3339),
		},
		EventTime: req.CreatedAt,
	}

	// 发送消息
	messageID, err := p.mainProducer.Send(ctx, msg)
	if err != nil {
		p.updateStats("send_error")
		return fmt.Errorf("failed to send message to pulsar: %w", err)
	}

	p.updateStats("sent")
	utils.Log.Debugf("Request %s sent to Pulsar with message ID: %s", req.ID, messageID.String())

	return nil
}

// StartConsumer 启动主队列消费者
func (p *pulsarQueueManagerImpl) StartConsumer(ctx context.Context, handler RequestHandler) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.started {
		return fmt.Errorf("consumer already started")
	}

	// 创建消费者
	consumer, err := p.client.Subscribe(pulsar.ConsumerOptions{
		Topic:                       p.config.PulsarTopic,
		SubscriptionName:            p.config.PulsarSubscription,
		Type:                        pulsar.Shared,
		SubscriptionInitialPosition: pulsar.SubscriptionPositionLatest,
		ReceiverQueueSize:           1000,
		NackRedeliveryDelay:         60 * time.Second,
		// // MaxRedeliverCount: 3, // 移除不支持的字段 // 移除不支持的字段
	})
	if err != nil {
		return fmt.Errorf("failed to create consumer: %w", err)
	}
	p.mainConsumer = consumer

	p.ctx, p.cancel = context.WithCancel(ctx)
	p.started = true

	// 启动消费协程
	p.wg.Add(1)
	go p.consumeMessages(p.mainConsumer, handler, "main")

	utils.Log.Infof("Main consumer started on topic: %s", p.config.PulsarTopic)
	return nil
}

// StartRetryConsumer 启动重试队列消费者
func (p *pulsarQueueManagerImpl) StartRetryConsumer(ctx context.Context, handler RequestHandler) error {
	// 创建重试消费者
	consumer, err := p.client.Subscribe(pulsar.ConsumerOptions{
		Topic:                       p.config.PulsarRetryTopic,
		SubscriptionName:            p.config.PulsarSubscription + "-retry",
		Type:                        pulsar.Shared,
		SubscriptionInitialPosition: pulsar.SubscriptionPositionLatest,
		ReceiverQueueSize:           100,
		NackRedeliveryDelay:         120 * time.Second,
	})
	if err != nil {
		return fmt.Errorf("failed to create retry consumer: %w", err)
	}
	p.retryConsumer = consumer

	// 启动重试消费协程
	p.wg.Add(1)
	go p.consumeMessages(p.retryConsumer, handler, "retry")

	utils.Log.Infof("Retry consumer started on topic: %s", p.config.PulsarRetryTopic)
	return nil
}

// consumeMessages 消费消息
func (p *pulsarQueueManagerImpl) consumeMessages(consumer pulsar.Consumer, handler RequestHandler, queueType string) {
	defer p.wg.Done()
	defer consumer.Close()

	utils.Log.Infof("Started consuming messages from %s queue", queueType)

	for {
		select {
		case <-p.ctx.Done():
			utils.Log.Infof("Stopping %s queue consumer", queueType)
			return
		default:
			// 接收消息（带超时）
			ctx, cancel := context.WithTimeout(p.ctx, 5*time.Second)
			msg, err := consumer.Receive(ctx)
			cancel()

			if err != nil {
				if err == context.DeadlineExceeded {
					continue // 超时是正常的，继续循环
				}
				utils.Log.Errorf("Failed to receive message from %s queue: %v", queueType, err)
				time.Sleep(1 * time.Second)
				continue
			}

			// 处理消息
			p.handleMessage(consumer, msg, handler, queueType)
		}
	}
}

// handleMessage 处理单个消息
func (p *pulsarQueueManagerImpl) handleMessage(consumer pulsar.Consumer, msg pulsar.Message, handler RequestHandler, queueType string) {
	startTime := time.Now()

	// 解析消息
	var req models.LLMRequestMessage
	if err := json.Unmarshal(msg.Payload(), &req); err != nil {
		utils.Log.Errorf("Failed to unmarshal message from %s queue: %v", queueType, err)
		consumer.Nack(msg)
		p.updateStats("unmarshal_error")
		return
	}

	utils.Log.Debugf("Processing message %s from %s queue", req.ID, queueType)

	// 创建处理上下文
	ctx, cancel := context.WithTimeout(context.Background(), req.MaxWaitTime)
	defer cancel()

	// 处理请求
	if err := handler.HandleRequest(ctx, &req); err != nil {
		utils.Log.Errorf("Failed to handle request %s: %v", req.ID, err)

		// 检查是否应该重试
		if p.shouldRetryError(err) && req.RetryCount < req.MaxRetries {
			// 发送到重试队列
			delay := p.calculateRetryDelay(req.RetryCount)
			if retryErr := p.SendToRetryQueue(ctx, &req, delay); retryErr != nil {
				utils.Log.Errorf("Failed to send request %s to retry queue: %v", req.ID, retryErr)
				consumer.Nack(msg)
			} else {
				consumer.Ack(msg)
				utils.Log.Infof("Request %s scheduled for retry after %v", req.ID, delay)
			}
		} else {
			// 不重试，确认消息
			consumer.Nack(msg)
			utils.Log.Errorf("Request %s failed permanently: %v", req.ID, err)
		}

		p.updateStats("handle_error")
		return
	}

	// 成功处理
	consumer.Ack(msg)
	duration := time.Since(startTime)
	utils.Log.Debugf("Successfully processed message %s from %s queue in %v", req.ID, queueType, duration)
	p.updateStats("success")
}

// SendToRetryQueue 发送到重试队列
func (p *pulsarQueueManagerImpl) SendToRetryQueue(ctx context.Context, req *models.LLMRequestMessage, delay time.Duration) error {
	req.RetryCount++

	// 序列化请求
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal retry request: %w", err)
	}

	// 创建延迟消息
	deliverAt := time.Now().Add(delay)
	msg := &pulsar.ProducerMessage{
		Payload:   data,
		Key:       req.ID,
		DeliverAt: deliverAt,
		Properties: map[string]string{
			"user_id":     req.UserID,
			"company_id":  req.CompanyID,
			"priority":    fmt.Sprintf("%d", int(req.Priority)),
			"retry_count": fmt.Sprintf("%d", req.RetryCount),
			"deliver_at":  deliverAt.Format(time.RFC3339),
		},
	}

	// 发送到重试队列
	messageID, err := p.retryProducer.Send(ctx, msg)
	if err != nil {
		return fmt.Errorf("failed to send retry message: %w", err)
	}

	utils.Log.Debugf("Retry request %s sent with message ID: %s, delivery at: %v",
		req.ID, messageID.String(), deliverAt)

	p.updateStats("retry_sent")
	return nil
}

// shouldRetryError 判断错误是否应该重试
func (p *pulsarQueueManagerImpl) shouldRetryError(err error) bool {
	errStr := err.Error()
	return contains(errStr, "429") ||
		contains(errStr, "timeout") ||
		contains(errStr, "rate limit") ||
		contains(errStr, "too many requests") ||
		contains(errStr, "service unavailable") ||
		contains(errStr, "internal server error")
}

// calculateRetryDelay 计算重试延迟
func (p *pulsarQueueManagerImpl) calculateRetryDelay(retryCount int) time.Duration {
	// 指数退避：wait = 2^attempt * base_delay
	delay := p.config.RetryBaseDelay * time.Duration(1<<retryCount)

	// 设置最大延迟限制
	maxDelay := 5 * time.Minute
	if delay > maxDelay {
		delay = maxDelay
	}

	return delay
}

// updateStats 更新统计信息
func (p *pulsarQueueManagerImpl) updateStats(action string) {
	p.statsMutex.Lock()
	defer p.statsMutex.Unlock()

	switch action {
	case "sent":
		// 发送成功
	case "send_error":
		p.stats.TotalErrors++
	case "success":
		p.stats.TotalProcessed++
	case "handle_error":
		p.stats.TotalErrors++
	case "retry_sent":
		p.stats.TotalRetries++
	case "unmarshal_error":
		p.stats.TotalErrors++
	}
}

// GetQueueStats 获取队列统计
func (p *pulsarQueueManagerImpl) GetQueueStats() *models.QueueStats {
	p.statsMutex.RLock()
	defer p.statsMutex.RUnlock()

	stats := *p.stats
	return &stats
}

// Close 关闭连接
func (p *pulsarQueueManagerImpl) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.started {
		return nil
	}

	utils.Log.Info("Closing Pulsar queue manager...")

	// 停止消费者
	if p.cancel != nil {
		p.cancel()
	}

	// 等待所有协程结束
	p.wg.Wait()

	// 关闭生产者
	if p.mainProducer != nil {
		p.mainProducer.Close()
	}
	if p.retryProducer != nil {
		p.retryProducer.Close()
	}

	p.started = false
	utils.Log.Info("Pulsar queue manager closed")

	return nil
}

// SimpleRequestHandler 简单的请求处理器实现
type SimpleRequestHandler struct {
	llmService services.LLMService
}

// NewSimpleRequestHandler 创建简单请求处理器
func NewSimpleRequestHandler(llmService services.LLMService) RequestHandler {
	return &SimpleRequestHandler{
		llmService: llmService,
	}
}

// HandleRequest 实现RequestHandler接口
func (h *SimpleRequestHandler) HandleRequest(ctx context.Context, req *models.LLMRequestMessage) error {
	response, err := h.llmService.ProcessRequest(ctx, req.Params)
	if err != nil {
		return err
	}

	// 如果有响应通道，发送响应
	if req.ResponseChan != nil {
		select {
		case req.ResponseChan <- response:
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 通道阻塞，记录警告但不失败
			utils.Log.Warnf("Response channel blocked for request %s", req.ID)
		}
	}

	return nil
}

// contains 是一个辅助函数，用于检查字符串是否包含子串。
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}
