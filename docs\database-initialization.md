# TaskD 数据库初始化文档

## 概述

本文档描述了 TaskD 项目的 PostgreSQL 数据库初始化逻辑和相关配置。

## 初始化架构

### 文件结构
```
internal/store/
├── schema.sql          # 数据库 schema 定义
├── postgres.go         # PostgreSQL 连接和迁移逻辑
└── store.go           # 存储接口定义

internal/app/setup/
└── token_module.go     # Token 模块初始化
```

### 初始化流程

1. **应用启动顺序**
   ```
   cmd/taskd/main.go → internal/app/setup/token_module.go → internal/store/postgres.go
   ```

2. **具体执行步骤**
   - 连接 PostgreSQL 数据库 (`InitPostgres()`)
   - 执行数据库迁移 (`RunMigration()`)
   - 一次性执行整个 `schema.sql` 文件

## 幂等性保证

为确保容器可以安全重启和重新部署，所有数据库对象创建都具备幂等性：

### 1. 枚举类型创建
```sql
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscription_type') THEN
        CREATE TYPE subscription_type AS ENUM ('free', 'pro', 'max');
    END IF;
END $$;
```

### 2. 表创建
```sql
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    -- ...
);
```

### 3. 索引创建
```sql
CREATE INDEX IF NOT EXISTS idx_token_consumption_user_id ON token_consumption(user_id);
CREATE INDEX IF NOT EXISTS idx_token_consumption_company_id ON token_consumption(company_id);
-- ...
```

### 4. 函数和触发器
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
-- ...
```

### 5. 数据插入
```sql
INSERT INTO token_limits (subscription_type, limit_type, token_limit) 
VALUES ('free', 'daily', 1000)
ON CONFLICT (subscription_type, limit_type) DO NOTHING;
```

## 数据库配置

### 环境变量配置
```bash
# PostgreSQL 连接配置
POSTGRESQL_HOST_ENV="***********"
POSTGRESQL_PORT_ENV="5433"
POSTGRESQL_USER_ENV="admin"
POSTGRESQL_PASSWORD_ENV="SecurePass123!"
POSTGRESQL_DATABASE_ENV="overseas"
```

### 配置文件示例
```yaml
postgresql:
  host: "***********"
  port: 5433
  user: "admin"
  password: "SecurePass123!"
  database: "overseas"
  ssl_mode: "disable"
  timeout_seconds: 10
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
```

## 容器化部署

### Docker 运行命令
```bash
docker run --rm -p 8601:8601 \
  -e MONGODB_URI_ENV="mongodb://formula:<EMAIL>:27017/overseas?authSource=admin" \
  -e POSTGRESQL_HOST_ENV="***********" \
  -e POSTGRESQL_PORT_ENV="5433" \
  -e POSTGRESQL_USER_ENV="admin" \
  -e POSTGRESQL_PASSWORD_ENV="SecurePass123!" \
  -e POSTGRESQL_DATABASE_ENV="overseas" \
  -e LLM_PROVIDERS_VOLCENGINE_ARK_API_KEYS="key1,key2,key3" \
  ************/specific-ai/taskd:latest
```

### 部署行为

**首次运行**：
- 创建所有数据库对象（表、索引、函数、枚举类型）
- 插入默认配置数据
- 应用成功启动

**重复运行**：
- 跳过已存在的数据库对象
- 不会报错 "already exists"
- 容器可以安全重启

## 数据库 Schema

### 核心表结构

1. **users** - 用户信息表
2. **companies** - 公司信息表
3. **token_limits** - 令牌限制配置表
4. **token_consumption** - 令牌消费记录表
5. **token_usage_summary** - 令牌使用汇总表

### 关键索引

- `idx_token_consumption_user_id` - 用户查询优化
- `idx_token_consumption_company_id` - 公司查询优化
- `idx_token_consumption_consumed_at` - 时间范围查询优化
- `idx_token_consumption_model` - 模型统计查询优化

## 故障排查

### 常见问题

1. **连接失败**
   ```
   {"level":"error","msg":"Failed to connect to PostgreSQL"}
   ```
   - 检查数据库服务是否运行
   - 验证连接参数（主机、端口、用户名、密码）

2. **权限问题**
   ```
   {"level":"error","msg":"Failed to execute schema: permission denied"}
   ```
   - 确保数据库用户具有创建表、索引的权限
   - 检查数据库用户是否为 owner 或具有足够权限

3. **Schema 执行失败**
   - 查看具体错误信息
   - 检查 PostgreSQL 版本兼容性
   - 验证 SQL 语法正确性

### 调试命令

```bash
# 检查数据库连接
kubectl exec -n ovs deployment/taskd -- \
  psql -h $POSTGRESQL_HOST -U $POSTGRESQL_USER -d $POSTGRESQL_DATABASE -c "SELECT 1;"

# 查看应用日志
kubectl logs -n ovs deployment/taskd | grep -i "postgres\|migration"

# 检查数据库对象
psql -h *********** -p 5433 -U admin -d overseas -c "\dt"  # 查看表
psql -h *********** -p 5433 -U admin -d overseas -c "\di"  # 查看索引
```

## 未来改进计划

1. **迁移版本控制**
   - 实现数据库迁移版本管理
   - 添加迁移历史跟踪表
   - 支持前滚和回滚操作

2. **健康检查增强**
   - 添加数据库连接健康检查
   - 实现数据库依赖检查

3. **性能优化**
   - 添加连接池监控
   - 优化查询性能
   - 实现读写分离支持

---

**更新日期**: 2025-07-19  
**版本**: v1.0  
**维护者**: TaskD 开发团队