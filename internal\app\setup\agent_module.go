package setup

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/services/agent"
	"gitlab.com/specific-ai/taskd/internal/services/bidding"
	"gitlab.com/specific-ai/taskd/internal/services/concurrent"
	"gitlab.com/specific-ai/taskd/internal/store"

	// 招投标核心Agent
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/database"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/orchestration"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/tender_preprocess"
	biddingInterfaces "gitlab.com/specific-ai/taskd/internal/modules/bidding/interfaces"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"

	// 实体提取模块
	extraction "gitlab.com/specific-ai/taskd/internal/modules/entity_extraction"
)

// MongoProvider 定义了一个可以提供*mongo.Client的接口
type MongoProvider interface {
	GetMongoClient() *mongo.Client
}

// CoreAgentAdapter 适配器，将core.Agent适配为A2A系统的biddingInterfaces.Agent接口
type CoreAgentAdapter struct {
	coreAgent core.Agent
}

// NewCoreAgentAdapter 创建核心Agent适配器
func NewCoreAgentAdapter(coreAgent core.Agent) *CoreAgentAdapter {
	return &CoreAgentAdapter{
		coreAgent: coreAgent,
	}
}

// 实现biddingInterfaces.Agent接口
func (adapter *CoreAgentAdapter) GetID() string {
	return adapter.coreAgent.GetID()
}

func (adapter *CoreAgentAdapter) GetAgentCard() biddingModels.AgentCard {
	capabilities := adapter.coreAgent.GetCapabilities()
	skills := make([]biddingModels.AgentSkill, len(capabilities))
	for i, cap := range capabilities {
		skills[i] = biddingModels.AgentSkill{
			ID:          cap.Name,
			Name:        cap.Name,
			Description: cap.Description,
			InputSchema: cap.InputSchema,
			Examples:    []string{}, // 可以从能力中提取示例
		}
	}

	return biddingModels.AgentCard{
		Name:        "Core Agent: " + adapter.coreAgent.GetID(),
		Description: "Adapted core agent for A2A system",
		Version:     "1.0.0",
		Skills:      skills,
		URL:         "/agents/execute/" + adapter.coreAgent.GetID(),
	}
}

func (adapter *CoreAgentAdapter) ExecuteSkill(skillID string, input map[string]interface{}, a2aContext biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	// 添加调试日志
	fmt.Printf("CoreAgentAdapter ExecuteSkill调用: skill_id=%s, user_id=%s, company_id=%s\n",
		skillID, a2aContext.UserID, a2aContext.CompanyID)

	// 创建Go context
	ctx := context.WithValue(
		context.Background(),
		"request_id", a2aContext.RequestID,
	)
	ctx = context.WithValue(ctx, "user_id", a2aContext.UserID)
	ctx = context.WithValue(ctx, "company_id", a2aContext.CompanyID)
	ctx = context.WithValue(ctx, "trace_id", a2aContext.TraceID)

	// 添加headers信息以支持token追踪
	headers := make(map[string]string)
	if a2aContext.UserID != "" {
		headers["X-User-ID"] = a2aContext.UserID
	}
	if a2aContext.CompanyID != "" {
		headers["X-Company-ID"] = a2aContext.CompanyID
	}
	if a2aContext.TraceID != "" {
		headers["X-Trace-ID"] = a2aContext.TraceID
	}
	if a2aContext.Language != "" {
		headers["X-Language"] = a2aContext.Language
	}
	ctx = context.WithValue(ctx, "headers", headers)

	fmt.Printf("Context设置完成: user_id=%s, company_id=%s\n",
		a2aContext.UserID, a2aContext.CompanyID)

	// 调用核心Agent的Execute方法
	response, err := adapter.coreAgent.Execute(ctx, skillID, input, map[string]interface{}{})
	if err != nil {
		return nil, fmt.Errorf("核心Agent执行失败: %w", err)
	}

	// 转换为A2A结果格式
	result := &biddingModels.A2AResult{
		TaskID: a2aContext.RequestID,
		Status: "completed",
		Output: response.Output,
		Metadata: biddingModels.A2AMetadata{
			ExecutionTime: int(response.Duration),
			AgentVersion:  "1.0.0",
		},
	}

	if !response.Success {
		result.Status = "failed"
		if response.Error != "" {
			result.Output = map[string]interface{}{
				"success": false,
				"error":   response.Error,
			}
		}
	}

	// 从response.Output中提取token信息（如果存在）
	if response.Output != nil {
		if metadata, ok := response.Output["metadata"].(map[string]interface{}); ok {
			if tokenUsage, ok := metadata["token_usage"].(map[string]interface{}); ok {
				if totalTokens, ok := tokenUsage["total_tokens"].(int); ok {
					result.Metadata.TokensUsed = totalTokens
				}
			}
		}
	}

	return result, nil
}

func (adapter *CoreAgentAdapter) HealthCheck() biddingModels.HealthStatus {
	// 健康检查应该只检查Agent的可用性，而不执行业务逻辑
	// 检查基本信息是否可用
	agentID := adapter.coreAgent.GetID()
	capabilities := adapter.coreAgent.GetCapabilities()

	// 基本可用性检查：ID不为空且有能力定义
	if agentID == "" || len(capabilities) == 0 {
		return biddingModels.HealthStatus{
			Status:    biddingModels.AgentStatusUnhealthy,
			Timestamp: time.Now(),
			Dependencies: map[string]string{
				"agent_id":     "missing",
				"capabilities": "missing",
			},
		}
	}

	return biddingModels.HealthStatus{
		Status:    biddingModels.AgentStatusHealthy,
		Timestamp: time.Now(),
		Dependencies: map[string]string{
			"agent_id":     "ok",
			"capabilities": fmt.Sprintf("%d available", len(capabilities)),
		},
		Performance: biddingModels.AgentPerformance{
			AvgResponseTime:   0, // 无实际执行，无响应时间
			SuccessRate:       1.0,
			RequestsPerMinute: 0, // 健康检查不计入业务请求
			ErrorRate:         0.0,
		},
	}
}

// BiddingAgentAdapter 适配器，将招投标Agent适配为core.Agent接口
type BiddingAgentAdapter struct {
	biddingAgent biddingInterfaces.Agent
	agentType    string
}

// NewBiddingAgentAdapter 创建招投标Agent适配器
func NewBiddingAgentAdapter(biddingAgent biddingInterfaces.Agent, agentType string) *BiddingAgentAdapter {
	return &BiddingAgentAdapter{
		biddingAgent: biddingAgent,
		agentType:    agentType,
	}
}

// 实现core.Agent接口
func (adapter *BiddingAgentAdapter) GetID() string {
	return adapter.biddingAgent.GetID()
}

func (adapter *BiddingAgentAdapter) GetType() models.AgentType {
	return models.AgentType(adapter.agentType)
}

func (adapter *BiddingAgentAdapter) GetCapabilities() []models.AgentCapability {
	agentCard := adapter.biddingAgent.GetAgentCard()
	capabilities := make([]models.AgentCapability, len(agentCard.Skills))
	for i, skill := range agentCard.Skills {
		capabilities[i] = models.AgentCapability{
			Name:        skill.ID,
			Description: skill.Description,
		}
	}
	return capabilities
}

func (adapter *BiddingAgentAdapter) Initialize(ctx context.Context, config map[string]interface{}) error {
	// 招投标Agent通常不需要额外初始化
	return nil
}

func (adapter *BiddingAgentAdapter) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	// 创建A2A上下文，安全获取context值
	requestID := "system-request"
	if val := ctx.Value("request_id"); val != nil {
		if strVal, ok := val.(string); ok {
			requestID = strVal
		}
	}

	traceID := "system-trace"
	if val := ctx.Value("trace_id"); val != nil {
		if strVal, ok := val.(string); ok {
			traceID = strVal
		}
	}

	// 从上下文中安全获取用户信息
	userID := "system"
	if val := ctx.Value("user_id"); val != nil {
		if strVal, ok := val.(string); ok && strVal != "" {
			userID = strVal
		}
	}

	companyID := "system"
	if val := ctx.Value("company_id"); val != nil {
		if strVal, ok := val.(string); ok && strVal != "" {
			companyID = strVal
		}
	}

	language := "chinese"
	if val := ctx.Value("language"); val != nil {
		if strVal, ok := val.(string); ok && strVal != "" {
			language = strVal
		}
	}

	a2aContext := biddingModels.A2AContext{
		RequestID: requestID,
		UserID:    userID,
		CompanyID: companyID,
		Language:  language,
		TraceID:   traceID,
		Timestamp: time.Now(),
	}

	result, err := adapter.biddingAgent.ExecuteSkill(capability, input, a2aContext)
	if err != nil {
		// 转换为错误响应
		return &models.AgentResponse{
			RequestID: requestID,
			Success:   false,
			Error:     err.Error(),
			CreatedAt: time.Now(),
		}, nil
	}

	// 成功时，我们假设err为nil，所以直接返回nil而不是包装后的错误
	if err != nil {
		return nil, err
	}

	// 转换为models.AgentResponse格式
	response := &models.AgentResponse{
		RequestID: requestID,
		Success:   true,
		Output:    result.Output,
		// Duration:  int64(result.Metadata.ExecutionTime * 1000), //  假设 ExecutionTime 是秒为单位的 float64
		CreatedAt: time.Now(),
	}

	return response, nil
}

func (adapter *BiddingAgentAdapter) HealthCheck(ctx context.Context) (*models.AgentHealthCheck, error) {
	status := adapter.biddingAgent.HealthCheck()

	healthCheck := &models.AgentHealthCheck{
		AgentID:   adapter.GetID(),
		Status:    models.AgentStatus(status.Status),
		Healthy:   status.Status == "healthy",
		Message:   fmt.Sprintf("version: %s, uptime: %v", status.Version, status.Uptime),
		CheckedAt: status.Timestamp,
	}

	return healthCheck, nil
}

func (adapter *BiddingAgentAdapter) Shutdown(ctx context.Context) error {
	// 招投标Agent通常不需要额外关闭逻辑
	return nil
}

// IntentRecognitionAgentAdapter 意图识别Agent适配器
type IntentRecognitionAgentAdapter struct {
	llmClient llm.LLMClient
	id        string
}

// NewIntentRecognitionAgentAdapter 创建意图识别Agent适配器
func NewIntentRecognitionAgentAdapter(llmClient llm.LLMClient) *IntentRecognitionAgentAdapter {
	return &IntentRecognitionAgentAdapter{
		llmClient: llmClient,
		id:        "intent-recognition-agent",
	}
}

func (adapter *IntentRecognitionAgentAdapter) GetID() string {
	return adapter.id
}

func (adapter *IntentRecognitionAgentAdapter) GetType() models.AgentType {
	return models.AgentType("utility")
}

func (adapter *IntentRecognitionAgentAdapter) GetCapabilities() []models.AgentCapability {
	return []models.AgentCapability{
		{
			Name:        "recognize_intent",
			Description: "识别用户意图并分类",
		},
		{
			Name:        "extract_entities",
			Description: "从文本中提取实体信息",
		},
	}
}

func (adapter *IntentRecognitionAgentAdapter) Initialize(ctx context.Context, config map[string]interface{}) error {
	return nil
}

func (adapter *IntentRecognitionAgentAdapter) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	requestID := "system-request"
	if val := ctx.Value("request_id"); val != nil {
		if strVal, ok := val.(string); ok {
			requestID = strVal
		}
	}

	// 模拟意图识别响应
	result := map[string]interface{}{
		"intent":     "bidding_query",
		"confidence": 0.85,
		"entities":   []map[string]interface{}{},
	}

	return &models.AgentResponse{
		RequestID: requestID,
		Success:   true,
		Output:    result,
		CreatedAt: time.Now(),
	}, nil
}

func (adapter *IntentRecognitionAgentAdapter) HealthCheck(ctx context.Context) (*models.AgentHealthCheck, error) {
	return &models.AgentHealthCheck{
		AgentID:   adapter.GetID(),
		Status:    models.AgentStatus("healthy"),
		Healthy:   true,
		Message:   "意图识别Agent运行正常",
		CheckedAt: time.Now(),
	}, nil
}

func (adapter *IntentRecognitionAgentAdapter) Shutdown(ctx context.Context) error {
	return nil
}

// ChatAgentAdapter 聊天Agent适配器
type ChatAgentAdapter struct {
	llmClient llm.LLMClient
	id        string
}

// NewChatAgentAdapter 创建聊天Agent适配器
func NewChatAgentAdapter(llmClient llm.LLMClient) *ChatAgentAdapter {
	return &ChatAgentAdapter{
		llmClient: llmClient,
		id:        "chat-agent",
	}
}

func (adapter *ChatAgentAdapter) GetID() string {
	return adapter.id
}

func (adapter *ChatAgentAdapter) GetType() models.AgentType {
	return models.AgentType("utility")
}

func (adapter *ChatAgentAdapter) GetCapabilities() []models.AgentCapability {
	return []models.AgentCapability{
		{
			Name:        "casual_chat",
			Description: "进行自然语言对话",
		},
		{
			Name:        "answer_questions",
			Description: "回答用户问题",
		},
	}
}

func (adapter *ChatAgentAdapter) Initialize(ctx context.Context, config map[string]interface{}) error {
	return nil
}

func (adapter *ChatAgentAdapter) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	requestID := "system-request"
	if val := ctx.Value("request_id"); val != nil {
		if strVal, ok := val.(string); ok {
			requestID = strVal
		}
	}

	// 模拟聊天响应
	result := map[string]interface{}{
		"response": "您好！我是TaskD的智能聊天助手，有什么可以帮助您的吗？",
		"type":     "text",
	}

	return &models.AgentResponse{
		RequestID: requestID,
		Success:   true,
		Output:    result,
		CreatedAt: time.Now(),
	}, nil
}

func (adapter *ChatAgentAdapter) HealthCheck(ctx context.Context) (*models.AgentHealthCheck, error) {
	return &models.AgentHealthCheck{
		AgentID:   adapter.GetID(),
		Status:    models.AgentStatus("healthy"),
		Healthy:   true,
		Message:   "聊天Agent运行正常",
		CheckedAt: time.Now(),
	}, nil
}

func (adapter *ChatAgentAdapter) Shutdown(ctx context.Context) error {
	return nil
}

// ProfileAgentAdapter 企业画像Agent适配器
type ProfileAgentAdapter struct {
	llmClient     llm.LLMClient
	promptManager *prompts.PromptManager
	id            string
}

// NewProfileAgentAdapter 创建企业画像Agent适配器
func NewProfileAgentAdapter(llmClient llm.LLMClient, promptManager *prompts.PromptManager) *ProfileAgentAdapter {
	return &ProfileAgentAdapter{
		llmClient:     llmClient,
		promptManager: promptManager,
		id:            "company-profile",
	}
}

func (adapter *ProfileAgentAdapter) GetID() string {
	return adapter.id
}

func (adapter *ProfileAgentAdapter) GetType() models.AgentType {
	return models.AgentType("business")
}

func (adapter *ProfileAgentAdapter) GetCapabilities() []models.AgentCapability {
	return []models.AgentCapability{
		{
			Name:        "generate_company_profile",
			Description: "基于企业基础信息生成五维度企业画像分析报告",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"company_name": map[string]interface{}{
						"type":        "string",
						"description": "企业名称",
					},
					"industry": map[string]interface{}{
						"type":        "string",
						"description": "所属行业",
					},
					"user_context": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"user_id": map[string]interface{}{
								"type":        "string",
								"description": "用户ID",
							},
						},
						"required": []string{"user_id"},
					},
				},
				"required": []string{"company_name", "industry", "user_context"},
			},
		},
	}
}

func (adapter *ProfileAgentAdapter) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	start := time.Now()
	requestID := fmt.Sprintf("req_%d", time.Now().UnixNano())
	responseID := fmt.Sprintf("resp_%d", time.Now().UnixNano())

	if capability != "generate_company_profile" {
		return &models.AgentResponse{
			ID:        responseID,
			RequestID: requestID,
			Success:   false,
			Error:     fmt.Sprintf("不支持的能力: %s", capability),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}

	// 这里应该调用实际的Profile服务，但为了简化，我们返回一个模拟响应
	// 在实际实现中，应该创建ProfileService实例并调用GenerateCompanyProfile
	return &models.AgentResponse{
		ID:        responseID,
		RequestID: requestID,
		Success:   true,
		Output: map[string]interface{}{
			"message":    "企业画像生成功能暂未完全集成，这是一个模拟响应",
			"capability": capability,
		},
		Duration:  time.Since(start).Milliseconds(),
		CreatedAt: time.Now(),
	}, nil
}

func (adapter *ProfileAgentAdapter) HealthCheck(ctx context.Context) (*models.AgentHealthCheck, error) {
	healthy := adapter.llmClient != nil && adapter.promptManager != nil
	message := "企业画像Agent运行正常"
	status := models.AgentStatusIdle

	if !healthy {
		message = "企业画像Agent依赖不可用"
		status = models.AgentStatusError
	}

	return &models.AgentHealthCheck{
		AgentID:   adapter.id,
		Status:    status,
		Healthy:   healthy,
		Message:   message,
		CheckedAt: time.Now(),
	}, nil
}

func (adapter *ProfileAgentAdapter) Initialize(ctx context.Context, config map[string]interface{}) error {
	// 初始化逻辑（如果需要的话）
	return nil
}

func (adapter *ProfileAgentAdapter) Shutdown(ctx context.Context) error {
	return nil
}

// AgentRegistrationInfo Agent注册信息
type AgentRegistrationInfo struct {
	ID           string
	Name         string
	AgentType    string
	Constructor  func() (core.Agent, error)
	Dependencies []string
	Optional     bool // 是否为可选Agent，注册失败不影响服务启动
}

// AgentModule Agent模块 - 重构后的统一Agent管理模块
type AgentModule struct {
	AgentManager  core.AgentManager
	AgentTemplate core.AgentTemplate
	AgentHandler  *handlers.AgentHandler
	Logger        *logrus.Logger

	// 依赖组件
	Store                      store.Store
	LLMClient                  llm.LLMClient
	MongoClient                *mongo.Client
	LLMService                 services.LLMService // 使用新的LLMService
	LLMConfig                  config.LLMConfigManager
	PromptManager              *prompts.PromptManager
	BiddingPromptManager       *prompts.BiddingPromptManager
	PreprocessingPromptManager *prompts.PreprocessingPromptManager

	// 已注册的Agent实例
	registeredAgents map[string]core.Agent
	mutex            sync.RWMutex

	// 统计信息
	registrationStats struct {
		Total    int
		Success  int
		Failed   int
		Optional int
	}
}

// SetupAgentModule 设置Agent模块 - 统一注册所有Agent
func SetupAgentModule(store store.Store, llmClient llm.LLMClient, appCfg *config.Config, logger *logrus.Logger) *AgentModule {
	logger.Info("开始初始化统一Agent模块...")

	// 创建Agent模板服务
	agentTemplate := agent.NewAgentTemplate(store)

	// 创建Agent管理器
	agentManager := agent.NewAgentManager(store)

	// 创建Agent处理器
	agentHandler := handlers.NewAgentHandler(agentManager)

	// 获取MongoDB客户端
	var mongoClient *mongo.Client
	if provider, ok := store.(MongoProvider); ok {
		mongoClient = provider.GetMongoClient()
	} else {
		logger.Warn("Store不支持GetMongoClient()方法，MongoDB相关的Agent可能无法工作")
	}

	// 创建TokenService
	tokenService := concurrent.NewTokenService()

	// 创建新的LLMService
	llmService := services.NewLLMService(llmClient, tokenService)

	// 创建Prompt管理器
	promptsDir := "internal/prompts"
	promptManager, err := prompts.NewPromptManager(appCfg.Prompts, promptsDir)
	if err != nil {
		logger.WithError(err).Warn("无法创建PromptManager，将使用默认配置")
	}

	var biddingPromptManager *prompts.BiddingPromptManager
	if bpm, err := prompts.NewBiddingPromptManager(promptsDir); err == nil {
		biddingPromptManager = bpm
	} else {
		logger.WithError(err).Warn("无法创建BiddingPromptManager，将使用默认配置")
	}

	var preprocessingPromptManager *prompts.PreprocessingPromptManager
	if ppm, err := prompts.NewPreprocessingPromptManager(promptsDir); err == nil {
		preprocessingPromptManager = ppm
	} else {
		logger.WithError(err).Warn("无法创建PreprocessingPromptManager，将使用默认配置")
	}

	module := &AgentModule{
		AgentManager:               agentManager,
		AgentTemplate:              agentTemplate,
		AgentHandler:               agentHandler,
		Logger:                     logger,
		Store:                      store,
		LLMClient:                  llmClient,
		MongoClient:                mongoClient,
		LLMService:                 llmService,
		LLMConfig:                  appCfg.LLM,
		PromptManager:              promptManager,
		BiddingPromptManager:       biddingPromptManager,
		PreprocessingPromptManager: preprocessingPromptManager,
		registeredAgents:           make(map[string]core.Agent),
	}

	// 统一注册所有Agent实例
	module.registerAllAgents()

	// 启动心跳检查
	go module.startHeartbeat()

	logger.WithFields(logrus.Fields{
		"total_agents":      module.registrationStats.Total,
		"successful_agents": module.registrationStats.Success,
		"failed_agents":     module.registrationStats.Failed,
		"optional_agents":   module.registrationStats.Optional,
	}).Info("统一Agent模块初始化完成")

	return module
}

// getPromptConfig 获取prompt配置 - 简化版本，避免循环依赖
func getPromptConfig() map[string]interface{} {
	// 返回基本配置，或者从环境变量获取
	return map[string]interface{}{
		"prompts_dir": "internal/prompts",
	}
}

// registerAllAgents 统一注册所有Agent - 核心注册逻辑
func (am *AgentModule) registerAllAgents() {
	am.Logger.Info("开始注册所有Agent实例...")

	// 定义所有Agent的注册信息
	agentRegistrations := am.getAllAgentRegistrations()

	am.registrationStats.Total = len(agentRegistrations)

	// 逐个注册Agent，失败不影响其他Agent和服务启动
	for _, registration := range agentRegistrations {
		am.registerSingleAgent(registration)
	}

	am.Logger.WithFields(logrus.Fields{
		"total":    am.registrationStats.Total,
		"success":  am.registrationStats.Success,
		"failed":   am.registrationStats.Failed,
		"optional": am.registrationStats.Optional,
	}).Info("Agent注册完成")
}

// getAllAgentRegistrations 获取所有Agent的注册信息
func (am *AgentModule) getAllAgentRegistrations() []AgentRegistrationInfo {
	return []AgentRegistrationInfo{
		// === 核心业务Agent ===
		{
			ID:           "bidding-agent-001",
			Name:         "招投标业务Agent",
			AgentType:    "bidding",
			Constructor:  am.createBiddingAgent,
			Dependencies: []string{"llm_client", "agent_template"},
			Optional:     false,
		},

		// === 招投标核心Agent ===
		{
			ID:           "ai-summary",
			Name:         "AI摘要生成Agent",
			AgentType:    "bidding_core",
			Constructor:  am.createAISummaryAgent,
			Dependencies: []string{"llm_service", "bidding_prompt_manager"},
			Optional:     false,
		},
		{
			ID:           "bidding-data-retrieval",
			Name:         "招投标数据检索Agent",
			AgentType:    "bidding_core",
			Constructor:  am.createDataRetrievalAgent,
			Dependencies: []string{"mongo_client"},
			Optional:     false,
		},
		{
			ID:           "requirement-analysis-search",
			Name:         "需求分析搜索Agent",
			AgentType:    "bidding_core",
			Constructor:  am.createRequirementAnalysisAgent,
			Dependencies: []string{"llm_service", "bidding_prompt_manager"},
			Optional:     false,
		},
		{
			ID:           "report-generation",
			Name:         "报告生成Agent",
			AgentType:    "bidding_core",
			Constructor:  am.createReportGenerationAgent,
			Dependencies: []string{"llm_service", "bidding_prompt_manager"},
			Optional:     false,
		},

		// === 预处理Agent系列 ===
		{
			ID:           "tender-data-extraction",
			Name:         "招投标数据提取Agent",
			AgentType:    "preprocessing",
			Constructor:  am.createExtractionAgent,
			Dependencies: []string{"llm_service", "preprocessing_prompt_manager"},
			Optional:     true,
		},
		{
			ID:           "tender-classification",
			Name:         "招投标分类Agent",
			AgentType:    "preprocessing",
			Constructor:  am.createClassificationAgent,
			Dependencies: []string{"llm_service"},
			Optional:     true,
		},
		{
			ID:           "tender-content-enhancement",
			Name:         "招投标内容增强Agent",
			AgentType:    "preprocessing",
			Constructor:  am.createEnhancementAgent,
			Dependencies: []string{"llm_service"},
			Optional:     true,
		},
		{
			ID:           "tender-multilingual",
			Name:         "多语言处理Agent",
			AgentType:    "preprocessing",
			Constructor:  am.createMultilingualAgent,
			Dependencies: []string{"llm_service"},
			Optional:     true,
		},
		{
			ID:           "tender-result-aggregation",
			Name:         "结果聚合Agent",
			AgentType:    "preprocessing",
			Constructor:  am.createAggregationAgent,
			Dependencies: []string{},
			Optional:     true,
		},

		// === 基础设施Agent ===
		{
			ID:           "tender-orchestration",
			Name:         "招投标编排Agent",
			AgentType:    "infrastructure",
			Constructor:  am.createOrchestrationAgent,
			Dependencies: []string{},
			Optional:     true,
		},
		{
			ID:           "database-query",
			Name:         "数据库查询Agent",
			AgentType:    "infrastructure",
			Constructor:  am.createDatabaseAgent,
			Dependencies: []string{"mongo_client"},
			Optional:     true,
		},

		// === 通用工具Agent ===
		{
			ID:           "entity-extraction-agent-001",
			Name:         "实体提取Agent",
			AgentType:    "utility",
			Constructor:  am.createEntityExtractionAgent,
			Dependencies: []string{"llm_client"},
			Optional:     false,
		},

		// === 智能服务Agent ===
		{
			ID:           "intent-recognition-agent",
			Name:         "意图识别Agent",
			AgentType:    "utility",
			Constructor:  am.createIntentRecognitionAgent,
			Dependencies: []string{"llm_client"},
			Optional:     false,
		},
		{
			ID:           "chat-agent",
			Name:         "智能聊天Agent",
			AgentType:    "utility",
			Constructor:  am.createChatAgent,
			Dependencies: []string{"llm_client"},
			Optional:     false,
		},

		// === 企业画像Agent ===
		{
			ID:           "company-profile",
			Name:         "企业画像生成Agent",
			AgentType:    "business",
			Constructor:  am.createProfileAgent,
			Dependencies: []string{"llm_client", "prompt_manager"},
			Optional:     false,
		},
	}
}

// registerSingleAgent 注册单个Agent，失败不影响其他Agent
func (am *AgentModule) registerSingleAgent(registration AgentRegistrationInfo) {
	logger := am.Logger.WithFields(logrus.Fields{
		"agent_id":   registration.ID,
		"agent_name": registration.Name,
		"agent_type": registration.AgentType,
		"optional":   registration.Optional,
	})

	logger.Debug("开始注册Agent...")

	// 检查依赖
	if !am.checkDependencies(registration.Dependencies) && !registration.Optional {
		logger.Error("Agent依赖检查失败，跳过注册")
		am.registrationStats.Failed++
		return
	}

	// 调用构造函数创建Agent实例
	agent, err := registration.Constructor()
	if err != nil {
		if registration.Optional {
			logger.WithError(err).Warn("可选Agent创建失败，跳过注册")
			am.registrationStats.Optional++
		} else {
			logger.WithError(err).Error("必需Agent创建失败")
			am.registrationStats.Failed++
		}
		return
	}

	// 注册到Agent管理器
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := am.AgentManager.RegisterAgent(ctx, agent); err != nil {
		if registration.Optional {
			logger.WithError(err).Warn("可选Agent注册失败")
			am.registrationStats.Optional++
		} else {
			logger.WithError(err).Error("必需Agent注册失败")
			am.registrationStats.Failed++
		}
		return
	}

	// 记录成功注册的Agent
	am.mutex.Lock()
	am.registeredAgents[registration.ID] = agent
	am.mutex.Unlock()

	// 只将业务Agent注册到A2A系统（用于/agents/docs展示）
	if am.isBusinessAgent(registration) {
		am.registerAgentToA2ASystem(agent, registration)
	}

	am.registrationStats.Success++
	logger.Info("Agent注册成功")
}

// isBusinessAgent 判断是否为业务Agent（需要在/agents/docs中展示）
func (am *AgentModule) isBusinessAgent(registration AgentRegistrationInfo) bool {
	// 只有业务相关的Agent类型才需要在A2A文档中展示
	businessAgentTypes := map[string]bool{
		"bidding":        true, // 招投标业务Agent
		"bidding_core":   true, // 招投标核心Agent
		"preprocessing":  true, // 预处理Agent
		"infrastructure": true, // 基础设施Agent（业务相关）
		"utility":        true, // 工具Agent（包含实体提取等业务功能）
	}

	return businessAgentTypes[registration.AgentType]
}

// registerAgentToA2ASystem 将Agent注册到A2A系统，用于/agents/docs显示
func (am *AgentModule) registerAgentToA2ASystem(agent core.Agent, registration AgentRegistrationInfo) {
	// 获取全局A2A注册表
	a2aRegistry := GetGlobalAgentRegistry()

	// 注册到A2A系统 - 使用CoreAgentAdapter来桥接真实的Agent实现
	coreAgentAdapter := NewCoreAgentAdapter(agent)
	if err := a2aRegistry.RegisterAgent(coreAgentAdapter); err != nil {
		am.Logger.WithError(err).WithField("agent_id", registration.ID).Warn("注册到A2A系统失败")
	} else {
		am.Logger.WithField("agent_id", registration.ID).Debug("成功注册到A2A系统")
	}
}

// checkDependencies 检查Agent依赖是否满足
func (am *AgentModule) checkDependencies(dependencies []string) bool {
	for _, dep := range dependencies {
		switch dep {
		case "llm_client":
			if am.LLMClient == nil {
				return false
			}
		case "llm_service":
			if am.LLMService == nil {
				return false
			}
		case "mongo_client":
			if am.MongoClient == nil {
				return false
			}
		case "agent_template":
			if am.AgentTemplate == nil {
				return false
			}
		case "bidding_prompt_manager":
			if am.BiddingPromptManager == nil {
				am.Logger.Warn("BiddingPromptManager不可用，Agent功能可能受限")
			}
		case "preprocessing_prompt_manager":
			if am.PreprocessingPromptManager == nil {
				am.Logger.Warn("PreprocessingPromptManager不可用，Agent功能可能受限")
			}
		default:
			am.Logger.WithField("dependency", dep).Warn("未知的依赖项")
		}
	}
	return true
}

// === Agent构造函数 ===

// createBiddingAgent 创建招投标业务Agent
func (am *AgentModule) createBiddingAgent() (core.Agent, error) {
	return bidding.NewBiddingAgent("bidding-agent-001", am.LLMClient, am.AgentTemplate), nil
}

// createAISummaryAgent 创建AI摘要Agent
func (am *AgentModule) createAISummaryAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	if am.BiddingPromptManager == nil {
		return nil, fmt.Errorf("BiddingPromptManager不可用")
	}
	modelID := am.getDefaultModelID()
	biddingAgent := agents.NewAISummaryAgent(am.LLMService, am.BiddingPromptManager, modelID)
	return NewBiddingAgentAdapter(biddingAgent, "bidding_core"), nil
}

// createDataRetrievalAgent 创建数据检索Agent
func (am *AgentModule) createDataRetrievalAgent() (core.Agent, error) {
	if am.MongoClient == nil {
		return nil, fmt.Errorf("MongoDB客户端不可用")
	}
	databaseName := "bidding"
	collectionName := "tender_awards"
	biddingAgent := agents.NewDataRetrievalAgent(am.MongoClient, databaseName, collectionName)
	return NewBiddingAgentAdapter(biddingAgent, "bidding_core"), nil
}

// createRequirementAnalysisAgent 创建需求分析Agent
func (am *AgentModule) createRequirementAnalysisAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	if am.BiddingPromptManager == nil {
		return nil, fmt.Errorf("BiddingPromptManager不可用")
	}
	modelID := am.getDefaultModelID()
	biddingAgent := agents.NewRequirementAnalysisAgent(am.LLMService, am.BiddingPromptManager, modelID)
	return NewBiddingAgentAdapter(biddingAgent, "bidding_core"), nil
}

// createReportGenerationAgent 创建报告生成Agent
func (am *AgentModule) createReportGenerationAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	if am.BiddingPromptManager == nil {
		return nil, fmt.Errorf("BiddingPromptManager不可用")
	}
	modelID := am.getDefaultModelID()
	biddingAgent := agents.NewReportGenerationAgent(am.LLMService, am.BiddingPromptManager, modelID)
	return NewBiddingAgentAdapter(biddingAgent, "bidding_core"), nil
}

// createExtractionAgent 创建数据提取Agent
func (am *AgentModule) createExtractionAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	biddingAgent := tender_preprocess.NewExtractionAgent(am.LLMService, am.PreprocessingPromptManager)
	return NewBiddingAgentAdapter(biddingAgent, "preprocessing"), nil
}

// createClassificationAgent 创建分类Agent
func (am *AgentModule) createClassificationAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	biddingAgent := tender_preprocess.NewClassificationAgent(am.LLMService)
	return NewBiddingAgentAdapter(biddingAgent, "preprocessing"), nil
}

// createEnhancementAgent 创建内容增强Agent
func (am *AgentModule) createEnhancementAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	biddingAgent := tender_preprocess.NewEnhancementAgent(am.LLMService)
	return NewBiddingAgentAdapter(biddingAgent, "preprocessing"), nil
}

// createMultilingualAgent 创建多语言Agent
func (am *AgentModule) createMultilingualAgent() (core.Agent, error) {
	if am.LLMService == nil {
		return nil, fmt.Errorf("LLMService不可用")
	}
	biddingAgent := tender_preprocess.NewMultilingualAgent(am.LLMService)
	return NewBiddingAgentAdapter(biddingAgent, "preprocessing"), nil
}

// createAggregationAgent 创建聚合Agent
func (am *AgentModule) createAggregationAgent() (core.Agent, error) {
	biddingAgent := tender_preprocess.NewAggregationAgent()
	return NewBiddingAgentAdapter(biddingAgent, "preprocessing"), nil
}

// createOrchestrationAgent 创建编排Agent
func (am *AgentModule) createOrchestrationAgent() (core.Agent, error) {
	biddingAgent := orchestration.NewOrchestrationAgent()
	return NewBiddingAgentAdapter(biddingAgent, "infrastructure"), nil
}

// createDatabaseAgent 创建数据库Agent
func (am *AgentModule) createDatabaseAgent() (core.Agent, error) {
	if am.MongoClient == nil {
		return nil, fmt.Errorf("MongoDB客户端不可用")
	}
	biddingAgent := database.NewDatabaseAgent(am.MongoClient, "bidding", "tender_awards")
	return NewBiddingAgentAdapter(biddingAgent, "infrastructure"), nil
}

// createEntityExtractionAgent 创建实体提取Agent
func (am *AgentModule) createEntityExtractionAgent() (core.Agent, error) {
	if am.LLMClient == nil {
		return nil, fmt.Errorf("LLM客户端不可用")
	}

	// 使用正确的实体提取Agent实现（带token追踪配置）
	config := &extraction.AgentConfig{
		LLMModel:            "deepseek-v3-250324",
		Temperature:         0.1,
		MaxTokens:           4000,
		Timeout:             30,
		MaxRetries:          3,
		ConfidenceThreshold: 0.7,
		EnableFallback:      true,
		AutoTrackTokens:     true,
	}

	return extraction.NewEntityExtractionAgent(am.LLMClient, config), nil
}

// createIntentRecognitionAgent 创建意图识别Agent
func (am *AgentModule) createIntentRecognitionAgent() (core.Agent, error) {
	if am.LLMClient == nil {
		return nil, fmt.Errorf("LLM客户端不可用")
	}

	// 创建意图识别适配器Agent
	return NewIntentRecognitionAgentAdapter(am.LLMClient), nil
}

// createChatAgent 创建聊天Agent
func (am *AgentModule) createChatAgent() (core.Agent, error) {
	if am.LLMClient == nil {
		return nil, fmt.Errorf("LLM客户端不可用")
	}

	// 创建聊天适配器Agent
	return NewChatAgentAdapter(am.LLMClient), nil
}

// createProfileAgent 创建企业画像Agent
func (am *AgentModule) createProfileAgent() (core.Agent, error) {
	if am.LLMClient == nil {
		return nil, fmt.Errorf("LLM客户端不可用")
	}
	if am.PromptManager == nil {
		return nil, fmt.Errorf("PromptManager不可用")
	}

	// 创建企业画像适配器Agent
	return NewProfileAgentAdapter(am.LLMClient, am.PromptManager), nil
}

// getDefaultModelID 安全地获取默认模型ID
func (am *AgentModule) getDefaultModelID() string {
	defaultProvider := am.LLMConfig.DefaultProvider
	if providerConfig, ok := am.LLMConfig.Providers[defaultProvider]; ok {
		if providerConfig.DefaultModelID != "" {
			return providerConfig.DefaultModelID
		}
		// 如果DefaultModelID为空，但有DefaultModelAlias，则尝试解析
		if actualModelID, aliasOk := providerConfig.Models[providerConfig.DefaultModelAlias]; aliasOk {
			return actualModelID
		}
	}
	am.Logger.Warn("无法获取默认模型ID, 将使用 'default-model' 作为备用")
	return "default-model" // Fallback
}

// startHeartbeat 启动心跳检查
func (am *AgentModule) startHeartbeat() {
	//ctx := context.Background()

	// 设置依赖（如果有的话）
	if managerImpl, ok := am.AgentManager.(*agent.AgentManagerImpl); ok {
		managerImpl.SetDependencies(nil, nil, nil, am.AgentTemplate)
	}

	// 禁用心跳检查以减少不必要的agent执行和日志
	// 健康检查由A2A注册表的定期检查负责，无需额外的心跳机制
	// if managerImpl, ok := am.AgentManager.(*agent.AgentManagerImpl); ok {
	//     managerImpl.StartHeartbeat(ctx, 5*time.Minute)
	// }
}

// RegisterRoutes 注册路由
func (am *AgentModule) RegisterRoutes(router *gin.RouterGroup) {
	am.AgentHandler.RegisterRoutes(router)
}

// GetRegisteredAgents 获取已注册的Agent列表
func (am *AgentModule) GetRegisteredAgents() map[string]core.Agent {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	result := make(map[string]core.Agent)
	for id, agent := range am.registeredAgents {
		result[id] = agent
	}
	return result
}

// GetRegistrationStats 获取注册统计信息
func (am *AgentModule) GetRegistrationStats() map[string]int {
	return map[string]int{
		"total":    am.registrationStats.Total,
		"success":  am.registrationStats.Success,
		"failed":   am.registrationStats.Failed,
		"optional": am.registrationStats.Optional,
	}
}

// Shutdown 关闭模块 - 优雅关闭所有Agent
func (am *AgentModule) Shutdown(ctx context.Context) error {
	am.Logger.Info("开始关闭Agent模块...")

	am.mutex.RLock()
	agents := make([]core.Agent, 0, len(am.registeredAgents))
	for _, agent := range am.registeredAgents {
		agents = append(agents, agent)
	}
	am.mutex.RUnlock()

	// 并发关闭所有Agent
	var wg sync.WaitGroup
	for _, agent := range agents {
		wg.Add(1)
		go func(a core.Agent) {
			defer wg.Done()
			if err := am.AgentManager.UnregisterAgent(ctx, a.GetID()); err != nil {
				am.Logger.WithError(err).WithField("agent_id", a.GetID()).Error("Agent注销失败")
			}
		}(agent)
	}

	// 等待所有Agent关闭完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		am.Logger.Info("所有Agent已成功关闭")
		return nil
	case <-ctx.Done():
		am.Logger.Warn("Agent关闭超时")
		return ctx.Err()
	}
}
