package utils

import (
	"strings"
	"unicode/utf8"
)

// TruncateString 将字符串截断到指定的rune（字符）长度，如果超出则添加省略号。
func TruncateString(s string, maxLength int) string {
	if maxLength <= 0 {
		return ""
	}
	if utf8.RuneCountInString(s) <= maxLength {
		return s
	}
	// 确保 maxLength 至少为3，以便能放下 "..."
	if maxLength < 3 {
		// 如果最大长度小于3，则直接截取rune，不加省略号
		runes := []rune(s)
		if len(runes) > maxLength {
			return string(runes[:maxLength])
		}
		return s // 应该不会到这里，因为上面已经判断过了 RuneCountInString
	}

	runes := []rune(s)
	// 截取 maxLength - 3 个rune，然后加上 "..."
	return string(runes[:maxLength-3]) + "..."
}

// ExtractJSONSubstring 尝试从字符串中提取第一个有效的 JSON 对象或数组子串。
// 这是一个基础实现，可能需要根据实际 LLM 输出进行调整。
func ExtractJSONSubstring(s string) string {
	// 尝试找到第一个 '{' 或 '['
	firstBrace := strings.Index(s, "{")
	firstBracket := strings.Index(s, "[")

	var startIdx int
	var startChar, endChar rune

	if firstBrace != -1 && (firstBracket == -1 || firstBrace < firstBracket) {
		startIdx = firstBrace
		startChar = '{'
		endChar = '}'
	} else if firstBracket != -1 {
		startIdx = firstBracket
		startChar = '['
		endChar = ']'
	} else {
		return "" // 没有找到 JSON 的起始符号
	}

	balance := 0
	for i := startIdx; i < len(s); i++ {
		char := rune(s[i])
		if char == startChar {
			balance++
		} else if char == endChar {
			balance--
		}
		if balance == 0 {
			return s[startIdx : i+1] // 找到了匹配的结束符号
		}
	}
	return "" // 没有找到完整的 JSON 结构
}
