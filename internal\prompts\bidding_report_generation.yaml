chinese:
  system: |
    你是招投标报告生成专家。请基于AI摘要和需求分析结果，生成完整的招投标分析报告。

  template: |
    招投标基础信息：
    摘要数据：{{toJSON .Summary}}
    
    需求分析结果：{{toJSON .Analysis}}

    {{if .ReportType}}报告类型：{{.ReportType}}{{end}}
    {{if .TargetAudience}}目标受众：{{.TargetAudience}}{{end}}
    {{if .IncludeSections}}包含章节：{{join .IncludeSections "、"}}{{end}}

    请生成完整的招投标分析报告，返回JSON格式：
    {
      "report_metadata": {
        "title": "报告标题",
        "generation_date": "YYYY-MM-DD",
        "report_type": "报告类型",
        "document_version": "版本号",
        "confidentiality_level": "保密级别"
      },
      "executive_summary": {
        "project_overview": "项目概述",
        "key_findings": ["关键发现1", "关键发现2", ...],
        "recommendation": "总体建议",
        "estimated_win_rate": "预估获胜率"
      },
      "tender_analysis": {
        "project_details": {
          "tender_title": "招标项目名称",
          "tender_organization": "招标机构",
          "project_value": "项目价值",
          "timeline": "项目时间线",
          "submission_deadline": "提交截止时间"
        },
        "requirement_breakdown": {
          "technical_requirements": ["技术要求1", "技术要求2", ...],
          "functional_requirements": ["功能要求1", "功能要求2", ...],
          "non_functional_requirements": ["非功能要求1", "非功能要求2", ...],
          "compliance_requirements": ["合规要求1", "合规要求2", ...]
        },
        "evaluation_criteria": {
          "technical_score_weight": "技术评分权重",
          "commercial_score_weight": "商务评分权重",
          "key_evaluation_factors": ["评估因素1", "评估因素2", ...]
        }
      },
      "competitive_landscape": {
        "market_analysis": "市场分析",
        "competitor_assessment": ["竞争对手评估1", "竞争对手评估2", ...],
        "our_competitive_position": "我方竞争地位",
        "differentiation_opportunities": ["差异化机会1", "差异化机会2", ...]
      },
      "risk_assessment": {
        "technical_risks": [
          {
            "risk": "技术风险描述",
            "impact": "影响程度",
            "probability": "发生概率",
            "mitigation": "缓解措施"
          }
        ],
        "commercial_risks": [
          {
            "risk": "商务风险描述",
            "impact": "影响程度", 
            "probability": "发生概率",
            "mitigation": "缓解措施"
          }
        ],
        "overall_risk_level": "整体风险等级"
      },
      "implementation_strategy": {
        "project_approach": "项目实施方法",
        "team_structure": "团队结构",
        "key_milestones": ["关键里程碑1", "关键里程碑2", ...],
        "resource_requirements": {
          "human_resources": "人力资源需求",
          "technical_resources": "技术资源需求",
          "budget_allocation": "预算分配"
        }
      },
      "financial_analysis": {
        "cost_breakdown": {
          "direct_costs": "直接成本",
          "indirect_costs": "间接成本",
          "contingency": "应急费用",
          "profit_margin": "利润率"
        },
        "pricing_strategy": "定价策略",
        "payment_terms_analysis": "付款条件分析",
        "profitability_assessment": "盈利能力评估"
      },
      "recommendations": {
        "bid_no_bid_decision": {
          "recommendation": "投标建议",
          "rationale": "建议依据",
          "success_probability": "成功概率"
        },
        "key_success_factors": ["成功关键因素1", "成功关键因素2", ...],
        "critical_activities": ["关键活动1", "关键活动2", ...],
        "next_steps": [
          {
            "action": "行动项",
            "timeline": "时间安排",
            "responsible": "责任人",
            "priority": "优先级"
          }
        ]
      },
      "appendices": {
        "technical_specifications": "技术规格说明",
        "commercial_terms": "商务条款",
        "compliance_checklist": ["合规检查项1", "合规检查项2", ...],
        "reference_documents": ["参考文档1", "参考文档2", ...]
      }
    }

    确保返回有效的JSON格式，报告内容详实完整。

english:
  system: |
    You are an expert in tender report generation. Please generate a comprehensive tender analysis report based on AI summary and requirement analysis results.

  template: |
    Tender Basic Information:
    Summary Data: {{toJSON .Summary}}
    
    Analysis Results: {{toJSON .Analysis}}

    {{if .ReportType}}Report Type: {{.ReportType}}{{end}}
    {{if .TargetAudience}}Target Audience: {{.TargetAudience}}{{end}}
    {{if .IncludeSections}}Include Sections: {{join .IncludeSections ", "}}{{end}}

    Please generate a comprehensive tender analysis report in JSON format:
    {
      "report_metadata": {
        "title": "report title",
        "generation_date": "YYYY-MM-DD",
        "report_type": "report type",
        "document_version": "version number",
        "confidentiality_level": "confidentiality level"
      },
      "executive_summary": {
        "project_overview": "project overview",
        "key_findings": ["key finding 1", "key finding 2", ...],
        "recommendation": "overall recommendation",
        "estimated_win_rate": "estimated win rate"
      },
      "tender_analysis": {
        "project_details": {
          "tender_title": "tender project title",
          "tender_organization": "tendering organization",
          "project_value": "project value",
          "timeline": "project timeline",
          "submission_deadline": "submission deadline"
        },
        "requirement_breakdown": {
          "technical_requirements": ["technical requirement 1", "technical requirement 2", ...],
          "functional_requirements": ["functional requirement 1", "functional requirement 2", ...],
          "non_functional_requirements": ["non-functional requirement 1", "non-functional requirement 2", ...],
          "compliance_requirements": ["compliance requirement 1", "compliance requirement 2", ...]
        },
        "evaluation_criteria": {
          "technical_score_weight": "technical score weight",
          "commercial_score_weight": "commercial score weight",
          "key_evaluation_factors": ["evaluation factor 1", "evaluation factor 2", ...]
        }
      },
      "competitive_landscape": {
        "market_analysis": "market analysis",
        "competitor_assessment": ["competitor assessment 1", "competitor assessment 2", ...],
        "our_competitive_position": "our competitive position",
        "differentiation_opportunities": ["differentiation opportunity 1", "differentiation opportunity 2", ...]
      },
      "risk_assessment": {
        "technical_risks": [
          {
            "risk": "technical risk description",
            "impact": "impact level",
            "probability": "occurrence probability",
            "mitigation": "mitigation measures"
          }
        ],
        "commercial_risks": [
          {
            "risk": "commercial risk description",
            "impact": "impact level",
            "probability": "occurrence probability",
            "mitigation": "mitigation measures"
          }
        ],
        "overall_risk_level": "overall risk level"
      },
      "implementation_strategy": {
        "project_approach": "project implementation approach",
        "team_structure": "team structure",
        "key_milestones": ["key milestone 1", "key milestone 2", ...],
        "resource_requirements": {
          "human_resources": "human resource requirements",
          "technical_resources": "technical resource requirements",
          "budget_allocation": "budget allocation"
        }
      },
      "financial_analysis": {
        "cost_breakdown": {
          "direct_costs": "direct costs",
          "indirect_costs": "indirect costs",
          "contingency": "contingency costs",
          "profit_margin": "profit margin"
        },
        "pricing_strategy": "pricing strategy",
        "payment_terms_analysis": "payment terms analysis",
        "profitability_assessment": "profitability assessment"
      },
      "recommendations": {
        "bid_no_bid_decision": {
          "recommendation": "bidding recommendation",
          "rationale": "recommendation rationale",
          "success_probability": "success probability"
        },
        "key_success_factors": ["key success factor 1", "key success factor 2", ...],
        "critical_activities": ["critical activity 1", "critical activity 2", ...],
        "next_steps": [
          {
            "action": "action item",
            "timeline": "timeline",
            "responsible": "responsible person",
            "priority": "priority"
          }
        ]
      },
      "appendices": {
        "technical_specifications": "technical specifications",
        "commercial_terms": "commercial terms",
        "compliance_checklist": ["compliance item 1", "compliance item 2", ...],
        "reference_documents": ["reference document 1", "reference document 2", ...]
      }
    }

    Ensure the response is valid JSON format with comprehensive and detailed report content.