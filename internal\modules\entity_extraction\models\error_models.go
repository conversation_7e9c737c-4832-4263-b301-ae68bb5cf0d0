package models

// 错误码常量定义
const (
	// 输入相关错误
	ErrInvalidInput         = "INVALID_INPUT"
	ErrEmptyInput          = "EMPTY_INPUT"
	ErrInputTooLong        = "INPUT_TOO_LONG"
	ErrUnsupportedLanguage = "UNSUPPORTED_LANGUAGE"
	
	// 模型相关错误
	ErrModelNotFound       = "MODEL_NOT_FOUND"
	ErrUnsupportedModel    = "UNSUPPORTED_MODEL"
	ErrInvalidModelSchema  = "INVALID_MODEL_SCHEMA"
	
	// 业务相关错误
	ErrUnsupportedBusiness = "UNSUPPORTED_BUSINESS_TYPE"
	ErrExtractionImpossible = "EXTRACTION_IMPOSSIBLE"
	ErrLowConfidence       = "LOW_CONFIDENCE"
	
	// LLM相关错误
	ErrLLMCallFailed       = "LLM_CALL_FAILED"
	ErrLLMTimeout          = "LLM_TIMEOUT"
	ErrLLMRateLimit        = "LLM_RATE_LIMIT"
	ErrTokenExceeded       = "TOKEN_EXCEEDED"
	
	// 解析相关错误
	ErrResponseParseFailed = "RESPONSE_PARSE_FAILED"
	ErrInvalidJSON         = "INVALID_JSON"
	ErrSchemaValidation    = "SCHEMA_VALIDATION_FAILED"
	
	// 系统相关错误
	ErrConfigNotFound      = "CONFIG_NOT_FOUND"
	ErrStorageFailed       = "STORAGE_FAILED"
	ErrInternalError       = "INTERNAL_ERROR"
	ErrExtractionFailed    = "EXTRACTION_FAILED"
)

// NewExtractionError 创建提取错误
func NewExtractionError(code, message string, details map[string]interface{}) *ExtractionError {
	return &ExtractionError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(field, message, code string) ValidationError {
	return ValidationError{
		Field:   field,
		Message: message,
		Code:    code,
	}
}

// IsRetryableError 判断是否为可重试错误
func IsRetryableError(code string) bool {
	retryableErrors := []string{
		ErrLLMTimeout,
		ErrLLMRateLimit,
		ErrLLMCallFailed,
		ErrInternalError,
	}
	
	for _, retryableErr := range retryableErrors {
		if code == retryableErr {
			return true
		}
	}
	
	return false
}