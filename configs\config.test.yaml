server:
  port: "8601"
  mode: "debug"

logger:
  level: "debug"

mongodb:
  uri: "mongodb://formula:<EMAIL>:27017/overseas?authSource=admin"
  database: "taskd_test_db"
  timeout_seconds: 10

postgresql:
  host: "localhost"
  port: 5432
  user: "admin"
  password: "SecurePass123!"
  database: "taskd_tokens_test"
  ssl_mode: "disable"
  timeout_seconds: 10
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300

pulsar:
  service_url: "pulsar://*************:30164"
  consumer:
    topic_report_summary: "persistent://public/default/taskd-test-report-summary"
    subscription_name: "taskd-test-summary-sub"

# 测试环境的并发控制配置（较小的值用于测试）
concurrent:
  max_concurrent_requests: 100
  main_queue_size: 100
  retry_queue_size: 10
  max_wait_time_seconds: 60
  max_retries: 3
  retry_base_delay_seconds: 2
  worker_count: 5
  pulsar_topic: "persistent://public/default/test-llm-requests"
  pulsar_retry_topic: "persistent://public/default/test-llm-retries"
  pulsar_subscription: "test-llm-processor"

llm:
  default_provider: "volcengine_ark"
  providers:
    volcengine_ark:
      api_key: "cdb5ba17-1758-4d11-be9b-379c9453fb16"
      api_keys: # API Key池，用于测试轮询功能
        - "cdb5ba17-1758-4d11-be9b-379c9453fb16"
        - "08eb4227-fd5c-49cc-a0e2-a9c176a4dfa2"
        - "ce2ded6b-dc20-478b-b138-657b33419a96"
        - "7bdf1086-334c-46c7-9511-e4ebeb1ad168"
        - "5d0268a3-f710-4a97-ad7d-834584ffeb92"
      base_url: "https://ark.cn-beijing.volces.com/api/v3"
      models:
        "doubao-pro-32k": "doubao-pro-32k"
        "test-model": "doubao-pro-32k"
        "deepseek-v3-250324": "deepseek-v3-250324" # Entity Extraction Agent使用的正确模型ID
      default_model_alias: "test-model"
      request_timeout_seconds: 60
      max_concurrent_requests: 10