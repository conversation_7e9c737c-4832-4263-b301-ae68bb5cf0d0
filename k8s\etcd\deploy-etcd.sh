#!/bin/bash

# etcd部署脚本
# 用途：在k3s集群中部署单节点etcd作为配置中心

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查kubectl是否可用
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未找到，请先安装kubectl"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到k8s集群，请检查kubeconfig配置"
        exit 1
    fi
    
    log_success "kubectl 检查通过"
}

# 部署etcd
deploy_etcd() {
    log_info "开始部署etcd到k8s集群..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 部署资源（按顺序）
    log_info "1. 创建namespace..."
    kubectl apply -f "${SCRIPT_DIR}/namespace.yaml"
    
    log_info "2. 创建PVC..."
    kubectl apply -f "${SCRIPT_DIR}/pvc.yaml"
    
    log_info "3. 创建ConfigMap..."
    kubectl apply -f "${SCRIPT_DIR}/configmap.yaml"
    
    log_info "4. 创建Secret..."
    kubectl apply -f "${SCRIPT_DIR}/secret.yaml"
    
    log_info "5. 创建Service..."
    kubectl apply -f "${SCRIPT_DIR}/service.yaml"
    
    log_info "6. 创建StatefulSet..."
    kubectl apply -f "${SCRIPT_DIR}/statefulset.yaml"
    
    log_success "etcd部署配置已提交"
}

# 等待etcd启动
wait_for_etcd() {
    log_info "等待etcd启动..."
    
    # 等待StatefulSet就绪
    kubectl wait --for=condition=ready pod -l app=etcd -n etcd --timeout=300s
    
    if [ $? -eq 0 ]; then
        log_success "etcd启动成功"
    else
        log_error "etcd启动超时，请检查日志"
        log_info "查看Pod状态: kubectl get pods -n etcd"
        log_info "查看Pod日志: kubectl logs -n etcd -l app=etcd"
        exit 1
    fi
}

# 验证etcd功能
verify_etcd() {
    log_info "验证etcd功能..."
    
    # 获取etcd Pod名称
    ETCD_POD=$(kubectl get pods -n etcd -l app=etcd -o jsonpath='{.items[0].metadata.name}')
    
    if [ -z "$ETCD_POD" ]; then
        log_error "未找到etcd Pod"
        exit 1
    fi
    
    # 测试写入和读取
    log_info "测试etcd读写功能..."
    
    # 写入测试数据
    kubectl exec -n etcd "$ETCD_POD" -- etcdctl put /test/deploy-check "etcd-deploy-success-$(date +%s)"
    
    # 读取测试数据
    TEST_VALUE=$(kubectl exec -n etcd "$ETCD_POD" -- etcdctl get /test/deploy-check --print-value-only)
    
    if [[ "$TEST_VALUE" == etcd-deploy-success-* ]]; then
        log_success "etcd读写测试通过"
        # 清理测试数据
        kubectl exec -n etcd "$ETCD_POD" -- etcdctl del /test/deploy-check
    else
        log_error "etcd读写测试失败"
        exit 1
    fi
}

# 显示连接信息
show_connection_info() {
    log_info "etcd连接信息："
    echo "  集群内访问地址: etcd-service.etcd.svc.cluster.local:2379"
    echo "  外部访问地址 (NodePort): <NODE_IP>:32379"
    echo ""
    log_info "常用操作命令："
    echo "  查看etcd状态: kubectl get pods -n etcd"
    echo "  查看etcd日志: kubectl logs -n etcd -l app=etcd -f"
    echo "  进入etcd容器: kubectl exec -it -n etcd <pod-name> -- sh"
    echo "  etcd健康检查: kubectl exec -n etcd <pod-name> -- etcdctl endpoint health"
    echo ""
    log_info "taskd配置说明："
    echo "  ETCD_ENDPOINTS: etcd-service.etcd.svc.cluster.local:2379"
    echo "  ETCD_ENABLED: true"
}

# 主函数
main() {
    log_info "开始部署etcd配置中心..."
    echo "========================================"
    
    check_kubectl
    deploy_etcd
    wait_for_etcd
    verify_etcd
    
    echo "========================================"
    log_success "etcd部署完成！"
    show_connection_info
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi