package services

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models/business"
	profileModels "gitlab.com/specific-ai/taskd/internal/modules/profile/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// StorageService 企业画像存储服务实现
type StorageService struct {
}

// NewStorageService 创建新的企业画像存储服务
func NewStorageService() *StorageService {
	return &StorageService{}
}

// SaveProfile 保存企业画像到数据库
func (s *StorageService) SaveProfile(profile *profileModels.ProfileResponse, req *profileModels.ProfileRequest) error {
	// 输入验证
	if profile == nil {
		return fmt.Errorf("企业画像数据不能为空")
	}
	if req == nil {
		return fmt.Errorf("企业画像请求数据不能为空")
	}
	if profile.UserID == "" {
		return fmt.Errorf("用户ID不能为空")
	}
	if profile.CompanyName == "" {
		return fmt.Errorf("企业名称不能为空")
	}
	// Industry字段已移除，不再验证

	db := store.GetProfileDB()
	if db == nil {
		return fmt.Errorf("企业画像数据库连接未初始化")
	}

	// 序列化 JSONB 字段
	businessCapabilitiesJSON, err := json.Marshal(profile.BusinessCapabilities)
	if err != nil {
		return fmt.Errorf("序列化业务能力数据失败: %w", err)
	}

	tenderMatchingJSON, err := json.Marshal(profile.TenderMatching)
	if err != nil {
		return fmt.Errorf("序列化招投标匹配数据失败: %w", err)
	}

	competitiveProfileJSON, err := json.Marshal(profile.CompetitiveProfile)
	if err != nil {
		return fmt.Errorf("序列化竞争力分析数据失败: %w", err)
	}

	internationalCapabilitiesJSON, err := json.Marshal(profile.InternationalCapabilities)
	if err != nil {
		return fmt.Errorf("序列化海外业务能力数据失败: %w", err)
	}

	riskToleranceJSON, err := json.Marshal(profile.RiskTolerance)
	if err != nil {
		return fmt.Errorf("序列化风险承受能力数据失败: %w", err)
	}

	// 插入数据到数据库
	query := `
		INSERT INTO company_profiles (
			user_id, company_name, industry, company_scale, business_scope, 
			location, established_year, description, website, additional_info,
			business_capabilities, tender_matching, competitive_profile, 
			international_capabilities, risk_tolerance,
			confidence_score, model_used, strategy_version, processing_time_ms,
			generated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
			$11, $12, $13, $14, $15,
			$16, $17, $18, $19, $20
		) RETURNING id`

	var profileID int64
	var additionalInfoJSON []byte

	// 由于字段已简化，使用空值
	additionalInfoJSON, _ = json.Marshal(map[string]interface{}{})

	err = db.QueryRow(
		query,
		profile.UserID, profile.CompanyName, "", // industry
		"", "", "", // company_scale, business_scope, location
		0, "", "", additionalInfoJSON, // established_year, description, website, additional_info
		businessCapabilitiesJSON, tenderMatchingJSON, competitiveProfileJSON,
		internationalCapabilitiesJSON, riskToleranceJSON,
		profile.ConfidenceScore, profile.Metadata.ModelUsed, profile.Metadata.StrategyVersion,
		profile.Metadata.ProcessingTime, profile.GeneratedAt,
	).Scan(&profileID)

	if err != nil {
		utils.Log.Errorf("保存企业画像到数据库失败: %v", err)
		return fmt.Errorf("保存企业画像失败: %w", err)
	}

	utils.Log.Infof("成功保存企业画像 - ID: %d, 企业: %s, 用户: %s",
		profileID, profile.CompanyName, profile.UserID)

	// 异步保存生成历史记录
	go func() {
		if err := s.saveGenerationHistory(profileID, req, profile); err != nil {
			utils.Log.Warnf("保存企业画像生成历史失败: %v", err)
		}
	}()

	return nil
}

// GetProfileByUserAndCompany 根据用户ID和企业名称获取企业画像
func (s *StorageService) GetProfileByUserAndCompany(userID, companyName string) (*profileModels.ProfileResponse, error) {
	// 输入验证
	if userID == "" {
		return nil, fmt.Errorf("用户ID不能为空")
	}
	if companyName == "" {
		return nil, fmt.Errorf("企业名称不能为空")
	}

	db := store.GetProfileDB()
	if db == nil {
		return nil, fmt.Errorf("企业画像数据库连接未初始化")
	}

	query := `
		SELECT 
			id, user_id, company_name, industry, company_scale, business_scope,
			location, established_year, description, website, additional_info,
			business_capabilities, tender_matching, competitive_profile,
			international_capabilities, risk_tolerance,
			confidence_score, model_used, strategy_version, processing_time_ms,
			generated_at
		FROM company_profiles 
		WHERE user_id = $1 AND company_name = $2 
		ORDER BY generated_at DESC 
		LIMIT 1`

	var (
		id                         int64
		industry, companyScale     *string
		businessScope, location    *string
		establishedYear            *int
		description, website       *string
		additionalInfo             []byte
		businessCapabilities       []byte
		tenderMatching             []byte
		competitiveProfile         []byte
		internationalCapabilities  []byte
		riskTolerance              []byte
		confidenceScore            int
		modelUsed, strategyVersion string
		processingTime             int64
		generatedAt                time.Time
	)

	err := db.QueryRow(query, userID, companyName).Scan(
		&id, &userID, &companyName, &industry, &companyScale, &businessScope,
		&location, &establishedYear, &description, &website, &additionalInfo,
		&businessCapabilities, &tenderMatching, &competitiveProfile,
		&internationalCapabilities, &riskTolerance,
		&confidenceScore, &modelUsed, &strategyVersion, &processingTime,
		&generatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("获取企业画像失败: %w", err)
	}

	// 反序列化 JSONB 字段
	var businessCap business.BusinessCapabilities
	if err := json.Unmarshal(businessCapabilities, &businessCap); err != nil {
		return nil, fmt.Errorf("反序列化业务能力数据失败: %w", err)
	}

	var tenderMatch business.TenderMatching
	if err := json.Unmarshal(tenderMatching, &tenderMatch); err != nil {
		return nil, fmt.Errorf("反序列化招投标匹配数据失败: %w", err)
	}

	var competitiveProf business.CompetitiveProfile
	if err := json.Unmarshal(competitiveProfile, &competitiveProf); err != nil {
		return nil, fmt.Errorf("反序列化竞争力分析数据失败: %w", err)
	}

	var internationalCap business.InternationalCapabilities
	if err := json.Unmarshal(internationalCapabilities, &internationalCap); err != nil {
		return nil, fmt.Errorf("反序列化海外业务能力数据失败: %w", err)
	}

	var riskTol business.RiskTolerance
	if err := json.Unmarshal(riskTolerance, &riskTol); err != nil {
		return nil, fmt.Errorf("反序列化风险承受能力数据失败: %w", err)
	}

	// 构建响应对象
	response := &profileModels.ProfileResponse{
		UserID:                    userID,
		CompanyName:               companyName,
		BusinessCapabilities:      &businessCap,
		TenderMatching:            &tenderMatch,
		CompetitiveProfile:        &competitiveProf,
		InternationalCapabilities: &internationalCap,
		RiskTolerance:             &riskTol,
		ConfidenceScore:           confidenceScore,
		GeneratedAt:               generatedAt,
		Metadata: &business.ProfileMetadata{
			ModelUsed:       modelUsed,
			StrategyVersion: strategyVersion,
			ProcessingTime:  processingTime,
		},
	}

	return response, nil
}

// GetUserProfiles 获取用户的所有企业画像
func (s *StorageService) GetUserProfiles(userID string, limit, offset int) ([]*profileModels.ProfileResponse, error) {
	db := store.GetProfileDB()
	if db == nil {
		return nil, fmt.Errorf("企业画像数据库连接未初始化")
	}

	if limit <= 0 {
		limit = 10 // 默认限制
	}

	query := `
		SELECT 
			user_id, company_name, confidence_score, generated_at
		FROM company_profiles 
		WHERE user_id = $1 
		ORDER BY generated_at DESC 
		LIMIT $2 OFFSET $3`

	rows, err := db.Query(query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询用户企业画像失败: %w", err)
	}
	defer rows.Close()

	var profiles []*profileModels.ProfileResponse
	for rows.Next() {
		var profile profileModels.ProfileResponse
		err := rows.Scan(
			&profile.UserID,
			&profile.CompanyName,
			&profile.ConfidenceScore,
			&profile.GeneratedAt,
		)
		if err != nil {
			utils.Log.Errorf("扫描企业画像行失败: %v", err)
			continue
		}
		profiles = append(profiles, &profile)
	}

	return profiles, nil
}

// DeleteProfile 删除企业画像
func (s *StorageService) DeleteProfile(userID, companyName string) error {
	db := store.GetProfileDB()
	if db == nil {
		return fmt.Errorf("企业画像数据库连接未初始化")
	}

	query := `DELETE FROM company_profiles WHERE user_id = $1 AND company_name = $2`
	result, err := db.Exec(query, userID, companyName)
	if err != nil {
		return fmt.Errorf("删除企业画像失败: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("未找到要删除的企业画像")
	}

	utils.Log.Infof("已删除企业画像 - 企业: %s, 用户: %s", companyName, userID)
	return nil
}

// saveGenerationHistory 保存企业画像生成历史记录
func (s *StorageService) saveGenerationHistory(profileID int64, req *profileModels.ProfileRequest, profile *profileModels.ProfileResponse) error {
	db := store.GetProfileDB()
	if db == nil {
		return fmt.Errorf("企业画像数据库连接未初始化")
	}

	// 序列化请求参数
	requestParams, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求参数失败: %w", err)
	}

	// 序列化生成元数据
	generationMetadata := map[string]interface{}{
		"model_used":       profile.Metadata.ModelUsed,
		"strategy_version": profile.Metadata.StrategyVersion,
		"processing_time":  profile.Metadata.ProcessingTime,
		"confidence_score": profile.ConfidenceScore,
		"data_sources":     profile.Metadata.DataSources,
	}

	metadataJSON, err := json.Marshal(generationMetadata)
	if err != nil {
		return fmt.Errorf("序列化生成元数据失败: %w", err)
	}

	query := `
		INSERT INTO profile_generation_history (
			profile_id, user_id, company_name, request_params, generation_metadata
		) VALUES ($1, $2, $3, $4, $5)`

	_, err = db.Exec(query, profileID, req.UserContext.UserID, req.CompanyName, requestParams, metadataJSON)
	if err != nil {
		return fmt.Errorf("保存生成历史记录失败: %w", err)
	}

	return nil
}

// GetProfileGenerationHistory 获取企业画像生成历史
func (s *StorageService) GetProfileGenerationHistory(userID string, limit, offset int) ([]map[string]interface{}, error) {
	// 输入验证
	if userID == "" {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	db := store.GetProfileDB()
	if db == nil {
		return nil, fmt.Errorf("企业画像数据库连接未初始化")
	}

	if limit <= 0 {
		limit = 20 // 默认限制
	}

	query := `
		SELECT 
			id, profile_id, company_name, request_params, generation_metadata, generated_at
		FROM profile_generation_history 
		WHERE user_id = $1 
		ORDER BY generated_at DESC 
		LIMIT $2 OFFSET $3`

	rows, err := db.Query(query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询生成历史失败: %w", err)
	}
	defer rows.Close()

	var history []map[string]interface{}
	for rows.Next() {
		var (
			id                 int64
			profileID          int64
			companyName        string
			requestParams      []byte
			generationMetadata []byte
			generatedAt        time.Time
		)

		err := rows.Scan(&id, &profileID, &companyName, &requestParams, &generationMetadata, &generatedAt)
		if err != nil {
			utils.Log.Errorf("扫描生成历史行失败: %v", err)
			continue
		}

		historyItem := map[string]interface{}{
			"id":           id,
			"profile_id":   profileID,
			"company_name": companyName,
			"generated_at": generatedAt,
		}

		// 解析请求参数（可选）
		var reqParams map[string]interface{}
		if json.Unmarshal(requestParams, &reqParams) == nil {
			historyItem["request_params"] = reqParams
		}

		// 解析生成元数据（可选）
		var metadata map[string]interface{}
		if json.Unmarshal(generationMetadata, &metadata) == nil {
			historyItem["generation_metadata"] = metadata
		}

		history = append(history, historyItem)
	}

	return history, nil
}