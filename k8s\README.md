# TaskD Kubernetes 部署

## 目录结构

```
k8s/
├── etcd/                    # etcd配置中心部署
│   ├── namespace.yaml       # etcd命名空间
│   ├── pvc.yaml            # 持久化存储
│   ├── configmap.yaml      # etcd配置
│   ├── secret.yaml         # etcd敏感配置
│   ├── statefulset.yaml    # etcd StatefulSet
│   ├── service.yaml        # etcd服务
│   ├── deploy-etcd.sh      # etcd部署脚本
│   ├── init-config.sh      # 配置初始化脚本
│   └── README.md           # etcd部署文档
├── taskd/                   # taskd服务部署
│   ├── namespace.yaml       # taskd命名空间
│   ├── configmap.yaml      # taskd配置
│   ├── secret.yaml         # taskd敏感配置
│   ├── deployment.yaml     # taskd部署
│   ├── service.yaml        # taskd服务
│   ├── deploy-taskd.sh     # taskd部署脚本
│   ├── debug-taskd.sh      # 调试脚本
│   ├── quick-restart.sh    # 快速重启脚本
│   └── README.md           # taskd部署文档
├── DEPLOYMENT_WITH_ETCD.md # 完整部署指南
└── README.md               # 本文档
```

## 快速开始

### 完整部署（etcd + taskd）

```bash
# 1. 部署etcd配置中心
cd etcd/
./deploy-etcd.sh
./init-config.sh

# 2. 部署taskd服务
cd ../taskd/
./deploy-taskd.sh
```

### 单独部署

```bash
# 仅部署etcd
cd etcd/
./deploy-etcd.sh

# 仅部署taskd（需要etcd已存在）
cd taskd/
./deploy-taskd.sh
```

## 架构说明

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TaskD Pod     │────│  etcd Service   │────│   etcd Pod      │
│ (ovs namespace) │    │ (etcd namespace)│    │ (etcd namespace)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              读取配置                          │
         └──────────────────────────────────────────────┘
```

## 依赖关系

- **taskd** 强依赖 **etcd**
- etcd不可用时，taskd启动失败
- 本地开发时可禁用etcd依赖

## 常用操作

### 查看状态

```bash
# 查看所有资源
kubectl get all -n etcd
kubectl get all -n ovs

# 查看Pod日志
kubectl logs -n etcd -l app=etcd -f
kubectl logs -n ovs -l app=taskd -f
```

### 配置管理

```bash
# 查看etcd中的配置
kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd --prefix

# 修改配置
kubectl exec -n etcd etcd-0 -- etcdctl put /config/taskd/mongodb/database "new_db"

# 重启taskd应用配置
kubectl delete pod -n ovs -l app=taskd
```

### 调试

```bash
# 进入taskd调试模式
cd taskd/
./debug-taskd.sh

# 快速重启taskd
cd taskd/
./quick-restart.sh
```

## 故障排除

### 常见问题

1. **taskd启动失败**
   ```bash
   kubectl logs -n ovs -l app=taskd
   # 检查是否为etcd连接问题
   ```

2. **etcd连接超时**
   ```bash
   kubectl get svc -n etcd etcd-service
   # 检查etcd服务是否正常
   ```

3. **配置未生效**
   ```bash
   kubectl delete pod -n ovs -l app=taskd
   # 重启Pod重新加载配置
   ```

### 清理部署

```bash
# 删除taskd
kubectl delete namespace ovs

# 删除etcd（慎重！会丢失所有配置数据）
kubectl delete namespace etcd
```

## 文档链接

- [etcd部署指南](etcd/README.md)
- [taskd部署指南](taskd/README.md)
- [完整部署流程](DEPLOYMENT_WITH_ETCD.md)

## 注意事项

1. **部署顺序**：必须先部署etcd，再部署taskd
2. **数据持久化**：etcd使用PVC持久化数据
3. **配置更新**：修改配置后需重启taskd Pod
4. **备份重要**：定期备份etcd数据
5. **监控必要**：监控etcd和taskd的健康状态

## 支持版本

- **Kubernetes**: v1.21+
- **k3s**: v1.21+
- **etcd**: v3.5.9
- **taskd**: 支持etcd集成的版本