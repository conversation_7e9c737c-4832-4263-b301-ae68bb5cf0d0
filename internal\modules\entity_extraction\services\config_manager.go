package services

import (
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// EntityExtractionConfigManager 实体提取配置管理器
type EntityExtractionConfigManager struct {
	config *EntityExtractionAgentConfig
}

// EntityExtractionAgentConfig 实体提取代理配置结构
type EntityExtractionAgentConfig struct {
	// Agent基础配置
	AgentID string `json:"agent_id" yaml:"agent_id"`
	Name    string `json:"name" yaml:"name"`
	Version string `json:"version" yaml:"version"`
	Enabled bool   `json:"enabled" yaml:"enabled"`

	// LLM配置
	LLM EntityExtractionLLMConfig `json:"llm" yaml:"llm"`

	// 多语言支持配置
	Languages EntityExtractionLanguageConfig `json:"languages" yaml:"languages"`

	// 性能配置
	Performance EntityExtractionPerformanceConfig `json:"performance" yaml:"performance"`

	// 验证配置
	Validation EntityExtractionValidationConfig `json:"validation" yaml:"validation"`
}

// EntityExtractionLLMConfig LLM配置
type EntityExtractionLLMConfig struct {
	Model       string        `json:"model" yaml:"model"`
	Temperature float32       `json:"temperature" yaml:"temperature"`
	MaxTokens   int           `json:"max_tokens" yaml:"max_tokens"`
	Timeout     time.Duration `json:"timeout" yaml:"timeout"`
	RetryCount  int           `json:"retry_count" yaml:"retry_count"`
}

// EntityExtractionLanguageConfig 语言配置
type EntityExtractionLanguageConfig struct {
	Supported []string `json:"supported" yaml:"supported"`
	Default   string   `json:"default" yaml:"default"`
	Fallback  string   `json:"fallback" yaml:"fallback"`
}

// EntityExtractionPerformanceConfig 性能配置
type EntityExtractionPerformanceConfig struct {
	MaxInputLength             int           `json:"max_input_length" yaml:"max_input_length"`
	MaxConcurrentRequests      int           `json:"max_concurrent_requests" yaml:"max_concurrent_requests"`
	RequestTimeout             time.Duration `json:"request_timeout" yaml:"request_timeout"`
	DefaultConfidenceThreshold float64       `json:"default_confidence_threshold" yaml:"default_confidence_threshold"`
}

// EntityExtractionValidationConfig 验证配置
type EntityExtractionValidationConfig struct {
	EnableInputValidation  bool `json:"enable_input_validation" yaml:"enable_input_validation"`
	EnableSchemaValidation bool `json:"enable_schema_validation" yaml:"enable_schema_validation"`
	EnableOutputValidation bool `json:"enable_output_validation" yaml:"enable_output_validation"`
	StrictMode             bool `json:"strict_mode" yaml:"strict_mode"`
}

// NewEntityExtractionConfigManager 创建实体提取配置管理器
func NewEntityExtractionConfigManager() *EntityExtractionConfigManager {
	return &EntityExtractionConfigManager{}
}

// LoadConfig 加载配置
func (cm *EntityExtractionConfigManager) LoadConfig() (*EntityExtractionAgentConfig, error) {
	// 如果已有配置，直接返回
	if cm.config != nil {
		return cm.config, nil
	}

	// 否则返回默认配置
	cm.config = cm.GetDefaultConfig()
	return cm.config, nil
}

// ValidateConfig 验证配置
func (cm *EntityExtractionConfigManager) ValidateConfig(config *EntityExtractionAgentConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证Agent基础配置
	if config.AgentID == "" {
		return fmt.Errorf("Agent ID不能为空")
	}

	if config.Name == "" {
		return fmt.Errorf("Agent名称不能为空")
	}

	if config.Version == "" {
		return fmt.Errorf("Agent版本不能为空")
	}

	// 验证LLM配置
	if err := cm.validateLLMConfig(&config.LLM); err != nil {
		return fmt.Errorf("LLM配置验证失败: %v", err)
	}

	// 验证语言配置
	if err := cm.validateLanguageConfig(&config.Languages); err != nil {
		return fmt.Errorf("语言配置验证失败: %v", err)
	}

	// 验证性能配置
	if err := cm.validatePerformanceConfig(&config.Performance); err != nil {
		return fmt.Errorf("性能配置验证失败: %v", err)
	}

	return nil
}

// validateLLMConfig 验证LLM配置
func (cm *EntityExtractionConfigManager) validateLLMConfig(config *EntityExtractionLLMConfig) error {
	if config.Model == "" {
		return fmt.Errorf("LLM模型不能为空")
	}

	if config.Temperature < 0 || config.Temperature > 2 {
		return fmt.Errorf("Temperature必须在0-2之间，当前值: %f", config.Temperature)
	}

	if config.MaxTokens <= 0 || config.MaxTokens > 100000 {
		return fmt.Errorf("MaxTokens必须在1-100000之间，当前值: %d", config.MaxTokens)
	}

	if config.Timeout <= 0 || config.Timeout > time.Minute*10 {
		return fmt.Errorf("Timeout必须在1秒-10分钟之间，当前值: %v", config.Timeout)
	}

	if config.RetryCount < 0 || config.RetryCount > 10 {
		return fmt.Errorf("RetryCount必须在0-10之间，当前值: %d", config.RetryCount)
	}

	return nil
}

// validateLanguageConfig 验证语言配置
func (cm *EntityExtractionConfigManager) validateLanguageConfig(config *EntityExtractionLanguageConfig) error {
	if len(config.Supported) == 0 {
		return fmt.Errorf("支持的语言列表不能为空")
	}

	validLanguages := map[string]bool{
		"zh": true, "en": true, "mixed": true,
	}

	for _, lang := range config.Supported {
		if !validLanguages[lang] {
			return fmt.Errorf("不支持的语言: %s", lang)
		}
	}

	if config.Default == "" {
		return fmt.Errorf("默认语言不能为空")
	}

	if !validLanguages[config.Default] {
		return fmt.Errorf("默认语言无效: %s", config.Default)
	}

	// 检查默认语言是否在支持列表中
	defaultSupported := false
	for _, lang := range config.Supported {
		if lang == config.Default {
			defaultSupported = true
			break
		}
	}

	if !defaultSupported {
		return fmt.Errorf("默认语言%s不在支持列表中", config.Default)
	}

	return nil
}

// validatePerformanceConfig 验证性能配置
func (cm *EntityExtractionConfigManager) validatePerformanceConfig(config *EntityExtractionPerformanceConfig) error {
	if config.MaxInputLength <= 0 || config.MaxInputLength > 1000000 {
		return fmt.Errorf("MaxInputLength必须在1-1000000之间，当前值: %d", config.MaxInputLength)
	}

	if config.MaxConcurrentRequests <= 0 || config.MaxConcurrentRequests > 1000 {
		return fmt.Errorf("MaxConcurrentRequests必须在1-1000之间，当前值: %d", config.MaxConcurrentRequests)
	}

	if config.RequestTimeout <= 0 || config.RequestTimeout > time.Minute*30 {
		return fmt.Errorf("RequestTimeout必须在1秒-30分钟之间，当前值: %v", config.RequestTimeout)
	}

	if config.DefaultConfidenceThreshold < 0 || config.DefaultConfidenceThreshold > 1 {
		return fmt.Errorf("DefaultConfidenceThreshold必须在0-1之间，当前值: %f", config.DefaultConfidenceThreshold)
	}

	return nil
}

// GetDefaultConfig 获取默认配置
func (cm *EntityExtractionConfigManager) GetDefaultConfig() *EntityExtractionAgentConfig {
	return &EntityExtractionAgentConfig{
		AgentID: "entity-extraction-agent-001",
		Name:    "通用实体提取Agent",
		Version: "1.0.0",
		Enabled: true,

		LLM: EntityExtractionLLMConfig{
			Model:       "deepseek-v3-250324",
			Temperature: 0.1,
			MaxTokens:   4000,
			Timeout:     30 * time.Second,
			RetryCount:  3,
		},

		Languages: EntityExtractionLanguageConfig{
			Supported: []string{"zh", "en", "mixed"},
			Default:   "zh",
			Fallback:  "en",
		},

		Performance: EntityExtractionPerformanceConfig{
			MaxInputLength:             50000,
			MaxConcurrentRequests:      50,
			RequestTimeout:             60 * time.Second,
			DefaultConfidenceThreshold: 0.7,
		},

		Validation: EntityExtractionValidationConfig{
			EnableInputValidation:  true,
			EnableSchemaValidation: true,
			EnableOutputValidation: true,
			StrictMode:             false,
		},
	}
}

// UpdateConfig 更新配置
func (cm *EntityExtractionConfigManager) UpdateConfig(config *EntityExtractionAgentConfig) error {
	// 验证新配置
	if err := cm.ValidateConfig(config); err != nil {
		return err
	}

	// 更新配置
	cm.config = config

	return nil
}

// GetConfig 获取当前配置
func (cm *EntityExtractionConfigManager) GetConfig() *EntityExtractionAgentConfig {
	if cm.config == nil {
		cm.config = cm.GetDefaultConfig()
	}
	return cm.config
}

// GetLLMConfig 获取LLM配置
func (cm *EntityExtractionConfigManager) GetLLMConfig() *EntityExtractionLLMConfig {
	config := cm.GetConfig()
	return &config.LLM
}

// GetLanguageConfig 获取语言配置
func (cm *EntityExtractionConfigManager) GetLanguageConfig() *EntityExtractionLanguageConfig {
	config := cm.GetConfig()
	return &config.Languages
}

// GetPerformanceConfig 获取性能配置
func (cm *EntityExtractionConfigManager) GetPerformanceConfig() *EntityExtractionPerformanceConfig {
	config := cm.GetConfig()
	return &config.Performance
}

// GetValidationConfig 获取验证配置
func (cm *EntityExtractionConfigManager) GetValidationConfig() *EntityExtractionValidationConfig {
	config := cm.GetConfig()
	return &config.Validation
}

// IsLanguageSupported 检查语言是否支持
func (cm *EntityExtractionConfigManager) IsLanguageSupported(language string) bool {
	config := cm.GetLanguageConfig()
	for _, supported := range config.Supported {
		if supported == language {
			return true
		}
	}
	return false
}

// GetDefaultLanguage 获取默认语言
func (cm *EntityExtractionConfigManager) GetDefaultLanguage() string {
	config := cm.GetLanguageConfig()
	return config.Default
}

// GetFallbackLanguage 获取降级语言
func (cm *EntityExtractionConfigManager) GetFallbackLanguage() string {
	config := cm.GetLanguageConfig()
	if config.Fallback != "" {
		return config.Fallback
	}
	return config.Default
}

// ApplyExtractionConfig 应用提取配置到请求
func (cm *EntityExtractionConfigManager) ApplyExtractionConfig(req *models.ExtractionRequest) {
	perfConfig := cm.GetPerformanceConfig()

	// 如果请求没有配置，使用默认配置
	if req.ExtractionConfig == nil {
		req.ExtractionConfig = &models.ExtractionConfig{
			ConfidenceThreshold: perfConfig.DefaultConfidenceThreshold,
			MaxRetries:          cm.GetLLMConfig().RetryCount,
			Timeout:             int(cm.GetLLMConfig().Timeout.Seconds()),
			EnableFallback:      true,
		}
		return
	}

	// 应用默认值
	if req.ExtractionConfig.ConfidenceThreshold == 0 {
		req.ExtractionConfig.ConfidenceThreshold = perfConfig.DefaultConfidenceThreshold
	}

	if req.ExtractionConfig.MaxRetries == 0 {
		req.ExtractionConfig.MaxRetries = cm.GetLLMConfig().RetryCount
	}

	if req.ExtractionConfig.Timeout == 0 {
		req.ExtractionConfig.Timeout = int(cm.GetLLMConfig().Timeout.Seconds())
	}
}

// GetConfigSummary 获取配置摘要
func (cm *EntityExtractionConfigManager) GetConfigSummary() map[string]interface{} {
	config := cm.GetConfig()

	return map[string]interface{}{
		"agent_id": config.AgentID,
		"name":     config.Name,
		"version":  config.Version,
		"enabled":  config.Enabled,
		"llm": map[string]interface{}{
			"model":       config.LLM.Model,
			"temperature": config.LLM.Temperature,
			"max_tokens":  config.LLM.MaxTokens,
			"timeout":     config.LLM.Timeout.String(),
		},
		"languages": map[string]interface{}{
			"supported": config.Languages.Supported,
			"default":   config.Languages.Default,
		},
		"performance": map[string]interface{}{
			"max_input_length":             config.Performance.MaxInputLength,
			"max_concurrent_requests":      config.Performance.MaxConcurrentRequests,
			"default_confidence_threshold": config.Performance.DefaultConfidenceThreshold,
		},
	}
}
