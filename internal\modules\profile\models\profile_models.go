package models

import (
	"gitlab.com/specific-ai/taskd/internal/models/business"
)

// 企业画像模块类型别名，引用现有business模型
// 这样保持了现有业务逻辑不变，同时符合模块化架构

// ProfileRequest 企业画像请求模型
type ProfileRequest = business.CompanyProfileRequest

// ProfileResponse 企业画像响应模型  
type ProfileResponse = business.CompanyProfileResponse

// ProfileMetadata 企业画像元数据
type ProfileMetadata = business.ProfileMetadata

// BusinessCapabilities 业务能力
type BusinessCapabilities = business.BusinessCapabilities

// TenderMatching 招投标匹配
type TenderMatching = business.TenderMatching

// CompetitiveProfile 竞争力分析
type CompetitiveProfile = business.CompetitiveProfile

// InternationalCapabilities 国际化能力
type InternationalCapabilities = business.InternationalCapabilities

// RiskTolerance 风险承受能力
type RiskTolerance = business.RiskTolerance

// UserInputField 用户输入字段
type UserInputField = business.UserInputField

// WebSearchResult 网络搜索结果
type WebSearchResult = business.WebSearchResult

// UserContext 用户上下文
type UserContext = business.UserContext