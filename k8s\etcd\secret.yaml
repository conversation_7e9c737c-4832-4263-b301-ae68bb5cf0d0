apiVersion: v1
kind: Secret
metadata:
  name: etcd-secret
  namespace: etcd
  labels:
    app: etcd
type: Opaque
data:
  # etcd用户名和密码 (base64编码)
  # 如果需要启用身份验证，取消注释并设置
  # 原值: etcd_admin
  # ETCD_ROOT_USERNAME: ZXRjZF9hZG1pbg==
  # 原值: etcd_secure_password_123
  # ETCD_ROOT_PASSWORD: ZXRjZF9zZWN1cmVfcGFzc3dvcmRfMTIz
  
  # TLS证书文件 (如果需要TLS，可以添加证书数据)
  # tls.crt: LS0tLS1CRUdJTi...
  # tls.key: LS0tLS1CRUdJTi...
  # ca.crt: LS0tLS1CRUdJTi...

---
# 注意：
# 1. 当前配置为非安全模式，生产环境建议启用TLS和身份验证
# 2. 使用以下命令生成base64编码：
#    echo -n "your-secret-value" | base64
# 3. 生产环境应该使用更强的密码和TLS证书