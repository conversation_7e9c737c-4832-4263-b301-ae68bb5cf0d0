# 实体提取Agent使用示例和最佳实践

## 概述

本文档提供了实体提取Agent的详细使用示例和最佳实践指南，涵盖各种业务场景的实际应用案例，帮助开发者快速上手并有效使用该Agent。

## 快速开始

### 基本调用示例

```python
import requests
import json

# Agent API基础配置
BASE_URL = "http://localhost:8080"
AGENT_ID = "entity-extraction-agent-001"

def call_entity_extraction(input_text, target_schema, language="zh"):
    """调用实体提取Agent"""
    url = f"{BASE_URL}/api/agents/{AGENT_ID}/execute"
    
    payload = {
        "capability": "extract_structured_data",
        "input": {
            "input_text": input_text,
            "target_schema": target_schema,
            "language": language
        },
        "config": {
            "timeout": 30,
            "priority": 1
        }
    }
    
    response = requests.post(url, json=payload)
    return response.json()

# 使用示例
schema = {
    "name": "ProductInfo",
    "description": "产品信息模型",
    "type": "object",
    "properties": {
        "name": {"type": "string", "description": "产品名称"},
        "price": {"type": "number", "description": "价格"}
    },
    "required": ["name"]
}

result = call_entity_extraction("苹果iPhone 15售价999美元", schema)
print(json.dumps(result, indent=2, ensure_ascii=False))
```

## 业务场景示例

### 1. 商机维度提取示例

#### 场景描述
从用户的自然语言描述中提取商机分析的维度信息，用于构建商机分析框架。

#### 完整示例代码

```python
def extract_opportunity_dimension():
    """商机维度提取示例"""
    
    # 定义商机维度Schema
    opportunity_schema = {
        "name": "OpportunityDimension",
        "description": "商机维度模型，用于分析商业机会的各个维度",
        "type": "object",
        "properties": {
            "name": {
                "type": "string",
                "description": "维度名称，如'商业机会'、'技术创新'、'市场趋势'等"
            },
            "description": {
                "type": "string", 
                "description": "维度的详细描述，解释该维度的含义和作用"
            },
            "keywords": {
                "type": "array",
                "description": "与该维度相关的关键字列表，支持中英文",
                "items": {"type": "string"}
            },
            "importance": {
                "type": "string",
                "enum": ["high", "medium", "low"],
                "description": "维度重要性等级"
            },
            "analysis_method": {
                "type": "string",
                "description": "建议的分析方法或工具"
            }
        },
        "required": ["name", "description"],
        "examples": [
            {
                "name": "商业机会",
                "description": "与企业增长或盈利潜力相关的机会。",
                "keywords": ["市场扩张", "新客户", "收入增长", "business opportunity"],
                "importance": "high",
                "analysis_method": "SWOT分析"
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        "请你帮我分析这条新闻的商业相关的机会",
        "我需要从技术创新的角度来分析这个项目",
        "帮我识别市场竞争格局的分析维度",
        "从风险评估的角度来看这个投资机会"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 测试用例 {i} ===")
        print(f"输入: {input_text}")
        
        result = call_entity_extraction(input_text, opportunity_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            print(f"  维度名称: {data['name']}")
            print(f"  维度描述: {data['description']}")
            if "keywords" in data:
                print(f"  关键词: {', '.join(data['keywords'])}")
            if "importance" in data:
                print(f"  重要性: {data['importance']}")
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_opportunity_dimension()
```
### 2. 招
标关键词信息提取示例

#### 场景描述
从用户的查询需求中提取招标项目的关键信息，用于构建精确的搜索条件。

#### 完整示例代码

```python
def extract_bidding_key_info():
    """招标关键词信息提取示例"""
    
    # 定义招标关键词Schema
    bidding_schema = {
        "name": "BiddingKeyInfo",
        "description": "招标关键词信息模型，用于提取招标项目的关键信息",
        "type": "object",
        "properties": {
            "subject": {
                "type": "string",
                "description": "标题或主要采购内容"
            },
            "industry": {
                "type": "string",
                "description": "所属行业分类"
            },
            "country": {
                "type": "string",
                "description": "国家或地区"
            },
            "region": {
                "type": "string",
                "description": "具体地区或城市"
            },
            "publication_date_from": {
                "type": "string",
                "format": "date-time",
                "description": "发布日期起始时间"
            },
            "publication_date_to": {
                "type": "string",
                "format": "date-time",
                "description": "发布日期结束时间"
            },
            "submission_deadline_from": {
                "type": "string",
                "format": "date-time",
                "description": "投标截止起始时间"
            },
            "submission_deadline_to": {
                "type": "string",
                "format": "date-time",
                "description": "投标截止结束时间"
            },
            "estimated_value_min": {
                "type": "number",
                "description": "预算下限"
            },
            "estimated_value_max": {
                "type": "number",
                "description": "预算上限"
            },
            "currency": {
                "type": "string",
                "description": "币种"
            },
            "procurement_type": {
                "type": "string",
                "enum": ["物品采购", "服务采购", "工程采购", "其他"],
                "description": "采购类型"
            },
            "issuing_organization": {
                "type": "string",
                "description": "发布机构名称"
            },
            "keywords": {
                "type": "array",
                "items": {"type": "string"},
                "description": "相关关键词列表"
            }
        },
        "required": ["subject"],
        "examples": [
            {
                "subject": "低平板挂车",
                "industry": "交通运输设备",
                "country": "德国",
                "publication_date_from": "2025-05-01T00:00:00Z",
                "procurement_type": "物品采购",
                "currency": "EUR"
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        "我想找德国2025年5月后发布的低平板挂车采购项目",
        "寻找美国政府在2024年12月前截止的IT服务采购，预算100万美元以上",
        "查找中国建筑工程类招标，北京地区，金额500万人民币以下",
        "需要法国的医疗设备采购项目，2025年全年的",
        "找英国的清洁能源项目招标，风力发电相关"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 招标信息提取测试 {i} ===")
        print(f"输入: {input_text}")
        
        result = call_entity_extraction(input_text, bidding_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            print(f"  采购标题: {data['subject']}")
            
            # 显示其他提取的信息
            optional_fields = [
                ("industry", "行业"), ("country", "国家"), ("region", "地区"),
                ("publication_date_from", "发布日期起"), ("publication_date_to", "发布日期止"),
                ("submission_deadline_from", "截止日期起"), ("submission_deadline_to", "截止日期止"),
                ("estimated_value_min", "预算下限"), ("estimated_value_max", "预算上限"),
                ("currency", "币种"), ("procurement_type", "采购类型"),
                ("issuing_organization", "发布机构")
            ]
            
            for field, label in optional_fields:
                if field in data and data[field]:
                    print(f"  {label}: {data[field]}")
            
            if "keywords" in data and data["keywords"]:
                print(f"  关键词: {', '.join(data['keywords'])}")
                
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_bidding_key_info()
```

### 3. 分析角度操作提取示例

#### 场景描述
解析用户对分析维度的操作需求，支持新增、修改、删除等操作。

#### 完整示例代码

```python
def extract_direction_conditions():
    """分析角度操作提取示例"""
    
    # 定义分析角度操作Schema
    direction_schema = {
        "name": "DirectionConditionsList",
        "description": "分析角度操作列表模型，用于解析用户对分析维度的操作需求",
        "type": "object",
        "properties": {
            "conditions_list": {
                "type": "array",
                "description": "分析角度操作列表",
                "items": {
                    "type": "object",
                    "properties": {
                        "operator": {
                            "type": "string",
                            "enum": ["删除", "修改", "新增", "替换", "调整"],
                            "description": "操作类型"
                        },
                        "name": {
                            "type": "string",
                            "description": "维度名称"
                        },
                        "description": {
                            "type": "string",
                            "description": "维度描述或修改说明"
                        },
                        "priority": {
                            "type": "string",
                            "enum": ["high", "medium", "low"],
                            "description": "优先级"
                        },
                        "reason": {
                            "type": "string",
                            "description": "操作原因或说明"
                        }
                    },
                    "required": ["operator", "name", "description"]
                }
            }
        },
        "required": ["conditions_list"],
        "examples": [
            {
                "conditions_list": [
                    {
                        "operator": "新增",
                        "name": "竞争对手分析",
                        "description": "对主要竞争对手的市场表现、战略和优势进行分析。",
                        "priority": "high",
                        "reason": "需要了解竞争格局"
                    },
                    {
                        "operator": "删除",
                        "name": "摘要分析",
                        "description": "对新闻内容进行简要总结。",
                        "reason": "信息重复，不需要单独分析"
                    }
                ]
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        "增加一条竞争对手分析，减少摘要分析",
        "删除风险评估维度，新增机会识别分析",
        "修改市场分析的描述，调整为更详细的市场趋势分析",
        "替换技术分析为创新分析，并提高优先级",
        "新增财务分析和运营分析两个维度",
        "删除所有低优先级的分析维度"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 分析角度操作测试 {i} ===")
        print(f"输入: {input_text}")
        
        result = call_entity_extraction(input_text, direction_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            
            for j, condition in enumerate(data["conditions_list"], 1):
                print(f"  操作 {j}:")
                print(f"    类型: {condition['operator']}")
                print(f"    维度: {condition['name']}")
                print(f"    描述: {condition['description']}")
                if "priority" in condition:
                    print(f"    优先级: {condition['priority']}")
                if "reason" in condition:
                    print(f"    原因: {condition['reason']}")
                    
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_direction_conditions()
```

### 4. 查询条件提取示例

#### 场景描述
从用户的自然语言查询中提取结构化的查询条件，用于数据库查询。

#### 完整示例代码

```python
def extract_query_conditions():
    """查询条件提取示例"""
    
    # 定义查询条件Schema
    query_schema = {
        "name": "QueryConditionsList",
        "description": "查询条件列表模型，用于解析用户的查询需求",
        "type": "object",
        "properties": {
            "query_conditions_list": {
                "type": "array",
                "description": "查询条件列表",
                "items": {
                    "type": "object",
                    "properties": {
                        "query_fields": {
                            "type": "string",
                            "description": "需要查询的字段名",
                            "enum": [
                                "country", "region", "city", "update_at", "create_at",
                                "title", "content", "source", "category", "status",
                                "price", "amount", "currency", "date_range"
                            ]
                        },
                        "query_value": {
                            "type": "string",
                            "description": "查询字段的值"
                        },
                        "query_operate": {
                            "type": "string",
                            "description": "查询操作符",
                            "enum": ["eq", "ne", "gt", "gte", "lt", "lte", "like", "in", "between"]
                        },
                        "data_type": {
                            "type": "string",
                            "enum": ["string", "number", "date", "boolean"],
                            "description": "数据类型"
                        }
                    },
                    "required": ["query_fields", "query_value", "query_operate"]
                }
            },
            "logic_operator": {
                "type": "string",
                "enum": ["AND", "OR"],
                "default": "AND",
                "description": "条件间的逻辑关系"
            },
            "sort_by": {
                "type": "string",
                "description": "排序字段"
            },
            "sort_order": {
                "type": "string",
                "enum": ["asc", "desc"],
                "description": "排序方向"
            }
        },
        "required": ["query_conditions_list"],
        "examples": [
            {
                "query_conditions_list": [
                    {
                        "query_fields": "country",
                        "query_value": "china",
                        "query_operate": "eq",
                        "data_type": "string"
                    },
                    {
                        "query_fields": "update_at",
                        "query_value": "2025-7-18",
                        "query_operate": "gt",
                        "data_type": "date"
                    }
                ],
                "logic_operator": "AND",
                "sort_by": "update_at",
                "sort_order": "desc"
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        "请你帮我查找中国的大于7月18号的新闻",
        "找到价格在100到500之间的产品，按价格升序排列",
        "搜索标题包含'人工智能'或者'AI'的文章，最近一个月的",
        "查询美国和欧洲的科技类新闻，状态为已发布",
        "找出金额大于1万美元的交易记录，按时间倒序",
        "搜索北京、上海、深圳三个城市的房价信息"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 查询条件提取测试 {i} ===")
        print(f"输入: {input_text}")
        
        result = call_entity_extraction(input_text, query_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            
            print("  查询条件:")
            for j, condition in enumerate(data["query_conditions_list"], 1):
                field = condition['query_fields']
                value = condition['query_value']
                op = condition['query_operate']
                data_type = condition.get('data_type', 'string')
                print(f"    {j}. {field} {op} {value} ({data_type})")
            
            if "logic_operator" in data:
                print(f"  逻辑关系: {data['logic_operator']}")
            if "sort_by" in data:
                sort_order = data.get('sort_order', 'asc')
                print(f"  排序: {data['sort_by']} ({sort_order})")
                
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_query_conditions()
```

## 复杂嵌套结构处理示例

### 用户档案复杂结构提取

```python
def extract_complex_user_profile():
    """复杂用户档案提取示例"""
    
    # 定义复杂用户档案Schema
    complex_schema = {
        "name": "ComplexUserProfile",
        "description": "复杂用户档案模型，包含多层嵌套结构",
        "type": "object",
        "properties": {
            "user": {
                "type": "object",
                "description": "用户基本信息",
                "properties": {
                    "personal": {
                        "type": "object",
                        "description": "个人信息",
                        "properties": {
                            "name": {"type": "string", "description": "姓名"},
                            "age": {"type": "integer", "description": "年龄"},
                            "gender": {
                                "type": "string",
                                "enum": ["male", "female", "other"],
                                "description": "性别"
                            },
                            "birth_date": {
                                "type": "string",
                                "format": "date",
                                "description": "出生日期"
                            }
                        },
                        "required": ["name"]
                    },
                    "contact": {
                        "type": "object",
                        "description": "联系方式",
                        "properties": {
                            "email": {
                                "type": "string",
                                "format": "email",
                                "description": "邮箱地址"
                            },
                            "phone": {"type": "string", "description": "电话号码"},
                            "address": {
                                "type": "object",
                                "description": "地址信息",
                                "properties": {
                                    "country": {"type": "string", "description": "国家"},
                                    "city": {"type": "string", "description": "城市"},
                                    "district": {"type": "string", "description": "区域"},
                                    "street": {"type": "string", "description": "街道"},
                                    "postal_code": {"type": "string", "description": "邮编"}
                                }
                            },
                            "social_media": {
                                "type": "array",
                                "description": "社交媒体账号",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "platform": {"type": "string", "description": "平台名称"},
                                        "account": {"type": "string", "description": "账号"},
                                        "verified": {"type": "boolean", "description": "是否认证"}
                                    },
                                    "required": ["platform", "account"]
                                }
                            }
                        }
                    },
                    "professional": {
                        "type": "object",
                        "description": "职业信息",
                        "properties": {
                            "company": {"type": "string", "description": "公司名称"},
                            "position": {"type": "string", "description": "职位"},
                            "industry": {"type": "string", "description": "行业"},
                            "experience_years": {"type": "integer", "description": "工作年限"},
                            "skills": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "技能列表"
                            }
                        }
                    }
                },
                "required": ["personal", "contact"]
            },
            "preferences": {
                "type": "object",
                "description": "用户偏好",
                "properties": {
                    "language": {"type": "string", "description": "首选语言"},
                    "timezone": {"type": "string", "description": "时区"},
                    "interests": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "兴趣爱好"
                    }
                }
            },
            "metadata": {
                "type": "object",
                "description": "元数据",
                "properties": {
                    "created_at": {
                        "type": "string",
                        "format": "date-time",
                        "description": "创建时间"
                    },
                    "last_updated": {
                        "type": "string",
                        "format": "date-time",
                        "description": "最后更新时间"
                    },
                    "source": {"type": "string", "description": "数据来源"},
                    "confidence_score": {"type": "number", "description": "数据置信度"}
                }
            }
        },
        "required": ["user"],
        "examples": [
            {
                "user": {
                    "personal": {
                        "name": "张三",
                        "age": 28,
                        "gender": "male",
                        "birth_date": "1995-03-15"
                    },
                    "contact": {
                        "email": "<EMAIL>",
                        "phone": "13800138000",
                        "address": {
                            "country": "中国",
                            "city": "北京",
                            "district": "朝阳区"
                        }
                    },
                    "professional": {
                        "company": "科技公司",
                        "position": "软件工程师",
                        "industry": "信息技术",
                        "skills": ["Python", "Java", "机器学习"]
                    }
                }
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        """用户张三，28岁男性，1995年3月15日出生，邮箱zhang<EMAIL>，
        手机13800138000，住址北京市朝阳区，在科技公司担任软件工程师，
        有5年工作经验，擅长Python、Java和机器学习，微信账号zhangsan123已认证""",
        
        """李女士，35岁，联系邮箱li<EMAIL>，公司地址上海市浦东新区，
        担任产品经理职位，工作8年，技能包括产品设计、用户研究、数据分析，
        兴趣爱好包括阅读、旅行、摄影""",
        
        """王先生，42岁，北京人，从事金融行业15年，现任某银行风控总监，
        办公电话010-12345678，个人微博@wangfinance，LinkedIn账号wang-finance-director"""
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 复杂用户档案提取测试 {i} ===")
        print(f"输入: {input_text[:100]}...")
        
        result = call_entity_extraction(input_text, complex_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            
            # 显示用户基本信息
            user = data["user"]
            if "personal" in user:
                personal = user["personal"]
                print(f"  姓名: {personal.get('name', 'N/A')}")
                if "age" in personal:
                    print(f"  年龄: {personal['age']}")
                if "gender" in personal:
                    print(f"  性别: {personal['gender']}")
                if "birth_date" in personal:
                    print(f"  出生日期: {personal['birth_date']}")
            
            # 显示联系方式
            if "contact" in user:
                contact = user["contact"]
                if "email" in contact:
                    print(f"  邮箱: {contact['email']}")
                if "phone" in contact:
                    print(f"  电话: {contact['phone']}")
                if "address" in contact:
                    addr = contact["address"]
                    addr_str = ", ".join([v for v in [
                        addr.get("country"), addr.get("city"), 
                        addr.get("district"), addr.get("street")
                    ] if v])
                    if addr_str:
                        print(f"  地址: {addr_str}")
                if "social_media" in contact:
                    print("  社交媒体:")
                    for social in contact["social_media"]:
                        verified = " (已认证)" if social.get("verified") else ""
                        print(f"    {social['platform']}: {social['account']}{verified}")
            
            # 显示职业信息
            if "professional" in user:
                prof = user["professional"]
                if "company" in prof:
                    print(f"  公司: {prof['company']}")
                if "position" in prof:
                    print(f"  职位: {prof['position']}")
                if "industry" in prof:
                    print(f"  行业: {prof['industry']}")
                if "experience_years" in prof:
                    print(f"  工作年限: {prof['experience_years']}年")
                if "skills" in prof:
                    print(f"  技能: {', '.join(prof['skills'])}")
            
            # 显示偏好信息
            if "preferences" in data:
                pref = data["preferences"]
                if "interests" in pref:
                    print(f"  兴趣: {', '.join(pref['interests'])}")
                    
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_complex_user_profile()
```## 日期时间格式处理示
例

### 智能日期时间识别

```python
def extract_datetime_info():
    """日期时间格式处理示例"""
    
    # 定义日期时间Schema
    datetime_schema = {
        "name": "DateTimeInfo",
        "description": "日期时间信息模型，支持多种日期时间格式",
        "type": "object",
        "properties": {
            "event_name": {"type": "string", "description": "事件名称"},
            "start_date": {
                "type": "string",
                "format": "date-time",
                "description": "开始时间"
            },
            "end_date": {
                "type": "string",
                "format": "date-time",
                "description": "结束时间"
            },
            "deadline": {
                "type": "string",
                "format": "date-time",
                "description": "截止时间"
            },
            "duration": {"type": "string", "description": "持续时间"},
            "timezone": {"type": "string", "description": "时区"},
            "recurring": {
                "type": "object",
                "description": "重复规则",
                "properties": {
                    "frequency": {
                        "type": "string",
                        "enum": ["daily", "weekly", "monthly", "yearly"],
                        "description": "重复频率"
                    },
                    "interval": {"type": "integer", "description": "间隔"},
                    "end_after": {"type": "integer", "description": "重复次数"}
                }
            }
        },
        "required": ["event_name"],
        "examples": [
            {
                "event_name": "项目会议",
                "start_date": "2024-01-15T14:00:00Z",
                "end_date": "2024-01-15T16:00:00Z",
                "duration": "2小时",
                "timezone": "Asia/Shanghai"
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        "项目会议安排在2024年1月15日下午2点到4点",
        "培训课程从明天上午9:30开始，持续3小时",
        "截止日期是下周五晚上11:59分",
        "每周二下午3点的例会，持续1小时，共8次",
        "年度总结会议定于12月31日上午10点，北京时间",
        "产品发布会2024-03-20 14:00:00 UTC开始"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 日期时间提取测试 {i} ===")
        print(f"输入: {input_text}")
        
        result = call_entity_extraction(input_text, datetime_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            print(f"  事件: {data['event_name']}")
            
            if "start_date" in data:
                print(f"  开始时间: {data['start_date']}")
            if "end_date" in data:
                print(f"  结束时间: {data['end_date']}")
            if "deadline" in data:
                print(f"  截止时间: {data['deadline']}")
            if "duration" in data:
                print(f"  持续时间: {data['duration']}")
            if "timezone" in data:
                print(f"  时区: {data['timezone']}")
            if "recurring" in data:
                rec = data["recurring"]
                print(f"  重复规则: {rec.get('frequency', 'N/A')}")
                if "interval" in rec:
                    print(f"  间隔: {rec['interval']}")
                if "end_after" in rec:
                    print(f"  重复次数: {rec['end_after']}")
                    
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_datetime_info()
```

## 枚举类型约束处理示例

### 状态和分类管理

```python
def extract_status_classification():
    """枚举类型约束处理示例"""
    
    # 定义状态分类Schema
    status_schema = {
        "name": "StatusClassification",
        "description": "状态分类模型，包含多种枚举约束",
        "type": "object",
        "properties": {
            "item_name": {"type": "string", "description": "项目名称"},
            "status": {
                "type": "string",
                "enum": ["pending", "in_progress", "completed", "cancelled", "on_hold"],
                "description": "项目状态"
            },
            "priority": {
                "type": "string",
                "enum": ["low", "medium", "high", "urgent", "critical"],
                "description": "优先级"
            },
            "category": {
                "type": "string",
                "enum": ["development", "testing", "deployment", "maintenance", "research"],
                "description": "项目类别"
            },
            "risk_level": {
                "type": "string",
                "enum": ["minimal", "low", "medium", "high", "severe"],
                "description": "风险等级"
            },
            "approval_status": {
                "type": "string",
                "enum": ["draft", "submitted", "under_review", "approved", "rejected"],
                "description": "审批状态"
            },
            "team_size": {
                "type": "string",
                "enum": ["small", "medium", "large", "enterprise"],
                "description": "团队规模"
            },
            "budget_range": {
                "type": "string",
                "enum": ["under_10k", "10k_50k", "50k_100k", "100k_500k", "over_500k"],
                "description": "预算范围"
            }
        },
        "required": ["item_name", "status"],
        "examples": [
            {
                "item_name": "网站重构项目",
                "status": "in_progress",
                "priority": "high",
                "category": "development",
                "risk_level": "medium",
                "approval_status": "approved"
            }
        ]
    }
    
    # 测试用例
    test_cases = [
        "网站重构项目正在进行中，优先级很高，属于开发类别，风险中等，已获批准",
        "API测试任务已完成，低优先级，测试类别，风险最小",
        "数据库维护工作暂停，中等优先级，维护类别，高风险，审核中",
        "新功能研究项目取消，紧急优先级，研究类别，严重风险，被拒绝",
        "系统部署任务待处理，关键优先级，部署类别，低风险，草稿状态",
        "大型企业级项目进行中，预算超过50万，团队规模大"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 枚举约束处理测试 {i} ===")
        print(f"输入: {input_text}")
        
        result = call_entity_extraction(input_text, status_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print(f"✓ 提取成功 (置信度: {result['output']['confidence']:.2f})")
            print(f"  项目名称: {data['item_name']}")
            print(f"  状态: {data['status']}")
            
            # 显示其他枚举字段
            enum_fields = [
                ("priority", "优先级"), ("category", "类别"), ("risk_level", "风险等级"),
                ("approval_status", "审批状态"), ("team_size", "团队规模"), ("budget_range", "预算范围")
            ]
            
            for field, label in enum_fields:
                if field in data:
                    print(f"  {label}: {data[field]}")
                    
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"✗ 提取失败: {error}")

# 运行示例
extract_status_classification()
```

## 错误处理和降级策略示例

### 智能错误恢复

```python
def demonstrate_error_handling():
    """错误处理和降级策略示例"""
    
    # 定义一个要求较高的Schema
    strict_schema = {
        "name": "StrictProductInfo",
        "description": "严格的产品信息模型",
        "type": "object",
        "properties": {
            "name": {"type": "string", "description": "产品名称"},
            "price": {"type": "number", "description": "价格"},
            "currency": {"type": "string", "description": "货币"},
            "category": {
                "type": "string",
                "enum": ["electronics", "clothing", "books", "food", "sports"],
                "description": "产品类别"
            },
            "brand": {"type": "string", "description": "品牌"},
            "model": {"type": "string", "description": "型号"},
            "specifications": {
                "type": "object",
                "description": "规格参数",
                "properties": {
                    "weight": {"type": "number", "description": "重量"},
                    "dimensions": {"type": "string", "description": "尺寸"},
                    "color": {"type": "string", "description": "颜色"}
                }
            }
        },
        "required": ["name", "price", "currency", "category", "brand"]
    }
    
    # 定义降级Schema（要求较低）
    fallback_schema = {
        "name": "BasicProductInfo",
        "description": "基础产品信息模型",
        "type": "object",
        "properties": {
            "name": {"type": "string", "description": "产品名称"},
            "price": {"type": "number", "description": "价格"},
            "description": {"type": "string", "description": "产品描述"}
        },
        "required": ["name"]
    }
    
    def extract_with_fallback(input_text, primary_schema, fallback_schema):
        """带降级策略的提取"""
        print(f"尝试使用主Schema提取...")
        
        # 首先尝试主Schema
        result = call_entity_extraction(input_text, primary_schema)
        
        if result.get("success") and result["output"]["success"]:
            confidence = result["output"]["confidence"]
            if confidence >= 0.7:  # 置信度足够高
                print(f"✓ 主Schema提取成功 (置信度: {confidence:.2f})")
                return result
            else:
                print(f"⚠ 主Schema置信度较低 ({confidence:.2f})，尝试降级...")
        else:
            print(f"✗ 主Schema提取失败，尝试降级...")
        
        # 尝试降级Schema
        print(f"使用降级Schema提取...")
        fallback_result = call_entity_extraction(input_text, fallback_schema)
        
        if fallback_result.get("success") and fallback_result["output"]["success"]:
            confidence = fallback_result["output"]["confidence"]
            print(f"✓ 降级Schema提取成功 (置信度: {confidence:.2f})")
            return fallback_result
        else:
            print(f"✗ 降级Schema也失败")
            return fallback_result
    
    # 测试用例
    test_cases = [
        # 信息完整的情况
        "苹果iPhone 15 Pro，价格999美元，电子产品类别，重量200克，颜色深空黑",
        
        # 信息不完整的情况
        "一款手机，大概1000块钱左右",
        
        # 完全不相关的情况
        "今天天气很好，阳光明媚",
        
        # 部分信息匹配的情况
        "Nike运动鞋，红色，很舒适"
    ]
    
    for i, input_text in enumerate(test_cases, 1):
        print(f"\n=== 错误处理测试 {i} ===")
        print(f"输入: {input_text}")
        
        result = extract_with_fallback(input_text, strict_schema, fallback_schema)
        
        if result.get("success") and result["output"]["success"]:
            data = result["output"]["data"]
            print("提取结果:")
            for key, value in data.items():
                print(f"  {key}: {value}")
        else:
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"最终失败: {error}")

# 运行示例
demonstrate_error_handling()
```

## 批量处理示例

### 高效批量提取

```python
def batch_extraction_example():
    """批量处理示例"""
    
    # 定义新闻摘要Schema
    news_schema = {
        "name": "NewsDigest",
        "description": "新闻摘要模型",
        "type": "object",
        "properties": {
            "title": {"type": "string", "description": "新闻标题"},
            "summary": {"type": "string", "description": "新闻摘要"},
            "category": {
                "type": "string",
                "enum": ["technology", "business", "politics", "sports", "entertainment"],
                "description": "新闻类别"
            },
            "sentiment": {
                "type": "string",
                "enum": ["positive", "negative", "neutral"],
                "description": "情感倾向"
            },
            "keywords": {
                "type": "array",
                "items": {"type": "string"},
                "description": "关键词"
            }
        },
        "required": ["title", "summary"]
    }
    
    # 批量新闻数据
    news_texts = [
        "苹果公司今日发布了最新的iPhone 15系列，搭载全新A17芯片，性能提升显著，预计将推动公司股价上涨。",
        "中国足球队在世界杯预选赛中以2:1战胜对手，成功晋级下一轮，球迷们欣喜若狂。",
        "新的环保政策将于下月实施，要求所有企业减少碳排放30%，这将对制造业产生重大影响。",
        "著名演员张某因涉嫌税务问题被调查，其主演的多部电影可能面临下架风险。",
        "人工智能技术在医疗领域取得突破，新算法能够提前诊断癌症，准确率达到95%以上。"
    ]
    
    def process_batch(texts, schema, batch_size=3):
        """批量处理函数"""
        results = []
        total = len(texts)
        
        for i in range(0, total, batch_size):
            batch = texts[i:i+batch_size]
            print(f"\n处理批次 {i//batch_size + 1} ({len(batch)} 条新闻)...")
            
            batch_results = []
            for j, text in enumerate(batch):
                print(f"  处理第 {i+j+1} 条新闻...")
                result = call_entity_extraction(text, schema)
                batch_results.append({
                    "index": i+j,
                    "text": text[:50] + "...",
                    "result": result
                })
            
            results.extend(batch_results)
            
            # 简单的批次间延迟，避免过于频繁的请求
            if i + batch_size < total:
                import time
                time.sleep(1)
        
        return results
    
    # 执行批量处理
    print("开始批量新闻摘要提取...")
    batch_results = process_batch(news_texts, news_schema)
    
    # 统计结果
    successful = 0
    failed = 0
    total_confidence = 0
    
    print(f"\n=== 批量处理结果 ===")
    for item in batch_results:
        result = item["result"]
        index = item["index"]
        
        if result.get("success") and result["output"]["success"]:
            successful += 1
            confidence = result["output"]["confidence"]
            total_confidence += confidence
            data = result["output"]["data"]
            
            print(f"\n✓ 新闻 {index+1} 处理成功 (置信度: {confidence:.2f})")
            print(f"  标题: {data['title']}")
            print(f"  摘要: {data['summary']}")
            if "category" in data:
                print(f"  类别: {data['category']}")
            if "sentiment" in data:
                print(f"  情感: {data['sentiment']}")
            if "keywords" in data:
                print(f"  关键词: {', '.join(data['keywords'])}")
        else:
            failed += 1
            error = result.get("output", {}).get("error", result.get("error", "未知错误"))
            print(f"\n✗ 新闻 {index+1} 处理失败: {error}")
    
    # 输出统计信息
    print(f"\n=== 批量处理统计 ===")
    print(f"总数: {len(batch_results)}")
    print(f"成功: {successful}")
    print(f"失败: {failed}")
    print(f"成功率: {successful/len(batch_results)*100:.1f}%")
    if successful > 0:
        print(f"平均置信度: {total_confidence/successful:.2f}")

# 运行示例
batch_extraction_example()
```

## 性能优化最佳实践

### 1. Schema优化建议

```python
def schema_optimization_tips():
    """Schema优化建议"""
    
    print("=== Schema优化最佳实践 ===\n")
    
    # 好的Schema示例
    good_schema = {
        "name": "OptimizedProductInfo",
        "description": "优化的产品信息模型 - 字段清晰、层级合理、示例丰富",
        "type": "object",
        "properties": {
            "name": {
                "type": "string",
                "description": "产品名称，应包含品牌和型号信息"
            },
            "price": {
                "type": "number",
                "description": "产品价格，数值类型，不包含货币符号"
            },
            "currency": {
                "type": "string",
                "enum": ["USD", "EUR", "CNY", "JPY"],
                "description": "货币类型，使用ISO 4217标准代码"
            },
            "category": {
                "type": "string",
                "enum": ["electronics", "clothing", "books", "food"],
                "description": "产品类别，限定在主要分类中"
            }
        },
        "required": ["name", "price"],  # 只设置真正必需的字段
        "examples": [
            {
                "name": "iPhone 15 Pro",
                "price": 999,
                "currency": "USD",
                "category": "electronics"
            },
            {
                "name": "Nike Air Max",
                "price": 120,
                "currency": "USD", 
                "category": "clothing"
            }
        ]
    }
    
    # 不好的Schema示例
    bad_schema = {
        "name": "BadProductInfo",
        "description": "不好的产品信息模型",  # 描述不够详细
        "type": "object",
        "properties": {
            "n": {  # 字段名不清晰
                "type": "string",
                "description": "名称"  # 描述过于简单
            },
            "p": {
                "type": "string",  # 类型不正确，价格应该是number
                "description": "价格"
            },
            "details": {
                "type": "object",
                "properties": {
                    "specs": {
                        "type": "object",
                        "properties": {
                            "tech": {
                                "type": "object",
                                "properties": {
                                    "cpu": {"type": "string"},
                                    "ram": {"type": "string"}
                                }
                            }
                        }
                    }
                }
            }  # 嵌套过深
        },
        "required": ["n", "p", "details"]  # 过多必填字段
        # 缺少examples
    }
    
    print("✓ 好的Schema特点:")
    print("  - 字段名称清晰明确")
    print("  - 详细的字段描述")
    print("  - 合适的数据类型")
    print("  - 合理的枚举约束")
    print("  - 适量的必填字段")
    print("  - 丰富的示例数据")
    print("  - 避免过深嵌套")
    
    print("\n✗ 避免的Schema问题:")
    print("  - 模糊的字段名称")
    print("  - 简单的描述信息")
    print("  - 错误的数据类型")
    print("  - 过多的必填字段")
    print("  - 缺少示例数据")
    print("  - 过深的嵌套结构")

# 运行示例
schema_optimization_tips()
```

### 2. 输入文本优化

```python
def input_text_optimization():
    """输入文本优化建议"""
    
    print("=== 输入文本优化最佳实践 ===\n")
    
    # 定义测试Schema
    test_schema = {
        "name": "EventInfo",
        "description": "事件信息模型",
        "type": "object",
        "properties": {
            "event_name": {"type": "string", "description": "事件名称"},
            "date": {"type": "string", "format": "date-time", "description": "事件日期"},
            "location": {"type": "string", "description": "事件地点"},
            "participants": {"type": "integer", "description": "参与人数"}
        },
        "required": ["event_name"]
    }
    
    # 好的输入文本示例
    good_inputs = [
        "技术分享会将于2024年1月15日下午2点在北京会议室举行，预计50人参加",
        "年度总结大会定于12月31日上午10点，地点在公司大礼堂，全体员工参与",
        "产品发布会安排在3月20日，上海国际会展中心，邀请200位嘉宾"
    ]
    
    # 不好的输入文本示例
    bad_inputs = [
        "那个会议",  # 信息不足
        "明天有个很重要的事情要做，大家都要来，地点你们知道的",  # 模糊不清
        "会议会议会议会议会议会议会议会议会议会议",  # 重复无意义
        "Meeting tomorrow at the usual place with the team"  # 语言不匹配
    ]
    
    print("✓ 好的输入文本特点:")
    for i, text in enumerate(good_inputs, 1):
        print(f"  {i}. {text}")
        result = call_entity_extraction(text, test_schema)
        if result.get("success") and result["output"]["success"]:
            confidence = result["output"]["confidence"]
            print(f"     → 提取成功，置信度: {confidence:.2f}")
        else:
            print(f"     → 提取失败")
    
    print(f"\n✗ 避免的输入文本问题:")
    for i, text in enumerate(bad_inputs, 1):
        print(f"  {i}. {text}")
        result = call_entity_extraction(text, test_schema)
        if result.get("success") and result["output"]["success"]:
            confidence = result["output"]["confidence"]
            print(f"     → 提取成功，但置信度较低: {confidence:.2f}")
        else:
            error = result.get("output", {}).get("error", "未知错误")
            print(f"     → 提取失败: {error}")
    
    print(f"\n输入文本优化建议:")
    print("  - 提供完整、具体的信息")
    print("  - 使用清晰、结构化的语言")
    print("  - 避免模糊或歧义的表达")
    print("  - 确保语言与配置匹配")
    print("  - 包含目标模型需要的关键信息")
    print("  - 适当的文本长度（不过长不过短）")

# 运行示例
input_text_optimization()
```

### 3. 配置调优建议

```python
def configuration_tuning():
    """配置调优建议"""
    
    print("=== 配置调优最佳实践 ===\n")
    
    # 测试不同配置的效果
    test_schema = {
        "name": "SimpleTest",
        "description": "简单测试模型",
        "type": "object",
        "properties": {
            "content": {"type": "string", "description": "内容"}
        },
        "required": ["content"]
    }
    
    test_text = "这是一个测试内容"
    
    # 不同置信度阈值的测试
    confidence_thresholds = [0.5, 0.7, 0.9]
    
    print("置信度阈值对比:")
    for threshold in confidence_thresholds:
        config = {
            "confidence_threshold": threshold,
            "max_retries": 3,
            "timeout": 30
        }
        
        input_data = {
            "input_text": test_text,
            "target_schema": test_schema,
            "extraction_config": config
        }
        
        result = call_entity_extraction(test_text, test_schema)
        
        if result.get("success") and result["output"]["success"]:
            actual_confidence = result["output"]["confidence"]
            print(f"  阈值 {threshold}: ✓ 成功 (实际置信度: {actual_confidence:.2f})")
        else:
            print(f"  阈值 {threshold}: ✗ 失败或置信度不足")
    
    print(f"\n配置调优建议:")
    print("  置信度阈值:")
    print("    - 0.5-0.6: 宽松模式，适合探索性提取")
    print("    - 0.7-0.8: 平衡模式，适合一般业务场景")
    print("    - 0.9+: 严格模式，适合高精度要求")
    
    print("  重试次数:")
    print("    - 1-2次: 快速响应场景")
    print("    - 3-5次: 一般业务场景")
    print("    - 5+次: 高可靠性要求")
    
    print("  超时时间:")
    print("    - 10-20秒: 简单文本和Schema")
    print("    - 30-60秒: 复杂文本或Schema")
    print("    - 60+秒: 极复杂场景或网络较慢")

# 运行示例
configuration_tuning()
```

## 总结

本文档提供了实体提取Agent的全面使用示例和最佳实践，涵盖了：

1. **基础使用**: 快速上手和基本调用方法
2. **业务场景**: 四种主要业务场景的详细示例
3. **复杂结构**: 嵌套对象、数组、日期时间等复杂数据类型处理
4. **错误处理**: 智能错误恢复和降级策略
5. **批量处理**: 高效的批量数据处理方法
6. **性能优化**: Schema设计、输入优化、配置调优等最佳实践

通过这些示例和建议，开发者可以：
- 快速集成实体提取Agent到自己的应用中
- 设计高效的Schema和输入文本
- 处理各种复杂的业务场景
- 优化性能和准确率
- 实现稳定可靠的生产环境部署

建议在实际使用中根据具体业务需求调整Schema设计和配置参数，并通过测试验证效果。