#!/bin/bash

# This script builds and deploys the taskd-bvt-runner to the k3s cluster.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# Your private registry URL
REGISTRY_URL="**************"
# The repository/project name in your registry
REPO_NAME="specific-ai"
# The image name for the BVT runner
IMAGE_NAME="taskd-bvt-runner"
# The image tag
IMAGE_TAG="latest"
# Full image path
FULL_IMAGE_NAME="${REGISTRY_URL}/${REPO_NAME}/${IMAGE_NAME}:${IMAGE_TAG}"
# Kubernetes namespace
NAMESPACE="ovs"
# Path to the Kubernetes configuration files, assuming this script is run from the project root.
K8S_CONFIG_PATH="./k8s/test_bvt_taskd"

# --- Main Script ---

echo "Starting BVT runner deployment..."
echo "-----------------------------------"

# 1. Build the Docker image
# The build context is the 'taskd' directory from the project root.
echo "Building Docker image: ${FULL_IMAGE_NAME}..."
docker build -t "${FULL_IMAGE_NAME}" -f "./taskd/${K8S_CONFIG_PATH}/Dockerfile" ./taskd

# 2. Push the Docker image to the private registry
echo "Pushing image to private registry at ${REGISTRY_URL}..."
docker push "${FULL_IMAGE_NAME}"

# 3. Deploy to Kubernetes
echo "Deploying resources to k3s cluster in namespace '${NAMESPACE}'..."

# Apply the ConfigMap first
echo "Applying ConfigMap..."
kubectl apply -f "./taskd/${K8S_CONFIG_PATH}/configmap.yaml"

# Delete any existing job to ensure a fresh run
echo "Deleting old job if it exists..."
kubectl delete job taskd-bvt-runner-job -n "${NAMESPACE}" --ignore-not-found=true

# Apply the Job to start the tests
echo "Applying new Job..."
kubectl apply -f "./taskd/${K8S_CONFIG_PATH}/job.yaml"

echo "-----------------------------------"
echo "✅ Deployment triggered successfully."
echo
echo "--- What to do next ---"
echo "1. Wait for the test pod to be created and running. You can watch its status with:"
echo "   kubectl get pods -n ${NAMESPACE} -l job-name=taskd-bvt-runner-job -w"
echo
echo "2. Once the pod is running or completed, view the test execution logs:"
echo "   POD_NAME=\$(kubectl get pods --namespace ${NAMESPACE} -l job-name=taskd-bvt-runner-job -o jsonpath='{.items[0].metadata.name}')"
echo "   echo \"Fetching logs for pod: \$POD_NAME\""
echo "   kubectl logs -f \$POD_NAME --namespace ${NAMESPACE}"
echo
echo "3. After you've reviewed the logs, you can clean up the test resources:"
echo "   kubectl delete job taskd-bvt-runner-job --namespace ${NAMESPACE}"
echo "   kubectl delete configmap taskd-bvt-runner-config --namespace ${NAMESPACE}"
echo "--------------------------" 