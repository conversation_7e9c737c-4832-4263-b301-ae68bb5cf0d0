apiVersion: v1
kind: ConfigMap
metadata:
  name: taskd-config
  namespace: ovs
  labels:
    app: taskd
    app.kubernetes.io/name: taskd
    app.kubernetes.io/component: service
data:
  # 服务器配置
  SERVER_PORT: "8601"
  SERVER_MODE: "release"
  
  # 日志配置
  LOGGER_LEVEL: "warn"
  
  # MongoDB配置（非敏感信息）
  MONGODB_DATABASE: "taskd_prod_db"
  MONGODB_TIMEOUT_SECONDS: "15"
  
  # PostgreSQL配置（非敏感信息）- 参考backend项目配置
  POSTGRESQL_HOST: "pg-server-prod-service.pg.svc.cluster.local"
  POSTGRESQL_PORT: "5432"
  POSTGRESQL_DATABASE: "overseas"
  POSTGRESQL_SSL_MODE: "disable"
  POSTGRESQL_TIMEOUT_SECONDS: "10"
  POSTGRESQL_MAX_OPEN_CONNS: "25"
  POSTGRESQL_MAX_IDLE_CONNS: "5"
  POSTGRESQL_CONN_MAX_LIFETIME: "300"
  
  # Pulsar配置（非敏感信息）
  PULSAR_CONSUMER_TOPIC_REPORT_SUMMARY: "persistent://public/default/taskd-prod-report-summary-requests"
  PULSAR_CONSUMER_SUBSCRIPTION_NAME: "taskd-prod-summary-sub"
  
  # LLM配置（非敏感信息）
  LLM_DEFAULT_PROVIDER: "volcengine_ark"
  LLM_PROVIDERS_VOLCENGINE_ARK_BASE_URL: "https://ark.cn-beijing.volces.com/api/v3"
  LLM_PROVIDERS_VOLCENGINE_ARK_DEFAULT_MODEL_ALIAS: "report-summarizer"
  LLM_PROVIDERS_VOLCENGINE_ARK_REQUEST_TIMEOUT_SECONDS: "90"
  LLM_PROVIDERS_VOLCENGINE_ARK_MAX_CONCURRENT_REQUESTS: "20"
  
  # ETCD配置中心配置
  ETCD_ENABLED: "true"
  ETCD_ENDPOINTS: "etcd-service.etcd.svc.cluster.local:2379"
  ETCD_DIAL_TIMEOUT_SEC: "5"
  ETCD_REQUEST_TIMEOUT_SEC: "10"
  ETCD_CONFIG_PREFIX: "/config/taskd" 