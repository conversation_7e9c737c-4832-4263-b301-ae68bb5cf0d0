package setup

import (
	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/services/infrastructure"
)

// ToolsModule 包含了工具类相关的服务和处理器
type ToolsModule struct {
	RelevanceService *infrastructure.RelevanceService
	ToolsHandler     *handlers.ToolsHandler
}

// NewToolsModule 初始化工具类相关的服务和处理器
func NewToolsModule(deps AppDependencies) (*ToolsModule, error) {
	// TODO: 实现并替换 MockWordEmbedder
	mockWordEmbedder := infrastructure.NewMockWordEmbedder(100)
	sentenceEmbedder := infrastructure.NewAverageWordEmbeddingsSentenceEmbedder(mockWordEmbedder)
	relevanceSvc := infrastructure.NewRelevanceService(sentenceEmbedder)

	toolsHandler := handlers.NewToolsHandler(relevanceSvc)

	return &ToolsModule{
		RelevanceService: relevanceSvc,
		ToolsHandler:     toolsHandler,
	}, nil
}
