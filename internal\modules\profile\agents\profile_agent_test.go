package agents

import (
	"testing"

	"github.com/sirupsen/logrus"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	profileModels "gitlab.com/specific-ai/taskd/internal/modules/profile/models"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

func init() {
	// 初始化日志，避免测试中的空指针错误
	utils.Log = logrus.New()
	utils.Log.SetLevel(logrus.InfoLevel)
}

// MockProfileService 模拟ProfileService用于测试
type MockProfileService struct{}

func (m *MockProfileService) GenerateCompanyProfile(req *profileModels.ProfileRequest) (*profileModels.ProfileResponse, error) {
	// 返回模拟的企业画像响应
	return &profileModels.ProfileResponse{
		UserID:      req.UserContext.UserID,
		CompanyName: req.CompanyName,
		BusinessCapabilities: &profileModels.BusinessCapabilities{
			CoreBusinessAreas:   []string{"技术开发", "产品设计"},
			ProductServiceTypes: []string{"软件开发", "技术咨询"},
			BusinessScale:       "中型企业",
		},
		TenderMatching: &profileModels.TenderMatching{
			ProjectTypes:       []string{"技术服务", "软件开发"},
			ProjectScale:       []string{"中型项目", "大型项目"},
			GeographicCoverage: []string{"北京", "全国"},
			BiddingStrategy:    "技术优势导向",
			HistoricalWinRate:  0.85,
		},
		CompetitiveProfile: &profileModels.CompetitiveProfile{
			CoreAdvantages:       []string{"技术实力", "创新能力"},
			Differentiators:      []string{"快速交付", "定制化服务"},
			MarketPosition:       "中等",
			PriceCompetitiveness: "中等",
			MarketInfluence:      "区域性影响力",
		},
		InternationalCapabilities: &profileModels.InternationalCapabilities{
			CrossBorderExperience:       "有限",
			LanguageCapabilities:        []string{"中文", "英文"},
			CulturalAdaptability:        "中等",
			InternationalCertifications: []string{},
		},
		RiskTolerance: &profileModels.RiskTolerance{
			PolicyRiskSensitivity:  "中等",
			ExchangeRateRisk:       "低",
			ProjectCyclePreference: []string{"短期项目", "中期项目"},
			RiskControlMechanisms:  []string{"合同保障", "技术评估"},
			InsuranceCoverage:      []string{"项目保险"},
		},
		ConfidenceScore: 85,
		Metadata: &profileModels.ProfileMetadata{
			ProcessingTime:  2500, // 毫秒
			ModelUsed:       "test-model-v1.0",
			StrategyVersion: "v1.0",
			DataSources:     []string{"mock_data"},
		},
	}, nil
}

func TestProfileAgent_Creation(t *testing.T) {
	// 创建模拟服务
	mockService := &MockProfileService{}

	// 创建ProfileAgent
	agent := NewProfileAgent(mockService)

	// 验证Agent基本信息
	if agent.GetID() != AgentID {
		t.Errorf("Expected agent ID %s, got %s", AgentID, agent.GetID())
	}

	// 验证AgentCard
	agentCard := agent.GetAgentCard()
	if agentCard.Name != AgentName {
		t.Errorf("Expected agent name %s, got %s", AgentName, agentCard.Name)
	}

	if len(agentCard.Skills) != 1 {
		t.Errorf("Expected 1 skill, got %d", len(agentCard.Skills))
	}

	if agentCard.Skills[0].ID != SkillGenerateCompanyProfile {
		t.Errorf("Expected skill ID %s, got %s", SkillGenerateCompanyProfile, agentCard.Skills[0].ID)
	}
}

func TestProfileAgent_ExecuteSkill(t *testing.T) {
	// 创建模拟服务
	mockService := &MockProfileService{}

	// 创建ProfileAgent
	agent := NewProfileAgent(mockService)

	// 准备测试输入
	input := map[string]interface{}{
		"company_name":     "测试公司",
		"industry":         "软件开发",
		"company_scale":    "中型企业",
		"business_scope":   "提供软件开发和技术咨询服务",
		"location":         "北京",
		"established_year": 2020,
		"description":      "一家专注于企业级软件开发的公司",
		"language":         "chinese",
		"user_context": map[string]interface{}{
			"user_id":    "test_user_123",
			"company_id": "test_company_456",
		},
	}

	// 准备A2A上下文
	context := biddingModels.A2AContext{
		RequestID: "test_request_123",
		UserID:    "test_user_123",
	}

	// 执行技能
	result, err := agent.ExecuteSkill(SkillGenerateCompanyProfile, input, context)

	// 验证结果
	if err != nil {
		t.Fatalf("ExecuteSkill failed: %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Status != "completed" {
		t.Errorf("Expected status 'completed', got %s", result.Status)
	}

	// 验证输出包含必要字段
	output := result.Output

	if companyName, exists := output["company_name"]; !exists || companyName != "测试公司" {
		t.Errorf("Expected company_name '测试公司', got %v", companyName)
	}

	if userID, exists := output["user_id"]; !exists || userID != "test_user_123" {
		t.Errorf("Expected user_id 'test_user_123', got %v", userID)
	}
}

func TestProfileAgent_ExecuteSkill_InvalidSkill(t *testing.T) {
	// 创建模拟服务
	mockService := &MockProfileService{}

	// 创建ProfileAgent
	agent := NewProfileAgent(mockService)

	// 准备测试输入
	input := map[string]interface{}{
		"company_name": "测试公司",
	}

	// 准备A2A上下文
	context := biddingModels.A2AContext{
		RequestID: "test_request_123",
		UserID:    "test_user_123",
	}

	// 执行无效技能
	result, err := agent.ExecuteSkill("invalid_skill", input, context)

	// 验证错误
	if err == nil {
		t.Fatal("Expected error for invalid skill, got nil")
	}

	if result != nil {
		t.Errorf("Expected nil result for invalid skill, got %v", result)
	}
}

func TestProfileAgent_HealthCheck(t *testing.T) {
	// 创建模拟服务
	mockService := &MockProfileService{}

	// 创建ProfileAgent
	agent := NewProfileAgent(mockService)

	// 执行健康检查
	healthStatus := agent.HealthCheck()

	// 验证健康状态
	if healthStatus.Status != "healthy" {
		t.Errorf("Expected status 'healthy', got %s", healthStatus.Status)
	}

	if healthStatus.Version == "" {
		t.Error("Expected version to be set")
	}
}

// 确保MockProfileService实现了ProfileServiceInterface
var _ services.ProfileServiceInterface = (*MockProfileService)(nil)
