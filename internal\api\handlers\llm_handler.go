// 各类原子能力的接口实现层
package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

type LLMHandler struct {
	reportSummaryService services.ReportSummaryService
	sentimentService     services.SentimentService
}

func NewLLMHandler(rsService services.ReportSummaryService, sentimentService services.SentimentService) *LLMHandler {
	return &LLMHandler{
		reportSummaryService: rsService,
		sentimentService:     sentimentService,
	}
}

// HandleSummaryReport 处理报告总结的 API 请求
// @Summary 总结报告
// @Description 接收报告文本和其他参数以生成摘要
// @Tags LLM
// @Accept  json
// @Produce  json
// @Param   reportRequest body models.ReportSummaryRequest true "报告总结请求"
// @Success 200 {object} models.ReportSummaryResponse
// @Failure 400 {object} models.StandardErrorResponse "请求参数错误"
// @Failure 500 {object} models.StandardErrorResponse "服务器内部错误"
// @Router /llms/v1/summary_report [post]
func (h *LLMHandler) HandleSummaryReport(c *gin.Context) {
	var req models.ReportSummaryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Warnf("绑定报告总结请求 JSON 失败: %v", err)
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{Error: "无效的请求负载: " + err.Error()})
		return
	}

	resp, err := h.reportSummaryService.Summarize(req)
	if err != nil {
		utils.Log.Errorf("总结报告时出错: %v", err)
		c.JSON(http.StatusInternalServerError, models.StandardErrorResponse{Error: "总结报告失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// HandleSentimentAnalysis 处理情感分析的 API 请求
// @Summary 分析文本情感
// @Description 接收包含多个待分析文本的字符串，进行情感分析。
// @Tags LLM
// @Accept  json
// @Produce  json
// @Param   sentimentRequest body models.SentimentAnalysisRequest true "情感分析请求"
// @Success 200 {object} models.SentimentAnalysisResponse
// @Failure 400 {object} models.StandardErrorResponse "请求参数错误"
// @Failure 500 {object} models.StandardErrorResponse "服务器内部错误"
// @Router /llms/v1/sentiment_analysis [post]
func (h *LLMHandler) HandleSentimentAnalysis(c *gin.Context) {
	var req models.SentimentAnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Warnf("绑定情感分析请求 JSON 失败: %v", err)
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{Error: "无效的请求负载: " + err.Error()})
		return
	}

	resp, err := h.sentimentService.AnalyzeSentiment(req) // 调用 sentimentService
	if err != nil {
		utils.Log.Errorf("情感分析时出错: %v", err)
		c.JSON(http.StatusInternalServerError, models.StandardErrorResponse{Error: "情感分析失败: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// HealthCheck 健康检查接口
// @Summary 健康检查
// @Description 返回服务状态
// @Tags Health
// @Produce json
// @Success 200 {object} map[string]string
// @Router /healthz [get]
func HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}
