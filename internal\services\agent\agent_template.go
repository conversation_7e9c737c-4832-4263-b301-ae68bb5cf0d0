package agent

import (
	"context"
	"fmt"
	"text/template"
	"bytes"
	"time"

	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"github.com/sirupsen/logrus"
)

// AgentTemplateImpl Agent模板服务实现
type AgentTemplateImpl struct {
	store  store.Store
	logger *logrus.Logger
}

// NewAgentTemplate 创建Agent模板服务
func NewAgentTemplate(store store.Store) core.AgentTemplate {
	return &AgentTemplateImpl{
		store:  store,
		logger: utils.Log, // 使用统一的logger
	}
}

// GetTemplate 获取模板
func (at *AgentTemplateImpl) GetTemplate(ctx context.Context, templateID string) (*models.AgentTemplate, error) {
	// 从数据库获取模板
	template, err := at.loadTemplate(ctx, templateID)
	if err != nil {
		return nil, fmt.Errorf("failed to load template: %w", err)
	}
	
	return template, nil
}

// CreateTemplate 创建模板
func (at *AgentTemplateImpl) CreateTemplate(ctx context.Context, agentTemplate *models.AgentTemplate) error {
	// 验证模板
	if err := at.validateTemplate(agentTemplate); err != nil {
		return fmt.Errorf("invalid template: %w", err)
	}
	
	// 设置创建时间
	agentTemplate.CreatedAt = time.Now()
	agentTemplate.UpdatedAt = time.Now()
	
	// 保存到数据库
	if err := at.saveTemplate(ctx, agentTemplate); err != nil {
		return fmt.Errorf("failed to save template: %w", err)
	}
	
	at.logger.WithFields(logrus.Fields{
		"template_id": agentTemplate.ID,
		"name":        agentTemplate.Name,
		"type":        agentTemplate.Type,
	}).Info("Agent模板创建成功")
	
	return nil
}

// UpdateTemplate 更新模板
func (at *AgentTemplateImpl) UpdateTemplate(ctx context.Context, agentTemplate *models.AgentTemplate) error {
	// 检查模板是否存在
	existing, err := at.GetTemplate(ctx, agentTemplate.ID)
	if err != nil {
		return fmt.Errorf("template not found: %w", err)
	}
	
	// 验证模板
	if err := at.validateTemplate(agentTemplate); err != nil {
		return fmt.Errorf("invalid template: %w", err)
	}
	
	// 保留创建时间，更新修改时间
	agentTemplate.CreatedAt = existing.CreatedAt
	agentTemplate.UpdatedAt = time.Now()
	
	// 保存到数据库
	if err := at.saveTemplate(ctx, agentTemplate); err != nil {
		return fmt.Errorf("failed to update template: %w", err)
	}
	
	at.logger.WithFields(logrus.Fields{
		"template_id": agentTemplate.ID,
		"name":        agentTemplate.Name,
		"type":        agentTemplate.Type,
	}).Info("Agent模板更新成功")
	
	return nil
}

// DeleteTemplate 删除模板
func (at *AgentTemplateImpl) DeleteTemplate(ctx context.Context, templateID string) error {
	// 检查模板是否存在
	if _, err := at.GetTemplate(ctx, templateID); err != nil {
		return fmt.Errorf("template not found: %w", err)
	}
	
	// 从数据库删除
	if err := at.deleteTemplate(ctx, templateID); err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}
	
	at.logger.WithField("template_id", templateID).Info("Agent模板删除成功")
	
	return nil
}

// ListTemplates 列出模板
func (at *AgentTemplateImpl) ListTemplates(ctx context.Context, agentType models.AgentType) ([]models.AgentTemplate, error) {
	templates, err := at.loadTemplates(ctx, agentType)
	if err != nil {
		return nil, fmt.Errorf("failed to load templates: %w", err)
	}
	
	return templates, nil
}

// RenderPrompt 渲染提示词
func (at *AgentTemplateImpl) RenderPrompt(ctx context.Context, templateID string, params map[string]interface{}) (string, string, error) {
	// 获取模板
	agentTemplate, err := at.GetTemplate(ctx, templateID)
	if err != nil {
		return "", "", fmt.Errorf("failed to get template: %w", err)
	}
	
	// 渲染系统提示词
	systemPrompt, err := at.renderTemplateString(agentTemplate.SystemPrompt, params)
	if err != nil {
		return "", "", fmt.Errorf("failed to render system prompt: %w", err)
	}
	
	// 渲染用户提示词
	userPrompt, err := at.renderTemplateString(agentTemplate.UserPromptTemplate, params)
	if err != nil {
		return "", "", fmt.Errorf("failed to render user prompt: %w", err)
	}
	
	return systemPrompt, userPrompt, nil
}

// renderTemplateString 渲染模板字符串
func (at *AgentTemplateImpl) renderTemplateString(templateStr string, params map[string]interface{}) (string, error) {
	// 创建模板
	tmpl, err := template.New("prompt").Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}
	
	// 渲染模板
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, params); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}
	
	return buf.String(), nil
}

// validateTemplate 验证模板
func (at *AgentTemplateImpl) validateTemplate(agentTemplate *models.AgentTemplate) error {
	if agentTemplate.ID == "" {
		return fmt.Errorf("template ID is required")
	}
	
	if agentTemplate.Name == "" {
		return fmt.Errorf("template name is required")
	}
	
	if agentTemplate.Type == "" {
		return fmt.Errorf("template type is required")
	}
	
	if agentTemplate.SystemPrompt == "" {
		return fmt.Errorf("system prompt is required")
	}
	
	if agentTemplate.UserPromptTemplate == "" {
		return fmt.Errorf("user prompt template is required")
	}
	
	// 验证模板语法
	if _, err := template.New("system").Parse(agentTemplate.SystemPrompt); err != nil {
		return fmt.Errorf("invalid system prompt template: %w", err)
	}
	
	if _, err := template.New("user").Parse(agentTemplate.UserPromptTemplate); err != nil {
		return fmt.Errorf("invalid user prompt template: %w", err)
	}
	
	return nil
}

// loadTemplate 从数据库加载模板
func (at *AgentTemplateImpl) loadTemplate(ctx context.Context, templateID string) (*models.AgentTemplate, error) {
	// 简化实现，返回预定义模板
	return at.getBuiltinTemplate(templateID)
}

// saveTemplate 保存模板到数据库
func (at *AgentTemplateImpl) saveTemplate(ctx context.Context, agentTemplate *models.AgentTemplate) error {
	// 简化实现，暂时不保存到数据库
	return nil
}

// deleteTemplate 从数据库删除模板
func (at *AgentTemplateImpl) deleteTemplate(ctx context.Context, templateID string) error {
	// 简化实现，暂时不从数据库删除
	return nil
}

// loadTemplates 从数据库加载模板列表
func (at *AgentTemplateImpl) loadTemplates(ctx context.Context, agentType models.AgentType) ([]models.AgentTemplate, error) {
	// 简化实现，返回预定义模板列表
	return at.getBuiltinTemplates(agentType), nil
}

// getBuiltinTemplate 获取内置模板
func (at *AgentTemplateImpl) getBuiltinTemplate(templateID string) (*models.AgentTemplate, error) {
	templates := map[string]*models.AgentTemplate{
		"bidding_summary": {
			ID:          "bidding_summary",
			Name:        "招投标摘要生成",
			Type:        models.AgentTypeBidding,
			Description: "生成招投标项目摘要",
			SystemPrompt: `你是一个专业的招标分析专家。你的任务是创建清晰、准确的招标摘要，只基于原始数据中实际存在的信息。不要推测或补充不存在的信息。

请严格按照以下JSON格式输出结果：
{
  "project_overview": "项目概述",
  "main_requirements": "主要需求",
  "technical_requirements": "技术要求",
  "contract_info": "合同信息",
  "deadline": "截止时间",
  "budget": "预算范围"
}`,
			UserPromptTemplate: `请分析以下招标信息并生成摘要：

招标数据：
{{.tender_data}}

请基于上述信息生成结构化的招标摘要，严格按照JSON格式输出。`,
			OutputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"project_overview": map[string]interface{}{
						"type": "string",
						"description": "项目概述",
					},
					"main_requirements": map[string]interface{}{
						"type": "string",
						"description": "主要需求",
					},
					"technical_requirements": map[string]interface{}{
						"type": "string",
						"description": "技术要求",
					},
					"contract_info": map[string]interface{}{
						"type": "string",
						"description": "合同信息",
					},
					"deadline": map[string]interface{}{
						"type": "string",
						"description": "截止时间",
					},
					"budget": map[string]interface{}{
						"type": "string",
						"description": "预算范围",
					},
				},
			},
			Config: map[string]interface{}{
				"model": "deepseek-v3-250324",
				"temperature": 0.1,
				"max_tokens": 2000,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		"bidding_analysis": {
			ID:          "bidding_analysis",
			Name:        "招投标背景分析",
			Type:        models.AgentTypeBidding,
			Description: "分析招投标项目背景和需求",
			SystemPrompt: `你是一个专业的招投标分析专家。你需要基于项目摘要和搜索结果，深入分析项目背景、技术需求、市场环境等信息。

请提供详细的分析报告，包括：
1. 项目背景分析
2. 技术需求分析
3. 市场环境分析
4. 竞争对手分析
5. 风险和机会评估`,
			UserPromptTemplate: `请基于以下信息进行深入的招投标背景分析：

项目摘要：
{{.summary}}

搜索结果：
{{.search_results}}

需求分析：
{{.requirements_analysis}}

请提供全面的分析报告。`,
			OutputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"background_analysis": map[string]interface{}{
						"type": "string",
						"description": "项目背景分析",
					},
					"technical_analysis": map[string]interface{}{
						"type": "string",
						"description": "技术需求分析",
					},
					"market_analysis": map[string]interface{}{
						"type": "string",
						"description": "市场环境分析",
					},
					"competitor_analysis": map[string]interface{}{
						"type": "string",
						"description": "竞争对手分析",
					},
					"risk_assessment": map[string]interface{}{
						"type": "string",
						"description": "风险和机会评估",
					},
				},
			},
			Config: map[string]interface{}{
				"model": "deepseek-v3-250324",
				"temperature": 0.2,
				"max_tokens": 4000,
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}
	
	template, exists := templates[templateID]
	if !exists {
		return nil, fmt.Errorf("template not found: %s", templateID)
	}
	
	return template, nil
}

// getBuiltinTemplates 获取内置模板列表
func (at *AgentTemplateImpl) getBuiltinTemplates(agentType models.AgentType) []models.AgentTemplate {
	allTemplates := []models.AgentTemplate{}
	
	templateIDs := []string{"bidding_summary", "bidding_analysis"}
	
	for _, templateID := range templateIDs {
		if template, err := at.getBuiltinTemplate(templateID); err == nil {
			if agentType == "" || template.Type == agentType {
				allTemplates = append(allTemplates, *template)
			}
		}
	}
	
	return allTemplates
}