# 招投标Agent系统实施计划

## 项目概述

本文档描述将bidding_rag_recommend中的招投标后处理业务模块拆分为4个独立Agent的实施计划。

## 总体目标

1. **模块化重构**: 将现有4步式处理流程拆分为独立的Agent服务
2. **标准化接口**: 采用A2A协议实现Agent间通信 
3. **集成taskd**: 利用taskd的Agent框架和并发控制能力
4. **保持功能**: 确保业务功能完整性和性能表现

## 实施阶段

### 阶段1: 基础设施准备 (1-2周)

#### 1.1 taskd框架扩展
**目标**: 扩展taskd以支持A2A协议的Agent

**任务列表**:
- [ ] 在taskd中实现A2A协议支持
  - [ ] 创建A2A消息处理器 (`internal/api/handlers/a2a_handler.go`)
  - [ ] 实现Agent注册和发现机制 (`internal/services/agent_registry.go`)
  - [ ] 添加Agent健康检查服务 (`internal/services/agent_health.go`)
  
- [ ] 扩展路由系统
  - [ ] 添加Agent专用路由 (`/agents/{agent-name}/*`)
  - [ ] 实现Agent Card端点 (`/.well-known/agent.json`)
  - [ ] 添加健康检查端点 (`/health`)

- [ ] 集成认证和权限控制
  - [ ] 扩展现有token服务以支持Agent间认证
  - [ ] 实现Agent级别的权限控制

**技术细节**:
```go
// internal/models/agent_models.go - 新增Agent相关模型
type A2AAgent struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Description string                 `json:"description"`
    URL         string                 `json:"url"`
    Version     string                 `json:"version"`
    Skills      []AgentSkill          `json:"skills"`
    Status      AgentStatus           `json:"status"`
    Metadata    map[string]interface{} `json:"metadata"`
}

type AgentSkill struct {
    ID           string      `json:"id"`
    Name         string      `json:"name"`
    Description  string      `json:"description"`
    InputSchema  interface{} `json:"inputSchema"`
    OutputSchema interface{} `json:"outputSchema"`
}
```

#### 1.2 开发环境配置
**任务列表**:
- [ ] 配置开发环境支持双项目协作
- [ ] 设置共享的数据库和AI服务配置
- [ ] 建立统一的日志和监控体系

### 阶段2: Agent基础框架开发 (2-3周)

#### 2.1 A2A Agent基础类
**目标**: 创建可复用的Agent基础框架

**任务列表**:
- [ ] 创建Agent基础框架 (`internal/core/a2a_agent.go`)
- [ ] 实现标准的A2A消息处理
- [ ] 添加错误处理和重试机制
- [ ] 集成日志和监控

**技术细节**:
```go
// internal/core/a2a_agent.go
type A2AAgentInterface interface {
    ExecuteSkill(skillID string, input map[string]interface{}, context AgentContext) (*AgentResponse, error)
    GetAgentCard() *A2AAgent
    HealthCheck() *HealthStatus
}

type BaseA2AAgent struct {
    config   *AgentConfig
    logger   *Logger
    skills   map[string]SkillHandler
    metrics  *MetricsCollector
}
```

- [ ] 实现Agent包装器以集成现有业务逻辑
- [ ] 创建技能注册和管理机制
- [ ] 添加性能监控和指标收集

#### 2.2 数据层适配
**任务列表**:
- [ ] 创建数据访问层适配器
- [ ] 确保Agent可访问MongoDB和PostgreSQL
- [ ] 实现数据连接池和事务管理

### 阶段3: 业务Agent实现 (3-4周)

#### 3.1 数据获取Agent (第1周)
**目标**: 实现bidding-data-retrieval Agent

**任务列表**:
- [ ] 创建Agent实现 (`internal/agents/bidding_data_agent.go`)
- [ ] 迁移step_1000.py的业务逻辑
- [ ] 实现数据验证和预处理
- [ ] 添加单元测试和集成测试

**技术细节**:
```go
// internal/agents/bidding_data_agent.go
type BiddingDataAgent struct {
    *BaseA2AAgent
    mongoClient *mongo.Client
    validator   *DataValidator
}

func (a *BiddingDataAgent) ExecuteSkill(skillID string, input map[string]interface{}, ctx AgentContext) (*AgentResponse, error) {
    switch skillID {
    case "retrieve_tender_data":
        return a.retrieveTenderData(input, ctx)
    default:
        return nil, fmt.Errorf("unknown skill: %s", skillID)
    }
}
```

- [ ] 配置Agent Card和技能定义
- [ ] 实现健康检查和监控
- [ ] 性能测试和优化

#### 3.2 AI摘要Agent (第2周)  
**目标**: 实现ai-summary Agent

**任务列表**:
- [ ] 创建Agent实现 (`internal/agents/ai_summary_agent.go`)
- [ ] 集成现有的LLM服务 (`internal/llm/`)
- [ ] 迁移step_2000.py的Prompt和逻辑
- [ ] 实现流式处理适配 (转换为同步响应)

**技术细节**:
- 复用现有的`internal/llm/client.go`和API密钥池
- 适配现有的Prompt模板系统
- 集成token统计和并发控制

#### 3.3 需求分析与搜索Agent (第3周)
**目标**: 实现requirement-analysis-search Agent

**任务列表**:
- [ ] 创建Agent实现 (`internal/agents/requirement_analysis_agent.go`)
- [ ] 集成MCP搜索服务
- [ ] 迁移step_3000.py的需求分析逻辑
- [ ] 实现搜索结果聚合和过滤

**技术挑战**:
- MCP搜索服务的Go语言集成
- 异步搜索转换为同步响应
- 搜索超时和错误处理

#### 3.4 报告生成Agent (第4周)
**目标**: 实现report-generation Agent

**任务列表**:
- [ ] 创建Agent实现 (`internal/agents/report_generation_agent.go`)
- [ ] 迁移step_4000.py的报告生成逻辑
- [ ] 实现报告模板和格式化
- [ ] 集成AI服务进行内容生成

### 阶段4: 集成测试与优化 (2周)

#### 4.1 系统集成测试
**任务列表**:
- [ ] 端到端流程测试
- [ ] Agent间通信测试
- [ ] 性能基准测试
- [ ] 错误恢复测试

#### 4.2 部署和运维
**任务列表**:
- [ ] 更新Docker镜像和K8s配置
- [ ] 配置Agent自动注册和发现
- [ ] 设置监控和告警
- [ ] 编写运维文档

### 阶段5: 上线和切换 (1周)

#### 5.1 灰度发布
**任务列表**:
- [ ] 在测试环境验证完整功能
- [ ] 小比例流量灰度测试
- [ ] 性能和稳定性监控
- [ ] 问题修复和优化

#### 5.2 全量切换
**任务列表**:
- [ ] 停止原有Python服务
- [ ] 切换到新Agent系统
- [ ] 监控系统稳定性
- [ ] 用户反馈收集

## 技术实现细节

### 代码组织结构
```
internal/
├── agents/                    # Agent实现
│   ├── bidding_data_agent.go
│   ├── ai_summary_agent.go
│   ├── requirement_analysis_agent.go
│   └── report_generation_agent.go
├── core/
│   ├── a2a_agent.go          # A2A Agent基础类
│   └── agent_interface.go    # Agent接口定义
├── api/handlers/
│   └── a2a_handler.go        # A2A协议处理器
├── services/
│   ├── agent_registry.go     # Agent注册服务
│   ├── agent_health.go       # Agent健康检查
│   └── agent_discovery.go    # Agent发现服务
└── models/
    └── a2a_models.go         # A2A相关数据模型
```

### 配置管理
```yaml
# configs/config.dev.yaml
agents:
  bidding_data_retrieval:
    enabled: true
    endpoint: "/agents/bidding-data-retrieval"
    max_concurrent: 10
    timeout: 30s
    
  ai_summary:
    enabled: true
    endpoint: "/agents/ai-summary"
    max_concurrent: 5
    timeout: 180s
    llm_config:
      provider: "volcengine_ark"
      model: "deepseek-v3-250324"
      
  requirement_analysis:
    enabled: true
    endpoint: "/agents/requirement-analysis-search"
    max_concurrent: 3
    timeout: 300s
    search_config:
      max_results: 15
      search_engines: ["exa", "bing"]
      
  report_generation:
    enabled: true
    endpoint: "/agents/report-generation"
    max_concurrent: 5
    timeout: 180s
```

### 监控指标
- **性能指标**: 响应时间、吞吐量、错误率
- **业务指标**: 处理成功率、AI token消耗、搜索结果质量
- **系统指标**: CPU、内存、网络使用情况
- **Agent指标**: 健康状态、技能执行次数、版本信息

## 风险和应对策略

### 主要风险
1. **性能风险**: Agent间通信可能增加延迟
2. **复杂度风险**: 系统复杂度显著增加
3. **兼容性风险**: 现有功能可能在迁移过程中丢失
4. **稳定性风险**: 新系统初期可能不够稳定

### 应对策略
1. **性能优化**: 
   - 使用连接池减少网络开销
   - 实现结果缓存机制
   - 优化序列化/反序列化

2. **复杂度管理**:
   - 充分的单元测试和集成测试
   - 完善的文档和监控
   - 逐步迁移，降低风险

3. **兼容性保证**:
   - 对比测试确保功能一致性
   - 保留原有系统作为备份
   - 实现功能开关进行切换

4. **稳定性保障**:
   - 灰度发布策略
   - 完善的错误处理和重试机制
   - 实时监控和告警

## 成功标准

### 功能标准
- [ ] 4个Agent能够独立运行并正确处理业务逻辑
- [ ] Agent间通信协议工作正常
- [ ] 端到端处理流程与原系统功能一致

### 性能标准
- [ ] 整体处理时间不超过原系统的120%
- [ ] 单个Agent响应时间符合SLA要求
- [ ] 系统并发处理能力满足需求

### 质量标准
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试覆盖核心业务场景
- [ ] 系统稳定性达到99.5%

### 运维标准
- [ ] 完整的监控和告警体系
- [ ] 自动化部署和配置管理
- [ ] 详细的运维和故障处理文档

## 后续发展

### 短期优化 (1-2个月)
- Agent间缓存机制
- 性能监控和优化
- 更多业务场景支持

### 中期发展 (3-6个月)
- Agent市场和插件系统
- 更智能的任务编排
- 多语言Agent支持

### 长期规划 (6个月+)
- Agent生态系统建设
- 第三方Agent集成
- AI Agent自动优化和学习