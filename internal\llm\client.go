package llm

import (
	"context"
	"gitlab.com/specific-ai/taskd/internal/models"
)

// Message 聊天消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float32   `json:"temperature,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
}

// ChatResponse 聊天响应结构
type ChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice 选择结构
type Choice struct {
	Index   int     `json:"index"`
	Message Message `json:"message"`
	Finish  string  `json:"finish_reason"`
}

// Usage 使用情况结构
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// Client 客户端接口（兼容原有代码）
type Client interface {
	Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
	ChatCompletions(params models.OpenAICompatibleRequestParams) (string, error)
}

// LLMClient 定义了与大语言模型交互的接口
// 使用 OpenAI 兼容的请求/响应结构
type LLMClient interface {
	// ChatCompletions 向 LLM 发送请求并获取响应
	ChatCompletions(params models.OpenAICompatibleRequestParams) (string, error) // 返回助手的消息内容
	// ChatCompletionsWithUsage 向 LLM 发送请求并获取包含token使用信息的完整响应
	ChatCompletionsWithUsage(params models.OpenAICompatibleRequestParams) (*ChatResponse, error)
	// Chat 聊天接口（兼容原有代码）
	Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
	// TODO: ChatCompletionsStream 用于流式响应
	// TODO: CalculateTokens (可能特定于提供商，或使用通用分词器)
}
