package concurrent

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"golang.org/x/sync/semaphore"
)

// ConcurrentService 并发控制服务接口
type ConcurrentService interface {
	// SubmitRequest 提交LLM请求
	SubmitRequest(ctx context.Context, req *models.LLMRequestMessage) (*models.LLMResponseMessage, error)

	// GetStats 获取队列统计信息
	GetStats() *models.QueueStats

	// Start 启动服务
	Start(ctx context.Context) error

	// Stop 停止服务
	Stop() error

	// GetUserPriority 获取用户优先级（从用户信息或配置中）
	GetUserPriority(userID string) models.UserPriority
}

// concurrentServiceImpl 并发控制服务实现
type concurrentServiceImpl struct {
	config       *models.ConcurrentConfig
	tokenService services.TokenService
	llmService   services.LLMService // 抽象的LLM服务接口
	semaphore    *semaphore.Weighted

	// 优先级队列
	highPriorityQueue   chan *models.LLMRequestMessage
	mediumPriorityQueue chan *models.LLMRequestMessage
	lowPriorityQueue    chan *models.LLMRequestMessage
	retryQueue          chan *models.LLMRequestMessage

	// 统计信息
	stats      *models.QueueStats
	statsMutex sync.RWMutex

	// 控制
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	started    bool
	mu         sync.Mutex
}

// NewConcurrentService 创建并发控制服务
func NewConcurrentService(config *models.ConcurrentConfig, tokenService services.TokenService, llmService services.LLMService) ConcurrentService {
	if config == nil {
		config = models.DefaultConcurrentConfig()
	}

	return &concurrentServiceImpl{
		config:              config,
		tokenService:        tokenService,
		llmService:          llmService,
		semaphore:           semaphore.NewWeighted(int64(config.MaxConcurrentRequests)),
		highPriorityQueue:   make(chan *models.LLMRequestMessage, config.MainQueueSize/3),
		mediumPriorityQueue: make(chan *models.LLMRequestMessage, config.MainQueueSize/3),
		lowPriorityQueue:    make(chan *models.LLMRequestMessage, config.MainQueueSize/3),
		retryQueue:          make(chan *models.LLMRequestMessage, config.RetryQueueSize),
		stats: &models.QueueStats{
			PriorityStats:     make(map[models.RequestPriority]int),
			UserPriorityStats: make(map[models.UserPriority]int),
		},
	}
}

// Start 启动并发控制服务
func (c *concurrentServiceImpl) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.started {
		return fmt.Errorf("concurrent service already started")
	}

	c.ctx, c.cancel = context.WithCancel(ctx)
	c.started = true

	// 启动工作协程
	for i := 0; i < c.config.WorkerCount; i++ {
		c.wg.Add(1)
		go c.worker(i)
	}

	// 启动重试协程
	c.wg.Add(1)
	go c.retryWorker()

	// 启动统计协程
	c.wg.Add(1)
	go c.statsWorker()

	utils.Log.Infof("Concurrent service started with %d workers", c.config.WorkerCount)
	return nil
}

// Stop 停止并发控制服务
func (c *concurrentServiceImpl) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.started {
		return nil
	}

	c.cancel()
	c.wg.Wait()
	c.started = false

	utils.Log.Info("Concurrent service stopped")
	return nil
}

// SubmitRequest 提交LLM请求
func (c *concurrentServiceImpl) SubmitRequest(ctx context.Context, req *models.LLMRequestMessage) (*models.LLMResponseMessage, error) {
	// 1. 验证Token限额
	if c.tokenService != nil && req.Params.AutoTrackTokens && req.Params.UserContext != nil {
		estimatedTokens := c.estimateTokens(req.Params.Messages)
		limitCheck, err := c.tokenService.CheckTokenLimits(req.UserID, estimatedTokens+500)
		if err != nil {
			utils.Log.Errorf("检查token限额失败: %v", err)
			return nil, fmt.Errorf("token limit check failed: %w", err)
		}
		if !limitCheck.CanProceed {
			return nil, fmt.Errorf("token limit exceeded: %s", limitCheck.Reason)
		}
	}

	// 2. 设置请求属性
	req.Context = ctx
	req.ResponseChan = make(chan *models.LLMResponseMessage, 1)
	if req.MaxWaitTime == 0 {
		req.MaxWaitTime = c.config.MaxWaitTime
	}
	if req.MaxRetries == 0 {
		req.MaxRetries = c.config.MaxRetries
	}

	// 3. 确定用户优先级和请求优先级
	userPriority := c.GetUserPriority(req.UserID)
	req.UserPriority = userPriority
	req.Priority = models.GetPriorityFromUser(userPriority)

	// 4. 检查是否应该拒绝请求
	currentQueueSize := c.getCurrentQueueSize()
	if c.config.ShouldRejectRequest(userPriority, currentQueueSize) {
		return nil, fmt.Errorf("request rejected: queue is busy and free users are temporarily restricted")
	}

	// 5. 将请求加入相应的优先级队列
	if err := c.enqueueRequest(req); err != nil {
		return nil, fmt.Errorf("failed to enqueue request: %w", err)
	}

	// 6. 等待响应或超时
	select {
	case response := <-req.ResponseChan:
		return response, nil
	case <-time.After(req.MaxWaitTime):
		return nil, fmt.Errorf("request timeout after %v", req.MaxWaitTime)
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// enqueueRequest 将请求加入队列
func (c *concurrentServiceImpl) enqueueRequest(req *models.LLMRequestMessage) error {
	var targetQueue chan *models.LLMRequestMessage

	switch req.Priority {
	case models.PriorityHigh:
		targetQueue = c.highPriorityQueue
	case models.PriorityMedium:
		targetQueue = c.mediumPriorityQueue
	case models.PriorityLow:
		targetQueue = c.lowPriorityQueue
	default:
		targetQueue = c.lowPriorityQueue
	}

	select {
	case targetQueue <- req:
		c.updateQueueStats(req, "enqueued")
		utils.Log.Debugf("Request %s enqueued with priority %v", req.ID, req.Priority)
		return nil
	default:
		// 队列满了，返回429错误
		return fmt.Errorf("queue full: status 429")
	}
}

// worker 工作协程
func (c *concurrentServiceImpl) worker(workerID int) {
	defer c.wg.Done()

	utils.Log.Debugf("Worker %d started", workerID)

	for {
		select {
		case <-c.ctx.Done():
			utils.Log.Debugf("Worker %d stopped", workerID)
			return
		default:
			// 按优先级处理请求：高 -> 中 -> 低
			req := c.dequeueRequest()
			if req == nil {
				time.Sleep(10 * time.Millisecond) // 短暂等待
				continue
			}

			c.processRequest(req, workerID)
		}
	}
}

// dequeueRequest 从队列中取出请求（按优先级）
func (c *concurrentServiceImpl) dequeueRequest() *models.LLMRequestMessage {
	// 高优先级队列
	select {
	case req := <-c.highPriorityQueue:
		return req
	default:
	}

	// 中优先级队列
	select {
	case req := <-c.mediumPriorityQueue:
		return req
	default:
	}

	// 低优先级队列
	select {
	case req := <-c.lowPriorityQueue:
		return req
	default:
	}

	return nil
}

// processRequest 处理单个请求
func (c *concurrentServiceImpl) processRequest(req *models.LLMRequestMessage, workerID int) {
	startTime := time.Now()

	// 获取信号量
	if err := c.semaphore.Acquire(req.Context, 1); err != nil {
		c.sendResponse(req, &models.LLMResponseMessage{
			ID:      req.ID,
			Success: false,
			Error:   fmt.Sprintf("failed to acquire semaphore: %v", err),
		})
		return
	}
	defer c.semaphore.Release(1)

	utils.Log.Debugf("Worker %d processing request %s", workerID, req.ID)

	// 更新统计
	c.updateQueueStats(req, "processing")

	// 调用LLM服务
	response, err := c.llmService.ProcessRequest(req.Context, req.Params)
	duration := time.Since(startTime)

	if err != nil {
		// 检查是否是429错误或超时，需要重试
		if c.shouldRetry(err) && req.RetryCount < req.MaxRetries {
			utils.Log.Warnf("Request %s failed with retryable error: %v, scheduling retry", req.ID, err)
			c.scheduleRetry(req)
			return
		}

		// 不可重试的错误或重试次数已达上限
		response = &models.LLMResponseMessage{
			ID:         req.ID,
			Success:    false,
			Error:      err.Error(),
			Duration:   duration,
			RetryCount: req.RetryCount,
		}
		c.updateQueueStats(req, "error")
	} else {
		response.Duration = duration
		response.RetryCount = req.RetryCount
		c.updateQueueStats(req, "success")
	}

	c.sendResponse(req, response)
}

// shouldRetry 判断是否应该重试
func (c *concurrentServiceImpl) shouldRetry(err error) bool {
	errStr := err.Error()
	// 检查是否是429限流错误或超时错误
	return strings.Contains(errStr, "429") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "rate limit") ||
		strings.Contains(errStr, "too many requests")
}

// scheduleRetry 安排重试
func (c *concurrentServiceImpl) scheduleRetry(req *models.LLMRequestMessage) {
	req.RetryCount++

	// 计算退避延迟：wait = 2^attempt * base_delay
	delay := c.config.RetryBaseDelay * time.Duration(1<<req.RetryCount)

	go func() {
		select {
		case <-time.After(delay):
			select {
			case c.retryQueue <- req:
				utils.Log.Debugf("Request %s scheduled for retry %d after %v delay",
					req.ID, req.RetryCount, delay)
			default:
				// 重试队列满了
				c.sendResponse(req, &models.LLMResponseMessage{
					ID:      req.ID,
					Success: false,
					Error:   "retry queue full",
				})
			}
		case <-c.ctx.Done():
			return
		}
	}()
}

// retryWorker 重试工作协程
func (c *concurrentServiceImpl) retryWorker() {
	defer c.wg.Done()

	for {
		select {
		case <-c.ctx.Done():
			return
		case req := <-c.retryQueue:
			// 重试请求重新进入主队列
			if err := c.enqueueRequest(req); err != nil {
				c.sendResponse(req, &models.LLMResponseMessage{
					ID:      req.ID,
					Success: false,
					Error:   fmt.Sprintf("failed to requeue retry request: %v", err),
				})
			}
			c.updateQueueStats(req, "retry")
		}
	}
}

// sendResponse 发送响应
func (c *concurrentServiceImpl) sendResponse(req *models.LLMRequestMessage, response *models.LLMResponseMessage) {
	select {
	case req.ResponseChan <- response:
	default:
		utils.Log.Warnf("Failed to send response for request %s: channel blocked", req.ID)
	}
}

// GetUserPriority 获取用户优先级
func (c *concurrentServiceImpl) GetUserPriority(userID string) models.UserPriority {
	// TODO: 从数据库或配置中获取用户优先级
	// 这里简化处理，实际应该查询用户表或配置
	if userID == "vip_user" || userID == "premium_user" {
		return models.PriorityVIP
	}
	if userID == "free_user" || userID == "trial_user" {
		return models.PriorityFree
	}
	return models.PriorityNormal
}

// getCurrentQueueSize 获取当前队列大小
func (c *concurrentServiceImpl) getCurrentQueueSize() int {
	return len(c.highPriorityQueue) + len(c.mediumPriorityQueue) + len(c.lowPriorityQueue)
}

// updateQueueStats 更新队列统计
func (c *concurrentServiceImpl) updateQueueStats(req *models.LLMRequestMessage, action string) {
	c.statsMutex.Lock()
	defer c.statsMutex.Unlock()

	switch action {
	case "enqueued":
		c.stats.PriorityStats[req.Priority]++
		c.stats.UserPriorityStats[req.UserPriority]++
	case "processing":
		c.stats.ProcessingCount++
	case "success":
		c.stats.ProcessingCount--
		c.stats.TotalProcessed++
	case "error":
		c.stats.ProcessingCount--
		c.stats.TotalErrors++
	case "retry":
		c.stats.TotalRetries++
	}
}

// GetStats 获取队列统计信息
func (c *concurrentServiceImpl) GetStats() *models.QueueStats {
	c.statsMutex.RLock()
	defer c.statsMutex.RUnlock()

	stats := *c.stats
	stats.MainQueueSize = c.getCurrentQueueSize()
	stats.RetryQueueSize = len(c.retryQueue)
	stats.ActiveWorkers = c.config.WorkerCount

	return &stats
}

// statsWorker 统计工作协程
func (c *concurrentServiceImpl) statsWorker() {
	defer c.wg.Done()

	ticker := time.NewTicker(10 * time.Minute)  // 改为10分钟打印一次
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			stats := c.GetStats()
			utils.Log.Infof("Queue stats: main=%d, retry=%d, processing=%d, total_processed=%d, total_errors=%d",
				stats.MainQueueSize, stats.RetryQueueSize, stats.ProcessingCount,
				stats.TotalProcessed, stats.TotalErrors)
		}
	}
}

// estimateTokens 估算token数量
func (c *concurrentServiceImpl) estimateTokens(messages []common.LLMMessage) int {
	totalChars := 0
	for _, msg := range messages {
		totalChars += len(msg.Content)
	}
	return totalChars / 4 // 简单估算
}