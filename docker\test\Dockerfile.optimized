# TaskD 轻量化测试环境 - 多阶段构建
# 目标：最小化镜像体积，提高构建速度

# ================================
# 第一阶段：构建环境 (Builder)
# ================================
FROM python:3.11-alpine AS builder

# 设置构建环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    UV_CACHE_DIR=/tmp/uv-cache

# 安装构建依赖
RUN apk add --no-cache --virtual .build-deps \
    gcc \
    musl-dev \
    libffi-dev \
    && apk add --no-cache \
    curl \
    git

# 安装 uv 包管理器
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:$PATH"

# 设置工作目录
WORKDIR /build

# 复制依赖文件
COPY test/requirements.txt test/test_concurrent_requirements.txt ./

# 使用 uv 创建虚拟环境
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 安装所有测试依赖到虚拟环境
RUN uv pip install --no-cache-dir -r requirements.txt -r test_concurrent_requirements.txt

# 清理构建缓存
RUN rm -rf /tmp/uv-cache /root/.local

# ================================
# 第二阶段：运行环境 (Runtime)
# ================================
FROM python:3.11-alpine AS runtime

# 设置运行时环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app:/app/test"

# 只安装运行时必需的系统包
RUN apk add --no-cache \
    curl \
    netcat-openbsd \
    && rm -rf /var/cache/apk/*

# 从构建阶段复制Python虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 设置工作目录
WORKDIR /app

# 复制最小化的项目文件
COPY test/ ./test/
COPY docker/test/.env.docker ./

# 创建入口点脚本 (兼容旧版 Docker Builder)
RUN echo '#!/bin/sh' > /app/entrypoint.sh && \
    echo 'set -e' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 如果参数是 "tail -f /dev/null"，直接执行' >> /app/entrypoint.sh && \
    echo 'if [ "$1" = "tail" ] && [ "$2" = "-f" ] && [ "$3" = "/dev/null" ]; then' >> /app/entrypoint.sh && \
    echo '    exec tail -f /dev/null' >> /app/entrypoint.sh && \
    echo 'fi' >> /app/entrypoint.sh && \
    echo '' >> /app/entrypoint.sh && \
    echo '# 其他情况，直接执行传入的命令' >> /app/entrypoint.sh && \
    echo 'exec "$@"' >> /app/entrypoint.sh

# 设置权限并创建必要目录
RUN chmod +x /app/entrypoint.sh \
    && mkdir -p test/logs test/reports

# 移除构建产物和不必要的文档以减小体积
RUN find test/ -name "*.pyc" -delete \
    && find test/ -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true \
    && find test/ -name "*.md" -delete

# 入口点
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["pytest", "test/"]

# 镜像标签
LABEL maintainer="TaskD Team" \
      version="1.0.0" \
      description="Optimized TaskD test environment" \
      size="minimal"