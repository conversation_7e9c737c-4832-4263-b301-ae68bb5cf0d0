package llm

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/utils"
)

// APIKeyPool API Key池管理器
type APIKeyPool struct {
	keys         []string          // 所有可用的API Keys
	currentIndex int               // 当前使用的Key索引
	keyStats     map[string]*KeyStats // 每个Key的统计信息
	mutex        sync.RWMutex      // 读写锁
	strategy     PoolStrategy      // 轮询策略
}

// KeyStats API Key统计信息
type KeyStats struct {
	Key            string    `json:"key"`
	RequestCount   int64     `json:"request_count"`   // 请求次数
	ErrorCount     int64     `json:"error_count"`     // 错误次数
	LastUsed       time.Time `json:"last_used"`       // 最后使用时间
	LastError      time.Time `json:"last_error"`      // 最后错误时间
	IsBlacklisted  bool      `json:"is_blacklisted"` // 是否被黑名单
	BlacklistUntil time.Time `json:"blacklist_until"` // 黑名单到期时间
}

// PoolStrategy 池策略
type PoolStrategy int

const (
	RoundRobin PoolStrategy = iota // 轮询
	LeastUsed                      // 最少使用
	Random                         // 随机
)

// NewAPIKeyPool 创建新的API Key池
func NewAPIKeyPool(keys []string, strategy PoolStrategy) *APIKeyPool {
	if len(keys) == 0 {
		return nil
	}

	pool := &APIKeyPool{
		keys:     make([]string, len(keys)),
		keyStats: make(map[string]*KeyStats),
		strategy: strategy,
	}

	// 复制keys并初始化统计
	copy(pool.keys, keys)
	for _, key := range keys {
		pool.keyStats[key] = &KeyStats{
			Key:      key,
			LastUsed: time.Now(),
		}
	}

	utils.Log.Infof("API Key池初始化完成，包含 %d 个keys", len(keys))
	return pool
}

// GetNextKey 获取下一个可用的API Key
func (p *APIKeyPool) GetNextKey() (string, error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if len(p.keys) == 0 {
		return "", fmt.Errorf("API Key池为空")
	}

	// 过滤掉被黑名单的keys
	availableKeys := p.getAvailableKeys()
	if len(availableKeys) == 0 {
		return "", fmt.Errorf("没有可用的API Key（所有key都被黑名单）")
	}

	var selectedKey string
	switch p.strategy {
	case RoundRobin:
		selectedKey = p.getRoundRobinKey(availableKeys)
	case LeastUsed:
		selectedKey = p.getLeastUsedKey(availableKeys)
	case Random:
		selectedKey = p.getRandomKey(availableKeys)
	default:
		selectedKey = p.getRoundRobinKey(availableKeys)
	}

	// 更新统计
	if stats, exists := p.keyStats[selectedKey]; exists {
		stats.RequestCount++
		stats.LastUsed = time.Now()
	}

	utils.Log.Debugf("选择API Key: %s (策略: %v)", maskKey(selectedKey), p.strategy)
	return selectedKey, nil
}

// getAvailableKeys 获取可用的keys（未被黑名单的）
func (p *APIKeyPool) getAvailableKeys() []string {
	now := time.Now()
	available := make([]string, 0, len(p.keys))

	for _, key := range p.keys {
		if stats, exists := p.keyStats[key]; exists {
			if !stats.IsBlacklisted || now.After(stats.BlacklistUntil) {
				// 如果黑名单已过期，重置状态
				if stats.IsBlacklisted && now.After(stats.BlacklistUntil) {
					stats.IsBlacklisted = false
					utils.Log.Infof("API Key %s 黑名单已过期，重新启用", maskKey(key))
				}
				available = append(available, key)
			}
		}
	}

	return available
}

// getRoundRobinKey 轮询策略
func (p *APIKeyPool) getRoundRobinKey(availableKeys []string) string {
	if len(availableKeys) == 0 {
		return ""
	}

	// 找到当前key在可用keys中的位置
	currentKey := ""
	if p.currentIndex < len(p.keys) {
		currentKey = p.keys[p.currentIndex]
	}

	// 找到下一个可用的key
	startIndex := 0
	for i, key := range availableKeys {
		if key == currentKey {
			startIndex = (i + 1) % len(availableKeys)
			break
		}
	}

	selectedKey := availableKeys[startIndex]
	
	// 更新currentIndex为选中key在原数组中的位置
	for i, key := range p.keys {
		if key == selectedKey {
			p.currentIndex = i
			break
		}
	}

	return selectedKey
}

// getLeastUsedKey 最少使用策略
func (p *APIKeyPool) getLeastUsedKey(availableKeys []string) string {
	if len(availableKeys) == 0 {
		return ""
	}

	leastUsedKey := availableKeys[0]
	leastCount := p.keyStats[leastUsedKey].RequestCount

	for _, key := range availableKeys[1:] {
		if stats, exists := p.keyStats[key]; exists && stats.RequestCount < leastCount {
			leastUsedKey = key
			leastCount = stats.RequestCount
		}
	}

	return leastUsedKey
}

// getRandomKey 随机策略
func (p *APIKeyPool) getRandomKey(availableKeys []string) string {
	if len(availableKeys) == 0 {
		return ""
	}

	// 简单的伪随机（基于时间）
	index := int(time.Now().UnixNano()) % len(availableKeys)
	return availableKeys[index]
}

// ReportError 报告API Key错误
func (p *APIKeyPool) ReportError(key string, err error) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	stats, exists := p.keyStats[key]
	if !exists {
		return
	}

	stats.ErrorCount++
	stats.LastError = time.Now()

	// 检查是否需要加入黑名单
	errorRate := float64(stats.ErrorCount) / float64(stats.RequestCount)
	recentErrors := stats.ErrorCount > 5 && time.Since(stats.LastError) < 5*time.Minute

	if (errorRate > 0.5 && stats.RequestCount > 10) || recentErrors {
		stats.IsBlacklisted = true
		stats.BlacklistUntil = time.Now().Add(10 * time.Minute) // 黑名单10分钟
		utils.Log.Warnf("API Key %s 被加入黑名单，原因: 错误率过高 (%.2f) 或频繁错误", 
			maskKey(key), errorRate)
	}

	utils.Log.Debugf("报告API Key错误: %s, 错误: %v", maskKey(key), err)
}

// GetStats 获取池统计信息
func (p *APIKeyPool) GetStats() map[string]*KeyStats {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	// 深拷贝统计信息
	result := make(map[string]*KeyStats)
	for key, stats := range p.keyStats {
		result[maskKey(key)] = &KeyStats{
			Key:            maskKey(key),
			RequestCount:   stats.RequestCount,
			ErrorCount:     stats.ErrorCount,
			LastUsed:       stats.LastUsed,
			LastError:      stats.LastError,
			IsBlacklisted:  stats.IsBlacklisted,
			BlacklistUntil: stats.BlacklistUntil,
		}
	}

	return result
}

// GetPoolInfo 获取池信息
func (p *APIKeyPool) GetPoolInfo() map[string]interface{} {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	availableKeys := p.getAvailableKeys()
	blacklistedCount := len(p.keys) - len(availableKeys)

	return map[string]interface{}{
		"total_keys":        len(p.keys),
		"available_keys":    len(availableKeys),
		"blacklisted_keys":  blacklistedCount,
		"strategy":          p.getStrategyName(),
		"current_index":     p.currentIndex,
	}
}

// getStrategyName 获取策略名称
func (p *APIKeyPool) getStrategyName() string {
	switch p.strategy {
	case RoundRobin:
		return "round_robin"
	case LeastUsed:
		return "least_used"
	case Random:
		return "random"
	default:
		return "unknown"
	}
}

// ResetBlacklist 重置黑名单（管理功能）
func (p *APIKeyPool) ResetBlacklist() {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	count := 0
	for _, stats := range p.keyStats {
		if stats.IsBlacklisted {
			stats.IsBlacklisted = false
			stats.BlacklistUntil = time.Time{}
			count++
		}
	}

	utils.Log.Infof("重置了 %d 个API Key的黑名单状态", count)
}

// AddKey 添加新的API Key到池中
func (p *APIKeyPool) AddKey(key string) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// 检查是否已存在
	for _, existingKey := range p.keys {
		if existingKey == key {
			utils.Log.Warnf("API Key已存在，跳过添加: %s", maskKey(key))
			return
		}
	}

	p.keys = append(p.keys, key)
	p.keyStats[key] = &KeyStats{
		Key:      key,
		LastUsed: time.Now(),
	}

	utils.Log.Infof("添加新的API Key到池中: %s", maskKey(key))
}

// RemoveKey 从池中移除API Key
func (p *APIKeyPool) RemoveKey(key string) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	for i, existingKey := range p.keys {
		if existingKey == key {
			// 移除key
			p.keys = append(p.keys[:i], p.keys[i+1:]...)
			delete(p.keyStats, key)
			
			// 调整currentIndex
			if p.currentIndex >= len(p.keys) {
				p.currentIndex = 0
			}
			
			utils.Log.Infof("从池中移除API Key: %s", maskKey(key))
			return true
		}
	}

	return false
}

// maskKey 掩码API Key用于日志显示
func maskKey(key string) string {
	if len(key) <= 8 {
		return "****"
	}
	return key[:4] + "****" + key[len(key)-4:]
}