# 闲聊Agent配置示例
chat:
  # Socket连接配置
  max_socket_connections: 100        # 最大Socket连接数
  ping_interval_seconds: 30          # Ping间隔时间(秒)
  
  # 会话配置
  session_timeout_minutes: 60        # 会话超时时间(分钟)
  default_token_limit: 4000          # 默认上下文token限制
  
  # 清理配置
  cleanup_interval_hours: 1          # 清理任务执行间隔(小时)
  
  # AI模型配置
  model_provider: "volcengine_ark"   # 模型提供商
  model_alias: "doubao-1-5-pro-32k-250115"      # 模型别名
  temperature: 0.7                   # 温度参数
  max_tokens: 1000                   # 最大输出token数
  
  # 功能配置
  enable_token_tracking: true        # 是否启用token跟踪
  
  # 系统提示词
  system_prompt: |
    你是一个友善、有帮助的AI助手，喜欢与用户进行轻松愉快的对话。
    请保持对话自然流畅，并根据用户的问题给出恰当的回应。
    你可以聊天、回答问题、提供建议，但请避免涉及敏感话题。
    保持积极正面的态度，让用户感到愉快和舒适。

# PostgreSQL数据库配置 (需要overseas数据库和taskd schema)
postgresql:
  host: "localhost"
  port: 5432
  user: "admin"
  password: "SecurePass123!"
  database: "overseas"              # 注意：使用overseas数据库
  ssl_mode: "disable"
  timeout_seconds: 10
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300

# LLM配置
llm:
  default_provider: "volcengine_ark"
  providers:
    volcengine_ark:
      base_url: "https://ark.cn-beijing.volces.com/api/v3"
      models:
        doubao-1-5-pro-32k-250115: "doubao-1-5-pro-32k-250115"
        chat-agent: "doubao-1-5-pro-32k-250115"
      default_model_alias: "chat-agent"
      request_timeout_seconds: 300
      max_concurrent_requests: 10

# 服务器配置
server:
  port: "8601"
  mode: "debug"

# 日志配置
logger:
  level: "info"