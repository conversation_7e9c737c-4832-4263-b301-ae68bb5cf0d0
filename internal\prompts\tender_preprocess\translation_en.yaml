name: "translation_en"
description: "English tender multilingual translation prompt"
language: "english"
version: "1.0"

system_prompt: |
  You are a professional translator specializing in tender and procurement documents. Translate the provided English content to {{target_language}} while maintaining professional terminology and context accuracy.

  Requirements:
  1. Maintain technical accuracy for procurement and tender terms
  2. Keep the structure and format of the original content
  3. Use appropriate business {{target_language}} terminology
  4. Return only the translated JSON content without additional explanation

user_prompt_template: |
  Please translate the following English tender-related content to {{target_language_name}}. Maintain the JSON structure and translate all text values:

  {{content_json}}

  Return only the translated JSON without any additional text.

variables:
  content_json:
    type: "string"
    description: "JSON content to be translated"
    required: true
  target_language:
    type: "string"
    description: "Target language code"
    required: true
  target_language_name:
    type: "string"
    description: "Target language name"
    required: true