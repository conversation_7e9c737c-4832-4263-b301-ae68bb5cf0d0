#!/bin/bash

# TaskD Docker 测试容器入口脚本
# 负责初始化测试环境并执行测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 加载环境变量
if [ -f .env.docker ]; then
    source .env.docker
    log_info "已加载 Docker 环境配置"
fi

# 设置默认值（跨网络测试）
TASKD_BASE_URL=${TASKD_BASE_URL:-"http://**************:8601"}
TASKD_TIMEOUT=${TASKD_TIMEOUT:-"120"}
TASKD_STARTUP_WAIT=${TASKD_STARTUP_WAIT:-"15"}
TEST_VERBOSE=${TEST_VERBOSE:-"true"}
NETWORK_CONNECT_TIMEOUT=${NETWORK_CONNECT_TIMEOUT:-"30"}

# 网络连通性检查（跨网络环境）
check_network() {
    log_info "检查跨网络连通性..."
    
    # 提取主机和端口
    local url_host=$(echo $TASKD_BASE_URL | sed -e 's|http[s]*://||' -e 's|:.*||')
    local url_port=$(echo $TASKD_BASE_URL | sed -e 's|.*:||' -e 's|/.*||')
    
    log_info "目标服务: $url_host:$url_port (Windows TaskD服务)"
    
    # 跳过DNS解析检查（IP地址直连）
    if [[ $url_host =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_info "使用IP地址直连，跳过DNS解析检查"
    else
        # DNS 解析检查
        if ! nslookup $url_host >/dev/null 2>&1; then
            log_warning "DNS 解析失败: $url_host"
            # 对于跨网络测试，DNS失败不阻塞
        fi
    fi
    
    # 端口连通性检查（增加超时时间）
    log_info "测试端口连通性..."
    if timeout $NETWORK_CONNECT_TIMEOUT nc -z $url_host $url_port 2>/dev/null; then
        log_success "网络连通性检查通过"
        return 0
    else
        log_warning "端口连通性检查失败: $url_host:$url_port"
        log_info "尝试使用telnet进一步检查..."
        
        # 使用telnet再次尝试
        if timeout 5 telnet $url_host $url_port </dev/null 2>/dev/null; then
            log_success "telnet连接成功"
            return 0
        else
            log_error "无法连接到TaskD服务，请检查："
            log_error "1. TaskD服务是否在Windows机器上运行"
            log_error "2. 防火墙是否允许8601端口"
            log_error "3. IP地址是否正确: $url_host"
            return 1
        fi
    fi
}

# 等待外部TaskD服务就绪
wait_for_taskd() {
    log_info "等待外部TaskD服务就绪..."
    log_info "服务地址: $TASKD_BASE_URL"
    
    local max_attempts=20
    local attempt=0
    local wait_interval=3
    
    while [ $attempt -lt $max_attempts ]; do
        # 使用更长的超时时间进行健康检查
        if curl -sf --max-time $NETWORK_CONNECT_TIMEOUT "${TASKD_BASE_URL}/healthz" >/dev/null 2>&1; then
            log_success "TaskD 服务已就绪"
            
            # 额外验证：测试一个简单的API调用
            log_info "验证API连通性..."
            if curl -sf --max-time 10 "${TASKD_BASE_URL}/v1/intent/supported" >/dev/null 2>&1; then
                log_success "API连通性验证通过"
                return 0
            else
                log_warning "健康检查通过，但API调用失败，继续尝试..."
            fi
        fi
        
        attempt=$((attempt + 1))
        log_info "等待服务就绪... ($attempt/$max_attempts) - 请确保Windows上的TaskD服务正在运行"
        sleep $wait_interval
    done
    
    log_error "TaskD 服务连接超时"
    log_error "故障排除建议："
    log_error "1. 检查Windows机器上TaskD服务是否运行: http://**************:8601/healthz"
    log_error "2. 检查防火墙设置是否允许8601端口访问"
    log_error "3. 确认IP地址**************是否正确"
    return 1
}

# 验证 Python 环境
verify_python_env() {
    log_info "验证 Python 环境..."
    
    # 检查 Python 版本
    python_version=$(python3 --version 2>&1)
    log_info "Python 版本: $python_version"
    
    # 检查 uv 版本
    uv_version=$(uv --version 2>&1)
    log_info "uv 版本: $uv_version"
    
    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        log_warning "虚拟环境不存在，正在创建..."
        uv sync --all-extras
    fi
    
    log_success "Python 环境验证通过"
}

# 运行测试
run_tests() {
    local test_type="$1"
    local test_args="$2"
    
    log_info "开始运行测试: $test_type"
    
    # 设置测试环境变量
    export TASKD_BASE_URL
    export TASKD_TIMEOUT
    export TEST_VERBOSE
    export PYTHONPATH="/app:$PYTHONPATH"
    
    case "$test_type" in
        "all")
            log_info "运行所有测试..."
            uv run pytest test/ -v --tb=short --durations=10 $test_args
            ;;
        "p0"|"P0")
            log_info "运行 P0 级别测试..."
            uv run pytest test/ -v -m "p0" --tb=short $test_args
            ;;
        "p1"|"P1")
            log_info "运行 P1 级别测试..."
            uv run pytest test/ -v -m "p1" --tb=short $test_args
            ;;
        "p2"|"P2")
            log_info "运行 P2 级别测试..."
            uv run pytest test/ -v -m "p2" --tb=short $test_args
            ;;
        "p3"|"P3")
            log_info "运行 P3 级别测试..."
            uv run pytest test/ -v -m "p3" --tb=short $test_args
            ;;
        "p4"|"P4")
            log_info "运行 P4 级别测试..."
            uv run pytest test/ -v -m "p4" --tb=short $test_args
            ;;
        "e2e"|"E2E")
            log_info "运行端到端测试..."
            uv run pytest test/ -v -m "e2e" --tb=short $test_args
            ;;
        "bidding")
            log_info "运行招投标模块测试..."
            uv run pytest test/test_bidding_agents.py -v --tb=short $test_args
            ;;
        "entity")
            log_info "运行实体提取模块测试..."
            uv run pytest test/test_entity_extraction_agent.py -v --tb=short $test_args
            ;;
        "token")
            log_info "运行 Token 管理测试..."
            uv run pytest test/test_token_management.py -v --tb=short $test_args
            ;;
        "concurrent")
            log_info "运行并发控制测试..."
            uv run pytest test/test_concurrent_control.py -v --tb=short $test_args
            ;;
        "chat")
            log_info "运行聊天模块测试..."
            uv run pytest test/test_chat_agent.py -v --tb=short $test_args
            ;;
        "preprocessing")
            log_info "运行预处理模块测试..."
            uv run pytest test/test_preprocessing_agents.py -v --tb=short $test_args
            ;;
        "intent")
            log_info "运行意图识别测试..."
            uv run pytest test/test_intent_recognition.py -v --tb=short $test_args
            ;;
        "agent")
            log_info "运行 Agent 模块测试..."
            uv run pytest test/test_agent_module.py -v --tb=short $test_args
            ;;
        "performance")
            log_info "运行性能测试..."
            uv run pytest test/test_agent_performance.py -v --tb=short $test_args
            ;;
        "batch")
            log_info "运行批量处理测试..."
            uv run pytest test/test_batch_processing.py -v --tb=short $test_args
            ;;
        *)
            log_info "运行自定义测试: $test_type"
            uv run pytest $test_type -v --tb=short $test_args
            ;;
    esac
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        log_success "测试完成: $test_type"
    else
        log_error "测试失败: $test_type (退出码: $exit_code)"
    fi
    
    return $exit_code
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    # 创建报告目录
    mkdir -p test/reports
    
    # 生成 HTML 报告
    if [ "$TEST_REPORT_FORMAT" = "html" ]; then
        log_info "生成 HTML 报告..."
        uv run pytest test/ --html=test/reports/report.html --self-contained-html
    fi
    
    # 生成 JUnit XML 报告
    log_info "生成 JUnit XML 报告..."
    uv run pytest test/ --junitxml=test/reports/junit.xml
    
    log_success "报告生成完成"
}

# 清理函数
cleanup() {
    log_info "执行清理工作..."
    
    # 清理临时文件
    if [ "$TEST_KEEP_DATA" != "true" ]; then
        log_info "清理测试数据..."
        # 这里可以添加具体的清理逻辑
    fi
    
    log_info "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
TaskD Docker 测试容器使用帮助

用法:
  docker run taskd-test [命令] [选项]

命令:
  test [类型]     运行测试 (默认)
  shell          进入交互式 shell
  help           显示帮助信息

测试类型:
  all            运行所有测试 (默认)
  p0, p1, p2, p3, p4  按优先级运行测试
  e2e            端到端测试
  bidding        招投标模块测试
  entity         实体提取模块测试
  token          Token 管理测试
  concurrent     并发控制测试
  chat           聊天模块测试
  preprocessing  预处理模块测试
  intent         意图识别测试
  agent          Agent 模块测试
  performance    性能测试
  batch          批量处理测试

环境变量:
  TASKD_BASE_URL         TaskD 服务地址 (默认: http://taskd:8601)
  TASKD_TIMEOUT          请求超时时间 (默认: 60)
  TEST_VERBOSE           是否显示详细输出 (默认: true)
  TEST_REPORT_FORMAT     报告格式 (console/html/json)

示例:
  docker run taskd-test                    # 运行所有测试
  docker run taskd-test test p0            # 运行 P0 测试
  docker run taskd-test test bidding       # 运行招投标测试
  docker run taskd-test shell              # 进入 shell
EOF
}

# 主函数
main() {
    log_info "====== TaskD Docker 测试容器启动 ======"
    log_info "容器版本: 1.0.0"
    log_info "Python 环境: $(python3 --version)"
    log_info "uv 版本: $(uv --version)"
    
    # 解析命令行参数
    local command="${1:-test}"
    local test_type="${2:-all}"
    local test_args="${@:3}"
    
    case "$command" in
        "help"|"--help"|"-h")
            show_help
            exit 0
            ;;
        "shell")
            log_info "进入交互式 shell..."
            exec /bin/bash
            ;;
        "test")
            # 验证环境
            verify_python_env
            
            # 网络检查
            if ! check_network; then
                log_warning "网络检查失败，但继续执行测试..."
            fi
            
            # 等待服务
            if ! wait_for_taskd; then
                log_error "TaskD 服务不可用，退出"
                exit 1
            fi
            
            # 运行测试
            if run_tests "$test_type" "$test_args"; then
                log_success "测试执行成功"
                exit_code=0
            else
                log_error "测试执行失败"
                exit_code=1
            fi
            
            # 生成报告
            generate_report
            
            # 清理
            cleanup
            
            exit $exit_code
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 设置信号处理
trap cleanup EXIT
trap 'log_info "接收到中断信号，正在清理..."; cleanup; exit 130' INT TERM

# 执行主函数
main "$@"