package business

import (
	"time"

	"gitlab.com/specific-ai/taskd/internal/models/common"
)

// CompanySummary 从web搜索结果中提取的企业信息结构
type CompanySummary struct {
	Name            string   `json:"name"`
	BusinessScope   string   `json:"businessScope"`
	Partnerships    []string `json:"partnerships"`
	MarketPosition  string   `json:"marketPosition"`
	RecentNews      []string `json:"recentNews"`
	TechnologyStack []string `json:"technologyStack"`
	KeyProducts     []string `json:"keyProducts"`
	Description     string   `json:"description"`
	GlobalPresence  string   `json:"globalPresence"`
	Website         string   `json:"website"`
	AnnualRevenue   string   `json:"annualRevenue"`
	Industry        string   `json:"industry"`
	EstablishedYear int      `json:"establishedYear"`
	Awards          []string `json:"awards"`
	Location        string   `json:"location"`
	CompanyScale    string   `json:"companyScale"`
	Certifications  []string `json:"certifications"`
	EmployeeCount   string   `json:"employeeCount"`
}

// WebSearchResult web搜索结果结构
type WebSearchResult struct {
	URL             string         `json:"url"`
	ID              string         `json:"id"`
	Title           string         `json:"title"`
	Score           float64        `json:"score"`
	PublishedDate   string         `json:"published_date"`
	Author          string         `json:"author"`
	Image           *string        `json:"image"`
	Favicon         *string        `json:"favicon"`
	Subpages        *string        `json:"subpages"`
	Extras          *string        `json:"extras"`
	Text            *string        `json:"text"`
	Highlights      *string        `json:"highlights"`
	HighlightScores *string        `json:"highlight_scores"`
	Summary         CompanySummary `json:"summary"`
}

// UserInputField 用户输入字段结构
type UserInputField struct {
	Field       string   `json:"field"`
	Description string   `json:"description"`
	Options     []string `json:"options"`
	UserInput   string   `json:"user_input"`
}

// CompanyProfileRequest 公司画像生成请求
type CompanyProfileRequest struct {
	// 用户上下文信息
	UserContext UserContext `json:"user_context" binding:"required"`

	// 核心请求参数
	CompanyName string `json:"company_name" binding:"required"`
	Language    string `json:"language,omitempty"` // 支持的语言: "chinese", "english", "japanese", 默认为"chinese"

	// 主要数据来源：web搜索结果和用户输入
	WebSearchResults []WebSearchResult `json:"web_search_results,omitempty"`
	UserInput        []UserInputField  `json:"user_input,omitempty"`
}

// UserContext 用户上下文信息
type UserContext struct {
	UserID         string `json:"user_id"`
	OrganizationID string `json:"organization_id"`
	RequestID      string `json:"request_id,omitempty"`
	Endpoint       string `json:"endpoint,omitempty"`
}

// CompanyProfileResponse 公司画像生成响应
type CompanyProfileResponse struct {
	UserID                    string                     `json:"user_id"`
	CompanyName               string                     `json:"company_name"`
	BusinessCapabilities      *BusinessCapabilities      `json:"business_capabilities"`
	TenderMatching            *TenderMatching            `json:"tender_matching"`
	CompetitiveProfile        *CompetitiveProfile        `json:"competitive_profile"`
	InternationalCapabilities *InternationalCapabilities `json:"international_capabilities"`
	RiskTolerance             *RiskTolerance             `json:"risk_tolerance"`
	ConfidenceScore           int                        `json:"confidence_score"`
	GeneratedAt               time.Time                  `json:"generated_at"`
	Metadata                  *ProfileMetadata           `json:"metadata"`
}

// BusinessCapabilities 业务能力画像
type BusinessCapabilities struct {
	CoreBusinessAreas     []string              `json:"core_business_areas"`
	ProductServiceTypes   []string              `json:"product_service_types"`
	TechnicalCapabilities []TechnicalCapability `json:"technical_capabilities"`
	BusinessScale         string                `json:"business_scale"`
	ProjectExperience     []ProjectExperience   `json:"project_experience"`
	Certifications        []string              `json:"certifications"`
}

// TechnicalCapability 技术能力
type TechnicalCapability struct {
	Name        string `json:"name"`
	Level       string `json:"level"` // advanced, intermediate, basic
	Description string `json:"description"`
}

// ProjectExperience 项目经验
type ProjectExperience struct {
	ProjectType string `json:"project_type"`
	Scale       string `json:"scale"`
	Count       int    `json:"count"`
	Description string `json:"description"`
}

// TenderMatching 招投标适配性
type TenderMatching struct {
	ProjectTypes       []string            `json:"project_types"`
	ProjectScale       []string            `json:"project_scale"`
	GeographicCoverage []string            `json:"geographic_coverage"`
	QualificationMatch []QualificationInfo `json:"qualification_match"`
	BiddingStrategy    string              `json:"bidding_strategy"`
	HistoricalWinRate  float64             `json:"historical_win_rate"`
}

// QualificationInfo 资质信息
type QualificationInfo struct {
	Type        string `json:"type"`
	Level       string `json:"level"`
	Description string `json:"description"`
}

// CompetitiveProfile 竞争力分析
type CompetitiveProfile struct {
	MarketPosition       string           `json:"market_position"`
	CoreAdvantages       []string         `json:"core_advantages"`
	Differentiators      []string         `json:"differentiators"`
	MainCompetitors      []CompetitorInfo `json:"main_competitors"`
	PriceCompetitiveness string           `json:"price_competitiveness"`
	MarketInfluence      string           `json:"market_influence"`
}

// CompetitorInfo 竞争对手信息
type CompetitorInfo struct {
	Name         string `json:"name"`
	Relationship string `json:"relationship"` // direct, indirect
	Advantage    string `json:"advantage"`
}

// InternationalCapabilities 海外业务能力
type InternationalCapabilities struct {
	OverseasMarkets             []OverseasMarket `json:"overseas_markets"`
	CrossBorderExperience       string           `json:"cross_border_experience"`
	InternationalCertifications []string         `json:"international_certifications"`
	PartnerNetwork              []PartnerInfo    `json:"partner_network"`
	LanguageCapabilities        []string         `json:"language_capabilities"`
	CulturalAdaptability        string           `json:"cultural_adaptability"`
}

// OverseasMarket 海外市场信息
type OverseasMarket struct {
	Region      string `json:"region"`
	Experience  string `json:"experience"`
	MarketShare string `json:"market_share"`
}

// PartnerInfo 合作伙伴信息
type PartnerInfo struct {
	Name        string `json:"name"`
	Region      string `json:"region"`
	Partnership string `json:"partnership"`
}

// RiskTolerance 风险承受能力
type RiskTolerance struct {
	PolicyRiskSensitivity  string             `json:"policy_risk_sensitivity"`
	ExchangeRateRisk       string             `json:"exchange_rate_risk"`
	ProjectCyclePreference []string           `json:"project_cycle_preference"`
	FundingCapability      *FundingCapability `json:"funding_capability"`
	RiskControlMechanisms  []string           `json:"risk_control_mechanisms"`
	InsuranceCoverage      []string           `json:"insurance_coverage"`
}

// FundingCapability 资金能力
type FundingCapability struct {
	CapitalScale      string   `json:"capital_scale"`
	FundingSources    []string `json:"funding_sources"`
	CashFlowStability string   `json:"cash_flow_stability"`
}

// ProfileMetadata 画像元数据
type ProfileMetadata struct {
	ModelUsed       string            `json:"model_used"`
	TokensUsed      common.TokenUsage `json:"tokens_used"`
	ProcessingTime  int64             `json:"processing_time_ms"`
	StrategyVersion string            `json:"strategy_version"`
	DataSources     []string          `json:"data_sources"`
}
