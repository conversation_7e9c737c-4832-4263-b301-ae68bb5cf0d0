apiVersion: v1
kind: ConfigMap
metadata:
  name: etcd-config
  namespace: etcd
  labels:
    app: etcd
data:
  # etcd配置参数
  ETCD_NAME: "etcd-single"
  ETCD_DATA_DIR: "/etcd-data"
  ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
  ETCD_ADVERTISE_CLIENT_URLS: "http://etcd-service.etcd.svc.cluster.local:2379"
  ETCD_LISTEN_PEER_URLS: "http://0.0.0.0:2380"
  ETCD_INITIAL_ADVERTISE_PEER_URLS: "http://etcd-service.etcd.svc.cluster.local:2380"
  ETCD_INITIAL_CLUSTER: "etcd-single=http://etcd-service.etcd.svc.cluster.local:2380"
  ETCD_INITIAL_CLUSTER_STATE: "new"
  ETCD_INITIAL_CLUSTER_TOKEN: "taskd-etcd-cluster"
  
  # 性能调优参数
  ETCD_HEARTBEAT_INTERVAL: "100"
  ETCD_ELECTION_TIMEOUT: "1000"
  ETCD_MAX_SNAPSHOTS: "5"
  ETCD_MAX_WALS: "5"
  ETCD_QUOTA_BACKEND_BYTES: "2147483648"  # 2GB
  
  # 日志配置
  ETCD_LOG_LEVEL: "warn"
  ETCD_LOGGER: "zap"
  ETCD_LOG_OUTPUTS: "stderr"
---