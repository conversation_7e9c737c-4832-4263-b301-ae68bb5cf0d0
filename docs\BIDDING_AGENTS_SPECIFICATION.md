# 招投标Agent规格说明

## 概述

本文档定义了招投标后处理系统的4个核心Agent，每个Agent负责处理流程中的特定步骤。

## Agent 1: 数据获取Agent

### Agent Card
```json
{
  "name": "Bidding Data Retrieval Agent",
  "description": "获取和预处理招投标数据，从MongoDB数据库中提取完整的招投标信息",
  "url": "http://taskd-service:8601/agents/bidding-data-retrieval", 
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "documentationUrl": "https://docs.taskd.platform/agents/bidding-data-retrieval",
  "capabilities": {
    "streaming": false,
    "pushNotifications": false,
    "stateTransitionHistory": true
  },
  "authentication": {
    "schemes": ["Bearer"]
  },
  "defaultInputModes": ["application/json"],
  "defaultOutputModes": ["application/json"],
  "skills": [
    {
      "id": "retrieve_tender_data",
      "name": "获取招投标数据",
      "description": "从MongoDB获取完整的招投标数据并进行预处理和验证",
      "tags": ["data", "mongodb", "tender", "retrieval"],
      "examples": [
        "获取招投标ID为TENDER_2024_001的完整数据",
        "检索指定招投标的中文版本数据"
      ],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "tender_id": {
            "type": "string",
            "description": "招投标唯一标识符",
            "example": "TENDER_2024_001"
          },
          "language": {
            "type": "string",
            "enum": ["chinese", "english"],
            "default": "chinese",
            "description": "数据语言偏好"
          },
          "include_metadata": {
            "type": "boolean",
            "default": true,
            "description": "是否包含元数据信息"
          },
          "fields": {
            "type": "array",
            "items": {"type": "string"},
            "description": "需要获取的字段列表，为空表示获取所有字段"
          }
        },
        "required": ["tender_id"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "tender_data": {
            "type": "object",
            "description": "完整的招投标数据"
          },
          "validation_status": {
            "type": "string",
            "enum": ["valid", "invalid", "partial"],
            "description": "数据验证状态"
          },
          "metadata": {
            "type": "object",
            "properties": {
              "source": {"type": "string"},
              "timestamp": {"type": "string"},
              "data_quality": {"type": "number"},
              "field_count": {"type": "integer"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 2: AI摘要Agent

### Agent Card
```json
{
  "name": "AI Summary Agent", 
  "description": "使用大语言模型生成招投标的智能摘要和结构化信息提取",
  "url": "http://taskd-service:8601/agents/ai-summary",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "capabilities": {
    "streaming": false,
    "pushNotifications": false,
    "stateTransitionHistory": true
  },
  "authentication": {
    "schemes": ["Bearer"]
  },
  "defaultInputModes": ["application/json"],
  "defaultOutputModes": ["application/json"],
  "skills": [
    {
      "id": "generate_tender_summary",
      "name": "生成招投标摘要",
      "description": "使用AI模型分析招投标内容，生成结构化摘要信息",
      "tags": ["ai", "llm", "summary", "analysis"],
      "examples": [
        "为政府采购项目生成详细摘要",
        "提取招投标的关键信息和截止日期"
      ],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "tender_data": {
            "type": "object",
            "description": "完整的招投标原始数据"
          },
          "language": {
            "type": "string",
            "enum": ["chinese", "english"],
            "default": "chinese",
            "description": "摘要语言"
          },
          "summary_type": {
            "type": "string",
            "enum": ["brief", "detailed", "executive"],
            "default": "detailed",
            "description": "摘要类型"
          },
          "focus_areas": {
            "type": "array",
            "items": {"type": "string"},
            "description": "重点关注的领域",
            "example": ["技术要求", "资质条件", "评分标准"]
          }
        },
        "required": ["tender_data"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "summary": {
            "type": "object",
            "properties": {
              "title": {
                "type": "string",
                "description": "原始标题"
              },
              "new_title": {
                "type": "string", 
                "description": "AI优化后的标题"
              },
              "deadline": {
                "type": "string",
                "description": "截止日期"
              },
              "summary_text": {
                "type": "string",
                "description": "摘要正文"
              },
              "key_requirements": {
                "type": "array",
                "items": {"type": "string"},
                "description": "关键要求列表"
              },
              "budget_info": {
                "type": "object",
                "description": "预算信息"
              }
            }
          },
          "confidence_score": {
            "type": "number",
            "minimum": 0,
            "maximum": 1,
            "description": "AI分析置信度"
          },
          "processing_metadata": {
            "type": "object",
            "properties": {
              "model_used": {"type": "string"},
              "tokens_consumed": {"type": "integer"},
              "processing_time": {"type": "number"}
            }
          }
        }
      }
    }
  ]
}
```

## Agent 3: 需求分析与搜索Agent

### Agent Card
```json
{
  "name": "Requirement Analysis and Search Agent",
  "description": "分析招投标需求并执行智能背景信息搜索",
  "url": "http://taskd-service:8601/agents/requirement-analysis-search",
  "provider": {
    "organization": "TaskD Platform", 
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "capabilities": {
    "streaming": false,
    "pushNotifications": false,
    "stateTransitionHistory": true
  },
  "authentication": {
    "schemes": ["Bearer"]
  },
  "defaultInputModes": ["application/json"],
  "defaultOutputModes": ["application/json"],
  "skills": [
    {
      "id": "analyze_and_search",
      "name": "需求分析与背景搜索",
      "description": "分析招投标需求并搜索相关背景信息、市场情况、技术趋势等",
      "tags": ["analysis", "search", "mcp", "background"],
      "examples": [
        "分析AI项目需求并搜索相关技术背景",
        "提取政府采购关键词并搜索市场信息"
      ],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "summary_data": {
            "type": "object",
            "description": "AI摘要Agent输出的摘要数据"
          },
          "search_depth": {
            "type": "string",
            "enum": ["shallow", "normal", "deep"],
            "default": "normal",
            "description": "搜索深度"
          },
          "search_domains": {
            "type": "array",
            "items": {"type": "string"},
            "description": "搜索领域限制",
            "example": ["technology", "market", "regulations"]
          },
          "max_results": {
            "type": "integer",
            "default": 10,
            "minimum": 1,
            "maximum": 50,
            "description": "最大搜索结果数量"
          }
        },
        "required": ["summary_data"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "requirement_analysis": {
            "type": "object",
            "properties": {
              "key_requirements": {
                "type": "array",
                "items": {"type": "string"}
              },
              "technical_keywords": {
                "type": "array", 
                "items": {"type": "string"}
              },
              "business_keywords": {
                "type": "array",
                "items": {"type": "string"}
              }
            }
          },
          "search_results": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "title": {"type": "string"},
                "url": {"type": "string"},
                "snippet": {"type": "string"},
                "source": {"type": "string"},
                "relevance_score": {"type": "number"},
                "published_date": {"type": "string"}
              }
            }
          },
          "search_summary": {
            "type": "string",
            "description": "搜索结果的简要总结"
          },
          "search_metadata": {
            "type": "object",
            "properties": {
              "total_results": {"type": "integer"},
              "search_time": {"type": "number"},
              "search_engines_used": {
                "type": "array",
                "items": {"type": "string"}
              }
            }
          }
        }
      }
    }
  ]
}
```

## Agent 4: 报告生成Agent

### Agent Card
```json
{
  "name": "Report Generation Agent",
  "description": "基于搜索结果和分析数据生成最终的招投标背景分析报告",
  "url": "http://taskd-service:8601/agents/report-generation",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "capabilities": {
    "streaming": false,
    "pushNotifications": false,
    "stateTransitionHistory": true
  },
  "authentication": {
    "schemes": ["Bearer"]
  },
  "defaultInputModes": ["application/json"],
  "defaultOutputModes": ["application/json"],
  "skills": [
    {
      "id": "generate_final_report",
      "name": "生成最终分析报告",
      "description": "整合所有分析数据，生成comprehensive的招投标背景分析报告",
      "tags": ["report", "analysis", "aggregation", "ai"],
      "examples": [
        "生成政府AI采购项目的完整背景分析报告",
        "整合市场信息生成投标建议报告"
      ],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "search_results": {
            "type": "array",
            "description": "需求分析Agent输出的搜索结果"
          },
          "summary_data": {
            "type": "object",
            "description": "AI摘要数据"
          },
          "requirement_analysis": {
            "type": "object",
            "description": "需求分析结果"
          },
          "report_template": {
            "type": "string",
            "enum": ["standard", "executive", "technical", "market_analysis"],
            "default": "standard",
            "description": "报告模板类型"
          },
          "focus_areas": {
            "type": "array",
            "items": {"type": "string"},
            "description": "报告重点关注领域"
          },
          "target_audience": {
            "type": "string",
            "enum": ["technical", "business", "executive", "general"],
            "default": "business",
            "description": "目标受众类型"
          }
        },
        "required": ["search_results", "summary_data", "requirement_analysis"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "final_report": {
            "type": "object",
            "properties": {
              "executive_summary": {
                "type": "string",
                "description": "执行摘要"
              },
              "project_overview": {
                "type": "string",
                "description": "项目概述"
              },
              "market_analysis": {
                "type": "string",
                "description": "市场分析"
              },
              "technical_background": {
                "type": "string", 
                "description": "技术背景"
              },
              "competitive_landscape": {
                "type": "string",
                "description": "竞争格局"
              },
              "recommendations": {
                "type": "array",
                "items": {"type": "string"},
                "description": "建议列表"
              },
              "risk_assessment": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "risk": {"type": "string"},
                    "impact": {"type": "string"},
                    "mitigation": {"type": "string"}
                  }
                }
              }
            }
          },
          "report_metadata": {
            "type": "object",
            "properties": {
              "generation_time": {"type": "string"},
              "word_count": {"type": "integer"},
              "confidence_score": {"type": "number"},
              "sources_count": {"type": "integer"},
              "report_version": {"type": "string"}
            }
          },
          "appendices": {
            "type": "object",
            "properties": {
              "source_references": {
                "type": "array",
                "items": {"type": "object"}
              },
              "data_tables": {
                "type": "array",
                "items": {"type": "object"}
              }
            }
          }
        }
      }
    }
  ]
}
```

## Agent通用规范

### 1. 所有Agent共同特性
- **标准HTTP接口**: 所有Agent都通过HTTP RESTful API提供服务
- **JSON-RPC 2.0**: 统一的消息协议格式
- **健康检查**: 提供`/health`端点
- **Agent Card**: 提供`/.well-known/agent.json`端点
- **统一认证**: Bearer Token认证方式
- **错误处理**: 统一的错误码和错误信息格式

### 2. 部署要求
- **容器化**: 所有Agent支持Docker容器部署
- **无状态**: Agent实例无状态，支持水平扩展
- **配置外部化**: 数据库连接、AI模型配置等通过环境变量注入
- **日志标准化**: 统一的结构化日志格式

### 3. 性能要求
- **响应时间**: 单个Agent处理时间 < 30秒
- **并发处理**: 支持至少10个并发请求
- **资源限制**: 内存使用 < 2GB per instance
- **超时保护**: 所有外部调用都有超时保护

### 4. 监控和可观测性
- **指标暴露**: 提供Prometheus格式的metrics端点
- **链路追踪**: 支持分布式链路追踪
- **结构化日志**: JSON格式的结构化日志输出
- **健康状态**: 详细的健康检查信息