#!/usr/bin/env python3

import json
import requests
import os
import uuid
import pytest

class TestAgentVisibility:
    """测试Agent在文档中的可见性"""
    
    def test_agents_docs_page(self):
        """测试Agent文档页面"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: Agent文档页面测试 ===")
        
        url = f"{base_url}/agents/docs"
        
        response = requests.get(url, timeout=30)
        print(f"请求: GET {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            print(f"Content-Type: {content_type}")
            print(f"响应长度: {len(response.text)}")
            
            # 检查是否包含意图识别和聊天Agent
            html_content = response.text
            
            if "intent-recognition-agent" in html_content:
                print("找到意图识别Agent")
            else:
                print("未找到意图识别Agent")
                
            if "chat-agent" in html_content:
                print("找到聊天Agent")  
            else:
                print("未找到聊天Agent")
                
            assert 'html' in content_type.lower(), "应该返回HTML内容"
        else:
            print(f"响应: {response.text}")
            if response.status_code not in [404, 501]:
                pytest.fail(f"Agent文档页面请求失败: {response.status_code}")
    
    def test_intent_agent_execution(self):
        """测试意图识别Agent执行"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 意图识别Agent执行测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "params": {
                "skill_id": "recognize_intent",
                "input": {
                    "text": "我想查看最新的招投标信息"
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': 'test_user_001',
            'X-Company-ID': 'test_company_001',
            'X-Language': 'chinese'
        }
        
        url = f"{base_url}/agents/execute/intent-recognition-agent"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"请求: POST {url}")
        print(f"请求头: {json.dumps(headers, ensure_ascii=False, indent=2)}")
        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            assert 'result' in data, "响应中应包含result字段"
        else:
            print(f"响应: {response.text}")
            if response.status_code not in [400, 404]:
                pytest.fail(f"意图识别Agent执行失败: {response.status_code}")
    
    def test_chat_agent_execution(self):
        """测试聊天Agent执行"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== P0: 聊天Agent执行测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "params": {
                "skill_id": "casual_chat",
                "input": {
                    "message": "你好"
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': 'test_user_001',
            'X-Company-ID': 'test_company_001',
            'X-Language': 'chinese'
        }
        
        url = f"{base_url}/agents/execute/chat-agent"
        
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        print(f"请求: POST {url}")
        print(f"请求头: {json.dumps(headers, ensure_ascii=False, indent=2)}")
        print(f"请求体: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            assert 'result' in data, "响应中应包含result字段"
        else:
            print(f"响应: {response.text}")
            if response.status_code not in [400, 404]:
                pytest.fail(f"聊天Agent执行失败: {response.status_code}")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])