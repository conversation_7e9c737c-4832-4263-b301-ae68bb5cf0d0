package test

import (
	"testing"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models/business"
)

// TestCompanyProfileRequestValidation 测试企业画像请求的数据验证功能
func TestCompanyProfileRequestValidation(t *testing.T) {
	// 测试空用户上下文
	req := &business.CompanyProfileRequest{
		CompanyName: "Test Company",
		Language:    "chinese",
	}
	if req.UserContext.UserID != "" {
		t.Error("Expected empty UserID in empty UserContext")
	}

	// 测试完整的请求
	req = &business.CompanyProfileRequest{
		UserContext: business.UserContext{
			UserID:         "user123",
			OrganizationID: "org123",
			RequestID:      "req123",
		},
		CompanyName: "Test Company",
		Language:    "chinese",
	}

	if req.UserContext.UserID == "" {
		t.Error("UserID should not be empty")
	}
	if req.CompanyName == "" {
		t.Error("CompanyName should not be empty")
	}
	if req.Language == "" {
		t.Error("Language should not be empty")
	}

	t.Logf("Profile request validation test passed")
}

// TestCreateSampleProfileRequest 创建示例企业画像请求
func TestCreateSampleProfileRequest(t *testing.T) {
	// 创建web搜索结果数据
	webSearchResults := []business.WebSearchResult{
		{
			URL:   "https://example.com",
			Title: "上海某某科技有限公司",
			Score: 0.9,
			Summary: business.CompanySummary{
				Name:            "上海某某科技有限公司",
				Industry:        "信息技术",
				BusinessScope:   "软件开发、AI技术服务",
				Location:        "上海市浦东新区",
				EstablishedYear: 2015,
				Description:     "专注于企业级AI解决方案的高新技术企业",
				Website:         "https://example.com",
				EmployeeCount:   "150人",
				AnnualRevenue:   "5000万人民币",
				Certifications:  []string{"ISO9001", "CMMI3"},
			},
		},
	}

	req := &business.CompanyProfileRequest{
		UserContext: business.UserContext{
			UserID:         "test-user-001",
			OrganizationID: "test-org-001",
			RequestID:      "test-req-001",
		},
		CompanyName:      "上海某某科技有限公司",
		Language:         "chinese",
		WebSearchResults: webSearchResults,
	}

	// 验证请求数据的完整性
	if req.UserContext.UserID == "" {
		t.Error("UserID should not be empty")
	}
	if req.CompanyName == "" {
		t.Error("CompanyName should not be empty")
	}
	if len(req.WebSearchResults) == 0 {
		t.Error("WebSearchResults should not be empty")
	}
	if req.WebSearchResults[0].Summary.EstablishedYear < 1900 || req.WebSearchResults[0].Summary.EstablishedYear > time.Now().Year() {
		t.Error("EstablishedYear should be valid")
	}

	t.Logf("Sample profile request created successfully: %+v", req)
}

// TestCreateSampleProfileResponse 创建示例企业画像响应
func TestCreateSampleProfileResponse(t *testing.T) {
	response := &business.CompanyProfileResponse{
		UserID:      "test-user-001",
		CompanyName: "上海某某科技有限公司",
		BusinessCapabilities: &business.BusinessCapabilities{
			CoreBusinessAreas:   []string{"软件开发", "AI技术服务", "数据分析"},
			ProductServiceTypes: []string{"企业级软件", "AI解决方案", "技术咨询"},
			BusinessScale:       "中型企业",
			Certifications:      []string{"ISO9001", "CMMI3", "高新技术企业"},
		},
		TenderMatching: &business.TenderMatching{
			ProjectTypes:       []string{"软件开发项目", "AI系统建设", "数字化转型"},
			ProjectScale:       []string{"中型项目", "大型项目"},
			GeographicCoverage: []string{"华东地区", "全国"},
			BiddingStrategy:    "技术优势导向，注重长期合作",
			HistoricalWinRate:  0.65,
		},
		CompetitiveProfile: &business.CompetitiveProfile{
			MarketPosition:       "细分领域领先",
			CoreAdvantages:       []string{"技术创新能力强", "团队经验丰富", "客户服务优质"},
			Differentiators:      []string{"专业的AI技术", "定制化解决方案", "快速响应能力"},
			PriceCompetitiveness: "中等偏上",
			MarketInfluence:      "区域性影响力",
		},
		InternationalCapabilities: &business.InternationalCapabilities{
			CrossBorderExperience:       "初级水平",
			InternationalCertifications: []string{},
			LanguageCapabilities:        []string{"中文", "英语"},
			CulturalAdaptability:        "需要提升",
		},
		RiskTolerance: &business.RiskTolerance{
			PolicyRiskSensitivity:  "中",
			ExchangeRateRisk:       "低",
			ProjectCyclePreference: []string{"中期", "长期"},
			FundingCapability: &business.FundingCapability{
				CapitalScale:      "中等",
				FundingSources:    []string{"自有资金", "银行贷款"},
				CashFlowStability: "稳定",
			},
			RiskControlMechanisms: []string{"项目风险评估", "定期财务审查"},
			InsuranceCoverage:     []string{"企业财产险", "职业责任险"},
		},
		ConfidenceScore: 85,
		GeneratedAt:     time.Now(),
		Metadata: &business.ProfileMetadata{
			ModelUsed:       "company_profile_model_structured",
			StrategyVersion: "v2.0.0",
			ProcessingTime:  1500, // 1.5秒
			DataSources:     []string{"llm_structured_analysis", "company_info", "json_schema_validation"},
		},
	}

	// 验证响应数据的完整性
	if response.UserID == "" {
		t.Error("UserID should not be empty")
	}
	if response.CompanyName == "" {
		t.Error("CompanyName should not be empty")
	}
	if response.BusinessCapabilities == nil {
		t.Error("BusinessCapabilities should not be nil")
	}
	if response.TenderMatching == nil {
		t.Error("TenderMatching should not be nil")
	}
	if response.CompetitiveProfile == nil {
		t.Error("CompetitiveProfile should not be nil")
	}
	if response.InternationalCapabilities == nil {
		t.Error("InternationalCapabilities should not be nil")
	}
	if response.RiskTolerance == nil {
		t.Error("RiskTolerance should not be nil")
	}
	if response.ConfidenceScore < 0 || response.ConfidenceScore > 100 {
		t.Error("ConfidenceScore should be between 0 and 100")
	}
	if response.Metadata == nil {
		t.Error("Metadata should not be nil")
	}

	t.Logf("Sample profile response created successfully with confidence score: %d", response.ConfidenceScore)
}

// BenchmarkProfileDataSerialization 基准测试：企业画像数据序列化性能
func BenchmarkProfileDataSerialization(b *testing.B) {
	// 创建测试数据
	response := &business.CompanyProfileResponse{
		UserID:      "bench-user",
		CompanyName: "Benchmark Company",
		BusinessCapabilities: &business.BusinessCapabilities{
			CoreBusinessAreas:   []string{"Area1", "Area2", "Area3"},
			ProductServiceTypes: []string{"Service1", "Service2"},
			BusinessScale:       "大型企业",
			Certifications:      []string{"Cert1", "Cert2", "Cert3"},
		},
		ConfidenceScore: 90,
		GeneratedAt:     time.Now(),
		Metadata: &business.ProfileMetadata{
			ModelUsed:      "test_model",
			ProcessingTime: 1000,
		},
	}

	// 运行基准测试
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 模拟序列化过程 (在实际存储服务中会用到)
		_ = response.UserID + response.CompanyName
		_ = len(response.BusinessCapabilities.CoreBusinessAreas)
	}
}
