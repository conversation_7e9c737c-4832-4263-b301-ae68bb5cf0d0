package profile

import (
	"context"
	"fmt"

	"gitlab.com/specific-ai/taskd/internal/models"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/agents"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/interfaces"
	profileModels "gitlab.com/specific-ai/taskd/internal/modules/profile/models"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// AgentRegistry Profile模块Agent注册器
type AgentRegistry struct {
	profileAgent   *agents.ProfileAgent
	profileService services.ProfileServiceInterface
}

// NewAgentRegistry 创建Profile模块Agent注册器
func NewAgentRegistry(profileService services.ProfileServiceInterface) *AgentRegistry {
	return &AgentRegistry{
		profileAgent:   agents.NewProfileAgent(profileService),
		profileService: profileService,
	}
}

// RegisterAgents 注册所有Profile相关的Agent
func (r *AgentRegistry) RegisterAgents() map[string]interfaces.ProfileAgent {
	agents := make(map[string]interfaces.ProfileAgent)
	agents[agents.AgentID] = r.profileAgent
	return agents
}

// GetProfileAgent 获取企业画像Agent
func (r *AgentRegistry) GetProfileAgent() interfaces.ProfileAgent {
	return r.profileAgent
}

// ExecuteProfileGeneration 执行企业画像生成
func (r *AgentRegistry) ExecuteProfileGeneration(ctx context.Context, req *models.AgentExecutionRequest) (*models.AgentResponse, error) {
	// 验证能力
	if req.Capability != agents.SkillGenerateCompanyProfile {
		return nil, fmt.Errorf("Profile Agent只支持 %s 能力，收到: %s", agents.SkillGenerateCompanyProfile, req.Capability)
	}

	utils.Log.Infof("开始执行企业画像生成，RequestID: %s, UserID: %s", req.RequestID, req.UserID)

	// 构建A2A上下文
	a2aContext := biddingModels.A2AContext{
		RequestID: req.RequestID,
		UserID:    req.UserID,
		Timestamp: req.CreatedAt,
	}

	// 执行Agent技能
	result, err := r.profileAgent.ExecuteSkill(agents.SkillGenerateCompanyProfile, req.Input, a2aContext)
	if err != nil {
		utils.Log.Errorf("企业画像生成失败: %v", err)
		return &models.AgentResponse{
			ID:        fmt.Sprintf("resp_%d", req.CreatedAt.Unix()),
			RequestID: req.RequestID,
			Success:   false,
			Error:     fmt.Sprintf("企业画像生成失败: %v", err),
			Duration:  0,
			CreatedAt: req.CreatedAt,
		}, nil
	}

	// 转换A2A结果为Agent响应
	response := &models.AgentResponse{
		ID:        fmt.Sprintf("resp_%d", req.CreatedAt.Unix()),
		RequestID: req.RequestID,
		Success:   result.Status == "completed",
		Output:    result.Output,
		Duration:  result.Metadata.ExecutionTime,
		CreatedAt: req.CreatedAt,
	}

	if result.Status != "completed" {
		response.Error = fmt.Sprintf("执行状态异常: %s", result.Status)
	}

	utils.Log.Infof("企业画像生成完成，TaskID: %s, 状态: %s", result.TaskID, result.Status)

	return response, nil
}

// GenerateCompanyProfile 生成企业画像（便捷方法）
func (r *AgentRegistry) GenerateCompanyProfile(req *profileModels.ProfileRequest) (*profileModels.ProfileResponse, error) {
	return r.profileService.GenerateCompanyProfile(req)
}

// GetAgentStats 获取Agent统计信息
func (r *AgentRegistry) GetAgentStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	// 获取Agent基本信息
	agentCard := r.profileAgent.GetAgentCard()
	stats["agent_info"] = map[string]interface{}{
		"name":        agentCard.Name,
		"version":     agentCard.Version,
		"description": agentCard.Description,
		"skills":      len(agentCard.Skills),
	}

	// 获取健康状态
	healthStatus := r.profileAgent.HealthCheck()
	stats["health"] = map[string]interface{}{
		"status":     healthStatus.Status,
		"uptime":     healthStatus.Uptime,
		"timestamp":  healthStatus.Timestamp,
	}

	// 获取性能指标
	if healthStatus.Performance != nil {
		stats["performance"] = map[string]interface{}{
			"avg_response_time":   healthStatus.Performance.AvgResponseTime,
			"requests_per_minute": healthStatus.Performance.RequestsPerMinute,
			"error_rate":          healthStatus.Performance.ErrorRate,
			"success_rate":        healthStatus.Performance.SuccessRate,
		}
	}

	return stats
}

// ListAgents 列出所有注册的Agent
func (r *AgentRegistry) ListAgents() []biddingModels.RegisteredAgent {
	agentCard := r.profileAgent.GetAgentCard()
	healthStatus := r.profileAgent.HealthCheck()

	return []biddingModels.RegisteredAgent{
		{
			ID:           r.profileAgent.GetID(),
			Name:         agentCard.Name,
			Description:  agentCard.Description,
			Version:      agentCard.Version,
			URL:          agentCard.URL,
			Status:       string(healthStatus.Status),
			Skills:       agentCard.Skills,
			RegisteredAt: healthStatus.Timestamp,
			LastSeen:     healthStatus.Timestamp,
		},
	}
}

// HealthCheck 执行健康检查
func (r *AgentRegistry) HealthCheck() map[string]interface{} {
	healthStatus := r.profileAgent.HealthCheck()
	
	return map[string]interface{}{
		"agent_id":    r.profileAgent.GetID(),
		"status":      healthStatus.Status,
		"version":     healthStatus.Version,
		"uptime":      healthStatus.Uptime,
		"timestamp":   healthStatus.Timestamp,
		"performance": healthStatus.Performance,
		"dependencies": healthStatus.Dependencies,
	}
}

// GetSupportedSkills 获取支持的技能列表
func (r *AgentRegistry) GetSupportedSkills() []biddingModels.AgentSkill {
	agentCard := r.profileAgent.GetAgentCard()
	return agentCard.Skills
}

// ValidateInput 验证输入参数
func (r *AgentRegistry) ValidateInput(skillID string, input map[string]interface{}) error {
	agentCard := r.profileAgent.GetAgentCard()
	
	// 查找对应的技能
	var targetSkill *biddingModels.AgentSkill
	for _, skill := range agentCard.Skills {
		if skill.ID == skillID {
			targetSkill = &skill
			break
		}
	}
	
	if targetSkill == nil {
		return fmt.Errorf("技能不存在: %s", skillID)
	}
	
	// 这里可以添加更详细的输入验证逻辑
	// 目前简单检查必需字段
	if skillID == agents.SkillGenerateCompanyProfile {
		if companyName, ok := input["company_name"].(string); !ok || companyName == "" {
			return fmt.Errorf("company_name 字段必需且不能为空")
		}
		if industry, ok := input["industry"].(string); !ok || industry == "" {
			return fmt.Errorf("industry 字段必需且不能为空")
		}
		if userContext, ok := input["user_context"].(map[string]interface{}); !ok {
			return fmt.Errorf("user_context 字段必需")
		} else {
			if userID, ok := userContext["user_id"].(string); !ok || userID == "" {
				return fmt.Errorf("user_context.user_id 字段必需且不能为空")
			}
		}
	}
	
	return nil
}

// RegisterToGlobalRegistry 注册到全局Agent注册表
func (r *AgentRegistry) RegisterToGlobalRegistry(globalRegistry interface{}) error {
	// 这里可以实现将Profile Agent注册到全局Agent注册表的逻辑
	// 具体实现取决于全局注册表的接口设计
	utils.Log.Infof("Profile Agent已注册到全局注册表: %s", r.profileAgent.GetID())
	return nil
}

// UnregisterFromGlobalRegistry 从全局Agent注册表注销
func (r *AgentRegistry) UnregisterFromGlobalRegistry(globalRegistry interface{}) error {
	// 这里可以实现从全局Agent注册表注销Profile Agent的逻辑
	utils.Log.Infof("Profile Agent已从全局注册表注销: %s", r.profileAgent.GetID())
	return nil
}
