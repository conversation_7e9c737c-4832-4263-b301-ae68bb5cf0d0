#!/bin/bash

# TaskD 简单测试运行脚本
# 使用 docker run 启动测试容器连接到本地 TaskD 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
TASKD_BASE_URL="${TASKD_BASE_URL:-http://192.168.50.254:8601}"
TEST_IMAGE="taskd-test:latest"
CONTAINER_NAME="taskd-test-runner"
DOCKER_SIZE="${DOCKER_SIZE:-optimized}"  # original, optimized, ultra-slim

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
TaskD 简单测试运行脚本

用法:
  $0 [命令] [选项]

命令:
  build              构建测试镜像
  test [类型]        运行测试 (默认: all)
  shell              进入测试容器 shell
  clean              清理容器和镜像
  help               显示帮助信息

测试类型:
  all                运行所有测试 (默认)
  p0, p1, p2, p3, p4  按优先级运行
  bidding            招投标模块
  entity             实体提取模块
  token              Token 管理
  concurrent         并发控制
  chat               聊天模块
  preprocessing      预处理模块
  intent             意图识别
  agent              Agent 模块

选项:
  --taskd-url URL    TaskD服务地址 (默认: http://192.168.50.254:8601)
  --verbose          显示详细输出
  --no-cache         构建时不使用缓存
  --size TYPE        镜像大小类型 (original/optimized/ultra-slim，默认: optimized)

环境变量:
  TASKD_BASE_URL     TaskD服务地址
  TEST_VERBOSE       是否显示详细输出 (true/false)
  DOCKER_SIZE        镜像大小类型 (original/optimized/ultra-slim)

示例:
  $0 build                                    # 构建测试镜像
  $0 test                                     # 运行所有测试
  $0 test p0                                  # 运行 P0 测试
  $0 test bidding --verbose                   # 运行招投标测试，显示详细输出
  $0 test --taskd-url http://10.0.0.1:8601   # 指定TaskD服务地址
  $0 shell                                    # 进入测试容器调试
  $0 clean                                    # 清理环境

EOF
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    log_success "Docker 检查通过"
}

# 检查TaskD服务连通性
check_taskd_connectivity() {
    log_info "检查 TaskD 服务连通性: $TASKD_BASE_URL"
    
    # 提取主机和端口
    local host=$(echo $TASKD_BASE_URL | sed -e 's|http[s]*://||' -e 's|:.*||')
    local port=$(echo $TASKD_BASE_URL | sed -e 's|.*:||' -e 's|/.*||')
    
    # 端口连通性检查
    if timeout 10 nc -z $host $port 2>/dev/null; then
        log_success "端口连通性检查通过: $host:$port"
    else
        log_error "无法连接到 TaskD 服务: $host:$port"
        log_error "请确保:"
        log_error "1. TaskD 服务在 Windows 机器上正常运行"
        log_error "2. 防火墙允许 $port 端口访问"
        exit 1
    fi
    
    # HTTP 健康检查
    if timeout 10 curl -sf "$TASKD_BASE_URL/healthz" >/dev/null 2>&1; then
        log_success "TaskD 服务健康检查通过"
    else
        log_warning "TaskD 服务端口可达，但健康检查失败"
        log_warning "服务可能还在启动中，继续执行测试..."
    fi
}

# 构建测试镜像
build_image() {
    log_info "构建测试镜像: $TEST_IMAGE (Size: $DOCKER_SIZE)"
    
    cd "$PROJECT_ROOT"
    
    local build_args=""
    if [ "$NO_CACHE" = "true" ]; then
        build_args="--no-cache"
    fi
    
    # 选择合适的 Dockerfile
    local dockerfile=""
    case "$DOCKER_SIZE" in
        "original")
            dockerfile="docker/test/Dockerfile"
            ;;
        "optimized")
            dockerfile="docker/test/Dockerfile.optimized"
            ;;
        "ultra-slim")
            dockerfile="docker/test/Dockerfile.ultra-slim"
            ;;
        *)
            log_error "不支持的镜像大小类型: $DOCKER_SIZE"
            log_error "支持的类型: original, optimized, ultra-slim"
            exit 1
            ;;
    esac
    
    log_info "使用 Dockerfile: $dockerfile"
    
    # 构建镜像
    docker build $build_args -t $TEST_IMAGE -f "$dockerfile" .
    
    log_success "测试镜像构建完成 ($DOCKER_SIZE 版本)"
}

# 停止并删除现有容器
cleanup_container() {
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_info "停止并删除现有容器: $CONTAINER_NAME"
        docker stop $CONTAINER_NAME >/dev/null 2>&1 || true
        docker rm $CONTAINER_NAME >/dev/null 2>&1 || true
    fi
}

# 运行测试
run_tests() {
    local test_type="${1:-all}"
    
    log_info "运行测试: $test_type"
    
    # 清理现有容器
    cleanup_container
    
    # 设置环境变量
    local env_vars=(
        "-e" "TASKD_BASE_URL=$TASKD_BASE_URL"
        "-e" "TASKD_TIMEOUT=120"
        "-e" "TEST_VERBOSE=${TEST_VERBOSE:-true}"
        "-e" "TEST_DATA_PREFIX=ubuntu_test_"
        "-e" "PYTHONPATH=/app"
    )
    
    # 设置卷挂载
    local volumes=(
        "-v" "$PROJECT_ROOT/test/logs:/app/test/logs"
        "-v" "$PROJECT_ROOT/test/reports:/app/test/reports"
    )
    
    # 创建日志目录
    mkdir -p "$PROJECT_ROOT/test/logs" "$PROJECT_ROOT/test/reports"
    
    log_info "启动测试容器..."
    log_info "连接到 TaskD 服务: $TASKD_BASE_URL"
    
    # 运行测试容器
    docker run --rm \
        --name $CONTAINER_NAME \
        --network host \
        "${env_vars[@]}" \
        "${volumes[@]}" \
        $TEST_IMAGE \
        test "$test_type"
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "测试完成: $test_type"
    else
        log_error "测试失败: $test_type"
    fi
    
    return $exit_code
}

# 进入测试容器shell
enter_shell() {
    log_info "进入测试容器 shell..."
    
    # 清理现有容器
    cleanup_container
    
    # 设置环境变量
    local env_vars=(
        "-e" "TASKD_BASE_URL=$TASKD_BASE_URL"
        "-e" "TASKD_TIMEOUT=120"
        "-e" "TEST_VERBOSE=true"
        "-e" "PYTHONPATH=/app"
    )
    
    # 设置卷挂载
    local volumes=(
        "-v" "$PROJECT_ROOT/test/logs:/app/test/logs"
        "-v" "$PROJECT_ROOT/test/reports:/app/test/reports"
    )
    
    # 创建日志目录
    mkdir -p "$PROJECT_ROOT/test/logs" "$PROJECT_ROOT/test/reports"
    
    # 运行交互式容器
    docker run -it --rm \
        --name $CONTAINER_NAME \
        --network host \
        "${env_vars[@]}" \
        "${volumes[@]}" \
        $TEST_IMAGE \
        shell
}

# 清理环境
clean_environment() {
    log_info "清理 Docker 环境..."
    
    # 停止并删除容器
    cleanup_container
    
    # 删除镜像
    if docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${TEST_IMAGE}$"; then
        log_info "删除测试镜像: $TEST_IMAGE"
        docker rmi $TEST_IMAGE 2>/dev/null || true
    fi
    
    # 清理无用的镜像和容器
    docker system prune -f >/dev/null 2>&1 || true
    
    log_success "环境清理完成"
}

# 主函数
main() {
    
    # 解析选项
    while [[ $# -gt 0 ]]; do
        case $1 in
            --taskd-url)
                shift
                TASKD_BASE_URL="$1"
                shift
                ;;
            --verbose)
                TEST_VERBOSE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --size)
                shift
                DOCKER_SIZE="$1"
                shift
                ;;
            -*)
                # 跳过未知选项
                shift
                ;;
            *)
                # 这是命令或测试类型，停止解析选项
                break
                ;;
        esac
    done
    
    # 重新设置位置参数
    local command="${1:-help}"
    local test_type="${2:-all}"
    
    echo -e "${BLUE}=== TaskD 简单测试环境 ===${NC}"
    echo "TaskD 服务地址: $TASKD_BASE_URL"
    echo "测试镜像: $TEST_IMAGE"
    echo "镜像大小类型: $DOCKER_SIZE"
    echo ""
    
    case "$command" in
        "build")
            check_docker
            build_image
            ;;
        "test")
            check_docker
            check_taskd_connectivity
            
            # 检查镜像是否存在
            if ! docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${TEST_IMAGE}$"; then
                log_info "测试镜像不存在，开始构建..."
                build_image
            fi
            
            run_tests "$test_type"
            ;;
        "shell")
            check_docker
            check_taskd_connectivity
            
            # 检查镜像是否存在
            if ! docker images --format '{{.Repository}}:{{.Tag}}' | grep -q "^${TEST_IMAGE}$"; then
                log_info "测试镜像不存在，开始构建..."
                build_image
            fi
            
            enter_shell
            ;;
        "clean")
            check_docker
            clean_environment
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"