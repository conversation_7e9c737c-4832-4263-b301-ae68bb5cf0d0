package handlers

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ConcurrentHandler 并发控制API处理器
type ConcurrentHandler struct {
	concurrentService services.EnhancedConcurrentService
}

// NewConcurrentHandler 创建并发控制处理器
func NewConcurrentHandler(concurrentService services.EnhancedConcurrentService) *ConcurrentHandler {
	return &ConcurrentHandler{
		concurrentService: concurrentService,
	}
}

// SubmitLLMRequest 提交LLM请求
func (h *ConcurrentHandler) SubmitLLMRequest(c *gin.Context) {
	var req LLMRequestDTO
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		utils.Log.Errorf("Failed to bind LLM request: %v", err)
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"message": err.Error(),
		})
		return
	}

	// 验证必填字段
	if req.UserID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Missing required field",
			"message": "user_id is required",
		})
		return
	}

	if req.CompanyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Missing required field",
			"message": "company_id is required",
		})
		return
	}

	if len(req.Messages) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Missing required field",
			"message": "messages is required",
		})
		return
	}

	// 构建请求消息
	llmReq := &models.LLMRequestMessage{
		ID:        uuid.New().String(),
		UserID:    req.UserID,
		CompanyID: req.CompanyID,
		Params: models.OpenAICompatibleRequestParams{
			Model:           req.Model,
			Messages:        req.Messages,
			Temperature:     req.Temperature,
			MaxTokens:       req.MaxTokens,
			AutoTrackTokens: true, // 自动启用token跟踪
			UserContext: &models.UserContext{
				UserID:    req.UserID,
				CompanyID: req.CompanyID,
				RequestID: req.RequestID,
				Endpoint:  c.Request.URL.Path,
			},
		},
		CreatedAt: time.Now(),
	}

	// 设置超时时间
	if req.TimeoutSeconds > 0 {
		llmReq.MaxWaitTime = time.Duration(req.TimeoutSeconds) * time.Second
	}

	// 设置优先级
	if req.Priority != "" {
		llmReq.UserPriority = models.UserPriority(req.Priority)
		llmReq.Priority = models.GetPriorityFromUser(llmReq.UserPriority)
	}

	// 创建请求上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), llmReq.MaxWaitTime)
	defer cancel()

	// 提交请求
	startTime := time.Now()
	response, err := h.concurrentService.SubmitRequest(ctx, llmReq)
	duration := time.Since(startTime)

	if err != nil {
		utils.Log.Errorf("LLM request failed: %v", err)

		// 根据错误类型返回不同的状态码
		statusCode := http.StatusInternalServerError
		if strings.Contains(err.Error(), "token limit") {
			statusCode = http.StatusForbidden
		} else if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "queue full") {
			statusCode = http.StatusTooManyRequests
		} else if strings.Contains(err.Error(), "timeout") {
			statusCode = http.StatusRequestTimeout
		}

		c.JSON(statusCode, gin.H{
			"error":   "LLM request failed",
			"message": err.Error(),
		})
		return
	}

	// 返回成功响应
	utils.Log.Infof("LLM request completed in %v for user %s", duration, req.UserID)
	c.JSON(http.StatusOK, LLMResponseDTO{
		ID:         response.ID,
		Success:    response.Success,
		Result:     response.Result,
		Error:      response.Error,
		TokenUsage: response.TokenUsage,
		Duration:   duration.Milliseconds(),
		RetryCount: response.RetryCount,
	})
}

// GetQueueStats 获取队列统计信息
func (h *ConcurrentHandler) GetQueueStats(c *gin.Context) {
	stats := h.concurrentService.GetStats()
	c.JSON(http.StatusOK, stats)
}

// LLMRequestDTO LLM请求DTO
type LLMRequestDTO struct {
	UserID         string              `json:"user_id" binding:"required"`
	CompanyID      string              `json:"company_id" binding:"required"`
	Model          string              `json:"model"`
	Messages       []common.LLMMessage `json:"messages" binding:"required"`
	Temperature    *float64            `json:"temperature,omitempty"`
	MaxTokens      *int                `json:"max_tokens,omitempty"`
	RequestID      string              `json:"request_id,omitempty"`
	Priority       string              `json:"priority,omitempty"` // vip, normal, free
	TimeoutSeconds int                 `json:"timeout_seconds,omitempty"`
}

// LLMResponseDTO LLM响应DTO
type LLMResponseDTO struct {
	ID         string            `json:"id"`
	Success    bool              `json:"success"`
	Result     string            `json:"result,omitempty"`
	Error      string            `json:"error,omitempty"`
	TokenUsage common.TokenUsage `json:"token_usage"`
	Duration   int64             `json:"duration_ms"`
	RetryCount int               `json:"retry_count"`
}

// QueueHealthDTO 队列健康状态DTO
type QueueHealthDTO struct {
	Status           string    `json:"status"`
	QueueUtilization float64   `json:"queue_utilization"`
	ErrorRate        float64   `json:"error_rate"`
	ActiveWorkers    int       `json:"active_workers"`
	ProcessingCount  int       `json:"processing_count"`
	TotalProcessed   int64     `json:"total_processed"`
	TotalErrors      int64     `json:"total_errors"`
	Timestamp        time.Time `json:"timestamp"`
}
