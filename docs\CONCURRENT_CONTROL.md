# 并发控制模块

## 概述

TaskD并发控制模块提供了企业级的LLM请求并发管理、队列控制、重试机制和优先级处理功能。该模块支持高达1000并发的火山模型调用，具备完善的限流、重试和监控机制。

## 功能特性

### 🚀 核心功能
- ✅ **高并发处理**: 支持最高1000并发请求
- ✅ **智能队列管理**: 1000个请求队列缓冲，支持429限流
- ✅ **优先级队列**: VIP > 普通 > 免费用户优先级处理
- ✅ **自动重试机制**: 指数退避策略，最多重试3次
- ✅ **Token集成**: 先验证Token限额，再进行并发控制
- ✅ **Pulsar消息队列**: 支持持久化队列和分布式处理
- ✅ **实时监控**: 完整的队列统计和健康状态监控

### 🔧 技术特性
- ✅ **双队列架构**: 内存队列 + Pulsar消息队列
- ✅ **多API Key支持**: API Key池轮询（预留设计）
- ✅ **超时控制**: 单次请求最大等待时间180秒（可配置）
- ✅ **状态码标准**: 429限流、408超时、403token限额等
- ✅ **优雅降级**: Pulsar不可用时自动回退到内存队列

## 架构设计

### 系统架构图

```
                    ┌─────────────────┐
                    │   Client API    │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │ ConcurrentHandler│
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │ Token Validation │
                    └─────────┬───────┘
                              │
            ┌─────────────────▼─────────────────┐
            │       Enhanced Concurrent         │
            │         Service                   │
            └─┬─────────────────────────────────┘
              │
    ┌─────────▼─────────┐    ┌─────────▼─────────┐
    │   Memory Queues   │    │  Pulsar Queues    │
    │  ┌─────────────┐  │    │ ┌───────────────┐ │
    │  │ High Priority│  │    │ │ Main Topic    │ │
    │  ├─────────────┤  │    │ ├───────────────┤ │
    │  │ Med Priority │  │    │ │ Retry Topic   │ │
    │  ├─────────────┤  │    │ └───────────────┘ │
    │  │ Low Priority │  │    └───────────────────┘
    │  └─────────────┘  │
    └───────────────────┘
              │
    ┌─────────▼─────────┐
    │   Worker Pool     │
    │  ┌─────────────┐  │
    │  │ Worker 1    │  │
    │  ├─────────────┤  │
    │  │ Worker 2    │  │
    │  ├─────────────┤  │
    │  │ ...         │  │
    │  ├─────────────┤  │
    │  │ Worker N    │  │
    │  └─────────────┘  │
    └─────────┬─────────┘
              │
    ┌─────────▼─────────┐
    │   LLM Service     │
    │ ┌───────────────┐ │
    │ │ Volcengine    │ │
    │ │ OpenAI        │ │
    │ │ Others        │ │
    │ └───────────────┘ │
    └───────────────────┘
```

### 核心组件

#### 1. EnhancedConcurrentService
- **功能**: 并发控制的核心服务
- **职责**: 请求调度、队列管理、工作线程池管理
- **特性**: 支持内存队列和Pulsar队列双模式

#### 2. PulsarQueueManager
- **功能**: Pulsar消息队列管理
- **职责**: 消息发送、消费、重试队列管理
- **特性**: 支持延迟消息、批量处理、自动重试

#### 3. 优先级队列系统
- **高优先级**: VIP用户请求
- **中优先级**: 普通用户请求  
- **低优先级**: 免费用户请求
- **重试队列**: 失败请求的重试处理

#### 4. Worker Pool
- **并发控制**: 基于semaphore的并发限制
- **工作模式**: 多协程并行处理
- **负载均衡**: 按优先级轮询处理

## 配置说明

### 配置文件示例

```yaml
# 并发控制配置
concurrent:
  max_concurrent_requests: 1000    # 最大并发请求数
  main_queue_size: 1000           # 主队列大小
  retry_queue_size: 200           # 重试队列大小
  max_wait_time_seconds: 180      # 最大等待时间
  max_retries: 3                  # 最大重试次数
  retry_base_delay_seconds: 2     # 重试基础延迟
  worker_count: 20                # 工作协程数
  pulsar_topic: "persistent://public/default/llm-requests"
  pulsar_retry_topic: "persistent://public/default/llm-retries"
  pulsar_subscription: "llm-processor"
  enable_priority_queue: true     # 启用优先级队列
  reject_free_user_when_busy: true # 繁忙时拒绝免费用户

# 生产环境配置
server:
  mode: "release"  # 自动调整为生产参数

# 测试环境配置  
server:
  mode: "debug"    # 自动调整为测试参数（并发100，队列100）
```

### 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_concurrent_requests | int | 1000 | 最大并发处理请求数 |
| main_queue_size | int | 1000 | 主队列缓冲大小 |
| retry_queue_size | int | 200 | 重试队列大小 |
| max_wait_time_seconds | int | 180 | 单次请求最大等待时间 |
| max_retries | int | 3 | 最大重试次数 |
| retry_base_delay_seconds | int | 2 | 重试基础延迟（指数退避基数） |
| worker_count | int | 20 | 工作协程数量 |
| enable_priority_queue | bool | true | 是否启用优先级队列 |
| reject_free_user_when_busy | bool | true | 繁忙时是否拒绝免费用户 |

## API 接口

### 1. 提交LLM请求

```http
POST /v1/concurrent/submit
Content-Type: application/json

{
  "user_id": "user123",
  "company_id": "company123",
  "model": "doubao-1-5-pro-32k-250115",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "priority": "normal",           // vip, normal, free
  "timeout_seconds": 60,
  "request_id": "req_123456"      // 可选
}
```

**响应示例**:
```json
{
  "id": "req_123456",
  "success": true,
  "result": "I'm doing well, thank you! How can I help you today?",
  "token_usage": {
    "prompt_tokens": 12,
    "completion_tokens": 15,
    "total_tokens": 27
  },
  "duration_ms": 1250,
  "retry_count": 0
}
```

**错误响应**:
```json
{
  "error": "Token limit exceeded",
  "message": "Monthly token limit exceeded"
}
```

### 2. 获取队列统计

```http
GET /v1/concurrent/stats
```

**响应示例**:
```json
{
  "main_queue_size": 45,
  "retry_queue_size": 3,
  "processing_count": 12,
  "active_workers": 20,
  "priority_stats": {
    "1": 15,  // 高优先级
    "2": 25,  // 中优先级
    "3": 5    // 低优先级
  },
  "user_priority_stats": {
    "vip": 15,
    "normal": 25,
    "free": 5
  },
  "total_processed": 1250,
  "total_errors": 23,
  "total_retries": 45
}
```

### 3. 获取队列健康状态

```http
GET /v1/concurrent/health
```

**响应示例**:
```json
{
  "status": "healthy",           // healthy, degraded, unhealthy
  "queue_utilization": 0.045,    // 队列使用率 (0-1)
  "error_rate": 0.018,          // 错误率 (0-1)
  "active_workers": 20,
  "processing_count": 12,
  "total_processed": 1250,
  "total_errors": 23,
  "timestamp": "2024-07-08T10:30:00Z"
}
```

### 4. 高级LLM接口

支持自动并发控制的简化接口：

```http
POST /v1/llm/chat
POST /v1/llm/summarize
```

使用相同的请求格式，但会自动应用并发控制。

## 优先级队列机制

### 用户优先级映射

| 用户类型 | 优先级等级 | 队列优先级 | 特殊权限 |
|----------|-----------|-----------|----------|
| VIP用户 | vip | 1 (最高) | 繁忙时不被拒绝 |
| 普通用户 | normal | 2 (中等) | 正常处理 |
| 免费用户 | free | 3 (最低) | 繁忙时可能被拒绝 |

### 队列处理逻辑

1. **请求入队**: 根据用户优先级分配到对应队列
2. **队列调度**: Worker按 高→中→低 的顺序处理请求
3. **繁忙保护**: 队列使用率>80%时，拒绝免费用户请求
4. **公平性保证**: 同优先级内按FIFO顺序处理

### 优先级识别

系统通过以下方式识别用户优先级：

```go
// 从用户ID模式识别（简化版）
func GetUserPriority(userID string) UserPriority {
    if strings.Contains(userID, "vip") || strings.Contains(userID, "premium") {
        return PriorityVIP
    }
    if strings.Contains(userID, "free") || strings.Contains(userID, "trial") {
        return PriorityFree
    }
    return PriorityNormal
}
```

在生产环境中，应该从数据库查询用户的订阅类型。

## 重试机制

### 重试策略

#### 触发条件
- HTTP 429 (Too Many Requests)
- HTTP 408 (Request Timeout) 
- HTTP 500/502/503/504 (服务器错误)
- 网络超时错误
- 服务不可用错误

#### 指数退避算法
```
wait_time = base_delay * (2 ^ attempt)
最大延迟: 5分钟
```

**重试延迟示例**:
- 第1次重试: 2秒后
- 第2次重试: 4秒后  
- 第3次重试: 8秒后

#### 重试队列管理
1. **队列占用**: 重试请求占用并发名额直到完全失败或成功
2. **延迟处理**: 使用Pulsar延迟消息或Go定时器
3. **失败处理**: 超过最大重试次数后标记为永久失败

### 重试配置

```go
type ConcurrentConfig struct {
    MaxRetries      int           `json:"max_retries"`       // 3
    RetryBaseDelay  time.Duration `json:"retry_base_delay"`  // 2秒
}
```

## Token限额集成

### 验证流程

```
Request → Token Validation → Concurrent Control → LLM Service
    ↓           ↓                    ↓               ↓
   用户       检查限额             队列管理          实际调用
   请求       月/周/日             优先级排队         记录消耗
```

### 验证逻辑

1. **预检查**: 估算请求token数量，检查是否超限
2. **精确记录**: LLM调用完成后，记录实际token消耗
3. **实时统计**: 更新用户token使用统计

### 限额错误处理

```json
{
  "error": "Token limit exceeded",
  "message": "Monthly token limit exceeded",
  "code": "TOKEN_LIMIT_EXCEEDED"
}
```

返回HTTP 403状态码，客户端应该停止发送请求直到下个周期。

## 监控和统计

### 实时监控指标

#### 队列指标
- **队列大小**: 主队列和重试队列当前长度
- **队列使用率**: 队列大小/最大容量
- **处理中请求数**: 当前正在处理的请求数量
- **活跃工作协程数**: 正在工作的协程数量

#### 性能指标
- **总处理数**: 累计处理的请求总数
- **总错误数**: 累计错误次数
- **总重试数**: 累计重试次数
- **平均响应时间**: 请求处理平均耗时
- **错误率**: 错误请求 / 总请求

#### 业务指标
- **优先级分布**: 各优先级队列的请求分布
- **用户类型分布**: VIP/普通/免费用户请求分布
- **处理吞吐量**: 每秒处理的请求数

### 健康状态评估

```go
// 健康状态评估规则
func evaluateHealth(stats *QueueStats) string {
    queueUtilization := float64(stats.MainQueueSize) / 1000.0
    errorRate := float64(stats.TotalErrors) / float64(stats.TotalProcessed + 1)
    
    if queueUtilization > 0.9 || errorRate > 0.1 {
        return "unhealthy"    // 不健康
    } else if queueUtilization > 0.7 || errorRate > 0.05 {
        return "degraded"     // 降级
    }
    return "healthy"          // 健康
}
```

### 监控告警建议

#### 队列告警
- 队列使用率 > 80%: 警告
- 队列使用率 > 95%: 紧急
- 队列满（100%）: 严重

#### 性能告警  
- 错误率 > 5%: 警告
- 错误率 > 10%: 严重
- 平均响应时间 > 30秒: 警告
- 平均响应时间 > 60秒: 严重

#### 业务告警
- 连续5分钟无请求处理: 警告
- 大量429错误: 警告
- VIP用户请求失败: 紧急

## 部署和运维

### Docker部署

```dockerfile
# 使用现有的Dockerfile，已包含并发控制模块
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o taskd cmd/taskd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/taskd .
COPY configs/ configs/
CMD ["./taskd", "-config", "configs/config.prod.yaml"]
```

### Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: taskd-concurrent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: taskd-concurrent
  template:
    metadata:
      labels:
        app: taskd-concurrent
    spec:
      containers:
      - name: taskd
        image: taskd:latest
        ports:
        - containerPort: 8601
        env:
        - name: CONCURRENT_MAX_REQUESTS
          value: "1000"
        - name: CONCURRENT_QUEUE_SIZE  
          value: "1000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /v1/concurrent/health
            port: 8601
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8601
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 生产环境配置建议

#### 资源配置
- **CPU**: 2-4核，支持高并发处理
- **内存**: 1-2GB，缓存队列和请求数据
- **网络**: 高带宽，支持大量并发连接

#### 参数调优
```yaml
# 生产环境推荐配置
concurrent:
  max_concurrent_requests: 1000
  main_queue_size: 1000
  retry_queue_size: 200
  worker_count: 50           # 根据CPU核心数调整
  max_wait_time_seconds: 180
  max_retries: 3
```

#### 监控配置
```yaml
# 监控端点
endpoints:
  - path: /v1/concurrent/stats
    interval: 30s
  - path: /v1/concurrent/health  
    interval: 10s
```

## 测试

### 功能测试

运行完整的功能测试套件：

```bash
# 安装测试依赖
pip install -r test/test_concurrent_requirements.txt

# 启动TaskD服务（测试配置）
go run cmd/taskd/main.go -config configs/config.test.yaml

# 运行测试
cd test/
./run_concurrent_tests.sh
```

### 测试用例覆盖

#### 基础功能测试
- ✅ LLM请求提交和响应
- ✅ Token限额验证
- ✅ 请求参数验证

#### 优先级队列测试
- ✅ VIP/普通/免费用户优先级处理
- ✅ 队列调度顺序验证
- ✅ 优先级插队机制

#### 限流和重试测试
- ✅ 队列溢出429响应
- ✅ 重试机制和指数退避
- ✅ 超时处理

#### 并发性能测试
- ✅ 100并发请求处理
- ✅ 成功率统计（>=70%）
- ✅ 响应时间统计（平均<60s）
- ✅ 吞吐量测试

#### 监控测试
- ✅ 队列统计信息
- ✅ 健康状态监控
- ✅ 性能指标收集

### 压力测试

```bash
# 运行压力测试（200并发）
python test/test_concurrent_control.py
```

**性能基准**:
- 并发处理能力: 100+ requests/second
- 成功率: >90%
- 平均响应时间: <30秒
- P95响应时间: <60秒

## 故障排除

### 常见问题

#### 1. 队列满错误 (429)
**现象**: 大量429错误
**原因**: 请求量超过队列容量
**解决**: 
- 增大queue_size配置
- 增加worker_count
- 优化LLM处理速度

#### 2. 请求超时
**现象**: 大量408超时错误  
**原因**: LLM响应慢或网络问题
**解决**:
- 检查LLM服务状态
- 增大max_wait_time_seconds
- 检查网络连接

#### 3. Token限额错误 (403)
**现象**: 403 token limit exceeded
**原因**: 用户超出token限额
**解决**:
- 检查用户token使用情况
- 调整用户限额配置
- 优化prompt减少token消耗

#### 4. Pulsar连接失败
**现象**: Pulsar队列不可用警告
**原因**: Pulsar服务不可用
**解决**:
- 检查Pulsar服务状态
- 确认网络连接
- 系统会自动回退到内存队列

### 调试工具

#### 1. 队列状态查询
```bash
curl http://localhost:8601/v1/concurrent/stats
```

#### 2. 健康状态检查
```bash
curl http://localhost:8601/v1/concurrent/health
```

#### 3. 日志查看
```bash
# 查看并发控制相关日志
grep "concurrent\|queue\|worker" /var/log/taskd.log
```

#### 4. 性能分析
```bash
# Go pprof性能分析
go tool pprof http://localhost:8601/debug/pprof/heap
```

## 扩展和优化

### 未来扩展功能

#### 1. 本地缓存 (待实现)
- **功能**: 对重复请求返回缓存结果
- **技术**: Redis或内存LRU缓存  
- **收益**: 减少LLM调用，提升响应速度

#### 2. API Key池轮询 (预留设计)
- **功能**: 多API Key负载均衡
- **技术**: Round-robin或加权轮询
- **收益**: 提高API调用限额

#### 3. 智能调度
- **功能**: 基于历史数据的智能请求调度
- **技术**: 机器学习预测模型
- **收益**: 优化响应时间和资源利用

#### 4. 分布式队列
- **功能**: 多实例间的队列共享
- **技术**: Redis Stream或Kafka
- **收益**: 更高的可扩展性

### 性能优化建议

#### 1. 连接池优化
```go
// HTTP客户端连接池配置
client := &http.Client{
    Transport: &http.Transport{
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
    },
    Timeout: 30 * time.Second,
}
```

#### 2. 内存优化
- 使用对象池减少GC压力
- 优化JSON序列化/反序列化
- 控制并发协程数量

#### 3. 网络优化
- 使用HTTP/2协议
- 启用gzip压缩
- 优化TCP参数

## 总结

TaskD并发控制模块提供了完整的企业级LLM请求管理解决方案：

### 🎯 核心价值
- **高并发处理**: 支持1000并发请求处理
- **智能队列**: 优先级队列和限流保护
- **可靠重试**: 指数退避重试机制
- **完整监控**: 实时统计和健康监控
- **生产就绪**: Docker/K8s部署支持

### 🚀 技术亮点
- **双队列架构**: 内存+Pulsar混合队列
- **Token集成**: 无缝集成token管理
- **优雅降级**: Pulsar故障时自动回退
- **丰富测试**: 功能、性能、压力测试全覆盖

### 📈 性能表现
- **处理能力**: 100+ requests/second
- **成功率**: >90%
- **响应时间**: 平均<30秒，P95<60秒
- **可扩展性**: 支持水平扩展

该模块已在测试环境验证，可直接部署到生产环境使用。