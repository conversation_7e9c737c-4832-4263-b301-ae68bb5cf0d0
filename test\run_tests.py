#!/usr/bin/env python3
"""
TaskD BVT测试套件执行器
支持分级测试和结果统计
"""

import subprocess
import sys
import os
import json
import time
from typing import Dict, List, Tuple

class BVTTestRunner:
    def __init__(self):
        self.base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        self.results = {}
        
    def run_test_file(self, test_file: str, priority: str) -> Tuple[bool, str]:
        """运行单个测试文件"""
        print(f"\n{'='*60}")
        print(f"运行{priority}测试: {test_file}")
        print(f"{'='*60}")
        
        try:
            cmd = [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            return success, output
            
        except subprocess.TimeoutExpired:
            return False, "测试超时"
        except Exception as e:
            return False, f"执行异常: {str(e)}"
    
    def run_p0_tests(self):
        """运行P0级别测试 - 必须100%通过"""
        print("\n开始P0级别测试 (Critical - 必须100%通过)")
        
        p0_tests = [
            "test_agent_registration_only.py",
            "test_intent_recognition.py",
            "test_agent_visibility.py"  # 测试新注册的Agent可见性
        ]
        
        p0_results = {}
        for test_file in p0_tests:
            success, output = self.run_test_file(test_file, "P0")
            p0_results[test_file] = {"success": success, "output": output}
            print(f"结果: {'PASS' if success else 'FAIL'}")
        
        self.results["P0"] = p0_results
        return all(result["success"] for result in p0_results.values())
    
    def run_p1_tests(self):
        """运行P1级别测试 - 核心业务功能"""
        print("\n开始P1级别测试 (High Priority - 核心业务功能)")
        
        p1_tests = [
            "test_entity_extraction_agent.py",
            "test_bidding_agents.py"
        ]
        
        p1_results = {}
        for test_file in p1_tests:
            success, output = self.run_test_file(test_file, "P1")
            p1_results[test_file] = {"success": success, "output": output}
            print(f"结果: {'PASS' if success else 'FAIL'}")
        
        self.results["P1"] = p1_results
        pass_rate = sum(1 for result in p1_results.values() if result["success"]) / len(p1_results)
        return pass_rate >= 0.95  # P1要求95%通过率
    
    def run_p2_tests(self):
        """运行P2级别测试 - 预处理功能"""
        print("\n开始P2级别测试 (Medium Priority - 预处理功能)")
        
        p2_tests = [
            "test_preprocessing_agents.py"
        ]
        
        p2_results = {}
        for test_file in p2_tests:
            success, output = self.run_test_file(test_file, "P2")
            p2_results[test_file] = {"success": success, "output": output}
            print(f"结果: {'PASS' if success else 'FAIL'}")
        
        self.results["P2"] = p2_results
        pass_rate = sum(1 for result in p2_results.values() if result["success"]) / len(p2_results)
        return pass_rate >= 0.90  # P2要求90%通过率
    
    def run_p3_tests(self):
        """运行P3级别测试 - 支撑功能"""
        print("\n开始P3级别测试 (Low Priority - 支撑功能)")
        
        p3_tests = [
            "test_token_management.py",
            "test_concurrent_control.py"
        ]
        
        p3_results = {}
        for test_file in p3_tests:
            success, output = self.run_test_file(test_file, "P3")
            p3_results[test_file] = {"success": success, "output": output}
            print(f"结果: {'PASS' if success else 'FAIL'}")
        
        self.results["P3"] = p3_results
        pass_rate = sum(1 for result in p3_results.values() if result["success"]) / len(p3_results)
        return pass_rate >= 0.85  # P3要求85%通过率
    
    def generate_summary(self):
        """生成测试总结报告"""
        print(f"\n{'='*80}")
        print("TaskD BVT测试总结报告")
        print(f"{'='*80}")
        print(f"测试服务地址: {self.base_url}")
        print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        total_passed = 0
        total_failed = 0
        
        for priority, tests in self.results.items():
            print(f"{priority}级别测试结果:")
            priority_passed = 0
            priority_total = len(tests)
            
            for test_file, result in tests.items():
                status = "PASS" if result["success"] else "FAIL"
                print(f"  {test_file:<35}: {status}")
                if result["success"]:
                    priority_passed += 1
                    total_passed += 1
                else:
                    total_failed += 1
            
            pass_rate = (priority_passed / priority_total) * 100 if priority_total > 0 else 0
            print(f"  {priority}级别通过率: {priority_passed}/{priority_total} ({pass_rate:.1f}%)")
            print()
        
        overall_total = total_passed + total_failed
        overall_rate = (total_passed / overall_total) * 100 if overall_total > 0 else 0
        
        print(f"总体测试结果:")
        print(f"  总测试数: {overall_total}")
        print(f"  通过数: {total_passed}")
        print(f"  失败数: {total_failed}")
        print(f"  总通过率: {overall_rate:.1f}%")
        
        # 保存详细结果到文件
        with open("bvt_test_results.json", "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
                "base_url": self.base_url,
                "summary": {
                    "total": overall_total,
                    "passed": total_passed,
                    "failed": total_failed,
                    "pass_rate": overall_rate
                },
                "details": self.results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细结果已保存到: bvt_test_results.json")
        print(f"{'='*80}")
        
        return overall_rate >= 80  # 总体要求80%通过率

def main():
    """主函数"""
    print("TaskD BVT测试套件")
    print("=" * 80)
    
    # 检查环境
    base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
    print(f"测试目标服务: {base_url}")
    
    runner = BVTTestRunner()
    
    try:
        # 设置环境变量和初始化测试数据
        print("\n=== 设置环境变量 ===")
        env_result = subprocess.run(["bash", "-c", "source ./set_test_env.sh && env | grep -E '^(TASKD_|DB_)'"], 
                                  capture_output=True, text=True, timeout=30, cwd=".")
        if env_result.returncode == 0:
            print("环境变量设置成功:")
            print(env_result.stdout)
        
        print("\n=== 初始化测试数据 ===")
        init_result = subprocess.run(["bash", "-c", "source ./set_test_env.sh && ./init_test_data.sh"], 
                                   capture_output=True, text=True, timeout=60, cwd=".")
        if init_result.returncode != 0:
            print(f"测试数据初始化失败: {init_result.stderr}")
            print(f"初始化输出: {init_result.stdout}")
        else:
            print("测试数据初始化成功")
            if init_result.stdout:
                print(init_result.stdout)
        
        # 按优先级依次执行测试
        p0_success = runner.run_p0_tests()
        p1_success = runner.run_p1_tests() 
        p2_success = runner.run_p2_tests()
        p3_success = runner.run_p3_tests()
        
        # 生成总结报告
        overall_success = runner.generate_summary()
        
        # 根据P0结果决定整体成功与否
        if not p0_success:
            print("\nP0级别测试未通过，系统存在严重问题！")
            return False
        elif overall_success:
            print("\nBVT测试整体通过")
            return True
        else:
            print("\nBVT测试整体未达到预期标准")
            return False
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return False
    except Exception as e:
        print(f"\n测试执行异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)