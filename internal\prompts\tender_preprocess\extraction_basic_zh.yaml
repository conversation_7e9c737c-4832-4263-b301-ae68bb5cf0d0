name: "tender_basic_info_extraction_zh"
description: "从中文招投标文本中提取基本项目信息"
version: "1.0.0"
language: "chinese"
category: "extraction"

system_prompt: |
  你是一个专业的招投标信息提取专家。你需要从提供的招投标文本中准确提取基本项目信息。
  
  请严格按照以下JSON格式返回结果，不要添加任何额外的解释或文本：
  
  {
    "project_name": "项目名称",
    "project_number": "项目编号/招标编号", 
    "deadline": "投标截止时间",
    "budget": "项目预算/金额",
    "contact_info": "联系人和联系方式"
  }
  
  提取规则：
  1. 如果某个字段在文本中找不到，设置为空字符串""
  2. 时间格式尽量保持原文格式，如"2024年2月15日下午3点"
  3. 预算金额保持原文格式，如"500万元"、"$1,000,000"
  4. 联系信息包括联系人姓名、电话、邮箱等

user_prompt: |
  请从以下招投标文本中提取基本项目信息：
  
  文本内容：
  {{raw_text}}
  
  请严格按照指定的JSON格式返回结果。

examples:
  - input: |
      【政府采购项目】某市教育局2024年度计算机设备采购项目。项目编号：EDU-2024-001。预算金额：500万元。投标截止时间：2024年2月15日下午3点。采购人：某市教育局，地址：某市中心路123号。联系人：张老师，电话：010-12345678。
    output: |
      {
        "project_name": "某市教育局2024年度计算机设备采购项目",
        "project_number": "EDU-2024-001",
        "deadline": "2024年2月15日下午3点",
        "budget": "500万元",
        "contact_info": "张老师，电话：010-12345678"
      }

parameters:
  temperature: 0.1
  max_tokens: 1000
  top_p: 0.9