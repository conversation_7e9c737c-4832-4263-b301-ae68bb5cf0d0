package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// EnhancedLLMHandler 包含自动token跟踪功能的LLM处理器
type EnhancedLLMHandler struct {
	llmClient llm.LLMClient
}

// NewEnhancedLLMHandler 创建新的增强LLM处理器
func NewEnhancedLLMHandler(llmClient llm.LLMClient) *EnhancedLLMHandler {
	return &EnhancedLLMHandler{
		llmClient: llmClient,
	}
}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	UserID      string                 `json:"user_id" binding:"required"`
	CompanyID   string                 `json:"company_id" binding:"required"`
	Model       string                 `json:"model,omitempty"`
	Messages    []common.LLMMessage    `json:"messages" binding:"required"`
	Temperature *float64               `json:"temperature,omitempty"`
	MaxTokens   *int                   `json:"max_tokens,omitempty"`
	RequestID   string                 `json:"request_id,omitempty"`
	Options     map[string]interface{} `json:"options,omitempty"`
}

// ChatResponse 聊天响应结构
type ChatResponse struct {
	Content   string `json:"content"`
	RequestID string `json:"request_id,omitempty"`
	Model     string `json:"model"`
}

// HandleChatWithAutoTracking 处理聊天请求并自动跟踪token消耗
func (h *EnhancedLLMHandler) HandleChatWithAutoTracking(c *gin.Context) {
	var req ChatRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Errorf("Invalid chat request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": err.Error(),
		})
		return
	}

	// 生成请求ID（如果未提供）
	if req.RequestID == "" {
		req.RequestID = generateRequestID()
	}

	// 构建LLM请求参数
	llmParams := models.OpenAICompatibleRequestParams{
		Model:           req.Model,
		Messages:        req.Messages,
		Temperature:     req.Temperature,
		MaxTokens:       req.MaxTokens,
		AutoTrackTokens: true, // 启用自动token跟踪
		UserContext: &models.UserContext{
			UserID:    req.UserID,
			CompanyID: req.CompanyID,
			RequestID: req.RequestID,
			Endpoint:  c.Request.URL.Path,
		},
	}

	// 处理其他参数
	if req.Options != nil {
		llmParams.ProviderSpecificParams = req.Options

		// 处理常见参数
		if freqPenalty, ok := req.Options["frequency_penalty"].(float64); ok {
			llmParams.FrequencyPenalty = &freqPenalty
		}
		if presPenalty, ok := req.Options["presence_penalty"].(float64); ok {
			llmParams.PresencePenalty = &presPenalty
		}
		if stream, ok := req.Options["stream"].(bool); ok {
			llmParams.Stream = stream
		}
	}

	// 调用LLM（会自动检查限额和记录消耗）
	response, err := h.llmClient.ChatCompletions(llmParams)
	if err != nil {
		utils.Log.Errorf("LLM call failed: %v", err)

		// 检查是否是token限额错误
		if isTokenLimitError(err) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Token limit exceeded",
				"message": err.Error(),
				"code":    "TOKEN_LIMIT_EXCEEDED",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "LLM processing failed",
			"message": err.Error(),
		})
		return
	}

	// 返回响应
	c.JSON(http.StatusOK, ChatResponse{
		Content:   response,
		RequestID: req.RequestID,
		Model:     req.Model,
	})
}

// SummarizeWithTracking 总结文本并自动跟踪token消耗
func (h *EnhancedLLMHandler) HandleSummarizeWithTracking(c *gin.Context) {
	var req SummarizeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Errorf("Invalid summarize request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": err.Error(),
		})
		return
	}

	// 生成请求ID
	if req.RequestID == "" {
		req.RequestID = generateRequestID()
	}

	// 构建总结提示
	messages := []common.LLMMessage{
		{
			Role:    "system",
			Content: "你是一个专业的文本总结助手。请对用户提供的文本进行简洁、准确的总结。",
		},
		{
			Role:    "user",
			Content: "请总结以下内容：\n\n" + req.Text,
		},
	}

	// 构建LLM请求参数
	llmParams := models.OpenAICompatibleRequestParams{
		Model:           req.Model,
		Messages:        messages,
		Temperature:     &[]float64{0.3}[0], // 使用较低的温度以获得更一致的总结
		AutoTrackTokens: true,               // 启用自动token跟踪
		UserContext: &models.UserContext{
			UserID:    req.UserID,
			CompanyID: req.CompanyID,
			RequestID: req.RequestID,
			Endpoint:  c.Request.URL.Path,
		},
	}

	if req.MaxLength > 0 {
		// 根据最大长度估算max_tokens
		maxTokens := req.MaxLength / 3 // 简单估算
		llmParams.MaxTokens = &maxTokens
	}

	// 调用LLM
	response, err := h.llmClient.ChatCompletions(llmParams)
	if err != nil {
		utils.Log.Errorf("LLM summarization failed: %v", err)

		if isTokenLimitError(err) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Token limit exceeded",
				"message": err.Error(),
				"code":    "TOKEN_LIMIT_EXCEEDED",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Summarization failed",
			"message": err.Error(),
		})
		return
	}

	// 返回总结结果
	c.JSON(http.StatusOK, SummarizeResponse{
		Summary:   response,
		RequestID: req.RequestID,
		Model:     req.Model,
	})
}

// SummarizeRequest 总结请求结构
type SummarizeRequest struct {
	UserID    string `json:"user_id" binding:"required"`
	CompanyID string `json:"company_id" binding:"required"`
	Text      string `json:"text" binding:"required"`
	Model     string `json:"model,omitempty"`
	MaxLength int    `json:"max_length,omitempty"`
	RequestID string `json:"request_id,omitempty"`
}

// SummarizeResponse 总结响应结构
type SummarizeResponse struct {
	Summary   string `json:"summary"`
	RequestID string `json:"request_id,omitempty"`
	Model     string `json:"model"`
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的请求ID生成（在实际应用中应该使用更强的ID生成器）
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return "req_" + hex.EncodeToString(bytes)
}

// isTokenLimitError 检查错误是否为token限额错误
func isTokenLimitError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := err.Error()
	return contains(errMsg, "token限额") || contains(errMsg, "token limit") ||
		contains(errMsg, "limit exceeded") || contains(errMsg, "quota exceeded")
}

// contains 检查字符串包含关系
func contains(s, substr string) bool {
	if len(s) < len(substr) {
		return false
	}
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
