package setup

import (
	"fmt"
	"go.mongodb.org/mongo-driver/mongo"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/interfaces"
	biddingInterfaces "gitlab.com/specific-ai/taskd/internal/modules/bidding/interfaces"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	biddingServices "gitlab.com/specific-ai/taskd/internal/modules/bidding/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// 全局Agent注册表单例
var (
	globalAgentRegistry biddingInterfaces.AgentRegistry
	registryOnce        sync.Once
)

// GetGlobalAgentRegistry 获取全局Agent注册表单例
func GetGlobalAgentRegistry() biddingInterfaces.AgentRegistry {
	registryOnce.Do(func() {
		globalAgentRegistry = biddingServices.NewAgentRegistryService()
		utils.Log.Info("创建全局Agent注册表单例")
	})
	return globalAgentRegistry
}

// SimpleAgent 简单的Agent实现，用于HTTP注册的Agent
type SimpleAgent struct {
	id        string
	agentCard biddingModels.AgentCard
}

// NewSimpleAgent 创建一个简单的Agent实例
func NewSimpleAgent(registeredAgent biddingModels.RegisteredAgent) *SimpleAgent {
	return &SimpleAgent{
		id:        registeredAgent.ID,
		agentCard: registeredAgent.AgentCard,
	}
}

// GetID 实现Agent接口
func (s *SimpleAgent) GetID() string {
	return s.id
}

// GetAgentCard 实现Agent接口
func (s *SimpleAgent) GetAgentCard() biddingModels.AgentCard {
	return s.agentCard
}

// HealthCheck 实现Agent接口
func (s *SimpleAgent) HealthCheck() biddingModels.HealthStatus {
	return biddingModels.HealthStatus{
		Status:    "healthy",
		Version:   "1.0.0",
		Uptime:    0,
		Dependencies: map[string]string{
			"registration": "http",
		},
		Performance: biddingModels.AgentPerformance{
			AvgResponseTime:   100,
			RequestsPerMinute: 0,
			ErrorRate:         0.0,
			SuccessRate:       1.0,
		},
		Timestamp: time.Now(),
	}
}

// ExecuteSkill 实现Agent接口
func (s *SimpleAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	// 简单实现，返回模拟结果
	return &biddingModels.A2AResult{
		TaskID: "task-" + s.id,
		Status: "completed",
		Output: map[string]interface{}{"message": "HTTP registered agent executed", "skill": skillID},
		Metadata: biddingModels.A2AMetadata{
			ExecutionTime: 100,
			TokensUsed:    0,
			AgentVersion:  "1.0.0",
		},
	}, nil
}

// RegistryAdapter adapts AgentRegistry to handlers.AgentRegistry interface
type RegistryAdapter struct {
	registry biddingInterfaces.AgentRegistry
	// 添加对具体实现的引用以访问UnregisterAgent方法
	registryService *biddingServices.AgentRegistryService
}

// NewRegistryAdapter creates a new registry adapter
func NewRegistryAdapter(registry biddingInterfaces.AgentRegistry) *RegistryAdapter {
	// 类型断言获取具体实现
	registryService, ok := registry.(*biddingServices.AgentRegistryService)
	if !ok {
		registryService = nil
	}
	return &RegistryAdapter{
		registry:        registry,
		registryService: registryService,
	}
}

// GetAgent adapts the GetAgent method to return handlers.Agent
func (r *RegistryAdapter) GetAgent(agentID string) (handlers.Agent, error) {
	agent, err := r.registry.GetAgent(agentID)
	if err != nil {
		return nil, err
	}
	return agent, nil
}

// ListAgents adapts the ListAgents method
func (r *RegistryAdapter) ListAgents() []biddingModels.RegisteredAgent {
	return r.registry.ListAgents()
}

// RegisterAgent adapts the RegisterAgent method
func (r *RegistryAdapter) RegisterAgent(agent biddingModels.RegisteredAgent) error {
	// 创建一个SimpleAgent实例并注册到底层registry
	simpleAgent := NewSimpleAgent(agent)
	return r.registry.RegisterAgent(simpleAgent)
}

// UnregisterAgent adapts the UnregisterAgent method
func (r *RegistryAdapter) UnregisterAgent(agentID string) error {
	// 直接调用具体实现的UnregisterAgent方法
	if r.registryService != nil {
		return r.registryService.UnregisterAgent(agentID)
	}
	// 如果没有具体实现，返回错误
	return fmt.Errorf("UnregisterAgent not supported")
}

// UpdateAgentStatus adapts the UpdateAgentStatus method
func (r *RegistryAdapter) UpdateAgentStatus(agentID string, status biddingModels.AgentStatus) error {
	_, err := r.registry.GetAgent(agentID)
	return err
}

// BiddingModule 招投标业务模块
type BiddingModule struct {
	AgentManager *biddingServices.BiddingAgentManager
	A2AHandler   *handlers.A2AHandler
}

// InitBiddingModule 初始化招投标模块
func InitBiddingModule(mongoClient *mongo.Client, llmService interfaces.LLMServiceInterface) (*BiddingModule, error) {
	utils.Log.Info("初始化招投标模块...")

	// 使用全局Agent注册表单例
	registryService := GetGlobalAgentRegistry()

	// 创建Agent管理器
	agentManager := biddingServices.NewBiddingAgentManager(registryService)

	// 创建注册表适配器
	registryAdapter := NewRegistryAdapter(registryService)

	// 创建A2A处理器
	a2aHandler := handlers.NewA2AHandler(registryAdapter)

	module := &BiddingModule{
		AgentManager: agentManager,
		A2AHandler:   a2aHandler,
	}

	utils.Log.Info("招投标模块初始化完成")
	return module, nil
}

// GetAgentManager 获取Agent管理器
func (m *BiddingModule) GetAgentManager() *biddingServices.BiddingAgentManager {
	return m.AgentManager
}

// GetA2AHandler 获取A2A处理器
func (m *BiddingModule) GetA2AHandler() *handlers.A2AHandler {
	return m.A2AHandler
}

// HealthCheck 检查模块健康状态
func (m *BiddingModule) HealthCheck() map[string]interface{} {
	return map[string]interface{}{
		"module": "bidding",
		"status": "healthy",
		"agents": m.AgentManager.HealthCheck(),
	}
}
