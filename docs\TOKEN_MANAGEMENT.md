# Token Management Module

## 概述

Token管理模块提供了完整的LLM token消耗跟踪、限额管理和统计功能。该模块支持多用户、多公司的token使用监控，并提供了灵活的套餐类型和限额配置。

## 功能特性

### 核心功能
- ✅ **Token消耗记录**: 记录所有LLM调用的输入输出token数
- ✅ **用户和公司管理**: 支持多租户架构
- ✅ **套餐类型**: 支持free、pro、max三种套餐
- ✅ **多维度限额**: 月度、周度、日度限额可配置
- ✅ **实时统计**: 提供详细的使用统计和分析
- ✅ **成本计算**: 自动计算token消耗成本

### 技术特性
- ✅ **PostgreSQL存储**: 高性能关系型数据库
- ✅ **RESTful API**: 标准化API接口
- ✅ **自动化测试**: 完整的Python测试套件
- ✅ **Docker支持**: 容器化部署
- ✅ **配置管理**: 灵活的配置选项

## 数据库设计

### 主要表结构

#### users (用户表)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(320),
    company_id VARCHAR(255),
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### companies (公司表)
```sql
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    company_id VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### token_consumption (Token消耗记录表)
```sql
CREATE TABLE token_consumption (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    model_provider VARCHAR(100) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    input_tokens INTEGER NOT NULL DEFAULT 0,
    output_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    cost_cents INTEGER DEFAULT 0,
    request_id VARCHAR(255),
    api_endpoint VARCHAR(255),
    consumed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### token_limits (Token限额配置表)
```sql
CREATE TABLE token_limits (
    id SERIAL PRIMARY KEY,
    subscription_type subscription_type NOT NULL,
    limit_type VARCHAR(50) NOT NULL, -- 'monthly', 'weekly', 'daily'
    token_limit INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### token_usage_summary (使用统计汇总表)
```sql
CREATE TABLE token_usage_summary (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    subscription_type subscription_type NOT NULL,
    period_type VARCHAR(50) NOT NULL, -- 'monthly', 'weekly', 'daily'
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_tokens INTEGER DEFAULT 0,
    total_cost_cents INTEGER DEFAULT 0,
    request_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 默认限额配置
| 套餐类型 | 月度限额 | 周度限额 | 日度限额 |
|---------|---------|---------|---------|
| free    | 100,000 | 25,000  | 5,000   |
| pro     | 1,000,000 | 250,000 | 50,000  |
| max     | 10,000,000 | 2,500,000 | 500,000 |

## API 接口

### Token管理接口

#### 记录Token消耗
```http
POST /v1/tokens/consumption
Content-Type: application/json

{
  "user_id": "user123",
  "company_id": "company123",
  "model_provider": "openai",
  "model_name": "gpt-3.5-turbo",
  "input_tokens": 100,
  "output_tokens": 50,
  "request_id": "req_abc123",
  "api_endpoint": "/v1/chat/completions"
}
```

#### 检查Token限额
```http
GET /v1/tokens/limits/{user_id}?tokens=1000
```

#### 获取用户Token统计
```http
GET /v1/tokens/stats/user/{user_id}
```

#### 获取公司Token统计
```http
GET /v1/tokens/stats/company/{company_id}
```

#### 获取Token消耗历史
```http
GET /v1/tokens/history/{user_id}?page=1&limit=20
```

#### 健康检查
```http
GET /v1/tokens/health
```

### 用户管理接口

#### 创建用户
```http
POST /v1/users
Content-Type: application/json

{
  "user_id": "user123",
  "username": "john_doe",
  "email": "<EMAIL>",
  "company_id": "company123",
  "subscription_type": "pro"
}
```

### 公司管理接口

#### 创建公司
```http
POST /v1/companies
Content-Type: application/json

{
  "company_id": "company123",
  "company_name": "Example Corp",
  "subscription_type": "max"
}
```

## 配置

### PostgreSQL配置 (config.dev.yaml)
```yaml
postgresql:
  host: "localhost"
  port: 5432
  user: "admin"
  password: "SecurePass123!"
  database: "taskd_tokens"
  ssl_mode: "disable"
  timeout_seconds: 10
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300
```

### 环境变量配置
```bash
# PostgreSQL连接
POSTGRESQL_HOST_ENV=localhost
POSTGRESQL_PORT_ENV=5432
POSTGRESQL_USER_ENV=admin
POSTGRESQL_PASSWORD_ENV=SecurePass123!
POSTGRESQL_DATABASE_ENV=taskd_tokens
```

## 部署

### Docker部署
```bash
# 使用docker-compose（推荐）
docker-compose up -d

# 或者单独构建和运行
docker build -f deploy/Dockerfile -t taskd:latest .
docker run -p 8601:8601 \
  -e POSTGRESQL_HOST_ENV=your-postgres-host \
  -e POSTGRESQL_PASSWORD_ENV=your-password \
  taskd:latest
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: taskd
spec:
  replicas: 3
  selector:
    matchLabels:
      app: taskd
  template:
    metadata:
      labels:
        app: taskd
    spec:
      containers:
      - name: taskd
        image: taskd:latest
        ports:
        - containerPort: 8601
        env:
        - name: POSTGRESQL_HOST_ENV
          value: "postgres-service"
        - name: POSTGRESQL_PASSWORD_ENV
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
```

## 使用示例

### 1. 基本用法
```bash
# 1. 创建公司
curl -X POST http://localhost:8601/v1/companies \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "test_company",
    "company_name": "Test Company",
    "subscription_type": "pro"
  }'

# 2. 创建用户
curl -X POST http://localhost:8601/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test_user",
    "username": "testuser",
    "email": "<EMAIL>",
    "company_id": "test_company",
    "subscription_type": "pro"
  }'

# 3. 记录Token消耗
curl -X POST http://localhost:8601/v1/tokens/consumption \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "test_user",
    "company_id": "test_company",
    "model_provider": "openai",
    "model_name": "gpt-3.5-turbo",
    "input_tokens": 100,
    "output_tokens": 50,
    "request_id": "req_123",
    "api_endpoint": "/v1/chat/completions"
  }'

# 4. 检查限额
curl "http://localhost:8601/v1/tokens/limits/test_user?tokens=1000"

# 5. 获取统计信息
curl "http://localhost:8601/v1/tokens/stats/user/test_user"
```

### 2. 集成到LLM调用中
```go
// 在LLM调用前检查限额
limitCheck, err := tokenService.CheckTokenLimits(userID, estimatedTokens)
if err != nil {
    return err
}
if !limitCheck.CanProceed {
    return fmt.Errorf("token limit exceeded: %s", limitCheck.Reason)
}

// 调用LLM...
response, err := llmClient.Call(request)
if err != nil {
    return err
}

// 记录实际消耗
consumption := &models.TokenConsumptionRequest{
    UserID:        userID,
    CompanyID:     companyID,
    ModelProvider: "openai",
    ModelName:     "gpt-3.5-turbo",
    InputTokens:   response.Usage.PromptTokens,
    OutputTokens:  response.Usage.CompletionTokens,
    RequestID:     requestID,
    APIEndpoint:   "/v1/chat/completions",
}

_, err = tokenService.LogTokenConsumption(consumption)
return err
```

## 测试

### 运行测试
```bash
# 安装测试依赖
pip install -r test/requirements.txt

# 确保服务运行
go run cmd/taskd/main.go -config configs/config.dev.yaml

# 运行所有测试
python -m pytest test/ -v

# 运行特定测试
python -m pytest test/test_token_management.py::TestTokenManagement -v
```

### 测试覆盖范围
- ✅ Token消耗记录API
- ✅ Token限额检查API
- ✅ 用户统计API
- ✅ 公司统计API
- ✅ 消耗历史API
- ✅ 用户管理API
- ✅ 公司管理API
- ✅ 健康检查API
- ✅ 集成工作流测试
- ✅ 错误场景测试

## 监控和告警

### 关键指标
- Token消耗量（按用户、公司、模型）
- 限额使用率
- 请求频率
- 成本统计
- 错误率

### 推荐告警
- 用户token使用率超过80%
- 公司月度使用量接近限额
- 异常高频调用
- 数据库连接异常

## 扩展和优化

### 性能优化
- 使用连接池优化数据库连接
- 实现缓存层减少数据库查询
- 分区大表提高查询性能
- 异步处理非关键路径

### 功能扩展
- 支持更多套餐类型
- 实现动态限额调整
- 添加使用预测功能
- 集成计费系统

### 安全加固
- 实现API认证授权
- 加密敏感数据
- 审计日志记录
- 防止SQL注入

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查PostgreSQL配置和网络连接
2. **Token统计不准确**: 检查触发器和汇总表数据
3. **限额检查错误**: 验证用户和公司数据完整性
4. **性能问题**: 检查数据库索引和查询优化

### 日志分析
```bash
# 检查应用日志
docker logs taskd-app

# 检查数据库日志
docker logs taskd-postgres

# 监控数据库性能
psql -h localhost -U admin -d taskd_tokens -c "
SELECT * FROM pg_stat_activity WHERE state = 'active';
"
```

## 联系和支持

如有问题或建议，请联系开发团队或提交issue到项目仓库。