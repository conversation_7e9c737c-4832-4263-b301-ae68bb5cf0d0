-- TaskD 测试数据初始化脚本
-- 创建测试用户、公司和token限额配置

\echo '=== TaskD Test Data Initialization ==='

-- 首先确保数据库schema和表结构存在
CREATE SCHEMA IF NOT EXISTS taskd;
SET search_path TO taskd, public;

-- 创建subscription_type枚举类型（如果不存在）
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type t 
        JOIN pg_namespace n ON t.typnamespace = n.oid 
        WHERE t.typname = 'subscription_type' AND n.nspname = 'taskd'
    ) THEN
        CREATE TYPE subscription_type AS ENUM ('free', 'pro', 'max', 'premium', 'basic');
    END IF;
END $$;

-- 创建基础表结构（如果不存在）
CREATE TABLE IF NOT EXISTS companies (
    id SERIAL PRIMARY KEY,
    company_id VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(320),
    company_id VARCHAR(255),
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS token_limits (
    id SERIAL PRIMARY KEY,
    subscription_type subscription_type NOT NULL,
    limit_type VARCHAR(50) NOT NULL,
    token_limit INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_subscription_limit UNIQUE (subscription_type, limit_type)
);

CREATE TABLE IF NOT EXISTS token_consumption (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    model_provider VARCHAR(100) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    input_tokens INTEGER NOT NULL DEFAULT 0,
    output_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    cost_cents INTEGER DEFAULT 0,
    request_id VARCHAR(255),
    api_endpoint VARCHAR(255),
    consumed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS token_usage_summary (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    subscription_type subscription_type NOT NULL,
    period_type VARCHAR(50) NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_tokens INTEGER DEFAULT 0,
    total_cost_cents INTEGER DEFAULT 0,
    request_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

\echo '✓ Database schema initialized'

-- 1. 创建测试公司
INSERT INTO companies (company_id, company_name, subscription_type, created_at, updated_at)
VALUES ('test_company_intent', 'Test Company for Intent Recognition', 'premium', NOW(), NOW())
ON CONFLICT (company_id) DO UPDATE SET
    company_name = EXCLUDED.company_name,
    subscription_type = EXCLUDED.subscription_type,
    updated_at = NOW();

\echo '✓ Test company created: test_company_intent'

-- 2. 创建测试用户
INSERT INTO users (user_id, username, company_id, subscription_type, created_at, updated_at)
VALUES ('test_user_intent', 'Test User for Intent Recognition', 'test_company_intent', 'premium', NOW(), NOW())
ON CONFLICT (user_id) DO UPDATE SET
    username = EXCLUDED.username,
    company_id = EXCLUDED.company_id,
    subscription_type = EXCLUDED.subscription_type,
    updated_at = NOW();

\echo '✓ Test user created: test_user_intent'

-- 3. 创建token限额配置 (100万token限额)
INSERT INTO token_limits (subscription_type, limit_type, token_limit, created_at, updated_at)
VALUES 
    ('premium', 'daily', 1000000, NOW(), NOW()),
    ('premium', 'weekly', 7000000, NOW(), NOW()),
    ('premium', 'monthly', 30000000, NOW(), NOW()),
    ('basic', 'daily', 10000, NOW(), NOW()),
    ('basic', 'weekly', 50000, NOW(), NOW()),
    ('basic', 'monthly', 200000, NOW(), NOW())
ON CONFLICT (subscription_type, limit_type) DO UPDATE SET
    token_limit = EXCLUDED.token_limit,
    updated_at = NOW();

\echo '✓ Token limits configured:'
\echo '  - Premium daily: 1,000,000 tokens'
\echo '  - Premium weekly: 7,000,000 tokens'  
\echo '  - Premium monthly: 30,000,000 tokens'

-- 4. 清理旧的测试数据
DELETE FROM token_consumption WHERE user_id = 'test_user_intent';
DELETE FROM token_usage_summary WHERE user_id = 'test_user_intent';

\echo '✓ Old test data cleaned'

-- 5. 验证数据创建成功
\echo ''
\echo '=== Verification ==='

-- 验证用户
SELECT 'User verified:' as info, user_id, company_id, subscription_type 
FROM users WHERE user_id = 'test_user_intent';

-- 验证公司
SELECT 'Company verified:' as info, company_id, company_name, subscription_type 
FROM companies WHERE company_id = 'test_company_intent';

-- 验证token限额
SELECT 'Token limits:' as info, subscription_type, limit_type, token_limit 
FROM token_limits 
WHERE subscription_type = 'premium' 
ORDER BY 
    CASE limit_type 
        WHEN 'daily' THEN 1 
        WHEN 'weekly' THEN 2 
        WHEN 'monthly' THEN 3 
    END;

\echo ''
\echo 'Test data initialization completed successfully!'
\echo 'You can now run the intent recognition tests.'