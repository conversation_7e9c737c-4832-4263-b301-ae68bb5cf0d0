# A2A Agent协议规范

## 概述

本文档定义了招投标后处理系统中Agent间通信的A2A（Agent-to-Agent）协议规范。

## 协议基础

### 1. 传输层
- **协议**: HTTP/HTTPS
- **消息格式**: JSON-RPC 2.0
- **编码**: UTF-8
- **内容类型**: application/json

### 2. 消息结构

#### 请求消息格式
```json
{
  "jsonrpc": "2.0",
  "id": "req_1234567890",
  "method": "agent/execute",
  "params": {
    "skill_id": "技能标识",
    "input": {
      "参数名": "参数值"
    },
    "context": {
      "user_id": "用户ID",
      "company_id": "公司ID", 
      "trace_id": "跟踪ID",
      "language": "语言偏好"
    }
  }
}
```

#### 成功响应格式
```json
{
  "jsonrpc": "2.0",
  "id": "req_1234567890",
  "result": {
    "task_id": "任务ID",
    "status": "completed|running|failed",
    "output": {
      "结果数据": "..."
    },
    "metadata": {
      "execution_time": 1250,
      "tokens_used": 150,
      "model_version": "v1.0",
      "agent_version": "1.0.0"
    }
  }
}
```

#### 错误响应格式
```json
{
  "jsonrpc": "2.0",
  "id": "req_1234567890",
  "error": {
    "code": -32603,
    "message": "错误描述",
    "data": {
      "error_type": "业务错误|系统错误|AI错误",
      "retry_after": 60,
      "agent": "agent_name",
      "timestamp": "2024-07-19T10:30:00Z"
    }
  }
}
```

### 3. 错误码规范

| 错误码 | 含义 | 说明 |
|--------|------|------|
| -32700 | Parse error | JSON解析错误 |
| -32600 | Invalid Request | 无效请求 |
| -32601 | Method not found | 方法不存在 |
| -32602 | Invalid params | 无效参数 |
| -32603 | Internal error | 内部错误 |
| -40001 | Business Logic Error | 业务逻辑错误 |
| -40002 | AI Service Error | AI服务错误 |
| -40003 | Data Not Found | 数据不存在 |
| -40004 | Validation Error | 参数验证失败 |

### 4. 状态码规范

| 状态 | 说明 |
|------|------|
| pending | 任务等待执行 |
| running | 任务执行中 |
| completed | 任务执行成功 |
| failed | 任务执行失败 |
| cancelled | 任务被取消 |

## 认证和安全

### 1. 认证方式
- **Bearer Token**: JWT格式的访问令牌
- **API Key**: 自定义API密钥（用于Agent间内部通信）

### 2. 请求头
```
Authorization: Bearer <token>
Content-Type: application/json
X-Request-ID: <unique_request_id>
X-Agent-Version: 1.0.0
```

## Agent Discovery

### 1. Agent Card格式
每个Agent必须提供Agent Card，描述其能力和接口。

```json
{
  "name": "Agent名称",
  "description": "Agent描述",
  "url": "http://taskd-service:8601/agents/{agent-name}",
  "provider": {
    "organization": "TaskD Platform"
  },
  "version": "1.0.0",
  "capabilities": {
    "streaming": false,
    "pushNotifications": false,
    "stateTransitionHistory": true
  },
  "skills": [
    {
      "id": "技能ID",
      "name": "技能名称",
      "description": "技能描述",
      "inputSchema": {
        "type": "object",
        "properties": {},
        "required": []
      },
      "outputSchema": {
        "type": "object", 
        "properties": {}
      }
    }
  ]
}
```

### 2. 注册机制
- Agent启动时向taskd注册自身能力
- 提供健康检查端点 `/health`
- 提供Agent Card端点 `/.well-known/agent.json`

## 超时和重试

### 1. 超时设置
- **连接超时**: 10秒
- **读取超时**: 300秒（5分钟）
- **AI处理超时**: 180秒（3分钟）

### 2. 重试策略
- **最大重试次数**: 3次
- **重试间隔**: 指数退避（1s, 2s, 4s）
- **可重试错误**: 网络错误、超时、5xx错误
- **不可重试错误**: 参数错误、认证错误、业务逻辑错误

## 监控和日志

### 1. 日志格式
```json
{
  "timestamp": "2024-07-19T10:30:00Z",
  "level": "INFO",
  "agent": "agent_name",
  "request_id": "req_123",
  "method": "agent/execute",
  "skill_id": "skill_name",
  "execution_time": 1250,
  "status": "completed",
  "tokens_used": 150
}
```

### 2. 监控指标
- 请求量（QPS）
- 响应时间（P50, P95, P99）
- 错误率
- Agent健康状态
- 资源使用情况