#!/bin/bash

# This script deploys the taskd-bvt-debugger pod for interactive troubleshooting.
# It assumes the Docker image has been rebuilt with diagnostic tools and pushed.

set -e

# --- Configuration ---
NAMESPACE="ovs"
K8S_CONFIG_PATH="."

# --- Main Script ---

echo "Deploying BVT Debugger Pod with permissive network policy..."
echo "------------------------------"

# 1. Apply a permissive network policy to rule out connectivity issues
echo "Applying PERMISSIVE NetworkPolicy..."
kubectl apply -f "${K8S_CONFIG_PATH}/allow-all-in-namespace.yaml"

# 2. Apply the ConfigMap (re-using from the BVT test)
echo "Applying ConfigMap..."
kubectl apply -f "${K8S_CONFIG_PATH}/configmap.yaml"

# 3. Delete any existing debugger pod to ensure a fresh start
echo "Deleting old debugger pod if it exists..."
kubectl delete pod taskd-bvt-debugger -n "${NAMESPACE}" --ignore-not-found=true

# 4. Apply the Debugger Pod
echo "Applying new Debugger Pod..."
kubectl apply -f "${K8S_CONFIG_PATH}/debug-pod.yaml"

echo "------------------------------"
echo "Debugger pod deployment triggered."
echo
echo "--- What to do next ---"
echo "1. Wait for the pod to be in the 'Running' state:"
echo "   kubectl get pod taskd-bvt-debugger -n ${NAMESPACE} -w"
echo
echo "2. Once running, get a shell inside the pod:"
echo "   kubectl exec -it taskd-bvt-debugger -n ${NAMESPACE} -- /bin/bash"
echo
echo "3. After you've finished debugging, clean up the resources:"
echo "   kubectl delete pod taskd-bvt-debugger -n ${NAMESPACE}"
echo "   kubectl delete networkpolicy allow-all-in-ovs-namespace -n ${NAMESPACE}"
# Note: You might want to leave the configmap for the main test
echo "--------------------------" 