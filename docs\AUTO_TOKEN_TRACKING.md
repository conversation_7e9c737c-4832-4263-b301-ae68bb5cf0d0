# 自动Token跟踪功能

## 概述

自动Token跟踪功能允许在调用LLM时自动识别用户身份并记录token消耗，无需手动调用token管理API。这极大简化了token管理的集成工作。

## 功能特性

### ✅ 已实现的功能
- **自动限额检查**: 调用LLM前自动检查用户token限额
- **自动消耗记录**: LLM调用完成后自动记录实际token消耗
- **透明集成**: 对现有LLM调用代码影响最小
- **错误处理**: 当token限额不足时返回明确的错误信息
- **多提供商支持**: 支持OpenAI、火山方舟等多种LLM提供商

### 🔄 工作流程
1. **请求准备**: 在LLM请求中包含用户上下文信息
2. **限额检查**: 自动检查用户的token限额（月度/周度/日度）
3. **LLM调用**: 如果限额充足，正常调用LLM API
4. **消耗记录**: 根据LLM响应中的usage信息自动记录token消耗
5. **错误处理**: 如果限额不足，返回相应错误信息

## 使用方法

### 1. 基本使用

```go
// 构建LLM请求参数
params := llm.OpenAICompatibleRequestParams{
    Model:    "gpt-3.5-turbo",
    Messages: messages,
    
    // 启用自动token跟踪
    AutoTrackTokens: true,
    UserContext: &llm.UserContext{
        UserID:    "user123",
        CompanyID: "company123", 
        RequestID: "req_abc123",
        Endpoint:  "/v1/chat/completions",
    },
}

// 调用LLM（会自动检查限额和记录消耗）
response, err := llmClient.ChatCompletions(params)
if err != nil {
    // 检查是否是token限额错误
    if strings.Contains(err.Error(), "token限额") {
        return fmt.Errorf("用户token限额不足")
    }
    return err
}
```

### 2. API接口使用

#### 聊天接口（自动跟踪）
```bash
curl -X POST http://localhost:8601/v1/llm/chat \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "company_id": "company123", 
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ],
    "request_id": "req_123",
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

#### 文本总结接口（自动跟踪）
```bash
curl -X POST http://localhost:8601/v1/llm/summarize \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "company_id": "company123",
    "text": "这是一段需要总结的长文本...",
    "model": "gpt-3.5-turbo",
    "max_length": 200,
    "request_id": "req_456"
  }'
```

### 3. 集成到现有服务

```go
// 在现有的LLM服务中集成自动token跟踪
func (s *MyLLMService) ProcessWithTracking(userID, companyID, text string) (string, error) {
    // 构建消息
    messages := []models.LLMMessage{
        {Role: "system", Content: "你是一个助手"},
        {Role: "user", Content: text},
    }
    
    // 构建请求参数（启用自动跟踪）
    params := llm.OpenAICompatibleRequestParams{
        Model:           "gpt-3.5-turbo",
        Messages:        messages,
        AutoTrackTokens: true,
        UserContext: &llm.UserContext{
            UserID:    userID,
            CompanyID: companyID,
            RequestID: generateRequestID(),
            Endpoint:  "/api/my-service",
        },
    }
    
    // 调用LLM（自动处理token跟踪）
    return s.llmClient.ChatCompletions(params)
}
```

## 配置说明

### 1. 启用自动跟踪

在应用启动时，确保LLM客户端使用带Token服务的构造函数：

```go
// 创建带Token服务的LLM客户端
llmClient, err := llm.NewOpenAIClientWithTokenService(providerCfg, tokenService)
```

### 2. 请求参数配置

```go
type OpenAICompatibleRequestParams struct {
    // ... 其他参数
    
    // Token跟踪相关
    AutoTrackTokens bool         `json:"auto_track_tokens,omitempty"` // 是否启用自动跟踪
    UserContext     *UserContext `json:"user_context,omitempty"`      // 用户上下文信息
}

type UserContext struct {
    UserID    string `json:"user_id"`              // 用户ID（必需）
    CompanyID string `json:"company_id"`           // 公司ID（必需） 
    RequestID string `json:"request_id,omitempty"` // 请求ID（可选）
    Endpoint  string `json:"endpoint,omitempty"`   // 调用端点（可选）
}
```

## 错误处理

### Token限额错误
当用户token限额不足时，系统会返回特定的错误：

```go
// 错误示例
err := fmt.Errorf("token限额不足: Monthly token limit exceeded")

// 检查是否为限额错误
func isTokenLimitError(err error) bool {
    if err == nil {
        return false
    }
    errMsg := err.Error()
    return strings.Contains(errMsg, "token限额") || 
           strings.Contains(errMsg, "token limit") ||
           strings.Contains(errMsg, "limit exceeded")
}
```

### API错误响应
```json
{
  "error": "Token limit exceeded",
  "message": "Monthly token limit exceeded",
  "code": "TOKEN_LIMIT_EXCEEDED"
}
```

## 监控和日志

### 自动记录的日志
```
INFO 自动记录token消耗: 用户=user123, 公司=company123, 输入=150, 输出=75, 总计=225
INFO Token usage: Prompt=150, Completion=75, Total=225
```

### 限额检查日志
```
WARN 用户 user123 接近token限额: 已使用 4500/5000 (90%)
ERROR token限额不足: Daily token limit exceeded
```

## 性能优化

### 1. Token估算优化
- 实现了简单的token估算算法（每4个字符≈1个token）
- 可以根据实际需求优化估算精度

### 2. 异步记录优化
```go
// 可选：异步记录token消耗以减少响应延迟
go func() {
    _, err := tokenService.LogTokenConsumption(consumption)
    if err != nil {
        utils.Log.Errorf("异步记录token消耗失败: %v", err)
    }
}()
```

### 3. 缓存优化
- 可以实现用户限额状态的短期缓存
- 减少数据库查询频率

## 最佳实践

### 1. 用户上下文管理
```go
// 从HTTP请求中提取用户上下文
func extractUserContext(c *gin.Context) *llm.UserContext {
    return &llm.UserContext{
        UserID:    c.GetHeader("X-User-ID"),
        CompanyID: c.GetHeader("X-Company-ID"),
        RequestID: c.GetHeader("X-Request-ID"),
        Endpoint:  c.Request.URL.Path,
    }
}
```

### 2. 批量操作处理
```go
// 对于批量处理，在开始前检查总体限额
func processBatch(userID string, items []string) error {
    // 估算总token消耗
    estimatedTokens := estimateBatchTokens(items)
    
    // 检查限额
    limitCheck, err := tokenService.CheckTokenLimits(userID, estimatedTokens)
    if err != nil || !limitCheck.CanProceed {
        return fmt.Errorf("批量处理token限额不足")
    }
    
    // 逐个处理
    for _, item := range items {
        err := processItem(userID, item)
        if err != nil {
            return err
        }
    }
    return nil
}
```

### 3. 错误重试机制
```go
func callLLMWithRetry(params llm.OpenAICompatibleRequestParams, maxRetries int) (string, error) {
    for attempt := 0; attempt < maxRetries; attempt++ {
        response, err := llmClient.ChatCompletions(params)
        if err == nil {
            return response, nil
        }
        
        // 如果是token限额错误，不要重试
        if isTokenLimitError(err) {
            return "", err
        }
        
        // 其他错误可以重试
        time.Sleep(time.Duration(attempt+1) * time.Second)
    }
    return "", fmt.Errorf("重试次数已用完")
}
```

## 扩展功能

### 1. 成本预警
```go
// 可以扩展为成本预警功能
func checkCostThreshold(userID string, estimatedCost int) error {
    stats, err := tokenService.GetTokenUsageStats(userID)
    if err != nil {
        return err
    }
    
    monthlyCost := stats.MonthlyUsage.TotalCostCents
    if monthlyCost + estimatedCost > COST_THRESHOLD {
        return fmt.Errorf("预估成本超过阈值")
    }
    return nil
}
```

### 2. 动态限额调整
```go
// 基于使用模式动态调整限额
func adjustDynamicLimit(userID string) error {
    // 分析用户历史使用模式
    // 动态调整限额
    return tokenService.UpdateUserLimits(userID, newLimits)
}
```

## 总结

自动Token跟踪功能提供了：

1. **透明的集成体验**: 最小化对现有代码的修改
2. **自动化管理**: 无需手动调用token管理API
3. **实时限额控制**: 防止用户超出token限额
4. **详细的使用统计**: 自动记录所有token消耗
5. **灵活的配置选项**: 支持各种使用场景

通过这个功能，开发者可以专注于业务逻辑，而无需担心token管理的复杂性。