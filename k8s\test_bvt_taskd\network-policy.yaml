apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-bvt-to-taskd
  namespace: ovs
spec:
  podSelector:
    matchLabels:
      app: taskd
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          # This label is automatically added to pods created by the Job
          job-name: taskd-bvt-runner-job
    ports:
    - protocol: TCP
      port: 8601 