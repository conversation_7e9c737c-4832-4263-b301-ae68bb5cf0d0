apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: etcd
  namespace: etcd
  labels:
    app: etcd
    component: database
spec:
  serviceName: etcd-service
  replicas: 1
  selector:
    matchLabels:
      app: etcd
  template:
    metadata:
      labels:
        app: etcd
        component: database
    spec:
      containers:
      - name: etcd
        image: quay.io/coreos/etcd:v3.5.9
        ports:
        - name: client
          containerPort: 2379
          protocol: TCP
        - name: peer
          containerPort: 2380
          protocol: TCP
        envFrom:
        - configMapRef:
            name: etcd-config
        - secretRef:
            name: etcd-secret
            optional: true
        volumeMounts:
        - name: etcd-data
          mountPath: /etcd-data
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 2379
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 2379
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 2379
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
      volumes:
      - name: etcd-data
        persistentVolumeClaim:
          claimName: etcd-data-pvc
      securityContext:
        fsGroup: 2000
        runAsNonRoot: true
        runAsUser: 1000
      terminationGracePeriodSeconds: 60
---