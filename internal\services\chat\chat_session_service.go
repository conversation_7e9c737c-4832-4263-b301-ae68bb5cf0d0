package chat

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ChatSessionService 聊天会话管理服务
type ChatSessionService struct {
	store  store.DBTX
	config *models.ChatConfig
}

// NewChatSessionService 创建聊天会话管理服务
func NewChatSessionService(store store.DBTX, config *models.ChatConfig) *ChatSessionService {
	return &ChatSessionService{
		store:  store,
		config: config,
	}
}

// CreateSession 创建聊天会话
func (s *ChatSessionService) CreateSession(ctx context.Context, req *models.ChatSessionRequest) (*models.ChatSession, error) {
	sessionID := uuid.New().String()
	now := time.Now()
	expiresAt := now.Add(time.Duration(s.config.SessionTimeoutMinutes) * time.Minute)

	contextTokenLimit := s.config.DefaultTokenLimit
	if req.ContextTokenLimit != nil && *req.ContextTokenLimit > 0 {
		contextTokenLimit = *req.ContextTokenLimit
	}

	session := &models.ChatSession{
		SessionID:         sessionID,
		UserID:            req.UserID,
		CompanyID:         req.CompanyID,
		Status:            models.ChatSessionStatusActive,
		CreatedAt:         now,
		LastActivityAt:    now,
		ExpiresAt:         expiresAt,
		ContextTokenLimit: contextTokenLimit,
		CurrentTokenCount: 0,
	}

	query := `
		INSERT INTO chat_sessions (session_id, user_id, company_id, status, created_at, last_activity_at, expires_at, context_token_limit, current_token_count)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id`

	err := s.store.QueryRowContext(ctx, query,
		session.SessionID,
		session.UserID,
		session.CompanyID,
		session.Status,
		session.CreatedAt,
		session.LastActivityAt,
		session.ExpiresAt,
		session.ContextTokenLimit,
		session.CurrentTokenCount,
	).Scan(&session.ID)

	if err != nil {
		utils.Log.Errorf("Failed to create chat session: %v", err)
		return nil, fmt.Errorf("failed to create chat session: %w", err)
	}

	utils.Log.Infof("Created chat session: %s for user: %s", sessionID, req.UserID)
	return session, nil
}

// GetSession 获取聊天会话
func (s *ChatSessionService) GetSession(ctx context.Context, sessionID string) (*models.ChatSession, error) {
	query := `
		SELECT id, session_id, user_id, company_id, status, created_at, last_activity_at, expires_at, 
		       closed_at, context_token_limit, current_token_count
		FROM chat_sessions 
		WHERE session_id = $1`

	var session models.ChatSession
	err := s.store.QueryRowContext(ctx, query, sessionID).Scan(
		&session.ID,
		&session.SessionID,
		&session.UserID,
		&session.CompanyID,
		&session.Status,
		&session.CreatedAt,
		&session.LastActivityAt,
		&session.ExpiresAt,
		&session.ClosedAt,
		&session.ContextTokenLimit,
		&session.CurrentTokenCount,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("chat session not found: %s", sessionID)
		}
		utils.Log.Errorf("Failed to get chat session: %v", err)
		return nil, fmt.Errorf("failed to get chat session: %w", err)
	}

	return &session, nil
}

// UpdateSessionActivity 更新会话活动时间并延长过期时间
func (s *ChatSessionService) UpdateSessionActivity(ctx context.Context, sessionID string) error {
	now := time.Now()
	expiresAt := now.Add(time.Duration(s.config.SessionTimeoutMinutes) * time.Minute)

	query := `
		UPDATE chat_sessions 
		SET last_activity_at = $1, expires_at = $2
		WHERE session_id = $3 AND status = 'active'`

	result, err := s.store.ExecContext(ctx, query, now, expiresAt, sessionID)
	if err != nil {
		utils.Log.Errorf("Failed to update session activity: %v", err)
		return fmt.Errorf("failed to update session activity: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("session not found or not active: %s", sessionID)
	}

	return nil
}

// CloseSession 关闭会话
func (s *ChatSessionService) CloseSession(ctx context.Context, sessionID string) error {
	query := `
		UPDATE chat_sessions 
		SET status = 'inactive', closed_at = CURRENT_TIMESTAMP 
		WHERE session_id = $1 AND status = 'active'`

	result, err := s.store.ExecContext(ctx, query, sessionID)
	if err != nil {
		utils.Log.Errorf("Failed to close session: %v", err)
		return fmt.Errorf("failed to close session: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		utils.Log.Warnf("Session not found or already closed: %s", sessionID)
	} else {
		utils.Log.Infof("Closed chat session: %s", sessionID)
	}

	return nil
}

// UpdateTokenCount 更新会话的token计数
func (s *ChatSessionService) UpdateTokenCount(ctx context.Context, sessionID string, additionalTokens int) error {
	query := `
		UPDATE chat_sessions 
		SET current_token_count = current_token_count + $1
		WHERE session_id = $2 AND status = 'active'`

	result, err := s.store.ExecContext(ctx, query, additionalTokens, sessionID)
	if err != nil {
		utils.Log.Errorf("Failed to update token count: %v", err)
		return fmt.Errorf("failed to update token count: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("session not found or not active: %s", sessionID)
	}

	return nil
}

// CheckTokenLimit 检查token限制
func (s *ChatSessionService) CheckTokenLimit(ctx context.Context, sessionID string, additionalTokens int) (bool, error) {
	query := `
		SELECT current_token_count, context_token_limit 
		FROM chat_sessions 
		WHERE session_id = $1 AND status = 'active'`

	var currentTokens, tokenLimit int
	err := s.store.QueryRowContext(ctx, query, sessionID).Scan(&currentTokens, &tokenLimit)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, fmt.Errorf("session not found or not active: %s", sessionID)
		}
		utils.Log.Errorf("Failed to check token limit: %v", err)
		return false, fmt.Errorf("failed to check token limit: %w", err)
	}

	return (currentTokens + additionalTokens) <= tokenLimit, nil
}

// GetActiveSessions 获取活跃会话列表
func (s *ChatSessionService) GetActiveSessions(ctx context.Context, userID *string, limit int, offset int) ([]models.ChatSession, error) {
	var query string
	var args []interface{}

	if userID != nil {
		query = `
			SELECT id, session_id, user_id, company_id, status, created_at, last_activity_at, expires_at, 
			       closed_at, context_token_limit, current_token_count
			FROM chat_sessions 
			WHERE status = 'active' AND user_id = $1
			ORDER BY last_activity_at DESC
			LIMIT $2 OFFSET $3`
		args = []interface{}{*userID, limit, offset}
	} else {
		query = `
			SELECT id, session_id, user_id, company_id, status, created_at, last_activity_at, expires_at, 
			       closed_at, context_token_limit, current_token_count
			FROM chat_sessions 
			WHERE status = 'active'
			ORDER BY last_activity_at DESC
			LIMIT $1 OFFSET $2`
		args = []interface{}{limit, offset}
	}

	rows, err := s.store.QueryContext(ctx, query, args...)
	if err != nil {
		utils.Log.Errorf("Failed to get active sessions: %v", err)
		return nil, fmt.Errorf("failed to get active sessions: %w", err)
	}
	defer rows.Close()

	var sessions []models.ChatSession
	for rows.Next() {
		var session models.ChatSession
		err := rows.Scan(
			&session.ID,
			&session.SessionID,
			&session.UserID,
			&session.CompanyID,
			&session.Status,
			&session.CreatedAt,
			&session.LastActivityAt,
			&session.ExpiresAt,
			&session.ClosedAt,
			&session.ContextTokenLimit,
			&session.CurrentTokenCount,
		)
		if err != nil {
			utils.Log.Errorf("Failed to scan session: %v", err)
			continue
		}
		sessions = append(sessions, session)
	}

	return sessions, nil
}

// CleanupExpiredSessions 清理过期会话
func (s *ChatSessionService) CleanupExpiredSessions(ctx context.Context) (int, error) {
	// 调用数据库函数清理过期会话
	query := `SELECT cleanup_expired_chat_data()`

	var expiredCount int
	err := s.store.QueryRowContext(ctx, query).Scan(&expiredCount)
	if err != nil {
		utils.Log.Errorf("Failed to cleanup expired sessions: %v", err)
		return 0, fmt.Errorf("failed to cleanup expired sessions: %w", err)
	}

	if expiredCount > 0 {
		utils.Log.Infof("Cleaned up %d expired chat sessions", expiredCount)
	}

	return expiredCount, nil
}

// GetSessionStats 获取会话统计信息
func (s *ChatSessionService) GetSessionStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 活跃会话数
	var activeCount int
	err := s.store.QueryRowContext(ctx, "SELECT COUNT(*) FROM chat_sessions WHERE status = 'active'").Scan(&activeCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get active session count: %w", err)
	}
	stats["active_sessions"] = activeCount

	// 今日创建的会话数
	var todayCount int
	err = s.store.QueryRowContext(ctx, "SELECT COUNT(*) FROM chat_sessions WHERE created_at >= CURRENT_DATE").Scan(&todayCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get today session count: %w", err)
	}
	stats["today_sessions"] = todayCount

	// 总会话数
	var totalCount int
	err = s.store.QueryRowContext(ctx, "SELECT COUNT(*) FROM chat_sessions").Scan(&totalCount)
	if err != nil {
		return nil, fmt.Errorf("failed to get total session count: %w", err)
	}
	stats["total_sessions"] = totalCount

	return stats, nil
}
