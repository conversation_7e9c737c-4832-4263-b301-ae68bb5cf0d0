package common

// Agent names constants
const (
	AgentExtractionName      = "tender-data-extraction"
	AgentClassificationName  = "tender-classification"
	AgentEnhancementName     = "tender-content-enhancement"
	AgentMultilingualName    = "tender-multilingual"
	AgentAggregationName     = "tender-result-aggregation"
	AgentOrchestrationName   = "tender-preprocessing-orchestration"
	AgentDatabaseName        = "database-query"
)

// Skill IDs constants
const (
	SkillExtractStructuredData     = "extract_structured_data"
	SkillClassifyTender            = "classify_tender"
	SkillEnhanceContent            = "enhance_content"
	SkillProcessMultilingual       = "process_multilingual"
	SkillAggregateResults          = "aggregate_results"
	SkillProcessTenderWorkflow     = "process_tender_workflow"
	SkillProcessTenderBatch        = "process_tender_batch"
	SkillQueryCandidateData        = "query_candidate_data"
	SkillUpdateProcessingStatus    = "update_processing_status"
	SkillExecuteComplexQuery       = "execute_complex_query"
)

// Processing status constants
const (
	ProcessingStatusPending     = "pending"
	ProcessingStatusProcessing  = "processing"
	ProcessingStatusCompleted   = "completed"
	ProcessingStatusFailed      = "failed"
	ProcessingStatusPartial     = "partial"
)

// Task status constants
const (
	TaskStatusCompleted   = "completed"
	TaskStatusFailed      = "failed"
	TaskStatusInProgress  = "in_progress"
)

// Language constants
const (
	LanguageChinese = "chinese"
	LanguageEnglish = "english"
	LanguageJapanese = "japanese"
	LanguageKorean  = "korean"
)

// Extraction mode constants
const (
	ExtractionModeStandard = "standard"
	ExtractionModeDetailed = "detailed"
	ExtractionModeQuick    = "quick"
)

// Classification depth constants
const (
	ClassificationDepthLevel1 = 1
	ClassificationDepthLevel2 = 2
	ClassificationDepthLevel3 = 3
)

// Procurement type constants
const (
	ProcurementTypeProduct = "product"
	ProcurementTypeService = "service"
	ProcurementTypeHybrid  = "hybrid"
)

// Translation mode constants
const (
	TranslationModeBatch      = "batch"
	TranslationModeIndividual = "individual"
	TranslationModeStreaming  = "streaming"
)

// Output format constants
const (
	OutputFormatJSON     = "json"
	OutputFormatStandard = "standard"
	OutputFormatCompact  = "compact"
	OutputFormatDetailed = "detailed"
)

// Database collection constants
const (
	CollectionBiddingResult = "bidding_result"
	CollectionTenderResult  = "tender_result"
	CollectionAwardedResult = "awarded_result"
)

// Crawl mode constants
const (
	CrawlModeNormal         = "normal"
	CrawlModeAutomationRobot = "automation_robot"
	CrawlModeDrone          = "drone"
	CrawlModeContinue       = "is_continue"
)

// Time field constants
const (
	TimeFieldCrawlTime  = "crawl_time"
	TimeFieldUpdateTime = "update_time"
)

// Sort order constants
const (
	SortOrderAsc  = "asc"
	SortOrderDesc = "desc"
)

// Content completeness constants
const (
	ContentCompletenessComplete = "complete"
	ContentCompletenessPartial  = "partial"
	ContentCompletenessMinimal  = "minimal"
	ContentCompletenessAll      = "all"
)

// Priority constants
const (
	PriorityHigh   = "high"
	PriorityMedium = "medium"
	PriorityLow    = "low"
	PriorityAll    = "all"
)

// Validation status constants
const (
	ValidationStatusValid   = "valid"
	ValidationStatusInvalid = "invalid"
	ValidationStatusPartial = "partial"
)

// Error type constants
const (
	ErrorTypeAIFailure      = "ai_failure"
	ErrorTypeValidation     = "validation_error"
	ErrorTypeTimeout        = "timeout"
	ErrorTypeInternalError  = "internal_error"
	ErrorTypeAgentUnavailable = "agent_unavailable"
)

// Default values
const (
	DefaultPageSize         = 100
	DefaultMaxPageSize      = 1000
	DefaultTimeout          = 300  // seconds
	DefaultRetryAttempts    = 3
	DefaultMaxErrorCount    = 3
	DefaultMinContentLength = 100
	DefaultBatchSize        = 50
	DefaultConcurrentLimit  = 10
)

// Quality score thresholds
const (
	QualityScoreMinimum = 0.0
	QualityScoreMaximum = 10.0
	QualityThresholdHigh = 8.0
	QualityThresholdMedium = 6.0
)

// Confidence score thresholds
const (
	ConfidenceScoreMinimum = 0.0
	ConfidenceScoreMaximum = 1.0
	ConfidenceThresholdHigh = 0.8
	ConfidenceThresholdMedium = 0.6
)