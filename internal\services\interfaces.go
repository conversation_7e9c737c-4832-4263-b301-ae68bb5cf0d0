package services

import (
	"context"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models"
)

// LLMService 定义了与大语言模型（LLM）交互的基础服务接口。
type LLMService interface {
	ProcessRequest(ctx context.Context, params models.OpenAICompatibleRequestParams) (*models.LLMResponseMessage, error)
}

// EnhancedLLMService 增强的LLM服务接口，支持批量和模板化推理。
type EnhancedLLMService interface {
	LLMService
	ProcessBatchRequest(ctx context.Context, requests []*BatchRequestItem) (*BatchResponseResult, error)
	ProcessTemplateRequest(ctx context.Context, template *TemplateRequest) (*models.LLMResponseMessage, error)
	GetServiceStats() *LLMServiceStats
}

// TokenService 定义了用于管理和验证访问令牌的服务接口。
type TokenService interface {
	GenerateToken(userID string, companyID string) (string, error)
	ValidateToken(tokenString string) (*models.AuthClaims, error)
	RefreshToken(tokenString string) (string, error)
	CheckTokenLimits(userID string, requestedTokens int) (*models.TokenLimitCheck, error)
}

// BatchRequestItem 批量请求中的单个项目
type BatchRequestItem struct {
	ID       string                               `json:"id"`
	Params   models.OpenAICompatibleRequestParams `json:"params"`
	Priority int                                  `json:"priority"`
	Timeout  time.Duration                        `json:"timeout"`
	Metadata map[string]interface{}               `json:"metadata"`
}

// BatchResponseResult 批量请求的整体响应
type BatchResponseResult struct {
	TotalRequests   int                  `json:"total_requests"`
	SuccessfulCount int                  `json:"successful_count"`
	FailedCount     int                  `json:"failed_count"`
	TotalDuration   time.Duration        `json:"total_duration"`
	Items           []*BatchResponseItem `json:"items"`
	TokenUsage      *BatchTokenUsage     `json:"token_usage"`
}

// BatchResponseItem 批量响应中的单个项目
type BatchResponseItem struct {
	ID       string                     `json:"id"`
	Success  bool                       `json:"success"`
	Response *models.LLMResponseMessage `json:"response"`
	Error    string                     `json:"error"`
	Duration time.Duration              `json:"duration"`
}

// BatchTokenUsage 批量请求的token使用情况
type BatchTokenUsage struct {
	TotalInputTokens  int `json:"total_input_tokens"`
	TotalOutputTokens int `json:"total_output_tokens"`
	TotalTokens       int `json:"total_tokens"`
}

// TemplateRequest 模板化请求
type TemplateRequest struct {
	TemplateName string                 `json:"template_name"`
	Variables    map[string]interface{} `json:"variables"`
	Language     string                 `json:"language"`
	Model        string                 `json:"model"`
	Temperature  float32                `json:"temperature"`
	MaxTokens    int                    `json:"max_tokens"`
}

// LLMServiceStats LLM服务统计信息
type LLMServiceStats struct {
	TotalRequests       int64     `json:"total_requests"`
	SuccessfulRequests  int64     `json:"successful_requests"`
	FailedRequests      int64     `json:"failed_requests"`
	TotalTokensUsed     int64     `json:"total_tokens_used"`
	AverageResponseTime float64   `json:"average_response_time"`
	BatchRequestsCount  int64     `json:"batch_requests_count"`
	LastRequestTime     time.Time `json:"last_request_time"`
}

// EnhancedConcurrentService 增强的并发控制服务接口
type EnhancedConcurrentService interface {
	SubmitRequest(ctx context.Context, req *models.LLMRequestMessage) (*models.LLMResponseMessage, error)
	GetStats() *models.QueueStats
	Start(ctx context.Context) error
	Stop() error
}

// ReportSummaryService 报告总结服务接口
type ReportSummaryService interface {
	Summarize(req models.ReportSummaryRequest) (*models.ReportSummaryResponse, error)
}

// SentimentService 情感分析服务接口
type SentimentService interface {
	AnalyzeSentiment(req models.SentimentAnalysisRequest) (*models.SentimentAnalysisResponse, error)
}

// RelevanceService 相关性计算服务接口
type RelevanceService interface {
	CalculateRelevance(req models.RelevanceCalculationRequest) (*models.RelevanceCalculationResponse, error)
}
