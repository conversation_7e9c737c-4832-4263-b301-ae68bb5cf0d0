package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// promptBuilder 提示词构建器实现
type promptBuilder struct{}

// NewPromptBuilder 创建提示词构建器
func NewPromptBuilder() PromptBuilder {
	return &promptBuilder{}
}

// BuildPrompt 构建提示词
func (pb *promptBuilder) BuildPrompt(ctx context.Context, req *models.ExtractionRequest) (*models.PromptRequest, error) {
	// 构建系统提示词
	systemPrompt := pb.BuildSystemPrompt(req.TargetSchema, req.Language)
	
	// 构建用户提示词
	userPrompt := pb.BuildUserPrompt(req.InputText, req.TargetSchema, req.Language)
	
	// 注入示例数据
	if len(req.TargetSchema.Examples) > 0 {
		systemPrompt = pb.InjectExamples(systemPrompt, req.TargetSchema.Examples)
	}
	
	return &models.PromptRequest{
		SystemPrompt: systemPrompt,
		UserPrompt:   userPrompt,
		ModelSchema:  req.TargetSchema,
		Language:     req.Language,
	}, nil
}

// BuildSystemPrompt 构建系统提示词
func (pb *promptBuilder) BuildSystemPrompt(schema models.JSONSchema, language string) string {
	var systemPrompt strings.Builder
	
	// 根据语言选择基础提示词
	if language == "zh" || language == "mixed" {
		systemPrompt.WriteString("你是一个专业的实体提取专家。")
		systemPrompt.WriteString("请从用户输入的文本中提取符合指定数据模型的结构化信息。\n\n")
	} else {
		systemPrompt.WriteString("You are a professional entity extraction expert. ")
		systemPrompt.WriteString("Please extract structured information from user input text that conforms to the specified data model.\n\n")
	}
	
	// 添加模型信息
	if language == "zh" || language == "mixed" {
		systemPrompt.WriteString(fmt.Sprintf("目标数据模型: %s\n", schema.Name))
		systemPrompt.WriteString(fmt.Sprintf("模型描述: %s\n\n", schema.Description))
	} else {
		systemPrompt.WriteString(fmt.Sprintf("Target Data Model: %s\n", schema.Name))
		systemPrompt.WriteString(fmt.Sprintf("Model Description: %s\n\n", schema.Description))
	}
	
	// 注入模型结构
	systemPrompt.WriteString(pb.buildSchemaDescription(schema, language))
	
	// 添加输出要求
	systemPrompt.WriteString(pb.buildOutputRequirements(language))
	
	// 添加错误处理说明
	systemPrompt.WriteString(pb.buildErrorHandlingInstructions(language))
	
	return systemPrompt.String()
}

// buildSchemaDescription 构建Schema描述
func (pb *promptBuilder) buildSchemaDescription(schema models.JSONSchema, language string) string {
	var desc strings.Builder
	
	if language == "zh" || language == "mixed" {
		desc.WriteString("数据模型结构:\n")
	} else {
		desc.WriteString("Data Model Structure:\n")
	}
	
	// 转换为JSON格式显示
	schemaJSON, _ := json.MarshalIndent(pb.convertSchemaToJSON(schema), "", "  ")
	desc.WriteString(string(schemaJSON))
	desc.WriteString("\n\n")
	
	// 添加字段详细说明
	if language == "zh" || language == "mixed" {
		desc.WriteString("字段说明:\n")
	} else {
		desc.WriteString("Field Descriptions:\n")
	}
	
	desc.WriteString(pb.buildFieldDescriptions(schema.Properties, "", language))
	desc.WriteString("\n")
	
	// 添加必填字段说明
	if len(schema.Required) > 0 {
		if language == "zh" || language == "mixed" {
			desc.WriteString(fmt.Sprintf("必填字段: %s\n\n", strings.Join(schema.Required, ", ")))
		} else {
			desc.WriteString(fmt.Sprintf("Required Fields: %s\n\n", strings.Join(schema.Required, ", ")))
		}
	}
	
	return desc.String()
}

// convertSchemaToJSON 将Schema转换为JSON格式
func (pb *promptBuilder) convertSchemaToJSON(schema models.JSONSchema) map[string]interface{} {
	result := map[string]interface{}{
		"type":       schema.Type,
		"properties": pb.convertPropertiesToJSON(schema.Properties),
	}
	
	if len(schema.Required) > 0 {
		result["required"] = schema.Required
	}
	
	return result
}

// convertPropertiesToJSON 将属性转换为JSON格式
func (pb *promptBuilder) convertPropertiesToJSON(properties map[string]models.Property) map[string]interface{} {
	result := make(map[string]interface{})
	
	for name, prop := range properties {
		propJSON := map[string]interface{}{
			"type":        prop.Type,
			"description": prop.Description,
		}
		
		if prop.Format != "" {
			propJSON["format"] = prop.Format
		}
		
		if len(prop.Enum) > 0 {
			propJSON["enum"] = prop.Enum
		}
		
		if prop.Items != nil {
			propJSON["items"] = pb.convertPropertyToJSON(*prop.Items)
		}
		
		if len(prop.Properties) > 0 {
			propJSON["properties"] = pb.convertPropertiesToJSON(prop.Properties)
		}
		
		if prop.Default != nil {
			propJSON["default"] = prop.Default
		}
		
		result[name] = propJSON
	}
	
	return result
}

// convertPropertyToJSON 将单个属性转换为JSON格式
func (pb *promptBuilder) convertPropertyToJSON(prop models.Property) map[string]interface{} {
	result := map[string]interface{}{
		"type":        prop.Type,
		"description": prop.Description,
	}
	
	if prop.Format != "" {
		result["format"] = prop.Format
	}
	
	if len(prop.Enum) > 0 {
		result["enum"] = prop.Enum
	}
	
	if prop.Items != nil {
		result["items"] = pb.convertPropertyToJSON(*prop.Items)
	}
	
	if len(prop.Properties) > 0 {
		result["properties"] = pb.convertPropertiesToJSON(prop.Properties)
	}
	
	return result
}

// buildFieldDescriptions 构建字段描述
func (pb *promptBuilder) buildFieldDescriptions(properties map[string]models.Property, prefix string, language string) string {
	var desc strings.Builder
	
	for name, prop := range properties {
		fullName := name
		if prefix != "" {
			fullName = prefix + "." + name
		}
		
		// 基本描述
		desc.WriteString(fmt.Sprintf("- %s (%s): %s", fullName, prop.Type, prop.Description))
		
		// 添加格式说明
		if prop.Format != "" {
			if language == "zh" || language == "mixed" {
				desc.WriteString(fmt.Sprintf(" [格式: %s]", prop.Format))
			} else {
				desc.WriteString(fmt.Sprintf(" [Format: %s]", prop.Format))
			}
		}
		
		// 添加枚举值说明
		if len(prop.Enum) > 0 {
			if language == "zh" || language == "mixed" {
				desc.WriteString(fmt.Sprintf(" [可选值: %s]", strings.Join(prop.Enum, ", ")))
			} else {
				desc.WriteString(fmt.Sprintf(" [Options: %s]", strings.Join(prop.Enum, ", ")))
			}
		}
		
		// 添加默认值说明
		if prop.Default != nil {
			if language == "zh" || language == "mixed" {
				desc.WriteString(fmt.Sprintf(" [默认值: %v]", prop.Default))
			} else {
				desc.WriteString(fmt.Sprintf(" [Default: %v]", prop.Default))
			}
		}
		
		desc.WriteString("\n")
		
		// 递归处理嵌套属性
		if len(prop.Properties) > 0 {
			desc.WriteString(pb.buildFieldDescriptions(prop.Properties, fullName, language))
		}
		
		// 处理数组项属性
		if prop.Items != nil && len(prop.Items.Properties) > 0 {
			desc.WriteString(pb.buildFieldDescriptions(prop.Items.Properties, fullName+"[]", language))
		}
	}
	
	return desc.String()
}

// buildOutputRequirements 构建输出要求
func (pb *promptBuilder) buildOutputRequirements(language string) string {
	var requirements strings.Builder
	
	if language == "zh" || language == "mixed" {
		requirements.WriteString("输出要求:\n")
		requirements.WriteString("1. 严格按照JSON格式输出，不要包含任何其他文本\n")
		requirements.WriteString("2. 确保输出符合上述数据模型结构\n")
		requirements.WriteString("3. 必填字段必须包含，可选字段如无法提取可设为null\n")
		requirements.WriteString("4. 日期时间格式请使用ISO 8601标准 (YYYY-MM-DDTHH:mm:ssZ)\n")
		requirements.WriteString("5. 枚举字段必须使用预定义的值\n")
		requirements.WriteString("6. 数组字段如果没有数据请使用空数组[]\n")
		requirements.WriteString("7. 支持中英文混合输入的处理\n\n")
	} else {
		requirements.WriteString("Output Requirements:\n")
		requirements.WriteString("1. Output strictly in JSON format without any other text\n")
		requirements.WriteString("2. Ensure output conforms to the above data model structure\n")
		requirements.WriteString("3. Required fields must be included, optional fields can be set to null if not extractable\n")
		requirements.WriteString("4. Use ISO 8601 standard for date-time format (YYYY-MM-DDTHH:mm:ssZ)\n")
		requirements.WriteString("5. Enum fields must use predefined values\n")
		requirements.WriteString("6. Use empty array [] for array fields if no data\n")
		requirements.WriteString("7. Support processing of mixed Chinese and English input\n\n")
	}
	
	return requirements.String()
}

// buildErrorHandlingInstructions 构建错误处理说明
func (pb *promptBuilder) buildErrorHandlingInstructions(language string) string {
	var instructions strings.Builder
	
	if language == "zh" || language == "mixed" {
		instructions.WriteString("特殊情况处理:\n")
		instructions.WriteString("1. 如果输入文本与目标模型完全无关，请返回: {\"extraction_impossible\": true, \"reason\": \"输入文本与模型无关联\"}\n")
		instructions.WriteString("2. 如果某些字段无法从输入中提取，请设为null而不是省略\n")
		instructions.WriteString("3. 对于模糊或不确定的信息，请尽力推断但保持合理性\n")
		instructions.WriteString("4. 对于日期信息，支持相对日期转换（如\"明天\"、\"下周\"等）\n")
		instructions.WriteString("5. 对于枚举值，支持近似匹配和智能映射\n\n")
	} else {
		instructions.WriteString("Special Case Handling:\n")
		instructions.WriteString("1. If input text is completely unrelated to target model, return: {\"extraction_impossible\": true, \"reason\": \"Input text unrelated to model\"}\n")
		instructions.WriteString("2. If certain fields cannot be extracted from input, set to null rather than omit\n")
		instructions.WriteString("3. For ambiguous or uncertain information, make reasonable inferences\n")
		instructions.WriteString("4. For date information, support relative date conversion (e.g., \"tomorrow\", \"next week\")\n")
		instructions.WriteString("5. For enum values, support approximate matching and intelligent mapping\n\n")
	}
	
	return instructions.String()
}

// BuildUserPrompt 构建用户提示词
func (pb *promptBuilder) BuildUserPrompt(inputText string, schema models.JSONSchema, language string) string {
	var userPrompt strings.Builder
	
	if language == "zh" || language == "mixed" {
		userPrompt.WriteString("请从以下文本中提取符合数据模型的结构化信息:\n\n")
		userPrompt.WriteString("输入文本:\n")
		userPrompt.WriteString(inputText)
		userPrompt.WriteString("\n\n")
		userPrompt.WriteString("请严格按照JSON格式输出提取结果:")
	} else {
		userPrompt.WriteString("Please extract structured information that conforms to the data model from the following text:\n\n")
		userPrompt.WriteString("Input Text:\n")
		userPrompt.WriteString(inputText)
		userPrompt.WriteString("\n\n")
		userPrompt.WriteString("Please output the extraction results strictly in JSON format:")
	}
	
	return userPrompt.String()
}

// InjectExamples 注入示例数据
func (pb *promptBuilder) InjectExamples(prompt string, examples []interface{}) string {
	if len(examples) == 0 {
		return prompt
	}
	
	var exampleSection strings.Builder
	exampleSection.WriteString("示例数据 / Examples:\n")
	
	for i, example := range examples {
		exampleJSON, err := json.MarshalIndent(example, "", "  ")
		if err != nil {
			continue
		}
		
		exampleSection.WriteString(fmt.Sprintf("示例 %d / Example %d:\n", i+1, i+1))
		exampleSection.WriteString(string(exampleJSON))
		exampleSection.WriteString("\n\n")
	}
	
	// 在输出要求之前插入示例
	outputRequirementsIndex := strings.Index(prompt, "输出要求:")
	if outputRequirementsIndex == -1 {
		outputRequirementsIndex = strings.Index(prompt, "Output Requirements:")
	}
	
	if outputRequirementsIndex != -1 {
		return prompt[:outputRequirementsIndex] + exampleSection.String() + prompt[outputRequirementsIndex:]
	}
	
	// 如果找不到插入点，就添加到末尾
	return prompt + "\n" + exampleSection.String()
}