#!/bin/bash

# This script deploys the pre-built taskd-bvt-runner to the k3s cluster.
# It assumes the Docker image has already been built and pushed to the registry.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# Kubernetes namespace
NAMESPACE="ovs"
# Path to the Kubernetes configuration files
K8S_CONFIG_PATH="." # Assumes script is run from its directory

# --- Main Script ---

echo "Deploying BVT runner job to k3s cluster..."
echo "-------------------------------------------"

# 1. Apply the Network Policy to allow traffic
echo "Applying NetworkPolicy..."
kubectl apply -f "${K8S_CONFIG_PATH}/network-policy.yaml"

# 2. Apply the ConfigMap
echo "Applying ConfigMap..."
kubectl apply -f "${K8S_CONFIG_PATH}/configmap.yaml"

# 3. Delete any existing job to ensure a fresh run
echo "Deleting old BVT runner job if it exists..."
kubectl delete job taskd-bvt-runner-job -n "${NAMESPACE}" --ignore-not-found=true

# 4. Apply the Job to start the tests
echo "Applying new Job to pull the image and run tests..."
kubectl apply -f "${K8S_CONFIG_PATH}/job.yaml"

echo "-------------------------------------------"
echo " Deployment triggered successfully."
echo
echo "--- What to do next ---"
echo "1. Wait for the test pod to be created. You can watch its status with:"
echo "   kubectl get pods -n ${NAMESPACE} -l job-name=taskd-bvt-runner-job -w"
echo
echo "2. Once the pod is running or has completed, view the test execution logs:"
echo "   POD_NAME=\$(kubectl get pods --namespace ${NAMESPACE} -l job-name=taskd-bvt-runner-job -o jsonpath='{.items[0].metadata.name}')"
echo "   echo \"Fetching logs for pod: \$POD_NAME\""
echo "   kubectl logs -f \$POD_NAME --namespace ${NAMESPACE}"
echo
echo "3. After reviewing the logs, you can clean up the test resources:"
echo "   kubectl delete job taskd-bvt-runner-job --namespace ${NAMESPACE}"
echo "   kubectl delete configmap taskd-bvt-runner-config --namespace ${NAMESPACE}"
echo "   kubectl delete networkpolicy allow-bvt-to-taskd --namespace ${NAMESPACE}"
echo "--------------------------" 