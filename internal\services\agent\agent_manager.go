package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"github.com/sirupsen/logrus"
)

// AgentManagerImpl Agent管理器实现
type AgentManagerImpl struct {
	agents    map[string]core.Agent
	mu        sync.RWMutex
	store     store.Store
	logger    *logrus.Logger
	executor  core.AgentExecutor
	monitor   core.AgentMonitor
	registry  core.AgentRegistry
	template  core.AgentTemplate
}

// NewAgentManager 创建Agent管理器
func NewAgentManager(store store.Store) *AgentManagerImpl {
	return &AgentManagerImpl{
		agents: make(map[string]core.Agent),
		store:  store,
		logger: utils.Log, // 使用统一的logger
	}
}

// SetDependencies 设置依赖
func (am *AgentManagerImpl) SetDependencies(executor core.AgentExecutor, monitor core.AgentMonitor, registry core.AgentRegistry, template core.AgentTemplate) {
	am.executor = executor
	am.monitor = monitor
	am.registry = registry
	am.template = template
}

// RegisterAgent 注册Agent
func (am *AgentManagerImpl) RegisterAgent(ctx context.Context, agent core.Agent) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	agentID := agent.GetID()
	
	// 检查Agent是否已存在
	if _, exists := am.agents[agentID]; exists {
		return fmt.Errorf("agent with ID '%s' already exists", agentID)
	}
	
	// 初始化Agent
	if err := agent.Initialize(ctx, nil); err != nil {
		return fmt.Errorf("failed to initialize agent '%s': %w", agentID, err)
	}
	
	// 注册到内存
	am.agents[agentID] = agent
	
	// 创建Agent实例记录
	instance := &models.AgentInstance{
		ID:        agentID,
		CardID:    agentID, // 简化处理，使用相同ID
		Status:    models.AgentStatusIdle,
		Config:    make(map[string]interface{}),
		StartedAt: time.Now(),
		LastPing:  time.Now(),
	}
	
	// 保存到数据库
	if err := am.saveAgentInstance(ctx, instance); err != nil {
		// 如果保存失败，从内存中移除
		delete(am.agents, agentID)
		return fmt.Errorf("failed to save agent instance: %w", err)
	}
	
	am.logger.WithFields(logrus.Fields{
		"agent_id": agentID,
		"type":     agent.GetType(),
	}).Info("Agent注册成功")
	
	return nil
}

// UnregisterAgent 注销Agent
func (am *AgentManagerImpl) UnregisterAgent(ctx context.Context, agentID string) error {
	am.mu.Lock()
	defer am.mu.Unlock()
	
	agent, exists := am.agents[agentID]
	if !exists {
		return fmt.Errorf("agent with ID '%s' not found", agentID)
	}
	
	// 关闭Agent
	if err := agent.Shutdown(ctx); err != nil {
		am.logger.WithError(err).WithField("agent_id", agentID).Warn("Agent关闭时出现错误")
	}
	
	// 从内存中移除
	delete(am.agents, agentID)
	
	// 从数据库中删除
	if err := am.deleteAgentInstance(ctx, agentID); err != nil {
		am.logger.WithError(err).WithField("agent_id", agentID).Warn("删除Agent实例时出现错误")
	}
	
	am.logger.WithField("agent_id", agentID).Info("Agent注销成功")
	
	return nil
}

// GetAgent 获取Agent实例
func (am *AgentManagerImpl) GetAgent(ctx context.Context, agentID string) (core.Agent, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	agent, exists := am.agents[agentID]
	if !exists {
		return nil, fmt.Errorf("agent with ID '%s' not found", agentID)
	}
	
	return agent, nil
}

// ListAgents 列出所有Agent
func (am *AgentManagerImpl) ListAgents(ctx context.Context, filter models.AgentListRequest) (*models.AgentListResponse, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	var instances []models.AgentInstance
	total := int64(0)
	
	// 从数据库获取Agent实例列表
	if err := am.loadAgentInstances(ctx, &instances, filter); err != nil {
		return nil, fmt.Errorf("failed to load agent instances: %w", err)
	}
	
	total = int64(len(instances))
	
	// 分页处理
	page := filter.Page
	if page <= 0 {
		page = 1
	}
	limit := filter.Limit
	if limit <= 0 {
		limit = 20
	}
	
	start := (page - 1) * limit
	end := start + limit
	
	if start < len(instances) {
		if end > len(instances) {
			end = len(instances)
		}
		instances = instances[start:end]
	} else {
		instances = []models.AgentInstance{}
	}
	
	return &models.AgentListResponse{
		Total:  total,
		Page:   page,
		Limit:  limit,
		Agents: instances,
	}, nil
}

// ExecuteAgent 执行Agent
func (am *AgentManagerImpl) ExecuteAgent(ctx context.Context, req *models.AgentExecutionRequest) (*models.AgentResponse, error) {
	// 获取Agent实例
	agent, err := am.GetAgent(ctx, req.AgentID)
	if err != nil {
		return nil, err
	}
	
	// 更新Agent状态为处理中
	am.updateAgentStatus(ctx, req.AgentID, models.AgentStatusProcessing)
	
	start := time.Now()
	
	// 执行Agent
	response, err := agent.Execute(ctx, req.Capability, req.Input, req.Config)
	if err != nil {
		// 更新Agent状态为错误
		am.updateAgentStatus(ctx, req.AgentID, models.AgentStatusError)
		return nil, fmt.Errorf("failed to execute agent: %w", err)
	}
	
	// 更新Agent状态为空闲
	am.updateAgentStatus(ctx, req.AgentID, models.AgentStatusIdle)
	
	// 记录执行历史
	execution := &models.AgentExecution{
		ID:         fmt.Sprintf("exec_%d", time.Now().UnixNano()),
		AgentID:    req.AgentID,
		RequestID:  response.RequestID,
		Capability: req.Capability,
		Input:      req.Input,
		Output:     response.Output,
		Success:    response.Success,
		Error:      response.Error,
		Duration:   time.Since(start).Milliseconds(),
		CreatedAt:  time.Now(),
	}
	
	if am.monitor != nil {
		if err := am.monitor.RecordExecution(ctx, execution); err != nil {
			am.logger.WithError(err).Warn("记录Agent执行历史失败")
		}
	}
	
	return response, nil
}

// GetAgentMetrics 获取Agent指标
func (am *AgentManagerImpl) GetAgentMetrics(ctx context.Context, agentID string) (*models.AgentMetrics, error) {
	if am.monitor == nil {
		return nil, fmt.Errorf("monitor service not available")
	}
	
	return am.monitor.GetMetrics(ctx, agentID)
}

// HealthCheckAll 检查所有Agent健康状态
func (am *AgentManagerImpl) HealthCheckAll(ctx context.Context) ([]models.AgentHealthCheck, error) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	var checks []models.AgentHealthCheck
	
	for agentID, agent := range am.agents {
		check, err := agent.HealthCheck(ctx)
		if err != nil {
			check = &models.AgentHealthCheck{
				AgentID:   agentID,
				Status:    models.AgentStatusError,
				Healthy:   false,
				Message:   fmt.Sprintf("Health check failed: %v", err),
				CheckedAt: time.Now(),
			}
		}
		checks = append(checks, *check)
	}
	
	return checks, nil
}

// StartHeartbeat 启动心跳检查
func (am *AgentManagerImpl) StartHeartbeat(ctx context.Context, interval time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			am.performHeartbeat(ctx)
		}
	}
}

// performHeartbeat 执行心跳检查
func (am *AgentManagerImpl) performHeartbeat(ctx context.Context) {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	for agentID, agent := range am.agents {
		go func(id string, a core.Agent) {
			if _, err := a.HealthCheck(ctx); err != nil {
				am.logger.WithError(err).WithField("agent_id", id).Warn("Agent健康检查失败")
				am.updateAgentStatus(ctx, id, models.AgentStatusError)
			} else {
				// 更新ping时间
				if baseAgent, ok := a.(*core.BaseAgent); ok {
					baseAgent.UpdatePing()
				}
				am.updateAgentStatus(ctx, id, models.AgentStatusIdle)
			}
		}(agentID, agent)
	}
}

// updateAgentStatus 更新Agent状态
func (am *AgentManagerImpl) updateAgentStatus(ctx context.Context, agentID string, status models.AgentStatus) {
	// 这里应该更新数据库中的Agent状态
	// 简化实现，只记录日志
	am.logger.WithFields(logrus.Fields{
		"agent_id": agentID,
		"status":   status,
	}).Debug("更新Agent状态")
}

// saveAgentInstance 保存Agent实例
func (am *AgentManagerImpl) saveAgentInstance(ctx context.Context, instance *models.AgentInstance) error {
	// 这里应该保存到数据库
	// 简化实现，暂时不保存
	return nil
}

// deleteAgentInstance 删除Agent实例
func (am *AgentManagerImpl) deleteAgentInstance(ctx context.Context, agentID string) error {
	// 这里应该从数据库删除
	// 简化实现，暂时不删除
	return nil
}

// loadAgentInstances 加载Agent实例列表
func (am *AgentManagerImpl) loadAgentInstances(ctx context.Context, instances *[]models.AgentInstance, filter models.AgentListRequest) error {
	am.mu.RLock()
	defer am.mu.RUnlock()
	
	// 简化实现，从内存构建实例列表
	for agentID, agent := range am.agents {
		instance := models.AgentInstance{
			ID:        agentID,
			CardID:    agentID,
			Status:    models.AgentStatusIdle,
			Config:    make(map[string]interface{}),
			StartedAt: time.Now(),
			LastPing:  time.Now(),
		}
		
		// 应用过滤器
		if filter.Type != "" && agent.GetType() != filter.Type {
			continue
		}
		
		*instances = append(*instances, instance)
	}
	
	return nil
}