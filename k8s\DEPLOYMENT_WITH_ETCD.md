# TaskD与etcd配置中心完整部署指南

## 概述

本文档详细说明如何在k3s集群中部署etcd配置中心，并将taskd配置为强依赖etcd的模式。

## 架构说明

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   TaskD Pod     │────│  etcd Service   │────│   etcd Pod      │
│ (ovs namespace) │    │ (etcd namespace)│    │ (etcd namespace)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         │              读取配置                          │
         └──────────────────────────────────────────────┘
```

## 部署步骤

### 第一步：部署etcd配置中心

```bash
# 1. 进入etcd目录
cd k8s/etcd/

# 2. 自动部署etcd
./deploy-etcd.sh

# 3. 初始化配置数据
./init-config.sh
```

### 第二步：验证etcd配置

```bash
# 检查etcd Pod状态
kubectl get pods -n etcd

# 检查配置数据
kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd --prefix

# 预期输出应包含：
# /config/taskd/mongodb/uri
# /config/taskd/postgresql/host
# /config/taskd/pulsar/service_url
# 等配置项
```

### 第三步：部署taskd服务

```bash
# 1. 进入taskd目录
cd ../taskd/

# 2. 自动部署taskd（推荐）
./deploy-taskd.sh

# 或者手动部署：
# kubectl apply -f namespace.yaml
# kubectl apply -f configmap.yaml
# kubectl apply -f secret.yaml
# kubectl apply -f service.yaml
# kubectl apply -f deployment.yaml

# 3. 检查taskd启动日志
kubectl logs -n ovs -l app=taskd -f
```

## 配置管理

### 查看配置

```bash
# 查看所有taskd配置
kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd --prefix

# 查看特定配置
kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd/mongodb/uri
```

### 修改配置

```bash
# 修改MongoDB数据库名
kubectl exec -n etcd etcd-0 -- etcdctl put /config/taskd/mongodb/database "new_database_name"

# 修改LLM API密钥
kubectl exec -n etcd etcd-0 -- etcdctl put /config/taskd/llm/providers/volcengine_ark/api_key "new-api-key"

# 重启taskd以应用新配置
kubectl delete pod -n ovs -l app=taskd
```

### 配置键命名规范

```
/config/taskd/
├── mongodb/
│   ├── uri
│   └── database
├── postgresql/
│   ├── host
│   ├── user
│   ├── password
│   └── database
├── pulsar/
│   └── service_url
└── llm/
    └── providers/
        └── volcengine_ark/
            ├── api_key
            └── base_url
```

## 故障排除

### TaskD启动失败

1. **检查etcd连接**：
```bash
kubectl logs -n ovs -l app=taskd | grep etcd
```

2. **验证etcd服务**：
```bash
kubectl get svc -n etcd etcd-service
nslookup etcd-service.etcd.svc.cluster.local
```

3. **检查网络连接**：
```bash
kubectl exec -n ovs <taskd-pod> -- nc -zv etcd-service.etcd.svc.cluster.local 2379
```

### 配置未生效

1. **验证配置存在**：
```bash
kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd/<config-key>
```

2. **重启taskd**：
```bash
kubectl delete pod -n ovs -l app=taskd
```

### etcd数据丢失

1. **检查PVC状态**：
```bash
kubectl get pvc -n etcd
```

2. **恢复配置**：
```bash
./init-config.sh
```

## 本地开发模式

对于本地开发，taskd仍支持YAML配置文件模式：

```bash
# 使用本地配置文件启动
ETCD_ENABLED=false ./taskd
```

## 安全建议

1. **生产环境配置**：
   - 启用etcd TLS加密
   - 配置etcd身份验证
   - 使用网络策略限制访问

2. **敏感配置管理**：
   - API密钥等敏感信息建议加密存储
   - 定期轮换密钥

3. **访问控制**：
   - 限制对etcd namespace的访问
   - 使用RBAC控制配置修改权限

## 监控和维护

### 监控指标

```bash
# etcd健康状态
kubectl exec -n etcd etcd-0 -- etcdctl endpoint health

# etcd性能指标
kubectl exec -n etcd etcd-0 -- etcdctl endpoint status -w table
```

### 数据备份

```bash
# 创建快照
kubectl exec -n etcd etcd-0 -- etcdctl snapshot save /tmp/backup.db

# 复制到本地
kubectl cp etcd/etcd-0:/tmp/backup.db ./etcd-backup-$(date +%Y%m%d).db
```

### 日志监控

```bash
# 查看etcd日志
kubectl logs -n etcd -l app=etcd -f

# 查看taskd配置加载日志
kubectl logs -n ovs -l app=taskd | grep -E "(etcd|config)"
```

## 常见问题

**Q: taskd启动时报"无法连接到etcd配置中心"？**
A: 检查etcd服务是否正常运行，网络是否可达。

**Q: 配置修改后没有生效？**
A: 需要重启taskd Pod以重新加载配置。

**Q: 如何回退到YAML配置模式？**
A: 设置环境变量 `ETCD_ENABLED=false` 并重启服务。

**Q: etcd数据丢失怎么办？**
A: 运行 `./init-config.sh` 重新初始化基础配置。

## 版本兼容性

- **k3s**: v1.21+
- **etcd**: v3.5.9
- **kubectl**: v1.21+
- **taskd**: 支持etcd的版本

## 联系支持

如有问题，请查看：
1. Pod日志：`kubectl logs -n <namespace> <pod-name>`
2. 事件日志：`kubectl get events -n <namespace>`
3. 资源状态：`kubectl describe <resource> -n <namespace>`