// internal/prompts/manager.go
package prompts

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/spf13/viper" // 用于读取独立的 prompt YAML 文件
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/utils" // 假设 utils.Log 存在
)

const (
	defaultPromptsDir = "prompts" // 默认存放独立 prompt 文件的目录
)

// PromptManager 管理 Prompt 模板
type PromptManager struct {
	prompts map[string]config.PromptSet
}

// NewPromptManager 创建 Prompt 管理器实例
// 它会首先加载 appCfg.Prompts (来自主配置文件)，然后扫描 promptsDir 加载独立的 prompt 文件。
func NewPromptManager(appCfgPrompts map[string]config.PromptSet, promptsDir string) (*PromptManager, error) {
	pm := &PromptManager{
		prompts: make(map[string]config.PromptSet),
	}

	// 1. 加载来自主配置的 Prompts (作为基础或公共部分)
	if appCfgPrompts != nil {
		for name, ps := range appCfgPrompts {
			pm.prompts[name] = ps
			utils.Log.Debugf("PromptManager: Loaded prompt set '%s' from main config.", name)
		}
	}

	// 2. 扫描并加载来自独立目录的 Prompts
	if promptsDir == "" {
		promptsDir = defaultPromptsDir
	}

	// 检查目录是否存在
	if _, err := os.Stat(promptsDir); os.IsNotExist(err) {
		utils.Log.Infof("PromptManager: Prompts directory '%s' not found, skipping loading individual prompt files.", promptsDir)
		// 如果主配置中也没有 prompts，且 prompts 目录不存在，则 prompts map 为空
		if len(pm.prompts) == 0 {
			utils.Log.Warn("PromptManager: No prompts loaded from main config or prompts directory.")
		}
		return pm, nil // 目录不存在不是致命错误，可能有些项目不需要独立 prompt 文件
	}

	_, err := os.ReadDir(promptsDir)
	if err != nil {
		return nil, fmt.Errorf("读取 prompts 目录 '%s' 失败: %w", promptsDir, err)
	}

	// 使用递归函数扫描所有子目录
	if err := pm.scanPromptsDir(promptsDir, promptsDir); err != nil {
		return nil, fmt.Errorf("扫描 prompts 目录失败: %w", err)
	}
	if len(pm.prompts) == 0 {
		utils.Log.Warn("PromptManager: No prompts were loaded after scanning main config and prompts directory.")
	}

	return pm, nil
}

// scanPromptsDir 递归扫描目录中的 prompt 文件
func (pm *PromptManager) scanPromptsDir(currentDir, rootDir string) error {
	files, err := os.ReadDir(currentDir)
	if err != nil {
		return fmt.Errorf("读取目录 '%s' 失败: %w", currentDir, err)
	}

	for _, file := range files {
		fullPath := filepath.Join(currentDir, file.Name())

		if file.IsDir() {
			// 递归扫描子目录
			if err := pm.scanPromptsDir(fullPath, rootDir); err != nil {
				utils.Log.Warnf("PromptManager: 扫描子目录 '%s' 失败: %v", fullPath, err)
				continue
			}
		} else {
			// 处理文件
			fileName := file.Name()
			ext := filepath.Ext(fileName)
			if ext != ".yaml" && ext != ".yml" {
				continue // 只处理 YAML 文件
			}

			promptName := strings.TrimSuffix(fileName, ext)
			
			// 计算相对于根目录的路径，用作prompt key
			relPath, err := filepath.Rel(rootDir, currentDir)
			if err != nil {
				utils.Log.Warnf("PromptManager: 计算相对路径失败 '%s': %v", currentDir, err)
				continue
			}
			
			// 构造prompt key：如果在子目录中，则为 "subdir/filename"
			var promptKey string
			if relPath == "." {
				promptKey = promptName
			} else {
				promptKey = filepath.Join(relPath, promptName)
				// 标准化路径分隔符为 /
				promptKey = strings.ReplaceAll(promptKey, string(filepath.Separator), "/")
			}

			// 使用 Viper 读取单个 prompt 文件
			vp := viper.New()
			vp.SetConfigFile(fullPath)
			if err := vp.ReadInConfig(); err != nil {
				utils.Log.Warnf("PromptManager: 读取 prompt 文件 '%s' 失败: %v. Skipping.", fullPath, err)
				continue
			}

			var ps config.PromptSet
			if err := vp.Unmarshal(&ps); err != nil {
				utils.Log.Warnf("PromptManager: 解析 prompt 文件 '%s' 失败: %v. Skipping.", fullPath, err)
				continue
			}

			// 校验加载的 PromptSet 是否完整 (可选)
			if ps.SysPrompt == "" && ps.UserPrompt == "" {
				utils.Log.Warnf("PromptManager: Prompt set in file '%s' for '%s' is empty or invalid (missing sys_prompt and user_prompt). Skipping.", fullPath, promptKey)
				continue
			}

			// 如果键名冲突，来自独立文件的会覆盖主配置中的
			if _, exists := pm.prompts[promptKey]; exists {
				utils.Log.Infof("PromptManager: Prompt set '%s' from file '%s' is overriding the one from main config.", promptKey, fullPath)
			} else {
				utils.Log.Infof("PromptManager: Loaded prompt set '%s' from file '%s'.", promptKey, fullPath)
			}
			pm.prompts[promptKey] = ps
		}
	}
	return nil
}

// GetPromptSet 获取指定名称的 Prompt 集合
func (pm *PromptManager) GetPromptSet(name string) (config.PromptSet, error) {
	ps, ok := pm.prompts[name]
	if !ok {
		return config.PromptSet{}, fmt.Errorf("未找到 Prompt 集合: '%s'", name)
	}
	return ps, nil
}

// GetPromptSetByLanguage 根据语言和模块路径获取指定名称的 Prompt 集合
// promptPath: 完整路径，如 "ovs-profile/english/company_profile"
// language: "chinese", "english", "japanese" 等
// name: prompt名称，如 "company_profile"
func (pm *PromptManager) GetPromptSetByLanguage(promptPath, language, name string) (config.PromptSet, error) {
	// 构造完整的key路径
	languageKey := fmt.Sprintf("%s/%s/%s", promptPath, language, name)
	
	// 优先查找指定语言的prompt
	if ps, ok := pm.prompts[languageKey]; ok {
		return ps, nil
	}
	
	// 如果没找到指定语言，尝试英语作为默认语言
	if language != "english" {
		englishKey := fmt.Sprintf("%s/english/%s", promptPath, name)
		if ps, ok := pm.prompts[englishKey]; ok {
			utils.Log.Warnf("PromptManager: 未找到路径 '%s' 中语言 '%s' 的 prompt '%s'，使用英语默认版本", promptPath, language, name)
			return ps, nil
		}
	}
	
	// 调试信息：打印所有可用的keys
	utils.Log.Debugf("PromptManager: 可用的 prompt keys: %v", pm.getAvailableKeys())
	
	return config.PromptSet{}, fmt.Errorf("未找到路径 '%s' 中的 prompt '%s'（语言：%s）", promptPath, name, language)
}

// getAvailableKeys 获取所有可用的prompt keys（用于调试）
func (pm *PromptManager) getAvailableKeys() []string {
	keys := make([]string, 0, len(pm.prompts))
	for key := range pm.prompts {
		keys = append(keys, key)
	}
	return keys
}

// FormatPrompt 使用给定数据格式化 Prompt 字符串
func (pm *PromptManager) FormatPrompt(promptTemplate string, data interface{}) (string, error) {
	tmpl, err := template.New("prompt").Parse(promptTemplate)
	if err != nil {
		return "", fmt.Errorf("解析 Prompt 模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("执行 Prompt 模板失败: %w", err)
	}
	return buf.String(), nil
}
