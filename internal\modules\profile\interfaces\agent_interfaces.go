package interfaces

import (
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	profileModels "gitlab.com/specific-ai/taskd/internal/modules/profile/models"
)

// ProfileAgent 企业画像Agent接口
type ProfileAgent interface {
	// ExecuteSkill 执行技能
	ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error)
	
	// GetAgentCard 获取Agent卡片
	GetAgentCard() biddingModels.AgentCard
	
	// HealthCheck 健康检查
	HealthCheck() biddingModels.HealthStatus
	
	// GetID 获取Agent ID
	GetID() string
}

// ProfileAgentManager 企业画像Agent管理器接口
type ProfileAgentManager interface {
	// GetProfileAgent 获取企业画像Agent
	GetProfileAgent() ProfileAgent
	
	// GenerateCompanyProfile 生成企业画像（便捷方法）
	GenerateCompanyProfile(req *profileModels.ProfileRequest) (*profileModels.ProfileResponse, error)
	
	// RegisterAgent 注册Agent到系统
	RegisterAgent() error
	
	// UnregisterAgent 注销Agent
	UnregisterAgent() error
}

// ProfileAgentRegistry 企业画像Agent注册表接口
type ProfileAgentRegistry interface {
	// RegisterProfileAgent 注册企业画像Agent
	RegisterProfileAgent(agent ProfileAgent) error
	
	// GetProfileAgent 获取企业画像Agent
	GetProfileAgent(agentID string) (ProfileAgent, error)
	
	// ListProfileAgents 列出所有企业画像相关Agent
	ListProfileAgents() []biddingModels.RegisteredAgent
	
	// GetAgentStats 获取Agent统计信息
	GetAgentStats() map[string]interface{}
}
