package services

import (
	"context"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// JSONSchemaValidator JSON Schema验证器接口
type JSONSchemaValidator interface {
	// ValidateSchema 验证Schema格式
	ValidateSchema(schema models.JSONSchema) *models.ValidationResult

	// ValidateData 验证数据是否符合Schema
	ValidateData(data map[string]interface{}, schema models.JSONSchema) *models.ValidationResult

	// GetRequiredFields 获取必填字段列表
	GetRequiredFields(schema models.JSONSchema) []string
}

// PromptBuilder 提示词构建器接口
type PromptBuilder interface {
	// BuildPrompt 构建提示词
	BuildPrompt(ctx context.Context, req *models.ExtractionRequest) (*models.PromptRequest, error)

	// BuildSystemPrompt 构建系统提示词
	BuildSystemPrompt(schema models.JSONSchema, language string) string

	// BuildUserPrompt 构建用户提示词
	BuildUserPrompt(inputText string, schema models.JSONSchema, language string) string

	// InjectExamples 注入示例数据
	InjectExamples(prompt string, examples []interface{}) string
}

// ResponseParser 响应解析器接口
type ResponseParser interface {
	// ParseResponse 解析LLM响应
	ParseResponse(ctx context.Context, responseText string, schema models.JSONSchema) (*models.ParseResult, error)

	// ExtractJSON 从文本中提取JSON
	ExtractJSON(text string) string

	// CalculateConfidence 计算置信度
	CalculateConfidence(data map[string]interface{}, validation *models.ValidationResult) float64

	// ValidateStructure 验证数据结构
	ValidateStructure(data map[string]interface{}, schema models.JSONSchema) *models.ValidationResult
}

// InputValidator 输入验证器接口
type InputValidator interface {
	// ValidateInput 验证输入参数
	ValidateInput(req *models.ExtractionRequest) *models.ValidationResult

	// ValidateText 验证文本内容
	ValidateText(text string) error

	// ValidateLanguage 验证语言参数
	ValidateLanguage(language string) error

	// ValidateConfig 验证配置参数
	ValidateConfig(config *models.ExtractionConfig) error
}

// ErrorHandler 错误处理器接口
type ErrorHandler interface {
	// HandleError 处理错误
	HandleError(ctx context.Context, err error, req *models.ExtractionRequest) *models.ExtractionResult

	// HandleExtractionImpossible 处理无法提取情况
	HandleExtractionImpossible(ctx context.Context, inputText, modelName string) *models.ExtractionResult

	// HandleLowConfidence 处理置信度过低情况
	HandleLowConfidence(ctx context.Context, result *models.ExtractionResult, threshold float64) *models.ExtractionResult

	// ShouldRetry 判断是否应该重试
	ShouldRetry(err error, retryCount int) bool
}

// SchemaProcessor Schema处理服务接口
type SchemaProcessor interface {
	// ProcessSchema 处理和验证Schema
	ProcessSchema(schemaData map[string]interface{}) (*models.ProcessedSchema, error)

	// ValidateSchemaData 验证Schema数据
	ValidateSchemaData(data map[string]interface{}, schema models.JSONSchema) (*models.ValidationResult, error)

	// GetSchemaInfo 获取Schema信息摘要
	GetSchemaInfo(schema models.JSONSchema) *models.SchemaInfo

	// GenerateSchemaExample 生成Schema示例数据
	GenerateSchemaExample(schema models.JSONSchema) map[string]interface{}
}

// LLMProcessor LLM处理器接口
type LLMProcessor interface {
	// ProcessExtraction 处理实体提取请求
	ProcessExtraction(ctx context.Context, req *models.ExtractionRequest) (*models.ExtractionResult, error)

	// ProcessBatch 批量处理提取请求
	ProcessBatch(ctx context.Context, requests []*models.ExtractionRequest) ([]*models.ExtractionResult, error)

	// GetStats 获取处理统计信息
	GetStats() map[string]interface{}
}

// ConfigManager 配置管理器接口
type ConfigManager interface {
	// LoadConfig 加载配置
	LoadConfig() (*EntityExtractionAgentConfig, error)

	// ValidateConfig 验证配置
	ValidateConfig(config *EntityExtractionAgentConfig) error

	// GetDefaultConfig 获取默认配置
	GetDefaultConfig() *EntityExtractionAgentConfig

	// UpdateConfig 更新配置
	UpdateConfig(config *EntityExtractionAgentConfig) error

	// GetConfig 获取当前配置
	GetConfig() *EntityExtractionAgentConfig

	// IsLanguageSupported 检查语言是否支持
	IsLanguageSupported(language string) bool

	// ApplyExtractionConfig 应用提取配置到请求
	ApplyExtractionConfig(req *models.ExtractionRequest)

	// GetConfigSummary 获取配置摘要
	GetConfigSummary() map[string]interface{}
}
