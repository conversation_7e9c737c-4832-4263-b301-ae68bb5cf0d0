#!/usr/bin/env python3
"""
招投标预处理Agent测试套件
包含数据提取、分类、内容增强、多语言处理、结果聚合等预处理Agent
"""

import json
import requests
import os
import time
import pytest

# 预处理专用测试用户 - 方便Token消耗记录跟踪
PREPROCESSING_TEST_USER_ID = "test_user_preprocessing"
PREPROCESSING_TEST_COMPANY_ID = "test_company_preprocessing"

class TestPreprocessingAgents:
    """招投标预处理Agent测试套件"""
    
    def _handle_a2a_response(self, response, test_name, check_token=True):
        """处理A2A响应的通用方法"""
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json() if response.status_code == 200 else response.text, ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200:
            data = response.json()
            if "result" in data:
                result = data["result"]
                status = result.get("status", "unknown")
                
                if status == "completed":
                    print(f"{test_name}成功")
                    
                    # 检查token使用情况
                    if check_token:
                        metadata = result.get("metadata", {})
                        tokens_used = metadata.get("tokens_used", 0)
                        output = result.get("output", {})
                        output_metadata = output.get("metadata", {})
                        token_usage = output_metadata.get("token_usage", {})
                        
                        print(f"任务级别token使用: {tokens_used}")
                        if token_usage:
                            print(f"输出级别Token信息: input={token_usage.get('input_tokens', 0)}, "
                                  f"output={token_usage.get('output_tokens', 0)}, total={token_usage.get('total_tokens', 0)}")
                    
                    return True
                elif status == "failed":
                    error_msg = result.get("error", "未知错误")
                    print(f"{test_name}失败: {error_msg}")
                    return False
            elif "error" in data:
                print(f"A2A协议错误: {data['error']}")
                return False
        
        print(f"{test_name}请求失败: {response.status_code}")
        if response.status_code == 404:
            pytest.fail(f"Agent未注册或URL错误: {response.status_code}")
        return response.status_code == 200

    def test_data_extraction_agent(self):
        """测试招投标数据提取Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 招投标数据提取Agent测试 ===")
        
        # 使用文档中的示例数据
        payload = {
            "jsonrpc": "2.0",
            "id": "tender_data_extraction_test",
            "method": "execute_skill",
            "params": {
                "skill_id": "extract_structured_data",
                "input": {
                    "raw_text": "【政府采购项目】某市教育局2024年度计算机设备采购项目。项目编号：EDU-2024-001。预算金额：500万元。投标截止时间：2024年2月15日下午3点。采购人：某市教育局，地址：某市中心路123号。联系人：张老师，电话：010-12345678。本项目采购台式计算机200台，笔记本电脑100台，投影仪50台。投标人需具备计算机信息系统集成资质，注册资金不少于1000万元，近三年有类似项目经验。",
                    "language": "chinese",
                    "extraction_mode": "standard",
                    "fields": ["basic_info", "organization", "tender_requirements", "supplier_requirements"]
                },
                "context": {
                    "user_id": PREPROCESSING_TEST_USER_ID,
                    "company_id": PREPROCESSING_TEST_COMPANY_ID,
                    "trace_id": "trace_001",
                    "timestamp": "2024-01-15T10:30:00Z"
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': PREPROCESSING_TEST_USER_ID,
            'X-Company-ID': PREPROCESSING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/tender-data-extraction"
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        success = self._handle_a2a_response(response, "数据提取")
        if not success:
            pytest.fail("数据提取Agent测试失败")

    def test_classification_agent(self):
        """测试招投标分类Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 招投标分类Agent测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": "tender_classification_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "classify_tender",
                "input": {
                    "extracted_data": {
                        "project_name": "2024年政府信息化设备采购项目",
                        "budget": "500万元",
                        "category_hints": ["IT设备", "服务器", "网络设备", "安全设备"],
                        "procurement_unit": "某市信息化管理中心"
                    },
                    "classification_depth": "detailed",
                    "language": "chinese"
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': PREPROCESSING_TEST_USER_ID,
            'X-Company-ID': PREPROCESSING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/tender-classification"
        response = requests.post(url, json=payload, headers=headers, timeout=45)
        
        success = self._handle_a2a_response(response, "招投标分类")
        if not success:
            pytest.fail("招投标分类Agent测试失败")

    def test_content_enhancement_agent(self):
        """测试招投标内容增强Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 招投标内容增强Agent测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": "tender_content_enhancement_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "enhance_content",
                "input": {
                    "original_data": {
                        "project_name": "IT设备采购",
                        "brief_description": "采购服务器等设备",
                        "basic_requirements": ["高性能", "质保3年"]
                    },
                    "enhancement_type": "comprehensive",
                    "language": "chinese",
                    "focus_areas": ["technical_specs", "market_context", "risk_analysis"]
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': PREPROCESSING_TEST_USER_ID,
            'X-Company-ID': PREPROCESSING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/tender-content-enhancement"
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        success = self._handle_a2a_response(response, "内容增强")
        if not success:
            pytest.fail("内容增强Agent测试失败")

    def test_multilingual_agent(self):
        """测试多语言处理Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 多语言处理Agent测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": "tender_multilingual_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "process_multilingual",
                "input": {
                    "content": {
                        "chinese": {
                            "project_name": "政府信息化设备采购项目",
                            "description": "采购高性能服务器、网络交换机和安全防护设备"
                        }
                    },
                    "target_languages": ["english"],
                    "processing_mode": "translation_and_localization",
                    "preserve_technical_terms": True
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': PREPROCESSING_TEST_USER_ID,
            'X-Company-ID': PREPROCESSING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/tender-multilingual"
        response = requests.post(url, json=payload, headers=headers, timeout=45)
        
        success = self._handle_a2a_response(response, "多语言处理")
        if not success:
            pytest.fail("多语言处理Agent测试失败")

    def test_result_aggregation_agent(self):
        """测试结果聚合Agent"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 结果聚合Agent测试 ===")
        
        payload = {
            "jsonrpc": "2.0",
            "id": "tender_result_aggregation_test",
            "method": "agent/execute",
            "params": {
                "skill_id": "aggregate_results",
                "input": {
                    "processing_results": [
                        {
                            "agent": "data_extraction",
                            "status": "completed",
                            "data": {
                                "project_name": "政府信息化设备采购项目",
                                "budget": "500万元"
                            }
                        },
                        {
                            "agent": "classification",
                            "status": "completed",
                            "data": {
                                "primary_category": "IT设备采购",
                                "sub_categories": ["服务器", "网络设备", "安全设备"]
                            }
                        },
                        {
                            "agent": "enhancement",
                            "status": "completed",
                            "data": {
                                "risk_level": "中等",
                                "market_competition": "激烈"
                            }
                        }
                    ],
                    "aggregation_strategy": "comprehensive",
                    "output_format": "structured"
                }
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-User-ID': PREPROCESSING_TEST_USER_ID,
            'X-Company-ID': PREPROCESSING_TEST_COMPANY_ID
        }
        
        url = f"{base_url}/agents/tender-result-aggregation"
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        success = self._handle_a2a_response(response, "结果聚合")
        if not success:
            pytest.fail("结果聚合Agent测试失败")

    def test_token_consumption_verification(self):
        """测试预处理Agent的token消耗记录"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        
        print("=== 预处理Agent Token消耗验证测试 ===")
        
        # 等待前面的token记录完成
        time.sleep(3)
        
        # 检查Token消耗统计
        token_url = f"{base_url}/v1/tokens/stats/user/{PREPROCESSING_TEST_USER_ID}"
        token_response = requests.get(token_url, timeout=30)
        
        print(f"Token统计API状态码: {token_response.status_code}")
        
        if token_response.status_code == 200:
            token_data = token_response.json()
            print(f"Token统计: {json.dumps(token_data, ensure_ascii=False, indent=2)}")
            
            # 验证基本字段
            assert token_data["user_id"] == PREPROCESSING_TEST_USER_ID
            assert token_data["company_id"] == PREPROCESSING_TEST_COMPANY_ID
            
            # 检查是否有token消耗记录
            last_consumed = token_data.get("last_consumed_at")
            monthly_tokens = token_data.get("monthly_usage", {}).get("total_tokens", 0)
            daily_tokens = token_data.get("daily_usage", {}).get("total_tokens", 0)
            
            if last_consumed is not None:
                print("预处理Agent token消耗记录正常")
                if monthly_tokens > 0 or daily_tokens > 0:
                    print(f"统计数据: 月度={monthly_tokens}, 日度={daily_tokens}")
                else:
                    print("注意: 统计数据可能需要更多时间更新")
            else:
                print("未检测到token消耗记录")
                
            print("预处理Agent token消耗测试完成!")
        else:
            print(f"Token统计API失败: {token_response.text}")

def test_preprocessing_agents_integration():
    """预处理Agent集成测试"""
    print("=== 预处理Agent集成测试 ===")
    
    test_suite = TestPreprocessingAgents()
    test_results = {}
    
    # 定义测试列表
    tests = [
        ("数据提取", test_suite.test_data_extraction_agent),
        ("招投标分类", test_suite.test_classification_agent),
        ("内容增强", test_suite.test_content_enhancement_agent),
        ("多语言处理", test_suite.test_multilingual_agent),
        ("结果聚合", test_suite.test_result_aggregation_agent),
        ("Token消耗验证", test_suite.test_token_consumption_verification)
    ]
    
    # 执行每个测试
    for test_name, test_func in tests:
        try:
            test_func()
            test_results[test_name] = "PASSED"
            print(f"{test_name}测试通过\n")
        except Exception as e:
            test_results[test_name] = f"FAILED: {str(e)}"
            print(f"{test_name}测试失败: {str(e)}\n")
        
        time.sleep(2)  # 避免请求过于频繁
    
    # 打印测试总结
    print("=== 预处理Agent测试总结 ===")
    passed_count = sum(1 for result in test_results.values() if result == "PASSED")
    total_count = len(test_results)
    
    for test_name, result in test_results.items():
        status = "PASS" if result == "PASSED" else "FAIL"
        print(f"[{status}] {test_name}: {result}")
    
    print(f"\n测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == 0:
        pytest.fail("所有预处理Agent测试都失败了")
    
    print("预处理Agent集成测试完成!")

if __name__ == "__main__":
    test_preprocessing_agents_integration()