package chat

import (
	"context"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ChatCleanupService 聊天清理服务，负责会话超时管理和数据清理
type ChatCleanupService struct {
	sessionService   *ChatSessionService
	connectionService *SocketConnectionService
	messageService   *ChatMessageService
	config           *models.ChatConfig
	
	// 清理任务控制
	stopChan chan struct{}
	wg       sync.WaitGroup
	running  bool
	mutex    sync.RWMutex
}

// NewChatCleanupService 创建聊天清理服务
func NewChatCleanupService(
	sessionService *ChatSessionService,
	connectionService *SocketConnectionService,
	messageService *ChatMessageService,
	config *models.ChatConfig,
) *ChatCleanupService {
	return &ChatCleanupService{
		sessionService:    sessionService,
		connectionService: connectionService,
		messageService:    messageService,
		config:           config,
		stopChan:         make(chan struct{}),
	}
}

// Start 启动清理服务
func (s *ChatCleanupService) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return nil // 已经在运行
	}

	s.running = true
	utils.Log.Info("Starting chat cleanup service")

	// 启动定期清理任务
	s.wg.Add(1)
	go s.runCleanupTasks(ctx)

	return nil
}

// Stop 停止清理服务
func (s *ChatCleanupService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return nil
	}

	utils.Log.Info("Stopping chat cleanup service")
	close(s.stopChan)
	s.wg.Wait()
	s.running = false

	return nil
}

// IsRunning 检查服务是否在运行
func (s *ChatCleanupService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.running
}

// runCleanupTasks 运行清理任务
func (s *ChatCleanupService) runCleanupTasks(ctx context.Context) {
	defer s.wg.Done()

	// 计算清理间隔
	cleanupInterval := time.Duration(s.config.CleanupIntervalHours) * time.Hour
	if cleanupInterval <= 0 {
		cleanupInterval = 1 * time.Hour // 默认1小时
	}

	// 计算连接超时时间
	connectionTimeout := time.Duration(s.config.PingIntervalSeconds*3) * time.Second
	if connectionTimeout <= 0 {
		connectionTimeout = 5 * time.Minute // 默认5分钟
	}

	utils.Log.Infof("Cleanup service running with interval: %v, connection timeout: %v", 
		cleanupInterval, connectionTimeout)

	ticker := time.NewTicker(cleanupInterval)
	defer ticker.Stop()

	// 立即执行一次清理
	s.performCleanup(ctx, connectionTimeout)

	for {
		select {
		case <-ticker.C:
			s.performCleanup(ctx, connectionTimeout)
		case <-s.stopChan:
			utils.Log.Info("Cleanup service stopped")
			return
		case <-ctx.Done():
			utils.Log.Info("Cleanup service stopped due to context cancellation")
			return
		}
	}
}

// performCleanup 执行清理操作
func (s *ChatCleanupService) performCleanup(ctx context.Context, connectionTimeout time.Duration) {
	utils.Log.Debug("Starting cleanup task")
	
	// 清理过期会话
	s.cleanupExpiredSessions(ctx)
	
	// 清理过期连接
	s.cleanupStaleConnections(ctx, connectionTimeout)
	
	// 清理旧数据
	s.cleanupOldData(ctx)
	
	utils.Log.Debug("Cleanup task completed")
}

// cleanupExpiredSessions 清理过期会话
func (s *ChatCleanupService) cleanupExpiredSessions(ctx context.Context) {
	expiredCount, err := s.sessionService.CleanupExpiredSessions(ctx)
	if err != nil {
		utils.Log.Errorf("Failed to cleanup expired sessions: %v", err)
		return
	}

	if expiredCount > 0 {
		utils.Log.Infof("Cleaned up %d expired chat sessions", expiredCount)
	}
}

// cleanupStaleConnections 清理过期连接
func (s *ChatCleanupService) cleanupStaleConnections(ctx context.Context, timeout time.Duration) {
	timeoutMinutes := int(timeout.Minutes())
	if timeoutMinutes < 1 {
		timeoutMinutes = 1
	}

	staleCount, err := s.connectionService.CleanupStaleConnections(ctx, timeoutMinutes)
	if err != nil {
		utils.Log.Errorf("Failed to cleanup stale connections: %v", err)
		return
	}

	if staleCount > 0 {
		utils.Log.Infof("Cleaned up %d stale socket connections", staleCount)
	}
}

// cleanupOldData 清理旧数据
func (s *ChatCleanupService) cleanupOldData(ctx context.Context) {
	// 这里可以添加清理超过一定时间的历史数据
	// 例如：删除30天前的已关闭会话和消息
	
	// 目前数据库trigger已经处理了24小时的清理
	// 这里可以做更长期的清理策略
	utils.Log.Debug("Old data cleanup completed")
}

// ForceCleanup 强制执行一次清理
func (s *ChatCleanupService) ForceCleanup(ctx context.Context) error {
	utils.Log.Info("Force cleanup requested")
	
	connectionTimeout := time.Duration(s.config.PingIntervalSeconds*3) * time.Second
	if connectionTimeout <= 0 {
		connectionTimeout = 5 * time.Minute
	}
	
	s.performCleanup(ctx, connectionTimeout)
	return nil
}

// GetCleanupStats 获取清理统计信息
func (s *ChatCleanupService) GetCleanupStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取会话统计
	sessionStats, err := s.sessionService.GetSessionStats(ctx)
	if err != nil {
		utils.Log.Errorf("Failed to get session stats: %v", err)
	} else {
		stats["sessions"] = sessionStats
	}

	// 获取连接统计
	activeConnections, err := s.connectionService.GetActiveConnectionCount(ctx)
	if err != nil {
		utils.Log.Errorf("Failed to get active connection count: %v", err)
	} else {
		stats["active_connections"] = activeConnections
	}

	// 添加服务状态
	stats["cleanup_service_running"] = s.IsRunning()
	stats["cleanup_interval_hours"] = s.config.CleanupIntervalHours
	stats["max_socket_connections"] = s.config.MaxSocketConnections

	return stats, nil
}

// ScheduleSessionCleanup 计划会话清理（在会话过期时调用）
func (s *ChatCleanupService) ScheduleSessionCleanup(ctx context.Context, sessionID string, delay time.Duration) {
	go func() {
		timer := time.NewTimer(delay)
		defer timer.Stop()

		select {
		case <-timer.C:
			// 检查会话是否真的过期了
			session, err := s.sessionService.GetSession(ctx, sessionID)
			if err != nil {
				utils.Log.Errorf("Failed to get session for cleanup: %v", err)
				return
			}

			if time.Now().After(session.ExpiresAt) && session.Status == models.ChatSessionStatusActive {
				err = s.sessionService.CloseSession(ctx, sessionID)
				if err != nil {
					utils.Log.Errorf("Failed to close expired session %s: %v", sessionID, err)
				} else {
					utils.Log.Infof("Automatically closed expired session: %s", sessionID)
				}
			}
		case <-s.stopChan:
			return
		case <-ctx.Done():
			return
		}
	}()
}