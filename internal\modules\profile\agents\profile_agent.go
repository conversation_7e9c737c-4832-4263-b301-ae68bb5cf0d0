package agents

import (
	"encoding/json"
	"fmt"

	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	profileModels "gitlab.com/specific-ai/taskd/internal/modules/profile/models"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

const (
	// Agent信息
	AgentID          = "company-profile"
	AgentName        = "Company Profile Generation Agent"
	AgentDescription = "基于企业基础信息生成五维度企业画像分析报告"
	AgentVersion     = "1.0.0"

	// 技能定义
	SkillGenerateCompanyProfile = "generate_company_profile"
)

// ProfileAgent 企业画像生成Agent
type ProfileAgent struct {
	*agents.BaseAgent
	profileService services.ProfileServiceInterface
}

// NewProfileAgent 创建企业画像Agent
func NewProfileAgent(profileService services.ProfileServiceInterface) *ProfileAgent {
	agentCard := biddingModels.AgentCard{
		Name:        AgentName,
		Description: AgentDescription,
		URL:         "http://taskd-service:8601/agents/company-profile",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: AgentVersion,
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          SkillGenerateCompanyProfile,
				Name:        "生成企业画像",
				Description: "分析企业信息并生成业务能力、招投标匹配、竞争力、国际化能力、风险承受能力五个维度的画像",
				Tags:        []string{"profile", "ai", "analysis", "business"},
				Examples: []string{
					"为制造业企业生成全面的业务画像分析",
					"分析科技公司的招投标适配性和竞争优势",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
				InputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"company_name": map[string]interface{}{
							"type":        "string",
							"description": "企业名称",
						},
						"industry": map[string]interface{}{
							"type":        "string",
							"description": "所属行业",
						},
						"company_scale": map[string]interface{}{
							"type":        "string",
							"description": "企业规模",
						},
						"business_scope": map[string]interface{}{
							"type":        "string",
							"description": "业务范围描述",
						},
						"location": map[string]interface{}{
							"type":        "string",
							"description": "企业所在地",
						},
						"established_year": map[string]interface{}{
							"type":        "integer",
							"description": "成立年份",
						},
						"description": map[string]interface{}{
							"type":        "string",
							"description": "企业描述",
						},
						"website": map[string]interface{}{
							"type":        "string",
							"description": "企业官网",
						},
						"language": map[string]interface{}{
							"type":        "string",
							"description": "输出语言，默认为english",
							"default":     "english",
						},
						"user_context": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"user_id": map[string]interface{}{
									"type":        "string",
									"description": "用户ID",
								},
								"company_id": map[string]interface{}{
									"type":        "string",
									"description": "公司ID",
								},
							},
							"required": []string{"user_id"},
						},
					},
					"required": []string{"company_name", "industry", "user_context"},
				},
				OutputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"user_id": map[string]interface{}{
							"type":        "string",
							"description": "用户ID",
						},
						"company_name": map[string]interface{}{
							"type":        "string",
							"description": "企业名称",
						},
						"business_capabilities": map[string]interface{}{
							"type":        "object",
							"description": "业务能力画像",
						},
						"tender_matching": map[string]interface{}{
							"type":        "object",
							"description": "招投标匹配分析",
						},
						"competitive_profile": map[string]interface{}{
							"type":        "object",
							"description": "竞争力分析",
						},
						"international_capabilities": map[string]interface{}{
							"type":        "object",
							"description": "国际化能力分析",
						},
						"risk_tolerance": map[string]interface{}{
							"type":        "object",
							"description": "风险承受能力分析",
						},
						"confidence_score": map[string]interface{}{
							"type":        "integer",
							"description": "置信度分数 (0-100)",
						},
						"generated_at": map[string]interface{}{
							"type":        "string",
							"format":      "date-time",
							"description": "生成时间",
						},
						"metadata": map[string]interface{}{
							"type":        "object",
							"description": "元数据信息",
						},
					},
				},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(AgentID, agentCard)

	return &ProfileAgent{
		BaseAgent:      baseAgent,
		profileService: profileService,
	}
}

// ExecuteSkill 执行技能
func (a *ProfileAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case SkillGenerateCompanyProfile:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.generateCompanyProfileHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// generateCompanyProfileHandler 处理企业画像生成请求
func (a *ProfileAgent) generateCompanyProfileHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	utils.Log.Infof("开始处理企业画像生成请求，RequestID: %s", context.RequestID)

	// 将输入转换为ProfileRequest
	profileReq, err := a.convertInputToProfileRequest(input)
	if err != nil {
		return nil, fmt.Errorf("输入参数转换失败: %w", err)
	}

	// 调用ProfileService生成画像
	profileResp, err := a.profileService.GenerateCompanyProfile(profileReq)
	if err != nil {
		return nil, fmt.Errorf("企业画像生成失败: %w", err)
	}

	// 将响应转换为输出格式
	output, err := a.convertProfileResponseToOutput(profileResp)
	if err != nil {
		return nil, fmt.Errorf("响应转换失败: %w", err)
	}

	utils.Log.Infof("企业画像生成成功，企业: %s, 置信度: %d", profileResp.CompanyName, profileResp.ConfidenceScore)

	return output, nil
}

// convertInputToProfileRequest 将A2A输入转换为ProfileRequest
func (a *ProfileAgent) convertInputToProfileRequest(input map[string]interface{}) (*profileModels.ProfileRequest, error) {
	// 序列化为JSON再反序列化，确保类型转换正确
	inputJSON, err := json.Marshal(input)
	if err != nil {
		return nil, fmt.Errorf("输入序列化失败: %w", err)
	}

	var profileReq profileModels.ProfileRequest
	if err := json.Unmarshal(inputJSON, &profileReq); err != nil {
		return nil, fmt.Errorf("输入反序列化失败: %w", err)
	}

	// 设置默认语言
	if profileReq.Language == "" {
		profileReq.Language = "english"
	}

	return &profileReq, nil
}

// convertProfileResponseToOutput 将ProfileResponse转换为A2A输出格式
func (a *ProfileAgent) convertProfileResponseToOutput(resp *profileModels.ProfileResponse) (map[string]interface{}, error) {
	// 序列化为JSON再反序列化为map，确保格式正确
	respJSON, err := json.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("响应序列化失败: %w", err)
	}

	var output map[string]interface{}
	if err := json.Unmarshal(respJSON, &output); err != nil {
		return nil, fmt.Errorf("响应反序列化失败: %w", err)
	}

	return output, nil
}
