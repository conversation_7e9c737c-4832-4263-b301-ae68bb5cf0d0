package interfaces

import (
	"context"

	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
)

// TokenServiceInterface 定义Token服务的接口，避免循环导入
type TokenServiceInterface interface {
	// CheckTokenLimits 检查Token限制
	CheckTokenLimits(userID string, requestedTokens int) (*models.TokenLimitCheck, error)

	// LogTokenConsumption 记录Token消费
	LogTokenConsumption(req *models.TokenConsumptionRequest) (*models.TokenConsumption, error)
}

// LLMServiceInterface 定义LLM服务的接口，避免循环导入
type LLMServiceInterface interface {
	// ProcessRequest 处理LLM请求
	ProcessRequest(ctx context.Context, req *common.LLMRequest) (*common.LLMResponse, error)
}
