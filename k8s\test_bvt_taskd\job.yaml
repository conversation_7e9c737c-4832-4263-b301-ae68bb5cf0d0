apiVersion: batch/v1
kind: Job
metadata:
  name: taskd-bvt-runner-job
  namespace: ovs
spec:
  template:
    metadata:
      annotations:
        # Disable Istio sidecar injection for this testing pod
        "sidecar.istio.io/inject": "false"
    spec:
      containers:
      - name: taskd-bvt-runner
        image: 192.168.50.112/specific-ai/taskd-bvt-runner:latest # Replace with your private registry
        imagePullPolicy: Always
        envFrom:
        - configMapRef:
            name: taskd-bvt-runner-config
      restartPolicy: Never
  backoffLimit: 1 