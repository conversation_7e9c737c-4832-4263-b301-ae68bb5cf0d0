package models

// ReportSummaryRequest 报告总结 API 请求结构
type ReportSummaryRequest struct {
	Text             string            `json:"text" binding:"required"`
	TargetCompany    string            `json:"target_company" binding:"required"`
	Language         string            `json:"language" binding:"required"`
	Temperature      *float64          `json:"temperature,omitempty"`
	MaxTokens        *int              `json:"max_tokens,omitempty"`
	PromptArgs       map[string]string `json:"prompt_args,omitempty"`       // 用于 sys_prompt 中的动态参数
	ModelAlias       string            `json:"model_alias,omitempty"`       // 可选，指定使用的模型别名
	FrequencyPenalty *float64          `json:"frequency_penalty,omitempty"` // OpenAI 参数
	PresencePenalty  *float64          `json:"presence_penalty,omitempty"`  // OpenAI 参数
}

// ReportSummaryResponse 报告总结 API 响应结构
type ReportSummaryResponse struct {
	Result string `json:"result"`
}

// ReportSummaryPulsarMessage Pulsar 消息队列中消费的报告总结请求结构
type ReportSummaryPulsarMessage struct {
	ReportSummaryRequest
	MessageID string `json:"message_id"` // 示例：Pulsar 消息的元数据
}
