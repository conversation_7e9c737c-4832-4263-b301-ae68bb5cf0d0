#!/usr/bin/env python3
"""
验证Token消耗记录是否正确保存到PostgreSQL数据库
"""

import psycopg2
import os
import json
from datetime import datetime, timedelta

def connect_to_db():
    """连接到PostgreSQL数据库"""
    try:
        # 从环境变量获取数据库连接信息
        db_host = os.environ.get("DB_HOST", "localhost")
        db_port = os.environ.get("DB_PORT", "5432")
        db_name = os.environ.get("DB_NAME", "overseas")
        db_user = os.environ.get("DB_USER", "taskd_user")
        db_password = os.environ.get("DB_PASSWORD", "taskd_password")
        
        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            database=db_name,
            user=db_user,
            password=db_password
        )
        
        print(f"[DB_CONNECTION_SUCCESS] connected to {db_host}:{db_port}/{db_name}")
        return conn
        
    except Exception as e:
        print(f"[DB_CONNECTION_FAILED] error={e}")
        return None

def verify_token_consumption_table(conn):
    """验证token_consumption表的记录"""
    try:
        cursor = conn.cursor()
        
        # 查询最近的token消耗记录
        query = """
        SELECT id, user_id, company_id, model_provider, model_name, 
               input_tokens, output_tokens, total_tokens, cost_cents,
               request_id, api_endpoint, consumed_at, created_at
        FROM token_consumption 
        ORDER BY created_at DESC 
        LIMIT 10
        """
        
        cursor.execute(query)
        records = cursor.fetchall()
        
        print(f"[TOKEN_CONSUMPTION_RECORDS] found={len(records)} records")
        
        if records:
            print("\n=== Recent Token Consumption Records ===")
            for i, record in enumerate(records, 1):
                print(f"Record {i}:")
                print(f"  ID: {record[0]}")
                print(f"  User: {record[1]}")
                print(f"  Company: {record[2]}")
                print(f"  Provider: {record[3]}")
                print(f"  Model: {record[4]}")
                print(f"  Input Tokens: {record[5]}")
                print(f"  Output Tokens: {record[6]}")
                print(f"  Total Tokens: {record[7]}")
                print(f"  Cost (cents): {record[8]}")
                print(f"  Request ID: {record[9]}")
                print(f"  API Endpoint: {record[10]}")
                print(f"  Consumed At: {record[11]}")
                print(f"  Created At: {record[12]}")
                print()
        else:
            print("[TOKEN_CONSUMPTION_WARNING] no records found in table")
            
        # 检查最近1小时内的记录
        cursor.execute("""
        SELECT COUNT(*) FROM token_consumption 
        WHERE created_at > NOW() - INTERVAL '1 hour'
        """)
        recent_count = cursor.fetchone()[0]
        print(f"[RECENT_ACTIVITY] records_in_last_hour={recent_count}")
        
        cursor.close()
        return len(records) > 0
        
    except Exception as e:
        print(f"[TOKEN_VERIFICATION_FAILED] error={e}")
        return False

def verify_users_and_companies(conn):
    """验证users和companies表"""
    try:
        cursor = conn.cursor()
        
        # 检查users表
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"[USERS_TABLE] count={user_count}")
        
        if user_count > 0:
            cursor.execute("SELECT user_id, company_id, subscription_type FROM users LIMIT 5")
            users = cursor.fetchall()
            print("[USERS_SAMPLE]")
            for user in users:
                print(f"  user_id={user[0]}, company_id={user[1]}, subscription={user[2]}")
        
        # 检查companies表
        cursor.execute("SELECT COUNT(*) FROM companies")
        company_count = cursor.fetchone()[0]
        print(f"[COMPANIES_TABLE] count={company_count}")
        
        if company_count > 0:
            cursor.execute("SELECT company_id, company_name, subscription_type FROM companies LIMIT 5")
            companies = cursor.fetchall()
            print("[COMPANIES_SAMPLE]")
            for company in companies:
                print(f"  company_id={company[0]}, name={company[1]}, subscription={company[2]}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"[USER_COMPANY_VERIFICATION_FAILED] error={e}")
        return False

def main():
    """主函数"""
    print("=== Token Database Verification ===")
    print(f"[START_TIME] {datetime.now()}")
    
    # 连接数据库
    conn = connect_to_db()
    if not conn:
        print("[VERIFICATION_FAILED] cannot connect to database")
        return False
    
    try:
        # 验证各个表
        token_records_exist = verify_token_consumption_table(conn)
        users_companies_exist = verify_users_and_companies(conn)
        
        print("\n=== Verification Summary ===")
        print(f"[TOKEN_RECORDS_EXIST] {token_records_exist}")
        print(f"[USERS_COMPANIES_EXIST] {users_companies_exist}")
        
        if token_records_exist:
            print("[VERIFICATION_SUCCESS] Token consumption is being recorded to database")
        else:
            print("[VERIFICATION_WARNING] No token consumption records found")
            print("This could mean:")
            print("1. No API calls have been made yet")
            print("2. Token tracking is not working")
            print("3. Database connection issues")
        
        return token_records_exist
        
    finally:
        conn.close()
        print(f"[END_TIME] {datetime.now()}")

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)