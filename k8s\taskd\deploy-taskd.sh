#!/bin/bash

# taskd部署脚本
# 用途：在k3s集群中部署taskd服务（依赖etcd配置中心）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查kubectl是否可用
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未找到，请先安装kubectl"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到k8s集群，请检查kubeconfig配置"
        exit 1
    fi
    
    log_success "kubectl 检查通过"
}

# 检查etcd依赖
check_etcd_dependency() {
    log_info "检查etcd配置中心依赖..."
    
    # 检查etcd namespace是否存在
    if ! kubectl get namespace etcd &> /dev/null; then
        log_error "未找到etcd namespace，请先部署etcd配置中心"
        log_info "运行命令: cd ../etcd && ./deploy-etcd.sh"
        exit 1
    fi
    
    # 检查etcd pod是否运行
    if ! kubectl get pods -n etcd -l app=etcd | grep -q Running; then
        log_error "etcd Pod未运行，请检查etcd部署状态"
        log_info "检查命令: kubectl get pods -n etcd"
        exit 1
    fi
    
    # 检查etcd service是否可达
    if ! kubectl get svc -n etcd etcd-service &> /dev/null; then
        log_error "etcd服务不可达"
        exit 1
    fi
    
    log_success "etcd配置中心依赖检查通过"
}

# 部署taskd
deploy_taskd() {
    log_info "开始部署taskd服务..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 部署资源（按顺序）
    log_info "1. 创建namespace..."
    kubectl apply -f "${SCRIPT_DIR}/namespace.yaml"
    
    log_info "2. 创建ConfigMap..."
    kubectl apply -f "${SCRIPT_DIR}/configmap.yaml"
    
    log_info "3. 创建Secret..."
    kubectl apply -f "${SCRIPT_DIR}/secret.yaml"
    
    log_info "4. 创建Service..."
    kubectl apply -f "${SCRIPT_DIR}/service.yaml"
    
    log_info "5. 创建Deployment..."
    kubectl apply -f "${SCRIPT_DIR}/deployment.yaml"
    
    log_success "taskd部署配置已提交"
}

# 等待taskd启动
wait_for_taskd() {
    log_info "等待taskd启动..."
    
    # 等待Deployment就绪
    kubectl wait --for=condition=available deployment/taskd -n ovs --timeout=60s
    
    if [ $? -eq 0 ]; then
        log_success "taskd启动成功"
    else
        log_error "taskd启动超时，请检查日志"
        log_info "查看Pod状态: kubectl get pods -n ovs"
        log_info "查看Pod日志: kubectl logs -n ovs -l app=taskd"
        exit 1
    fi
}

# 验证taskd功能
verify_taskd() {
    log_info "验证taskd功能..."
    
    # 获取taskd service信息
    TASKD_SERVICE=$(kubectl get svc -n ovs taskd-service -o jsonpath='{.spec.clusterIP}:{.spec.ports[0].port}')
    
    if [ -z "$TASKD_SERVICE" ]; then
        log_error "未找到taskd服务"
        exit 1
    fi
    
    log_info "taskd服务地址: $TASKD_SERVICE"
    
    # 检查Pod状态
    POD_STATUS=$(kubectl get pods -n ovs -l app=taskd -o jsonpath='{.items[0].status.phase}')
    if [ "$POD_STATUS" = "Running" ]; then
        log_success "taskd Pod运行正常"
    else
        log_warn "taskd Pod状态: $POD_STATUS"
    fi
    
    # 检查etcd配置加载日志
    log_info "检查etcd配置加载..."
    kubectl logs -n ovs -l app=taskd --tail=50 | grep -E "(etcd|config)" || log_warn "未找到etcd配置日志"
}

# 显示部署信息
show_deployment_info() {
    log_info "taskd部署信息："
    echo "  Namespace: ovs"
    echo "  Service: taskd-service"
    echo "  Port: 8601"
    echo ""
    log_info "常用操作命令："
    echo "  查看taskd状态: kubectl get pods -n ovs"
    echo "  查看taskd日志: kubectl logs -n ovs -l app=taskd -f"
    echo "  重启taskd: kubectl delete pod -n ovs -l app=taskd"
    echo "  进入taskd容器: kubectl exec -it -n ovs <pod-name> -- sh"
    echo ""
    log_info "配置管理："
    echo "  查看etcd配置: kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd --prefix"
    echo "  修改配置后重启: kubectl delete pod -n ovs -l app=taskd"
}

# 主函数
main() {
    log_info "开始部署taskd服务..."
    echo "========================================"
    
    check_kubectl
    check_etcd_dependency
    deploy_taskd
    wait_for_taskd
    verify_taskd
    
    echo "========================================"
    log_success "taskd部署完成！"
    show_deployment_info
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi