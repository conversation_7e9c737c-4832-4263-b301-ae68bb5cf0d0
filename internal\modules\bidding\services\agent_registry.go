package services

import (
	"fmt"
	"sync"
	"time"

	biddingInterfaces "gitlab.com/specific-ai/taskd/internal/modules/bidding/interfaces"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// AgentRegistryService implements agent registration and discovery
type AgentRegistryService struct {
	agents map[string]*RegisteredAgentEntry
	mutex  sync.RWMutex
}

// RegisteredAgentEntry represents an agent entry with additional runtime info
type RegisteredAgentEntry struct {
	biddingModels.RegisteredAgent
	Agent    biddingInterfaces.Agent
	LastPing time.Time
}

// NewAgentRegistryService creates a new agent registry service
func NewAgentRegistryService() biddingInterfaces.AgentRegistry {
	service := &AgentRegistryService{
		agents: make(map[string]*RegisteredAgentEntry),
	}

	// Start health check routine
	go service.healthCheckRoutine()

	return service
}

// RegisterAgent registers a new agent in the system
func (s *AgentRegistryService) RegisterAgent(agent biddingInterfaces.Agent) error {
	return s.RegisterAgentWithInstance(agent)
}

// RegisterAgentWithInstance registers an agent with its implementation instance
func (s *AgentRegistryService) RegisterAgentWithInstance(agent biddingInterfaces.Agent) error {
	agentCard := agent.GetAgentCard()

	registeredAgent := biddingModels.RegisteredAgent{
		ID:        agent.GetID(),
		AgentCard: agentCard,
		Status: biddingModels.AgentStatus{
			Status:      agent.HealthCheck().Status,
			LastUpdated: time.Now(),
		},
		RegisterTime: time.Now(),
		LastSeen:     time.Now(),
		Endpoints:    []string{agentCard.URL},
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	entry := &RegisteredAgentEntry{
		RegisteredAgent: registeredAgent,
		Agent:           agent,
		LastPing:        time.Now(),
	}

	s.agents[agent.GetID()] = entry

	utils.Log.Infof("Agent registered with instance: %s (%s)", agent.GetID(), agentCard.Name)
	return nil
}

// UnregisterAgent removes an agent from the system
func (s *AgentRegistryService) UnregisterAgent(agentID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if _, exists := s.agents[agentID]; !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	delete(s.agents, agentID)
	utils.Log.Infof("Agent unregistered: %s", agentID)
	return nil
}

// GetAgent retrieves an agent by ID
func (s *AgentRegistryService) GetAgent(agentID string) (biddingInterfaces.Agent, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	entry, exists := s.agents[agentID]
	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}

	if entry.Agent == nil {
		return nil, fmt.Errorf("agent instance not available: %s", agentID)
	}

	return entry.Agent, nil
}

// ListAgents returns all registered agents
func (s *AgentRegistryService) ListAgents() []biddingModels.RegisteredAgent {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	agents := make([]biddingModels.RegisteredAgent, 0, len(s.agents))
	for _, entry := range s.agents {
		// 只更新last seen时间，不执行健康检查
		// 健康检查由定期的healthCheckRoutine负责
		if entry.Agent != nil {
			entry.LastSeen = time.Now()
		}
		agents = append(agents, entry.RegisteredAgent)
	}

	return agents
}

// UpdateAgentStatus updates the status of an agent
func (s *AgentRegistryService) UpdateAgentStatus(agentID string, status biddingModels.AgentStatus) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	entry, exists := s.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	entry.Status = status
	entry.Status.LastUpdated = time.Now()
	entry.LastPing = time.Now()

	return nil
}

// FindAgentsBySkill finds agents that support a specific skill
func (s *AgentRegistryService) FindAgentsBySkill(skillID string) []biddingModels.RegisteredAgent {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var matchingAgents []biddingModels.RegisteredAgent

	for _, entry := range s.agents {
		for _, skill := range entry.AgentCard.Skills {
			if skill.ID == skillID {
				matchingAgents = append(matchingAgents, entry.RegisteredAgent)
				break
			}
		}
	}

	return matchingAgents
}

// FindAgentsByTag finds agents that have a specific tag
func (s *AgentRegistryService) FindAgentsByTag(tag string) []biddingModels.RegisteredAgent {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var matchingAgents []biddingModels.RegisteredAgent

	for _, entry := range s.agents {
		for _, skill := range entry.AgentCard.Skills {
			for _, skillTag := range skill.Tags {
				if skillTag == tag {
					matchingAgents = append(matchingAgents, entry.RegisteredAgent)
					goto nextAgent
				}
			}
		}
	nextAgent:
	}

	return matchingAgents
}

// GetAgentStats returns statistics about registered agents
func (s *AgentRegistryService) GetAgentStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_agents":     len(s.agents),
		"healthy_agents":   0,
		"unhealthy_agents": 0,
		"agent_types":      make(map[string]int),
		"skill_count":      make(map[string]int),
	}

	for _, entry := range s.agents {
		// Count health status
		if entry.Status.Status == biddingModels.AgentStatusHealthy {
			stats["healthy_agents"] = stats["healthy_agents"].(int) + 1
		} else {
			stats["unhealthy_agents"] = stats["unhealthy_agents"].(int) + 1
		}

		// Count agent types (based on provider organization)
		orgType := entry.AgentCard.Provider.Organization
		if orgType == "" {
			orgType = "unknown"
		}
		agentTypes := stats["agent_types"].(map[string]int)
		agentTypes[orgType]++

		// Count skills
		skillCount := stats["skill_count"].(map[string]int)
		for _, skill := range entry.AgentCard.Skills {
			skillCount[skill.ID]++
		}
	}

	return stats
}

// healthCheckRoutine periodically checks agent health
func (s *AgentRegistryService) healthCheckRoutine() {
	// 进一步减少健康检查频率，每15分钟检查一次，避免过多日志
	// 对于稳定的生产环境，15分钟的检查间隔是合适的
	ticker := time.NewTicker(15 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		s.performHealthChecks()
	}
}

// performHealthChecks checks the health of all registered agents
func (s *AgentRegistryService) performHealthChecks() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	for agentID, entry := range s.agents {
		if entry.Agent != nil {
			// Get fresh health status
			healthStatus := entry.Agent.HealthCheck()
			entry.Status = biddingModels.AgentStatus{
				Status:      healthStatus.Status,
				LastUpdated: now,
			}
			entry.LastPing = now

			if healthStatus.Status != biddingModels.AgentStatusHealthy {
				utils.Log.Warnf("Agent %s health check failed: %s", agentID, healthStatus.Status)
			}
		} else {
			// Check if agent hasn't been seen for too long
			if now.Sub(entry.LastPing) > 5*time.Minute {
				entry.Status.Status = biddingModels.AgentStatusUnavailable
				utils.Log.Warnf("Agent %s marked as unavailable (last seen: %v)", agentID, entry.LastPing)
			}
		}
	}
}

// PingAgent updates the last ping time for an agent
func (s *AgentRegistryService) PingAgent(agentID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	entry, exists := s.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	entry.LastPing = time.Now()
	if entry.Status.Status == biddingModels.AgentStatusUnavailable {
		entry.Status.Status = biddingModels.AgentStatusHealthy
	}

	return nil
}
