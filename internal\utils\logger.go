package utils

import (
	"os"
	"strings"

	"github.com/sirupsen/logrus"
)

var Log *logrus.Logger // 全局日志实例

// InitLogger 初始化日志器
func InitLogger(level string) {
	Log = logrus.New()
	Log.SetFormatter(&logrus.JSONFormatter{}) // JSON 格式，便于日志收集系统处理
	Log.SetOutput(os.Stdout)

	logLevel, err := logrus.ParseLevel(strings.ToLower(level))
	if err != nil {
		logLevel = logrus.InfoLevel
		Log.Warnf("无效的日志级别 '%s', 将使用默认级别 'info'", level)
	}
	Log.SetLevel(logLevel)
}
