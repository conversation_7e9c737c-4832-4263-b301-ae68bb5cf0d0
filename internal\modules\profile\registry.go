package profile

import (
	"fmt"
	"sync"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// Registry 企业画像模块注册表
type Registry struct {
	factory        *ServiceFactory
	profileService services.ProfileServiceInterface
	storageService services.StorageServiceInterface
	schemaService  services.SchemaServiceInterface
	mu             sync.RWMutex
	initialized    bool
}

var (
	registry *Registry
	once     sync.Once
)

// GetRegistry 获取全局注册表实例（单例）
func GetRegistry() *Registry {
	once.Do(func() {
		registry = &Registry{}
	})
	return registry
}

// Initialize 初始化注册表
func (r *Registry) Initialize(llmClient llm.LLMClient, promptManager *prompts.PromptManager) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.initialized {
		return fmt.Errorf("profile registry already initialized")
	}

	// 创建服务工厂
	r.factory = NewServiceFactory(llmClient, promptManager)

	// 创建所有服务
	r.profileService, r.storageService, r.schemaService = r.factory.CreateAllServices()

	r.initialized = true
	utils.Log.Info("Profile module registry initialized successfully")

	return nil
}

// GetProfileService 获取企业画像生成服务
func (r *Registry) GetProfileService() (services.ProfileServiceInterface, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.initialized {
		return nil, fmt.Errorf("profile registry not initialized")
	}

	return r.profileService, nil
}

// GetStorageService 获取存储服务
func (r *Registry) GetStorageService() (services.StorageServiceInterface, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.initialized {
		return nil, fmt.Errorf("profile registry not initialized")
	}

	return r.storageService, nil
}

// GetSchemaService 获取Schema服务
func (r *Registry) GetSchemaService() (services.SchemaServiceInterface, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.initialized {
		return nil, fmt.Errorf("profile registry not initialized")
	}

	return r.schemaService, nil
}

// IsInitialized 检查是否已初始化
func (r *Registry) IsInitialized() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.initialized
}

// Reset 重置注册表（主要用于测试）
func (r *Registry) Reset() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.factory = nil
	r.profileService = nil
	r.storageService = nil
	r.schemaService = nil
	r.initialized = false

	utils.Log.Info("Profile module registry reset")
}