#!/usr/bin/env python3
"""
A2A Agent模块功能测试 - 基于 A2A 协议
专门测试 A2A Agent 的注册、管理、执行等功能
不依赖 PostgreSQL 数据库，专注于 Agent 功能测试
"""

import json
import pytest
import requests
import time
import os
from urllib.parse import urlparse
from typing import Dict, Any, List
from dataclasses import dataclass, field

@dataclass
class A2ATestConfig:
    """A2A测试配置，从环境变量加载"""
    host: str = field(init=False)
    port: int = field(init=False)
    timeout: int = field(init=False)

    def __post_init__(self):
        base_url = os.environ.get("TASKD_BASE_URL", "http://localhost:8601")
        parsed_url = urlparse(base_url)
        self.host = parsed_url.hostname or "localhost"
        self.port = parsed_url.port or 8601
        self.timeout = int(os.environ.get("TASKD_TIMEOUT", "120"))


@dataclass
class A2ATestResult:
    """A2A测试结果"""
    success: bool
    message: str
    data: Dict[str, Any] = None
    duration: float = 0.0

class A2AAgentTest:
    """A2A Agent测试类 - 专注于 A2A 协议测试"""
    
    def __init__(self, config: A2ATestConfig):
        self.config = config
        # A2A Agent 路由在根路径下
        self.base_url = f"http://{config.host}:{config.port}"
        self.agent_url = f"{self.base_url}/agents"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'A2A-Agent-Test-Client/1.0'
        })
        # 设置统一的请求超时
        self.session.timeout = 30
    
    def test_global_health_check(self) -> A2ATestResult:
        """测试全局健康检查"""
        print("测试全局健康检查...")
        
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/healthz")
            duration = time.time() - start_time
            
            if response.status_code != 200:
                return A2ATestResult(
                    success=False,
                    message=f"全局健康检查失败: {response.status_code} - {response.text}",
                    duration=duration
                )
            
            # 可能返回简单的字符串或JSON
            try:
                data = response.json()
            except:
                # 如果不是JSON，检查文本内容
                text = response.text.strip()
                if text.lower() in ['ok', 'healthy']:
                    data = {"status": "ok", "message": text}
                else:
                    return A2ATestResult(
                        success=False,
                        message=f"健康检查返回异常内容: {text}",
                        duration=duration
                    )
            
            print(f"全局健康检查完成")
            print(f"   - 响应: {data}")
            
            return A2ATestResult(
                success=True,
                message="全局健康检查通过",
                data=data,
                duration=duration
            )
            
        except Exception as e:
            return A2ATestResult(
                success=False,
                message=f"全局健康检查异常: {str(e)}",
                duration=time.time() - start_time
            )
    
    def test_list_agents(self) -> A2ATestResult:
        """测试获取 A2A Agent 列表"""
        print("测试获取 A2A Agent 列表...")
        
        start_time = time.time()
        try:
            response = self.session.get(self.agent_url)
            duration = time.time() - start_time
            
            if response.status_code != 200:
                return A2ATestResult(
                    success=False,
                    message=f"获取Agent列表失败: {response.status_code} - {response.text}",
                    duration=duration
                )
            
            data = response.json()
            
            # A2A Handler 返回的响应结构: {agents: [], count: N, timestamp: "..."}
            required_fields = ['agents', 'count']
            for field in required_fields:
                if field not in data:
                    return A2ATestResult(
                        success=False,
                        message=f"响应缺少必需字段: {field}",
                        duration=duration
                    )
            
            agents = data['agents']
            count = data['count']
            print(f"成功获取 A2A Agent列表，共 {count} 个Agent")
            
            # 打印Agent详情
            for agent in agents:
                print(f"   - Agent ID: {agent.get('id')}")
                print(f"     Agent Name: {agent.get('agent_card', {}).get('name')}")
                print(f"     Status: {agent.get('status', {}).get('status')}")
                print(f"     URL: {agent.get('agent_card', {}).get('url')}")
            
            return A2ATestResult(
                success=True,
                message=f"成功获取A2A Agent列表，共 {count} 个Agent",
                data=data,
                duration=duration
            )
            
        except Exception as e:
            return A2ATestResult(
                success=False,
                message=f"获取Agent列表异常: {str(e)}",
                duration=time.time() - start_time
            )
    
    def test_agent_documentation(self) -> A2ATestResult:
        """测试获取Agent文档页面"""
        print("测试获取Agent文档页面...")
        
        start_time = time.time()
        try:
            response = self.session.get(f"{self.agent_url}/docs")
            duration = time.time() - start_time
            
            if response.status_code != 200:
                return A2ATestResult(
                    success=False,
                    message=f"获取Agent文档失败: {response.status_code} - {response.text}",
                    duration=duration
                )
            
            # 验证返回的是HTML内容
            content_type = response.headers.get('content-type', '')
            if 'html' not in content_type.lower():
                return A2ATestResult(
                    success=False,
                    message=f"期望HTML内容，实际收到: {content_type}",
                    duration=duration
                )
            
            html_content = response.text
            # 验证HTML包含关键内容
            if "TaskD Agents Documentation" not in html_content:
                return A2ATestResult(
                    success=False,
                    message="HTML文档缺少预期的标题内容",
                    duration=duration
                )
            
            print(f"成功获取Agent文档页面，内容长度: {len(html_content)} 字符")
            
            return A2ATestResult(
                success=True,
                message="成功获取Agent文档页面",
                data={"content_length": len(html_content)},
                duration=duration
            )
            
        except Exception as e:
            return A2ATestResult(
                success=False,
                message=f"获取Agent文档异常: {str(e)}",
                duration=time.time() - start_time
            )
    
    def test_execute_a2a_agent(self, agent_id: str = None) -> A2ATestResult:
        """测试执行 A2A Agent"""
        print("测试执行 A2A Agent...")
        
        # 如果没有指定agent_id，使用可能存在的默认agent
        if not agent_id:
            # 根据招投标模块，尝试一些可能的agent名称
            possible_agents = [
                "bidding-summary-agent",
                "bidding-agent-001", 
                "ai-summary-agent",
                "data-retrieval-agent",
                "test-agent"
            ]
            agent_id = possible_agents[0]  # 使用第一个作为默认
        
        # A2A 协议请求格式
        a2a_request = {
            "jsonrpc": "2.0",
            "id": f"test-{int(time.time())}",
            "params": {
                "skill_id": "generate_summary",
                "input": {
                    "tender_data": {
                        "project_name": "某市政府采购IT设备项目",
                        "budget": "500万元",
                        "deadline": "2024-12-31",
                        "description": "采购服务器、网络设备等IT基础设施",
                        "requirements": "需要具备相关资质，有类似项目经验"
                    }
                }
            }
        }
        
        start_time = time.time()
        try:
            # A2A 执行路径
            execute_url = f"{self.agent_url}/execute/{agent_id}"
            response = self.session.post(execute_url, json=a2a_request)
            duration = time.time() - start_time
            
            # A2A可能返回404（agent不存在）或其他错误
            if response.status_code == 404:
                return A2ATestResult(
                    success=False,
                    message=f"Agent不存在: {agent_id}",
                    duration=duration
                )
            elif response.status_code != 200:
                return A2ATestResult(
                    success=False,
                    message=f"执行A2A Agent失败: {response.status_code} - {response.text}",
                    duration=duration
                )
            
            data = response.json()
            
            # A2A 协议响应验证
            required_fields = ['jsonrpc', 'id']
            for field in required_fields:
                if field not in data:
                    return A2ATestResult(
                        success=False,
                        message=f"A2A响应缺少必需字段: {field}",
                        duration=duration
                    )
            
            # 检查是否有错误
            if 'error' in data:
                error = data['error']
                return A2ATestResult(
                    success=False,
                    message=f"A2A Agent执行错误: {error.get('message', 'Unknown error')}",
                    duration=duration
                )
            
            # 检查结果
            if 'result' not in data:
                return A2ATestResult(
                    success=False,
                    message="A2A响应缺少result字段",
                    duration=duration
                )
            
            result = data['result']
            print(f"A2A Agent执行成功")
            print(f"   - Agent ID: {agent_id}")
            print(f"   - Request ID: {data.get('id')}")
            if result:
                print(f"   - 执行结果类型: {type(result)}")
                if isinstance(result, dict) and len(str(result)) < 200:
                    print(f"   - 执行结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            return A2ATestResult(
                success=True,
                message=f"A2A Agent执行成功: {agent_id}",
                data=data,
                duration=duration
            )
            
        except Exception as e:
            return A2ATestResult(
                success=False,
                message=f"执行A2A Agent异常: {str(e)}",
                duration=time.time() - start_time
            )
    
    def test_agent_health_check(self, agent_id: str = None) -> A2ATestResult:
        """测试Agent健康检查"""
        if not agent_id:
            agent_id = "bidding-summary-agent"
            
        print(f"测试Agent健康检查: {agent_id}")
        
        start_time = time.time()
        try:
            # A2A 健康检查路径
            health_url = f"{self.agent_url}/execute/{agent_id}/health"
            response = self.session.get(health_url)
            duration = time.time() - start_time
            
            # Agent不存在时返回404是正常的
            if response.status_code == 404:
                return A2ATestResult(
                    success=False,
                    message=f"Agent不存在: {agent_id}",
                    duration=duration
                )
            
            # 健康检查可能返回200或503
            if response.status_code not in [200, 503]:
                return A2ATestResult(
                    success=False,
                    message=f"Agent健康检查请求失败: {response.status_code} - {response.text}",
                    duration=duration
                )
            
            try:
                data = response.json()
            except:
                # 如果不是JSON，可能是简单的文本响应
                text = response.text.strip()
                data = {"status": "unknown", "message": text}
            
            # 验证健康检查响应结构
            status = data.get('status', 'unknown')
            is_healthy = status == 'healthy'
            
            print(f"Agent健康检查完成")
            print(f"   - Agent ID: {agent_id}")
            print(f"   - 健康状态: {'健康' if is_healthy else '不健康'}")
            print(f"   - 状态: {status}")
            
            return A2ATestResult(
                success=True,
                message=f"Agent健康检查完成，状态: {status}",
                data=data,
                duration=duration
            )
            
        except Exception as e:
            return A2ATestResult(
                success=False,
                message=f"Agent健康检查异常: {str(e)}",
                duration=time.time() - start_time
            )
    
    def run_comprehensive_test(self) -> Dict[str, A2ATestResult]:
        """运行A2A Agent模块综合测试"""
        print("开始 A2A Agent 模块综合测试")
        print("=" * 60)
        
        results = {}
        
        # 1. 测试全局健康检查
        results['global_health_check'] = self.test_global_health_check()
        
        # 2. 测试获取Agent列表
        results['list_agents'] = self.test_list_agents()
        
        # 3. 测试Agent文档页面
        results['agent_documentation'] = self.test_agent_documentation()
        
        # 4. 获取可用的Agent进行详细测试
        available_agent_id = None
        if results['list_agents'].success and results['list_agents'].data:
            agents = results['list_agents'].data.get('agents', [])
            if agents:
                available_agent_id = agents[0].get('id')
        
        if available_agent_id:
            print(f"找到可用的Agent: {available_agent_id}，进行详细测试...")
            # 5. 测试Agent健康检查
            results['agent_health_check'] = self.test_agent_health_check(available_agent_id)
            
            # 6. 测试执行Agent
            results['execute_a2a_agent'] = self.test_execute_a2a_agent(available_agent_id)
        else:
            print("没有找到可用的Agent，使用默认配置进行测试...")
            # 使用默认Agent进行测试
            results['agent_health_check'] = self.test_agent_health_check()
            results['execute_a2a_agent'] = self.test_execute_a2a_agent()
        
        return results
    
    def generate_test_report(self, results: Dict[str, A2ATestResult]) -> str:
        """生成测试报告"""
        report = []
        report.append("A2A Agent模块测试报告")
        report.append("=" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result.success)
        failed_tests = total_tests - passed_tests
        
        report.append(f"总测试数: {total_tests}")
        report.append(f"通过: {passed_tests}")
        report.append(f"失败: {failed_tests}")
        report.append(f"通过率: {passed_tests/total_tests*100:.1f}%")
        report.append("")
        
        # 详细测试结果
        report.append("详细测试结果:")
        report.append("-" * 40)
        
        for test_name, result in results.items():
            status = "通过" if result.success else "失败"
            report.append(f"{test_name}: {status}")
            report.append(f"  消息: {result.message}")
            report.append(f"  耗时: {result.duration:.3f}秒")
            report.append("")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("A2A Agent模块测试工具")
    print("=" * 60)
    
    # 加载测试配置
    config = A2ATestConfig()
    
    # 创建测试实例
    tester = A2AAgentTest(config)
    
    try:
        # 运行综合测试
        results = tester.run_comprehensive_test()
        
        # 生成测试报告
        report = tester.generate_test_report(results)
        
        print("\n" + "=" * 60)
        print(report)
        
        # 保存测试报告
        with open('a2a_agent_test_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"测试报告已保存到: a2a_agent_test_report.txt")
        
        # 返回测试结果
        passed_tests = sum(1 for result in results.values() if result.success)
        return passed_tests == len(results)
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        return False

def test_a2a_agent_module_suite():
    """
    通过pytest运行完整的A2A Agent模块测试套件。
    """
    print("\n[Pytest] 开始执行 A2A Agent 模块测试套件...")
    assert main() is True, "A2A Agent模块综合测试未通过"
    print("[Pytest] A2A Agent 模块测试套件执行成功。")

# 单独的测试函数，可以通过pytest单独运行
def test_global_health():
    """测试全局健康检查"""
    config = A2ATestConfig()
    tester = A2AAgentTest(config)
    result = tester.test_global_health_check()
    assert result.success, f"全局健康检查失败: {result.message}"

def test_list_agents():
    """测试Agent列表获取"""
    config = A2ATestConfig()
    tester = A2AAgentTest(config)
    result = tester.test_list_agents()
    assert result.success, f"Agent列表获取失败: {result.message}"

def test_agent_docs():
    """测试Agent文档页面"""
    config = A2ATestConfig()
    tester = A2AAgentTest(config)
    result = tester.test_agent_documentation()
    assert result.success, f"Agent文档页面获取失败: {result.message}"

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 