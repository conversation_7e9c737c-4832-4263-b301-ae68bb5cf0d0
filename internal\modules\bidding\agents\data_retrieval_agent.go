package agents

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// DataRetrievalAgent handles tender data retrieval from MongoDB
type DataRetrievalAgent struct {
	*BaseAgent
	mongoClient *mongo.Client
	database    string
	collection  string
}

// NewDataRetrievalAgent creates a new data retrieval agent
func NewDataRetrievalAgent(mongoClient *mongo.Client, database, collection string) *DataRetrievalAgent {
	agentCard := biddingModels.AgentCard{
		Name:        "Bidding Data Retrieval Agent",
		Description: "获取和预处理招投标数据，从MongoDB数据库中提取完整的招投标信息",
		URL:         "http://taskd-service:8601/agents/bidding-data-retrieval",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version:          "1.0.0",
		DocumentationURL: "https://docs.taskd.platform/agents/bidding-data-retrieval",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          "retrieve_tender_data",
				Name:        "获取招投标数据",
				Description: "从MongoDB获取完整的招投标数据并进行预处理和验证",
				Tags:        []string{"data", "mongodb", "tender", "retrieval"},
				Examples: []string{
					"获取招投标ID为TENDER_2024_001的完整数据",
					"检索指定招投标的中文版本数据",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
				InputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"tender_id": map[string]interface{}{
							"type":        "string",
							"description": "招投标唯一标识符",
							"example":     "TENDER_2024_001",
						},
						"language": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"chinese", "english"},
							"default":     "chinese",
							"description": "数据语言偏好 / Data language preference",
						},
						"include_metadata": map[string]interface{}{
							"type":        "boolean",
							"default":     true,
							"description": "是否包含元数据信息",
						},
						"fields": map[string]interface{}{
							"type":        "array",
							"items":       map[string]interface{}{"type": "string"},
							"description": "需要获取的字段列表，为空表示获取所有字段",
						},
					},
					"required": []string{"tender_id"},
				},
				OutputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"tender_data": map[string]interface{}{
							"type":        "object",
							"description": "完整的招投标数据",
						},
						"validation_status": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"valid", "invalid", "partial"},
							"description": "数据验证状态",
						},
						"metadata": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"source":       map[string]interface{}{"type": "string"},
								"timestamp":    map[string]interface{}{"type": "string"},
								"data_quality": map[string]interface{}{"type": "number"},
								"field_count":  map[string]interface{}{"type": "integer"},
							},
						},
					},
				},
			},
		},
	}

	baseAgent := NewBaseAgent("bidding-data-retrieval", agentCard)

	return &DataRetrievalAgent{
		BaseAgent:   baseAgent,
		mongoClient: mongoClient,
		database:    database,
		collection:  collection,
	}
}

// ExecuteSkill executes the specified skill
func (agent *DataRetrievalAgent) ExecuteSkill(skillID string, input map[string]interface{},
	context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {

	switch skillID {
	case "retrieve_tender_data":
		return agent.ExecuteSkillWithHandler(skillID, input, context, agent.retrieveTenderData)
	default:
		return nil, fmt.Errorf("unknown skill: %s", skillID)
	}
}

// retrieveTenderData handles the tender data retrieval logic
func (agent *DataRetrievalAgent) retrieveTenderData(input map[string]interface{},
	a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {

	// Parse input parameters
	tenderID, ok := input["tender_id"].(string)
	if !ok || tenderID == "" {
		return nil, fmt.Errorf("tender_id is required and must be a string")
	}

	language := "chinese"
	if lang, exists := input["language"]; exists {
		if langStr, ok := lang.(string); ok {
			language = langStr
		}
	}

	includeMetadata := true
	if meta, exists := input["include_metadata"]; exists {
		if metaBool, ok := meta.(bool); ok {
			includeMetadata = metaBool
		}
	}

	var requestedFields []string
	if fields, exists := input["fields"]; exists {
		if fieldsArray, ok := fields.([]interface{}); ok {
			for _, field := range fieldsArray {
				if fieldStr, ok := field.(string); ok {
					requestedFields = append(requestedFields, fieldStr)
				}
			}
		}
	}

	utils.Log.Infof("Retrieving tender data: ID=%s, Language=%s, Fields=%v",
		tenderID, language, requestedFields)

	// Execute retrieval with longer timeout to handle network delays
	var tenderData biddingModels.TenderData
	var validationStatus biddingModels.DataValidationStatus

	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()
	err := agent.fetchTenderFromDatabase(ctx, tenderID, &tenderData, &validationStatus)

	if err != nil {
		return nil, fmt.Errorf("failed to retrieve tender data: %w", err)
	}

	// Filter fields if requested
	if len(requestedFields) > 0 {
		tenderData = agent.filterTenderFields(tenderData, requestedFields)
	}

	// Build response
	response := map[string]interface{}{
		"tender_data":       tenderData,
		"validation_status": validationStatus,
	}

	if includeMetadata {
		metadata := biddingModels.RetrievalMetadata{
			Source:      fmt.Sprintf("mongodb://%s/%s", agent.database, agent.collection),
			Timestamp:   time.Now(),
			DataQuality: validationStatus.Quality,
			FieldCount:  agent.countTenderFields(tenderData),
			Schema:      "taskd",
		}
		response["metadata"] = metadata
	}

	utils.Log.Infof("Successfully retrieved tender data for ID: %s (quality: %.2f)",
		tenderID, validationStatus.Quality)

	return response, nil
}

// fetchTenderFromDatabase retrieves tender data from MongoDB
func (agent *DataRetrievalAgent) fetchTenderFromDatabase(ctx context.Context, tenderID string,
	tenderData *biddingModels.TenderData, validationStatus *biddingModels.DataValidationStatus) error {

	// Use global MongoDB connection instead of pinging each time
	// The global connection pool is managed by the application lifecycle
	db := agent.mongoClient.Database(agent.database)
	collection := db.Collection(agent.collection)

	utils.Log.Infof("Querying collection: %s.%s for tender ID: %s", agent.database, agent.collection, tenderID)

	// Build query filter - handle both string and ObjectId
	var filter bson.M
	if objectID, err := primitive.ObjectIDFromHex(tenderID); err == nil {
		// Try ObjectId first (for MongoDB ObjectId format like 6846ab859c792c503ff05964)
		filter = bson.M{"_id": objectID}
		utils.Log.Infof("Using ObjectId filter for ID: %s", tenderID)
	} else {
		// Fallback to string ID
		filter = bson.M{"_id": tenderID}
		utils.Log.Infof("Using string filter for ID: %s", tenderID)
	}

	// Execute query
	var result bson.M
	err := collection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			// If not found with ObjectId, try searching with string ID as well
			if _, convErr := primitive.ObjectIDFromHex(tenderID); convErr == nil {
				utils.Log.Infof("ObjectId search failed, trying string search for ID: %s", tenderID)
				stringFilter := bson.M{"_id": tenderID}
				if stringErr := collection.FindOne(ctx, stringFilter).Decode(&result); stringErr == nil {
					utils.Log.Infof("Found document with string ID filter")
					err = nil // Clear the error since we found it
				}
			}
			if err != nil {
				// Log additional debugging info
				utils.Log.Errorf("Tender not found in collection %s.%s with ID: %s", agent.database, agent.collection, tenderID)
				// Try to list some documents to verify collection exists
				count, countErr := collection.CountDocuments(ctx, bson.M{})
				if countErr == nil {
					utils.Log.Infof("Collection %s.%s contains %d documents", agent.database, agent.collection, count)
				}
				return fmt.Errorf("tender not found: %s", tenderID)
			}
		} else {
			utils.Log.Errorf("Database query failed with error: %v", err)
			return fmt.Errorf("database query failed: %w", err)
		}
	}

	// Convert to TenderData struct
	*tenderData, err = agent.convertBSONToTenderData(result)
	if err != nil {
		return fmt.Errorf("failed to convert data: %w", err)
	}

	// Validate the data
	*validationStatus = agent.validateTenderData(*tenderData)

	return nil
}

// convertBSONToTenderData converts BSON result to TenderData struct
func (agent *DataRetrievalAgent) convertBSONToTenderData(result bson.M) (biddingModels.TenderData, error) {
	tenderData := biddingModels.TenderData{}

	// Basic fields - handle both string and ObjectId
	if id, ok := result["_id"].(string); ok {
		tenderData.ID = id
	} else if objectID, ok := result["_id"].(primitive.ObjectID); ok {
		tenderData.ID = objectID.Hex()
	}
	if title, ok := result["title"].(string); ok {
		tenderData.Title = title
	}
	if content, ok := result["content"].(string); ok {
		tenderData.Content = content
	}

	// Deadline (handle various time formats)
	if deadline, ok := result["deadline"]; ok {
		if deadlineTime, ok := deadline.(time.Time); ok {
			tenderData.Deadline = deadlineTime
		} else if deadlineStr, ok := deadline.(string); ok {
			// Try to parse string date
			if parsedTime, err := time.Parse(time.RFC3339, deadlineStr); err == nil {
				tenderData.Deadline = parsedTime
			}
		}
	}

	// Budget information
	if budget, ok := result["budget"].(bson.M); ok {
		budgetInfo := biddingModels.BudgetInfo{}
		if total, ok := budget["total"].(string); ok {
			budgetInfo.Total = total
		}
		if currency, ok := budget["currency"].(string); ok {
			budgetInfo.Currency = currency
		}
		if budgetType, ok := budget["budget_type"].(string); ok {
			budgetInfo.BudgetType = budgetType
		}
		if paymentTerms, ok := budget["payment_terms"].(string); ok {
			budgetInfo.PaymentTerms = paymentTerms
		}
		tenderData.Budget = budgetInfo
	}

	// Requirements
	if requirements, ok := result["requirements"].(bson.M); ok {
		reqInfo := biddingModels.RequirementInfo{}
		if technical, ok := requirements["technical"].([]interface{}); ok {
			for _, item := range technical {
				if str, ok := item.(string); ok {
					reqInfo.Technical = append(reqInfo.Technical, str)
				}
			}
		}
		if business, ok := requirements["business"].([]interface{}); ok {
			for _, item := range business {
				if str, ok := item.(string); ok {
					reqInfo.Business = append(reqInfo.Business, str)
				}
			}
		}
		if compliance, ok := requirements["compliance"].([]interface{}); ok {
			for _, item := range compliance {
				if str, ok := item.(string); ok {
					reqInfo.Compliance = append(reqInfo.Compliance, str)
				}
			}
		}
		tenderData.Requirements = reqInfo
	}

	// Contact information
	if contact, ok := result["contact_info"].(bson.M); ok {
		contactInfo := biddingModels.ContactInfo{}
		if org, ok := contact["organization"].(string); ok {
			contactInfo.Organization = org
		}
		if person, ok := contact["contact_person"].(string); ok {
			contactInfo.ContactPerson = person
		}
		if phone, ok := contact["phone"].(string); ok {
			contactInfo.Phone = phone
		}
		if email, ok := contact["email"].(string); ok {
			contactInfo.Email = email
		}
		tenderData.ContactInfo = contactInfo
	}

	// Metadata
	if metadata, ok := result["metadata"].(bson.M); ok {
		tenderData.Metadata = make(map[string]interface{})
		for key, value := range metadata {
			tenderData.Metadata[key] = value
		}
	}

	return tenderData, nil
}

// validateTenderData validates the quality and completeness of tender data
func (agent *DataRetrievalAgent) validateTenderData(data biddingModels.TenderData) biddingModels.DataValidationStatus {
	status := biddingModels.DataValidationStatus{
		Status:        "valid",
		ErrorFields:   []string{},
		MissingFields: []string{},
		Quality:       1.0,
	}

	requiredFields := map[string]interface{}{
		"id":      data.ID,
		"title":   data.Title,
		"content": data.Content,
	}

	missingCount := 0
	totalFields := len(requiredFields)

	// Check required fields
	for field, value := range requiredFields {
		if value == nil || value == "" {
			status.MissingFields = append(status.MissingFields, field)
			missingCount++
		}
	}

	// Check optional but important fields
	if data.Deadline.IsZero() {
		status.MissingFields = append(status.MissingFields, "deadline")
		missingCount++
		totalFields++
	}

	if data.Budget.Total == "" {
		status.MissingFields = append(status.MissingFields, "budget.total")
		missingCount++
		totalFields++
	}

	// Calculate quality score
	if totalFields > 0 {
		status.Quality = float64(totalFields-missingCount) / float64(totalFields)
	}

	// Set status based on quality
	if status.Quality < 0.5 {
		status.Status = "invalid"
	} else if status.Quality < 0.8 {
		status.Status = "partial"
	}

	return status
}

// filterTenderFields filters tender data to include only requested fields
func (agent *DataRetrievalAgent) filterTenderFields(data biddingModels.TenderData,
	requestedFields []string) biddingModels.TenderData {

	filtered := biddingModels.TenderData{}
	fieldMap := make(map[string]bool)
	for _, field := range requestedFields {
		fieldMap[field] = true
	}

	// Always include ID
	filtered.ID = data.ID

	if fieldMap["title"] {
		filtered.Title = data.Title
	}
	if fieldMap["content"] {
		filtered.Content = data.Content
	}
	if fieldMap["deadline"] {
		filtered.Deadline = data.Deadline
	}
	if fieldMap["budget"] {
		filtered.Budget = data.Budget
	}
	if fieldMap["requirements"] {
		filtered.Requirements = data.Requirements
	}
	if fieldMap["contact_info"] {
		filtered.ContactInfo = data.ContactInfo
	}
	if fieldMap["metadata"] {
		filtered.Metadata = data.Metadata
	}

	return filtered
}

// countTenderFields counts the number of non-empty fields in tender data
func (agent *DataRetrievalAgent) countTenderFields(data biddingModels.TenderData) int {
	count := 0

	if data.ID != "" {
		count++
	}
	if data.Title != "" {
		count++
	}
	if data.Content != "" {
		count++
	}
	if !data.Deadline.IsZero() {
		count++
	}
	if data.Budget.Total != "" {
		count++
	}
	if len(data.Requirements.Technical) > 0 {
		count++
	}
	if len(data.Requirements.Business) > 0 {
		count++
	}
	if len(data.Requirements.Compliance) > 0 {
		count++
	}
	if data.ContactInfo.Organization != "" {
		count++
	}
	if len(data.Metadata) > 0 {
		count++
	}

	return count
}

// HealthCheck returns enhanced health status for this agent
func (agent *DataRetrievalAgent) HealthCheck() biddingModels.HealthStatus {
	status := agent.BaseAgent.HealthCheck()

	// Since we're using a global connection pool managed by the application lifecycle,
	// we no longer perform a ping to check MongoDB connection status
	status.Dependencies["mongodb"] = "connected (global pool)"

	return status
}
