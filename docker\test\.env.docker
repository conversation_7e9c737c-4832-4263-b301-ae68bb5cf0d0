# TaskD 跨网络测试环境配置
# 测试容器部署在Ubuntu服务器，TaskD服务运行在Windows本地机器

# ================================
# TaskD 服务配置（外部网络）
# ================================

# TaskD 服务地址（Windows本地机器）
# 请根据实际IP地址修改
TASKD_BASE_URL=http://**************:8601

# API 版本前缀
TASKD_API_VERSION=v1

# 请求超时时间（秒）- 跨网络访问增加超时时间
TASKD_TIMEOUT=120

# 重试次数 - 网络环境增加重试次数
TASKD_MAX_RETRIES=8

# 服务启动等待时间（秒）- 外部服务等待时间
TASKD_STARTUP_WAIT=15

# 网络连接超时（秒）
NETWORK_CONNECT_TIMEOUT=30

# ================================
# 测试配置
# ================================

# 测试数据前缀（避免与生产数据冲突）
TEST_DATA_PREFIX=ubuntu_test_

# 测试超时时间（秒）- 跨网络测试增加超时
TEST_TIMEOUT=180

# 并发测试工作进程数
TEST_CONCURRENT_WORKERS=8

# 是否启用详细日志
TEST_VERBOSE=true

# 是否保留测试数据（调试用）
TEST_KEEP_DATA=false

# 测试报告格式 (console, json, html)
TEST_REPORT_FORMAT=console

# ================================
# Agent 配置
# ================================

# 默认测试的 Agent 类型
DEFAULT_TEST_AGENTS=entity_extraction_agent,tender-data-extraction,tender-classification

# Agent 执行超时时间（秒）
AGENT_EXECUTION_TIMEOUT=180

# Agent 测试数据大小限制（字节）
AGENT_TEST_DATA_MAX_SIZE=2097152

# ================================
# LLM 配置
# ================================

# 测试用的模型提供商
TEST_LLM_PROVIDER=volcengine_ark

# 测试用的模型名称
TEST_LLM_MODEL=doubao-1-5-pro-32k-250115

# 测试用的最大 Token 数
TEST_MAX_TOKENS=8000

# ================================
# 数据库配置（测试用）
# ================================

# 测试用的数据库前缀
TEST_DB_PREFIX=docker_test_

# 是否在测试后清理数据库
TEST_CLEANUP_DB=true

# ================================
# 性能测试配置
# ================================

# 性能测试并发连接数
PERF_TEST_CONCURRENT_CONNECTIONS=20

# 性能测试持续时间（秒）
PERF_TEST_DURATION=120

# 性能测试 QPS 目标
PERF_TEST_TARGET_QPS=200

# ================================
# 聊天测试配置
# ================================

# WebSocket 连接超时时间（秒）
WEBSOCKET_TIMEOUT=60

# 聊天会话测试数量
CHAT_SESSION_TEST_COUNT=10

# 单个会话最大消息数
CHAT_MAX_MESSAGES_PER_SESSION=50

# ================================
# 日志配置
# ================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# 是否启用结构化日志
LOG_STRUCTURED=true

# 日志文件保留天数
LOG_RETENTION_DAYS=3

# 是否输出彩色日志
LOG_COLORED=true

# ================================
# Docker 专用配置
# ================================

# 容器内网络检查
DOCKER_NETWORK_CHECK=true

# DNS 解析超时（秒）
DNS_RESOLUTION_TIMEOUT=10

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30