package tender_preprocess

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ExtractionAgent 数据提取与验证Agent
type ExtractionAgent struct {
	*agents.BaseAgent
	llmService    services.LLMService
	promptManager *prompts.PreprocessingPromptManager
}

// NewExtractionAgent 创建数据提取Agent
func NewExtractionAgent(llmService services.LLMService, promptManager *prompts.PreprocessingPromptManager) *ExtractionAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentExtractionName,
		Description: "从原始招投标文本中提取结构化信息并进行数据验证",
		URL:         "http://taskd-service:8601/agents/tender-data-extraction",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillExtractStructuredData,
				Name:        "提取结构化数据",
				Description: "从原始招投标文本中并行提取基本信息、机构信息、招标要求和供应商要求",
				Tags:        []string{"extraction", "ai", "llm", "parallel"},
				Examples: []string{
					"从政府采购公告中提取基本项目信息和要求",
					"解析建设工程招标书的结构化数据",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentExtractionName, agentCard)

	return &ExtractionAgent{
		BaseAgent:     baseAgent,
		llmService:    llmService,
		promptManager: promptManager,
	}
}

// ExecuteSkill 执行技能
func (a *ExtractionAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillExtractStructuredData:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.extractStructuredDataHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// extractStructuredDataHandler 结构化数据提取处理器
func (a *ExtractionAgent) extractStructuredDataHandler(input map[string]interface{}, a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseExtractionParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting data extraction for text length: %d", len(params.RawText))

	// 验证语言
	if !agentCommon.ValidateLanguage(params.Language) {
		return nil, fmt.Errorf("unsupported language: %s", params.Language)
	}

	// 验证提取模式
	if !agentCommon.ValidateExtractionMode(params.ExtractionMode) {
		return nil, fmt.Errorf("unsupported extraction mode: %s", params.ExtractionMode)
	}

	// 提取数据
	tenderData, metadata, err := a.extractData(params, a2aContext)
	if err != nil {
		return nil, fmt.Errorf("data extraction failed: %v", err)
	}

	// 验证提取结果
	validationResult := a.validateExtractedData(tenderData)

	// 构建输出结果
	output := map[string]interface{}{
		"extracted_data":      tenderData,
		"validation_result":   validationResult,
		"processing_metadata": metadata,
	}

	return output, nil
}

// ExtractionParams 提取参数
type ExtractionParams struct {
	RawText        string   `json:"raw_text"`
	Language       string   `json:"language"`
	ExtractionMode string   `json:"extraction_mode"`
	Fields         []string `json:"fields"`
}

// parseExtractionParams 解析提取参数
func (a *ExtractionAgent) parseExtractionParams(input map[string]interface{}) (*ExtractionParams, error) {
	rawText, ok := input["raw_text"].(string)
	if !ok || rawText == "" {
		return nil, fmt.Errorf("raw_text is required and must be non-empty string")
	}

	language, _ := input["language"].(string)
	if language == "" {
		language = agentCommon.LanguageChinese
	}

	extractionMode, _ := input["extraction_mode"].(string)
	if extractionMode == "" {
		extractionMode = agentCommon.ExtractionModeStandard
	}

	var fields []string
	if fieldsInterface, ok := input["fields"]; ok {
		if fieldsArray, ok := fieldsInterface.([]interface{}); ok {
			for _, field := range fieldsArray {
				if fieldStr, ok := field.(string); ok {
					fields = append(fields, fieldStr)
				}
			}
		}
	}

	return &ExtractionParams{
		RawText:        rawText,
		Language:       language,
		ExtractionMode: extractionMode,
		Fields:         fields,
	}, nil
}

// extractData 提取数据
func (a *ExtractionAgent) extractData(params *ExtractionParams, a2aContext biddingModels.A2AContext) (*agentCommon.TenderData, *agentCommon.ProcessingMetadata, error) {
	startTime := time.Now()

	// 使用prompt管理器获取prompt
	template, err := a.promptManager.GetPromptTemplate("data_extraction", params.Language)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get prompt template: %v", err)
	}

	// 准备变量
	variables := map[string]interface{}{
		"raw_text": params.RawText,
	}

	// 渲染prompt
	systemPrompt, userPrompt, err := a.promptManager.RenderPrompt(template, variables)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to render prompt: %v", err)
	}

	// 构建LLM请求
	temperature := 0.1
	maxTokens := 2000
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model: "gpt-4",
		Messages: []common.LLMMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	// 调用LLM服务
	ctx := context.Background()
	response, err := a.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return nil, nil, fmt.Errorf("LLM request failed: %v", err)
	}

	// 解析JSON结果
	var tenderData agentCommon.TenderData
	if err := json.Unmarshal([]byte(response.Result), &tenderData); err != nil {
		return nil, nil, fmt.Errorf("failed to parse extraction result: %v", err)
	}

	// 构建处理元数据
	metadata := &agentCommon.ProcessingMetadata{
		ModelUsed:       llmRequest.Model,
		TokensConsumed:  response.TokenUsage.TotalTokens,
		ProcessingTime:  time.Since(startTime).Seconds(),
		ConfidenceScore: 0.9, // 简化处理
	}

	return &tenderData, metadata, nil
}

// validateExtractedData 验证提取的数据
func (a *ExtractionAgent) validateExtractedData(tenderData *agentCommon.TenderData) *agentCommon.ValidationResult {
	var missingFields []string
	var errors []string

	// 检查基本信息
	if tenderData.BasicInfo.ProjectName == "" {
		missingFields = append(missingFields, "project_name")
	}
	if tenderData.BasicInfo.Deadline == "" {
		missingFields = append(missingFields, "deadline")
	}
	if tenderData.BasicInfo.ContactInfo == "" {
		missingFields = append(missingFields, "contact_info")
	}

	// 检查机构信息
	if tenderData.Organization.PurchaserName == "" {
		missingFields = append(missingFields, "purchaser_name")
	}

	// 计算完整度分数
	completenessScore := agentCommon.CalculateCompleteness(tenderData)

	// 确定验证状态
	status := agentCommon.ValidationStatusValid
	if len(missingFields) > 3 {
		status = agentCommon.ValidationStatusInvalid
	} else if len(missingFields) > 0 {
		status = agentCommon.ValidationStatusPartial
	}

	return &agentCommon.ValidationResult{
		Status:            status,
		CompletenessScore: completenessScore,
		MissingFields:     missingFields,
		ValidationErrors:  errors,
	}
}
