package common

import "time"

// LLMMessage represents a single message in LLM conversation
type LLMMessage struct {
	Role    string `json:"role" binding:"required"`
	Content string `json:"content" binding:"required"`
}

// TokenUsage represents token consumption statistics
type TokenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// LLMRequest represents a request to LLM service
type LLMRequest struct {
	Messages    []LLMMessage           `json:"messages" binding:"required"`
	Config      map[string]interface{} `json:"config,omitempty"`
	Priority    int                    `json:"priority,omitempty"`
	Timeout     int                    `json:"timeout,omitempty"`
	RequestType string                 `json:"request_type,omitempty"`
}

// LLMResponse represents response from LLM service
type LLMResponse struct {
	Content    string     `json:"content"`
	TokenUsage TokenUsage `json:"token_usage"`
	Model      string     `json:"model"`
	Duration   int64      `json:"duration_ms"`
	Timestamp  time.Time  `json:"timestamp"`
}

// ErrorResponse represents standard error response format
type ErrorResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp,omitempty"`
} 