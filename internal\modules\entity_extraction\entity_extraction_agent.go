package entity_extraction

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	extractionModels "gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/services"
)

const (
	// Agent信息
	AgentID          = "entity-extraction-agent-001"
	AgentName        = "实体提取Agent"
	AgentDescription = "通用实体提取Agent，支持基于JSON Schema的结构化数据提取"
	AgentVersion     = "1.0.0"

	// 能力定义
	CapabilityExtractStructuredData = "extract_structured_data"
)

// EntityExtractionAgent 实体提取Agent
type EntityExtractionAgent struct {
	*core.BaseAgent

	// 核心服务组件
	schemaProcessor services.SchemaProcessor
	promptBuilder   services.PromptBuilder
	responseParser  services.ResponseParser
	inputValidator  services.InputValidator
	errorHandler    services.ErrorHandler
	llmProcessor    services.LLMProcessor

	// 配置
	config *AgentConfig
}

// AgentConfig Agent配置
type AgentConfig struct {
	LLMModel            string  `json:"llm_model"`
	Temperature         float64 `json:"temperature"`
	MaxTokens           int     `json:"max_tokens"`
	Timeout             int     `json:"timeout"`
	MaxRetries          int     `json:"max_retries"`
	ConfidenceThreshold float64 `json:"confidence_threshold"`
	EnableFallback      bool    `json:"enable_fallback"`
	AutoTrackTokens     bool    `json:"auto_track_tokens"`
}

// DefaultAgentConfig 默认配置
func DefaultAgentConfig() *AgentConfig {
	return &AgentConfig{
		LLMModel:            "deepseek-v3-250324",
		Temperature:         0.1,
		MaxTokens:           4000,
		Timeout:             30,
		MaxRetries:          3,
		ConfidenceThreshold: 0.7,
		EnableFallback:      true,
		AutoTrackTokens:     true,
	}
}

// NewEntityExtractionAgent 创建实体提取Agent
func NewEntityExtractionAgent(llmClient llm.LLMClient, config *AgentConfig) *EntityExtractionAgent {
	if config == nil {
		config = DefaultAgentConfig()
	}

	// 定义Agent能力
	capabilities := []models.AgentCapability{
		{
			Name:        CapabilityExtractStructuredData,
			Description: "通用结构化数据提取，基于JSON Schema定义提取符合指定结构的数据",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"input_text": map[string]interface{}{
						"type":        "string",
						"description": "输入文本内容（自然语言文本或结构化文本）",
					},
					"target_schema": map[string]interface{}{
						"type":        "object",
						"description": "目标JSON Schema定义，包含实体模型结构",
						"properties": map[string]interface{}{
							"name": map[string]interface{}{
								"type":        "string",
								"description": "模型名称",
							},
							"description": map[string]interface{}{
								"type":        "string",
								"description": "模型描述",
							},
							"type": map[string]interface{}{
								"type":        "string",
								"enum":        []string{"object"},
								"description": "数据类型，固定为object",
							},
							"properties": map[string]interface{}{
								"type":        "object",
								"description": "实体字段定义",
							},
							"required": map[string]interface{}{
								"type":        "array",
								"items":       map[string]interface{}{"type": "string"},
								"description": "必填字段列表",
							},
							"examples": map[string]interface{}{
								"type":        "array",
								"description": "示例数据列表",
							},
						},
						"required": []string{"name", "description", "type", "properties"},
					},
					"language": map[string]interface{}{
						"type":        "string",
						"enum":        []string{"zh", "en", "mixed"},
						"default":     "zh",
						"description": "输入文本语言",
					},
					"extraction_config": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"confidence_threshold": map[string]interface{}{
								"type":        "number",
								"default":     0.7,
								"description": "置信度阈值",
							},
							"max_retries": map[string]interface{}{
								"type":        "integer",
								"default":     3,
								"description": "最大重试次数",
							},
							"timeout": map[string]interface{}{
								"type":        "integer",
								"default":     30,
								"description": "超时时间(秒)",
							},
							"enable_fallback": map[string]interface{}{
								"type":        "boolean",
								"default":     true,
								"description": "是否启用降级策略",
							},
						},
					},
				},
				"required": []string{"input_text", "target_schema"},
			},
			OutputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"success": map[string]interface{}{
						"type":        "boolean",
						"description": "提取是否成功",
					},
					"data": map[string]interface{}{
						"type":        "object",
						"description": "提取的结构化数据，符合target_schema定义",
					},
					"confidence": map[string]interface{}{
						"type":        "number",
						"minimum":     0,
						"maximum":     1,
						"description": "提取结果的置信度",
					},
					"error": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"code":    map[string]interface{}{"type": "string", "description": "错误代码"},
							"message": map[string]interface{}{"type": "string", "description": "错误信息"},
							"details": map[string]interface{}{"type": "object", "description": "错误详情"},
						},
						"description": "错误信息（仅当success为false时存在）",
					},
					"metadata": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"model_used": map[string]interface{}{
								"type":        "string",
								"description": "使用的LLM模型",
							},
							"processing_time_ms": map[string]interface{}{
								"type":        "integer",
								"description": "处理时间（毫秒）",
							},
							"token_usage": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"input_tokens":  map[string]interface{}{"type": "integer"},
									"output_tokens": map[string]interface{}{"type": "integer"},
									"total_tokens":  map[string]interface{}{"type": "integer"},
								},
							},
							"language": map[string]interface{}{
								"type":        "string",
								"description": "检测到的语言",
							},
							"retry_count": map[string]interface{}{
								"type":        "integer",
								"description": "实际重试次数",
							},
							"processed_at": map[string]interface{}{
								"type":        "string",
								"format":      "date-time",
								"description": "处理时间",
							},
						},
					},
					"raw_output": map[string]interface{}{
						"type":        "string",
						"description": "LLM原始输出（仅在解析失败时提供）",
					},
				},
				"required": []string{"success", "confidence", "metadata"},
			},
			Required: []string{"input_text", "target_schema"},
		},
	}

	// 创建基础Agent
	baseAgent := core.NewBaseAgent(
		AgentID,
		models.AgentTypeCustom,
		AgentName,
		AgentDescription,
		capabilities,
		llmClient,
	)

	// 创建服务组件
	schemaValidator := services.NewJSONSchemaValidator()
	schemaProcessor := services.NewSchemaProcessor(schemaValidator)
	promptBuilder := services.NewPromptBuilder()
	responseParser := services.NewResponseParser(schemaValidator)
	inputValidator := services.NewInputValidator(schemaValidator)
	errorHandler := services.NewErrorHandler(promptBuilder, responseParser, inputValidator)

	// 创建LLM处理器配置
	llmConfig := &services.EntityExtractionLLMProcessorConfig{
		Model:           config.LLMModel,
		Temperature:     config.Temperature,
		MaxTokens:       config.MaxTokens,
		Timeout:         config.Timeout,
		MaxRetries:      config.MaxRetries,
		AutoTrackTokens: config.AutoTrackTokens,
	}

	llmProcessor := services.NewEntityExtractionLLMProcessor(
		llmClient,
		promptBuilder,
		responseParser,
		errorHandler,
		llmConfig,
	)

	return &EntityExtractionAgent{
		BaseAgent:       baseAgent,
		schemaProcessor: schemaProcessor,
		promptBuilder:   promptBuilder,
		responseParser:  responseParser,
		inputValidator:  inputValidator,
		errorHandler:    errorHandler,
		llmProcessor:    llmProcessor,
		config:          config,
	}
}

// Execute 执行Agent能力
func (ea *EntityExtractionAgent) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	start := time.Now()
	requestID := fmt.Sprintf("req_%d", time.Now().UnixNano())
	responseID := fmt.Sprintf("resp_%d", time.Now().UnixNano())

	// 检查能力
	if capability != CapabilityExtractStructuredData {
		return &models.AgentResponse{
			ID:        responseID,
			RequestID: requestID,
			Success:   false,
			Error:     fmt.Sprintf("不支持的能力: %s", capability),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}

	// 转换输入为提取请求
	extractionReq, err := ea.convertToExtractionRequest(input)
	if err != nil {
		return &models.AgentResponse{
			ID:        responseID,
			RequestID: requestID,
			Success:   false,
			Error:     fmt.Sprintf("输入参数转换失败: %v", err),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}

	// 验证输入
	validationResult := ea.inputValidator.ValidateInput(extractionReq)
	if !validationResult.Valid {
		return &models.AgentResponse{
			ID:        responseID,
			RequestID: requestID,
			Success:   false,
			Error:     fmt.Sprintf("输入验证失败: %v", ea.formatValidationErrors(validationResult.Errors)),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}

	// 处理提取请求
	result, err := ea.llmProcessor.ProcessExtraction(ctx, extractionReq)
	if err != nil {
		return &models.AgentResponse{
			ID:        responseID,
			RequestID: requestID,
			Success:   false,
			Error:     fmt.Sprintf("提取处理失败: %v", err),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}

	// 转换结果为Agent响应格式
	output := ea.convertExtractionResultToOutput(result)

	return &models.AgentResponse{
		ID:        responseID,
		RequestID: requestID,
		Success:   result.Success,
		Output:    output,
		Error:     ea.getErrorMessage(result),
		Duration:  time.Since(start).Milliseconds(),
		CreatedAt: time.Now(),
	}, nil
}

// convertToExtractionRequest 转换输入为提取请求
func (ea *EntityExtractionAgent) convertToExtractionRequest(input map[string]interface{}) (*extractionModels.ExtractionRequest, error) {
	// 序列化为JSON再反序列化，确保类型转换正确
	inputJSON, err := json.Marshal(input)
	if err != nil {
		return nil, fmt.Errorf("输入序列化失败: %v", err)
	}

	var req extractionModels.ExtractionRequest
	if err := json.Unmarshal(inputJSON, &req); err != nil {
		return nil, fmt.Errorf("输入反序列化失败: %v", err)
	}

	// 设置默认语言
	if req.Language == "" {
		req.Language = "zh"
	}

	// 设置默认配置
	if req.ExtractionConfig == nil {
		req.ExtractionConfig = &extractionModels.ExtractionConfig{
			ConfidenceThreshold: ea.config.ConfidenceThreshold,
			MaxRetries:          ea.config.MaxRetries,
			Timeout:             ea.config.Timeout,
			EnableFallback:      ea.config.EnableFallback,
		}
	}

	return &req, nil
}

// convertExtractionResultToOutput 转换提取结果为输出格式
func (ea *EntityExtractionAgent) convertExtractionResultToOutput(result *extractionModels.ExtractionResult) map[string]interface{} {
	output := map[string]interface{}{
		"success":    result.Success,
		"confidence": result.Confidence,
		"metadata": map[string]interface{}{
			"model_used":         result.Metadata.ModelUsed,
			"processing_time_ms": result.Metadata.ProcessingTime,
			"token_usage": map[string]interface{}{
				"input_tokens":  result.Metadata.TokenUsage.InputTokens,
				"output_tokens": result.Metadata.TokenUsage.OutputTokens,
				"total_tokens":  result.Metadata.TokenUsage.TotalTokens,
			},
			"language":     result.Metadata.Language,
			"retry_count":  result.Metadata.RetryCount,
			"processed_at": result.Metadata.ProcessedAt.Format(time.RFC3339),
		},
	}

	if result.Data != nil {
		output["data"] = result.Data
	}

	if result.Error != nil {
		output["error"] = map[string]interface{}{
			"code":    result.Error.Code,
			"message": result.Error.Message,
			"details": result.Error.Details,
		}
	}

	if result.RawOutput != "" {
		output["raw_output"] = result.RawOutput
	}

	return output
}

// getErrorMessage 获取错误信息
func (ea *EntityExtractionAgent) getErrorMessage(result *extractionModels.ExtractionResult) string {
	if result.Error != nil {
		return result.Error.Message
	}
	return ""
}

// formatValidationErrors 格式化验证错误
func (ea *EntityExtractionAgent) formatValidationErrors(errors []extractionModels.ValidationError) string {
	if len(errors) == 0 {
		return ""
	}

	var messages []string
	for _, err := range errors {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}

	return fmt.Sprintf("[%s]", fmt.Sprintf("%v", messages))
}

// Initialize 初始化Agent
func (ea *EntityExtractionAgent) Initialize(ctx context.Context, config map[string]interface{}) error {
	// 调用基础初始化
	if err := ea.BaseAgent.Initialize(ctx, config); err != nil {
		return err
	}

	// 更新Agent配置
	if err := ea.updateConfigFromMap(config); err != nil {
		ea.Logger.WithError(err).Warn("更新配置失败，使用默认配置")
	}

	ea.Logger.WithField("agent_id", ea.GetID()).Info("实体提取Agent初始化完成")
	return nil
}

// updateConfigFromMap 从map更新配置
func (ea *EntityExtractionAgent) updateConfigFromMap(configMap map[string]interface{}) error {
	if configMap == nil {
		return nil
	}

	// 序列化为JSON再反序列化，确保类型转换正确
	configJSON, err := json.Marshal(configMap)
	if err != nil {
		return fmt.Errorf("配置序列化失败: %v", err)
	}

	var newConfig AgentConfig
	if err := json.Unmarshal(configJSON, &newConfig); err != nil {
		return fmt.Errorf("配置反序列化失败: %v", err)
	}

	// 合并配置（只更新非零值）
	if newConfig.LLMModel != "" {
		ea.config.LLMModel = newConfig.LLMModel
	}
	if newConfig.Temperature != 0 {
		ea.config.Temperature = newConfig.Temperature
	}
	if newConfig.MaxTokens != 0 {
		ea.config.MaxTokens = newConfig.MaxTokens
	}
	if newConfig.Timeout != 0 {
		ea.config.Timeout = newConfig.Timeout
	}
	if newConfig.MaxRetries != 0 {
		ea.config.MaxRetries = newConfig.MaxRetries
	}
	if newConfig.ConfidenceThreshold != 0 {
		ea.config.ConfidenceThreshold = newConfig.ConfidenceThreshold
	}

	return nil
}

// GetConfig 获取Agent配置
func (ea *EntityExtractionAgent) GetConfig() *AgentConfig {
	return ea.config
}

// UpdateConfig 更新Agent配置
func (ea *EntityExtractionAgent) UpdateConfig(config *AgentConfig) {
	if config != nil {
		ea.config = config

		// 更新LLM处理器配置
		llmConfig := &services.EntityExtractionLLMProcessorConfig{
			Model:           config.LLMModel,
			Temperature:     config.Temperature,
			MaxTokens:       config.MaxTokens,
			Timeout:         config.Timeout,
			MaxRetries:      config.MaxRetries,
			AutoTrackTokens: config.AutoTrackTokens,
		}

		if processor, ok := ea.llmProcessor.(*services.EntityExtractionLLMProcessor); ok {
			processor.UpdateConfig(llmConfig)
		}
	}
}

// GetSchemaProcessor 获取Schema处理器（用于测试和调试）
func (ea *EntityExtractionAgent) GetSchemaProcessor() services.SchemaProcessor {
	return ea.schemaProcessor
}

// GetLLMProcessor 获取LLM处理器（用于测试和调试）
func (ea *EntityExtractionAgent) GetLLMProcessor() services.LLMProcessor {
	return ea.llmProcessor
}
