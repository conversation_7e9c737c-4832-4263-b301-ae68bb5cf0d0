package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/services/concurrent"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// TokenHandler handles token management API endpoints
type TokenHandler struct {
	tokenService *concurrent.TokenService
}

// NewTokenHandler creates a new token handler instance
func NewTokenHandler(tokenService *concurrent.TokenService) *TokenHandler {
	return &TokenHandler{
		tokenService: tokenService,
	}
}

// LogTokenConsumption logs token consumption
// @Summary Log token consumption
// @Description Log token consumption for a user
// @Tags tokens
// @Accept json
// @Produce json
// @Param consumption body models.TokenConsumptionRequest true "Token consumption request"
// @Success 201 {object} models.TokenConsumption
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/tokens/consumption [post]
func (h *TokenHandler) LogTokenConsumption(c *gin.Context) {
	var req models.TokenConsumptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Errorf("Invalid token consumption request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": err.Error(),
		})
		return
	}

	consumption, err := h.tokenService.LogTokenConsumption(&req)
	if err != nil {
		utils.Log.Errorf("Failed to log token consumption: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to log token consumption",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, consumption)
}

// CheckTokenLimits checks token limits for a user
// @Summary Check token limits
// @Description Check if user can consume requested tokens
// @Tags tokens
// @Accept json
// @Produce json
// @Param user_id path string true "User ID"
// @Param tokens query int true "Requested tokens"
// @Success 200 {object} models.TokenLimitCheck
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/tokens/limits/{user_id} [get]
func (h *TokenHandler) CheckTokenLimits(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "user_id is required",
		})
		return
	}

	tokensStr := c.Query("tokens")
	if tokensStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "tokens parameter is required",
		})
		return
	}

	tokens, err := strconv.Atoi(tokensStr)
	if err != nil || tokens < 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "tokens must be a positive integer",
		})
		return
	}

	limitCheck, err := h.tokenService.CheckTokenLimits(userID, tokens)
	if err != nil {
		utils.Log.Errorf("Failed to check token limits: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to check token limits",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, limitCheck)
}

// GetTokenUsageStats returns token usage statistics for a user
// @Summary Get user token usage statistics
// @Description Get comprehensive token usage statistics for a user
// @Tags tokens
// @Accept json
// @Produce json
// @Param user_id path string true "User ID"
// @Success 200 {object} models.TokenUsageStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/tokens/stats/user/{user_id} [get]
func (h *TokenHandler) GetTokenUsageStats(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "user_id is required",
		})
		return
	}

	stats, err := h.tokenService.GetTokenUsageStats(userID)
	if err != nil {
		utils.Log.Errorf("Failed to get token usage stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get token usage statistics",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetCompanyTokenStats returns token usage statistics for a company
// @Summary Get company token usage statistics
// @Description Get comprehensive token usage statistics for a company
// @Tags tokens
// @Accept json
// @Produce json
// @Param company_id path string true "Company ID"
// @Success 200 {object} models.CompanyTokenStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/tokens/stats/company/{company_id} [get]
func (h *TokenHandler) GetCompanyTokenStats(c *gin.Context) {
	companyID := c.Param("company_id")
	if companyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "company_id is required",
		})
		return
	}

	stats, err := h.tokenService.GetCompanyTokenStats(companyID)
	if err != nil {
		utils.Log.Errorf("Failed to get company token stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get company token statistics",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// CreateUser creates a new user
// @Summary Create a new user
// @Description Create a new user in the system
// @Tags users
// @Accept json
// @Produce json
// @Param user body models.User true "User data"
// @Success 201 {object} models.User
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/users [post]
func (h *TokenHandler) CreateUser(c *gin.Context) {
	var user models.User
	if err := c.ShouldBindJSON(&user); err != nil {
		utils.Log.Errorf("Invalid user creation request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": err.Error(),
		})
		return
	}

	// Validate required fields
	if user.UserID == "" || user.Username == "" || user.CompanyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "user_id, username, and company_id are required",
		})
		return
	}

	// Set default subscription type if not provided
	if user.SubscriptionType == "" {
		user.SubscriptionType = models.SubscriptionFree
	}

	err := h.tokenService.CreateUser(&user)
	if err != nil {
		utils.Log.Errorf("Failed to create user: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create user",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, user)
}

// CreateCompany creates a new company
// @Summary Create a new company
// @Description Create a new company in the system
// @Tags companies
// @Accept json
// @Produce json
// @Param company body models.Company true "Company data"
// @Success 201 {object} models.Company
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/companies [post]
func (h *TokenHandler) CreateCompany(c *gin.Context) {
	var company models.Company
	if err := c.ShouldBindJSON(&company); err != nil {
		utils.Log.Errorf("Invalid company creation request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": err.Error(),
		})
		return
	}

	// Validate required fields
	if company.CompanyID == "" || company.CompanyName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "company_id and company_name are required",
		})
		return
	}

	// Set default subscription type if not provided
	if company.SubscriptionType == "" {
		company.SubscriptionType = models.SubscriptionFree
	}

	err := h.tokenService.CreateCompany(&company)
	if err != nil {
		utils.Log.Errorf("Failed to create company: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create company",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, company)
}

// GetTokenConsumptionHistory returns token consumption history for a user
// @Summary Get token consumption history
// @Description Get paginated token consumption history for a user
// @Tags tokens
// @Accept json
// @Produce json
// @Param user_id path string true "User ID"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 20, max: 100)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/tokens/history/{user_id} [get]
func (h *TokenHandler) GetTokenConsumptionHistory(c *gin.Context) {
	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"message": "user_id is required",
		})
		return
	}

	// Parse pagination parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	limit := 20
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	offset := (page - 1) * limit

	history, total, err := h.tokenService.GetTokenConsumptionHistory(userID, limit, offset)
	if err != nil {
		utils.Log.Errorf("Failed to get token consumption history: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get token consumption history",
			"message": err.Error(),
		})
		return
	}

	totalPages := (total + limit - 1) / limit

	c.JSON(http.StatusOK, gin.H{
		"data": history,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}
