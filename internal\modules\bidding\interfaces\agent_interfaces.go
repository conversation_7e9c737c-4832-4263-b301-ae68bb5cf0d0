package interfaces

import (
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
)

// Agent 定义Agent的通用接口
type Agent interface {
	ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error)
	GetAgentCard() biddingModels.AgentCard
	HealthCheck() biddingModels.HealthStatus
	GetID() string
}

// AgentRegistry 定义Agent注册表的接口
type AgentRegistry interface {
	RegisterAgent(agent Agent) error
	RegisterAgentWithInstance(agent Agent) error
	GetAgent(agentID string) (Agent, error)
	ListAgents() []biddingModels.RegisteredAgent
	FindAgentsBySkill(skillID string) []biddingModels.RegisteredAgent
	GetAgentStats() map[string]interface{}
}
