# etcd配置中心部署指南

## 概述

本目录包含在k3s集群中部署单节点etcd的所有配置文件和脚本。etcd将作为taskd微服务的配置中心，存储共享的配置数据。

## 目录结构

```
k8s/etcd/
├── namespace.yaml     # etcd命名空间
├── pvc.yaml          # 持久化存储声明
├── configmap.yaml    # etcd配置参数
├── secret.yaml       # etcd敏感配置（可选）
├── statefulset.yaml  # etcd StatefulSet
├── service.yaml      # etcd服务
├── deploy-etcd.sh    # 自动部署脚本
├── init-config.sh    # 配置初始化脚本
└── README.md         # 本文档
```

## 快速部署

### 方式1：自动部署（推荐）

```bash
# 进入etcd目录
cd k8s/etcd/

# 运行部署脚本
./deploy-etcd.sh

# 初始化配置数据
./init-config.sh
```

### 方式2：手动部署

```bash
# 1. 创建命名空间
kubectl apply -f namespace.yaml

# 2. 创建PVC
kubectl apply -f pvc.yaml

# 3. 创建ConfigMap
kubectl apply -f configmap.yaml

# 4. 创建Secret（可选）
kubectl apply -f secret.yaml

# 5. 创建Service
kubectl apply -f service.yaml

# 6. 创建StatefulSet
kubectl apply -f statefulset.yaml

# 7. 等待Pod启动
kubectl wait --for=condition=ready pod -l app=etcd -n etcd --timeout=300s

# 8. 初始化配置数据
./init-config.sh
```

## 验证部署

```bash
# 检查Pod状态
kubectl get pods -n etcd

# 检查etcd健康状态
kubectl exec -n etcd etcd-0 -- etcdctl endpoint health

# 测试读写功能
kubectl exec -n etcd etcd-0 -- etcdctl put /test/hello "world"
kubectl exec -n etcd etcd-0 -- etcdctl get /test/hello
```

## 连接信息

- **集群内访问地址**: `etcd-service.etcd.svc.cluster.local:2379`
- **外部访问地址（NodePort）**: `<NODE_IP>:32379`
- **命名空间**: `etcd`

## taskd集成配置

在taskd的ConfigMap或环境变量中添加：

```yaml
# 启用etcd支持
ETCD_ENABLED: "true"
ETCD_ENDPOINTS: "etcd-service.etcd.svc.cluster.local:2379"

# 可选：安全配置
# ETCD_USERNAME: ""
# ETCD_PASSWORD: ""
# ETCD_TLS_ENABLED: "false"
```

## 配置说明

### 资源配置

- **CPU**: 500m (requests) / 1000m (limits)
- **内存**: 512Mi (requests) / 1Gi (limits)  
- **存储**: 10Gi PVC
- **副本数**: 1（单节点）

### 安全配置

当前部署为**非安全模式**，适合开发和测试环境。生产环境建议：

1. 启用TLS加密
2. 配置身份验证
3. 限制网络访问

### 性能参数

- **心跳间隔**: 100ms
- **选举超时**: 1000ms
- **快照保留**: 5个
- **WAL保留**: 5个
- **后端配额**: 2GB

## 常用操作

```bash
# 查看etcd状态
kubectl get all -n etcd

# 查看etcd日志
kubectl logs -n etcd -l app=etcd -f

# 进入etcd容器
kubectl exec -it -n etcd etcd-0 -- sh

# 查看集群成员
kubectl exec -n etcd etcd-0 -- etcdctl member list

# 查看所有键
kubectl exec -n etcd etcd-0 -- etcdctl get "" --prefix --keys-only

# 备份etcd数据
kubectl exec -n etcd etcd-0 -- etcdctl snapshot save /tmp/etcd-backup.db

# 检查快照状态
kubectl exec -n etcd etcd-0 -- etcdctl snapshot status /tmp/etcd-backup.db -w table
```

## 故障排除

### Pod无法启动

1. 检查PVC是否已绑定：`kubectl get pvc -n etcd`
2. 检查资源是否足够：`kubectl describe pod -n etcd etcd-0`
3. 查看容器日志：`kubectl logs -n etcd etcd-0`

### 健康检查失败

```bash
# 检查端口监听
kubectl exec -n etcd etcd-0 -- netstat -ln | grep 2379

# 检查etcd进程
kubectl exec -n etcd etcd-0 -- ps aux | grep etcd

# 手动健康检查
kubectl exec -n etcd etcd-0 -- etcdctl endpoint health --endpoints=localhost:2379
```

### 数据持久化问题

```bash
# 检查挂载点
kubectl exec -n etcd etcd-0 -- df -h /etcd-data

# 检查目录权限
kubectl exec -n etcd etcd-0 -- ls -la /etcd-data
```

## 卸载etcd

```bash
# 删除所有资源
kubectl delete -f statefulset.yaml
kubectl delete -f service.yaml
kubectl delete -f secret.yaml
kubectl delete -f configmap.yaml
kubectl delete -f pvc.yaml
kubectl delete -f namespace.yaml

# 或者直接删除命名空间（会删除所有资源）
kubectl delete namespace etcd
```

## 监控和备份

### 监控指标

etcd暴露了丰富的Prometheus指标，可以通过以下端点访问：
- `http://etcd-service.etcd.svc.cluster.local:2379/metrics`

### 备份策略

建议设置定期备份任务：

```bash
# 创建备份脚本
cat > backup-etcd.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
kubectl exec -n etcd etcd-0 -- etcdctl snapshot save /tmp/backup_${DATE}.db
kubectl cp etcd/etcd-0:/tmp/backup_${DATE}.db ./backup_${DATE}.db
EOF

# 设置定时任务
0 2 * * * /path/to/backup-etcd.sh
```

## 注意事项

1. **单点故障**: 当前为单节点部署，etcd故障会影响所有依赖的服务
2. **数据一致性**: 多个服务同时写入同一配置时需要考虑锁机制
3. **网络延迟**: 服务启动时必须能够访问etcd
4. **备份重要性**: 定期备份etcd数据，避免配置丢失
5. **监控必要性**: 监控etcd的健康状态和性能指标