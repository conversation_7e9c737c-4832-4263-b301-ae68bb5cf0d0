# TaskD 超轻量测试环境 - 最小化镜像
# 使用 distroless 基础镜像，极致优化体积

# ================================
# 第一阶段：构建环境
# ================================
FROM python:3.11-alpine AS builder

# 构建环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

# 安装最小构建依赖
RUN apk add --no-cache --virtual .build-deps gcc musl-dev libffi-dev

WORKDIR /build

# 创建超精简的依赖列表
RUN echo "requests==2.31.0" > requirements.txt && \
    echo "pytest==7.4.0" >> requirements.txt && \
    echo "faker==19.6.2" >> requirements.txt && \
    echo "python-dotenv==1.0.0" >> requirements.txt

# 创建独立的Python环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 清理不必要的文件
RUN find /opt/venv -name "*.pyc" -delete \
    && find /opt/venv -name "__pycache__" -type d -exec rm -rf {} + \
    && find /opt/venv -name "*.pyo" -delete \
    && find /opt/venv -name "*.pyd" -delete \
    && find /opt/venv -name "*.so" -exec strip {} \; \
    && rm -rf /opt/venv/share /opt/venv/lib/python*/site-packages/pip* \
    && rm -rf /opt/venv/lib/python*/site-packages/setuptools*

# ================================
# 第二阶段：最小运行环境
# ================================
FROM gcr.io/distroless/python3:latest AS runtime

# 复制Python环境
COPY --from=builder /opt/venv /opt/venv

# 设置环境变量
ENV PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app" \
    PYTHONUNBUFFERED=1

WORKDIR /app

# 复制核心文件
COPY docker/test/docker-entrypoint-slim.sh ./entrypoint.sh
COPY docker/test/.env.docker ./
COPY test/conftest.py ./test/
COPY test/test_*.py ./test/

# 非root用户
USER 65534

ENTRYPOINT ["./entrypoint.sh"]
CMD ["test"]