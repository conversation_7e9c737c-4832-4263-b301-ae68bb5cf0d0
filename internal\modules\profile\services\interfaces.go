package services

import (
	"gitlab.com/specific-ai/taskd/internal/modules/profile/models"
)

// ProfileServiceInterface 企业画像生成服务接口
type ProfileServiceInterface interface {
	// GenerateCompanyProfile 生成企业画像（AI生成服务）
	GenerateCompanyProfile(req *models.ProfileRequest) (*models.ProfileResponse, error)
}

// StorageServiceInterface 企业画像存储服务接口
type StorageServiceInterface interface {
	// SaveProfile 保存企业画像到数据库
	SaveProfile(profile *models.ProfileResponse, req *models.ProfileRequest) error
	
	// GetProfileByUserAndCompany 根据用户ID和企业名称获取企业画像
	GetProfileByUserAndCompany(userID, companyName string) (*models.ProfileResponse, error)
	
	// GetUserProfiles 获取用户的所有企业画像
	GetUserProfiles(userID string, limit, offset int) ([]*models.ProfileResponse, error)
	
	// DeleteProfile 删除企业画像
	DeleteProfile(userID, companyName string) error
	
	// GetProfileGenerationHistory 获取企业画像生成历史
	GetProfileGenerationHistory(userID string, limit, offset int) ([]map[string]interface{}, error)
}

// SchemaServiceInterface JSON Schema管理服务接口
type SchemaServiceInterface interface {
	// GetCompanyProfileSchema 获取企业画像的JSON Schema
	GetCompanyProfileSchema() map[string]interface{}
	
	// ValidateProfileData 验证企业画像数据结构
	ValidateProfileData(data interface{}) error
}