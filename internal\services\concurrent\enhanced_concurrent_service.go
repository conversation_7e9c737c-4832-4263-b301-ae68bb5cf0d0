package concurrent

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/services/infrastructure"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// EnhancedConcurrentService 增强的并发控制服务
type EnhancedConcurrentService struct {
	config       *models.ConcurrentConfig
	tokenService *TokenService
	llmService   services.LLMService
	queueManager infrastructure.PulsarQueueManager

	// 内存队列作为Pulsar的补充
	priorityQueues map[models.RequestPriority]chan *models.LLMRequestMessage
	retryQueue     chan *models.LLMRequestMessage

	// 统计和监控
	stats      *models.QueueStats
	statsMutex sync.RWMutex

	// 并发控制
	semaphore      chan struct{}
	activeRequests map[string]*models.LLMRequestMessage
	requestsMutex  sync.RWMutex

	// 生命周期控制
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	started bool
	mu      sync.Mutex
}

// NewEnhancedConcurrentService 创建增强的并发控制服务
func NewEnhancedConcurrentService(
	config *models.ConcurrentConfig,
	tokenService *TokenService,
	llmService services.LLMService,
	queueManager infrastructure.PulsarQueueManager,
) *EnhancedConcurrentService {
	if config == nil {
		config = models.DefaultConcurrentConfig()
	}

	service := &EnhancedConcurrentService{
		config:         config,
		tokenService:   tokenService,
		llmService:     llmService,
		queueManager:   queueManager,
		priorityQueues: make(map[models.RequestPriority]chan *models.LLMRequestMessage),
		retryQueue:     make(chan *models.LLMRequestMessage, config.RetryQueueSize),
		semaphore:      make(chan struct{}, config.MaxConcurrentRequests),
		activeRequests: make(map[string]*models.LLMRequestMessage),
		stats: &models.QueueStats{
			PriorityStats:     make(map[models.RequestPriority]int),
			UserPriorityStats: make(map[models.UserPriority]int),
		},
	}

	// 初始化优先级队列
	service.priorityQueues[models.PriorityHigh] = make(chan *models.LLMRequestMessage, config.MainQueueSize/3)
	service.priorityQueues[models.PriorityMedium] = make(chan *models.LLMRequestMessage, config.MainQueueSize/3)
	service.priorityQueues[models.PriorityLow] = make(chan *models.LLMRequestMessage, config.MainQueueSize/3)

	return service
}

// Start 启动服务
func (s *EnhancedConcurrentService) Start(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.started {
		return fmt.Errorf("service already started")
	}

	s.ctx, s.cancel = context.WithCancel(ctx)
	s.started = true

	// 启动工作协程
	for i := 0; i < s.config.WorkerCount; i++ {
		s.wg.Add(1)
		go s.worker(i)
	}

	// 启动重试处理协程
	s.wg.Add(1)
	go s.retryWorker()

	// 启动统计协程
	s.wg.Add(1)
	go s.statsWorker()

	// 启动Pulsar消费者（如果配置了）
	if s.queueManager != nil {
		handler := &ConcurrentRequestHandler{service: s}
		if err := s.queueManager.StartConsumer(s.ctx, handler); err != nil {
			utils.Log.Errorf("Failed to start Pulsar consumer: %v", err)
		}
		if err := s.queueManager.StartRetryConsumer(s.ctx, handler); err != nil {
			utils.Log.Errorf("Failed to start Pulsar retry consumer: %v", err)
		}
	}

	utils.Log.Infof("Enhanced concurrent service started with %d workers", s.config.WorkerCount)
	return nil
}

// Stop 停止服务
func (s *EnhancedConcurrentService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.started {
		return nil
	}

	s.cancel()
	s.wg.Wait()

	if s.queueManager != nil {
		s.queueManager.Close()
	}

	s.started = false
	utils.Log.Info("Enhanced concurrent service stopped")
	return nil
}

// SubmitRequest 提交请求
func (s *EnhancedConcurrentService) SubmitRequest(ctx context.Context, req *models.LLMRequestMessage) (*models.LLMResponseMessage, error) {
	// 生成请求ID
	if req.ID == "" {
		req.ID = uuid.New().String()
	}

	// 设置默认值
	if req.MaxWaitTime == 0 {
		req.MaxWaitTime = s.config.MaxWaitTime
	}
	if req.MaxRetries == 0 {
		req.MaxRetries = s.config.MaxRetries
	}
	req.CreatedAt = time.Now()

	// 1. Token验证
	if err := s.validateTokens(req); err != nil {
		return nil, fmt.Errorf("token validation failed: %w", err)
	}

	// 2. 确定用户优先级
	req.UserPriority = s.GetUserPriority(req.UserID)
	req.Priority = models.GetPriorityFromUser(req.UserPriority)

	// 3. 检查是否应该拒绝请求
	if s.shouldRejectRequest(req) {
		return nil, fmt.Errorf("request rejected: status 429 - too many requests")
	}

	// 4. 创建响应通道
	req.ResponseChan = make(chan *models.LLMResponseMessage, 1)
	req.Context = ctx

	// 5. 优先尝试Pulsar队列
	if s.queueManager != nil {
		err := s.queueManager.SendRequest(ctx, req)
		if err == nil {
			// 成功发送到Pulsar，等待响应
			return s.waitForResponse(req)
		}
		utils.Log.Warnf("Failed to send request to Pulsar, falling back to memory queue: %v", err)
	}

	// 6. 回退到内存队列
	if err := s.enqueueToMemory(req); err != nil {
		return nil, fmt.Errorf("failed to enqueue request: %w", err)
	}

	// 7. 等待响应
	return s.waitForResponse(req)
}

// validateTokens 验证Token限额
func (s *EnhancedConcurrentService) validateTokens(req *models.LLMRequestMessage) error {
	if s.tokenService == nil || !req.Params.AutoTrackTokens {
		return nil
	}

	if req.Params.UserContext == nil {
		return fmt.Errorf("user context required for token tracking")
	}

	estimatedTokens := s.estimateTokens(req.Params.Messages)
	limitCheck, err := s.tokenService.CheckTokenLimits(req.UserID, estimatedTokens+500)
	if err != nil {
		return fmt.Errorf("token limit check failed: %w", err)
	}

	if !limitCheck.CanProceed {
		return fmt.Errorf("token limit exceeded: %s", limitCheck.Reason)
	}

	return nil
}

// shouldRejectRequest 判断是否应该拒绝请求
func (s *EnhancedConcurrentService) shouldRejectRequest(req *models.LLMRequestMessage) bool {
	// 检查并发限制
	if len(s.semaphore) >= s.config.MaxConcurrentRequests {
		// 如果是免费用户且配置了拒绝策略
		if s.config.RejectFreeUserWhenBusy && req.UserPriority == models.PriorityFree {
			return true
		}
	}

	// 检查队列容量
	currentQueueSize := s.getCurrentQueueSize()
	return s.config.ShouldRejectRequest(req.UserPriority, currentQueueSize)
}

// enqueueToMemory 加入内存队列
func (s *EnhancedConcurrentService) enqueueToMemory(req *models.LLMRequestMessage) error {
	queue, exists := s.priorityQueues[req.Priority]
	if !exists {
		return fmt.Errorf("unknown priority: %v", req.Priority)
	}

	select {
	case queue <- req:
		s.updateStats("enqueued", req)
		utils.Log.Debugf("Request %s enqueued to memory with priority %v", req.ID, req.Priority)
		return nil
	default:
		return fmt.Errorf("memory queue full for priority %v", req.Priority)
	}
}

// waitForResponse 等待响应
func (s *EnhancedConcurrentService) waitForResponse(req *models.LLMRequestMessage) (*models.LLMResponseMessage, error) {
	select {
	case response := <-req.ResponseChan:
		return response, nil
	case <-time.After(req.MaxWaitTime):
		return nil, fmt.Errorf("request timeout after %v", req.MaxWaitTime)
	case <-req.Context.Done():
		return nil, req.Context.Err()
	}
}

// worker 工作协程
func (s *EnhancedConcurrentService) worker(workerID int) {
	defer s.wg.Done()

	utils.Log.Debugf("Worker %d started", workerID)

	for {
		select {
		case <-s.ctx.Done():
			utils.Log.Debugf("Worker %d stopped", workerID)
			return
		default:
			req := s.dequeueFromMemory()
			if req == nil {
				time.Sleep(10 * time.Millisecond)
				continue
			}

			s.processRequest(req, workerID)
		}
	}
}

// dequeueFromMemory 从内存队列取出请求
func (s *EnhancedConcurrentService) dequeueFromMemory() *models.LLMRequestMessage {
	// 按优先级顺序处理
	priorities := []models.RequestPriority{
		models.PriorityHigh,
		models.PriorityMedium,
		models.PriorityLow,
	}

	for _, priority := range priorities {
		queue := s.priorityQueues[priority]
		select {
		case req := <-queue:
			return req
		default:
		}
	}

	return nil
}

// processRequest 处理请求
func (s *EnhancedConcurrentService) processRequest(req *models.LLMRequestMessage, workerID int) {
	// 获取并发信号量
	select {
	case s.semaphore <- struct{}{}:
	case <-s.ctx.Done():
		return
	case <-req.Context.Done():
		s.sendResponse(req, &models.LLMResponseMessage{
			ID:      req.ID,
			Success: false,
			Error:   "request cancelled",
		})
		return
	}
	defer func() { <-s.semaphore }()

	// 记录活跃请求
	s.requestsMutex.Lock()
	s.activeRequests[req.ID] = req
	s.requestsMutex.Unlock()

	defer func() {
		s.requestsMutex.Lock()
		delete(s.activeRequests, req.ID)
		s.requestsMutex.Unlock()
	}()

	startTime := time.Now()
	utils.Log.Debugf("Worker %d processing request %s", workerID, req.ID)

	s.updateStats("processing", req)

	// 调用LLM服务
	response, err := s.llmService.ProcessRequest(req.Context, req.Params)
	duration := time.Since(startTime)

	if err != nil {
		// 检查是否需要重试
		if s.shouldRetryError(err) && req.RetryCount < req.MaxRetries {
			utils.Log.Warnf("Request %s failed with retryable error: %v, scheduling retry", req.ID, err)
			s.scheduleRetry(req)
			return
		}

		// 不可重试的错误
		response = &models.LLMResponseMessage{
			ID:         req.ID,
			Success:    false,
			Error:      err.Error(),
			Duration:   duration,
			RetryCount: req.RetryCount,
		}
		s.updateStats("error", req)
	} else {
		response.Duration = duration
		response.RetryCount = req.RetryCount
		s.updateStats("success", req)
	}

	s.sendResponse(req, response)
}

// shouldRetryError 判断是否应该重试
func (s *EnhancedConcurrentService) shouldRetryError(err error) bool {
	errStr := strings.ToLower(err.Error())
	retryableErrors := []string{
		"429", "timeout", "rate limit", "too many requests",
		"service unavailable", "internal server error", "502", "503", "504",
	}

	for _, retryable := range retryableErrors {
		if strings.Contains(errStr, retryable) {
			return true
		}
	}

	return false
}

// scheduleRetry 安排重试
func (s *EnhancedConcurrentService) scheduleRetry(req *models.LLMRequestMessage) {
	req.RetryCount++

	// 计算退避延迟
	delay := s.calculateRetryDelay(req.RetryCount)

	// 优先使用Pulsar重试队列
	if s.queueManager != nil {
		err := s.queueManager.SendToRetryQueue(req.Context, req, delay)
		if err == nil {
			utils.Log.Debugf("Request %s sent to Pulsar retry queue", req.ID)
			return
		}
		utils.Log.Warnf("Failed to send to Pulsar retry queue: %v", err)
	}

	// 回退到内存重试队列
	go func() {
		select {
		case <-time.After(delay):
			select {
			case s.retryQueue <- req:
				utils.Log.Debugf("Request %s scheduled for retry %d after %v delay",
					req.ID, req.RetryCount, delay)
			default:
				s.sendResponse(req, &models.LLMResponseMessage{
					ID:      req.ID,
					Success: false,
					Error:   "retry queue full",
				})
			}
		case <-s.ctx.Done():
			return
		}
	}()
}

// calculateRetryDelay 计算重试延迟
func (s *EnhancedConcurrentService) calculateRetryDelay(retryCount int) time.Duration {
	delay := s.config.RetryBaseDelay * time.Duration(1<<retryCount)
	maxDelay := 5 * time.Minute
	if delay > maxDelay {
		delay = maxDelay
	}
	return delay
}

// retryWorker 重试工作协程
func (s *EnhancedConcurrentService) retryWorker() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case req := <-s.retryQueue:
			if err := s.enqueueToMemory(req); err != nil {
				s.sendResponse(req, &models.LLMResponseMessage{
					ID:      req.ID,
					Success: false,
					Error:   fmt.Sprintf("failed to requeue retry request: %v", err),
				})
			}
			s.updateStats("retry", req)
		}
	}
}

// sendResponse 发送响应
func (s *EnhancedConcurrentService) sendResponse(req *models.LLMRequestMessage, response *models.LLMResponseMessage) {
	if req.ResponseChan == nil {
		return
	}

	select {
	case req.ResponseChan <- response:
	default:
		utils.Log.Warnf("Failed to send response for request %s: channel blocked", req.ID)
	}
}

// GetUserPriority 获取用户优先级
func (s *EnhancedConcurrentService) GetUserPriority(userID string) models.UserPriority {
	// TODO: 从数据库查询用户的订阅类型
	// 这里简化处理
	if strings.Contains(userID, "vip") || strings.Contains(userID, "premium") {
		return models.PriorityVIP
	}
	if strings.Contains(userID, "free") || strings.Contains(userID, "trial") {
		return models.PriorityFree
	}
	return models.PriorityNormal
}

// getCurrentQueueSize 获取当前队列大小
func (s *EnhancedConcurrentService) getCurrentQueueSize() int {
	total := 0
	for _, queue := range s.priorityQueues {
		total += len(queue)
	}
	return total
}

// updateStats 更新统计信息
func (s *EnhancedConcurrentService) updateStats(action string, req *models.LLMRequestMessage) {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	switch action {
	case "enqueued":
		s.stats.PriorityStats[req.Priority]++
		s.stats.UserPriorityStats[req.UserPriority]++
	case "processing":
		s.stats.ProcessingCount++
	case "success":
		s.stats.ProcessingCount--
		s.stats.TotalProcessed++
	case "error":
		s.stats.ProcessingCount--
		s.stats.TotalErrors++
	case "retry":
		s.stats.TotalRetries++
	}
}

// GetStats 获取统计信息
func (s *EnhancedConcurrentService) GetStats() *models.QueueStats {
	s.statsMutex.RLock()
	defer s.statsMutex.RUnlock()

	stats := *s.stats
	stats.MainQueueSize = s.getCurrentQueueSize()
	stats.RetryQueueSize = len(s.retryQueue)
	stats.ActiveWorkers = s.config.WorkerCount

	// 添加Pulsar统计
	if s.queueManager != nil {
		pulsarStats := s.queueManager.GetQueueStats()
		stats.TotalProcessed += pulsarStats.TotalProcessed
		stats.TotalErrors += pulsarStats.TotalErrors
		stats.TotalRetries += pulsarStats.TotalRetries
	}

	return &stats
}

// statsWorker 统计工作协程
func (s *EnhancedConcurrentService) statsWorker() {
	defer s.wg.Done()

	ticker := time.NewTicker(10 * time.Minute) // 改为10分钟打印一次
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			stats := s.GetStats()
			utils.Log.Infof("Concurrent service stats: queue=%d, retry=%d, processing=%d, total=%d, errors=%d",
				stats.MainQueueSize, stats.RetryQueueSize, stats.ProcessingCount,
				stats.TotalProcessed, stats.TotalErrors)
		}
	}
}

// estimateTokens 估算token数量
func (s *EnhancedConcurrentService) estimateTokens(messages []common.LLMMessage) int {
	totalChars := 0
	for _, msg := range messages {
		totalChars += len(msg.Content)
	}
	return totalChars / 4
}

// ConcurrentRequestHandler Pulsar请求处理器
type ConcurrentRequestHandler struct {
	service *EnhancedConcurrentService
}

// HandleRequest 实现RequestHandler接口
func (h *ConcurrentRequestHandler) HandleRequest(ctx context.Context, req *models.LLMRequestMessage) error {
	// 直接处理请求，不经过队列
	response, err := h.service.llmService.ProcessRequest(ctx, req.Params)
	if err != nil {
		return err
	}

	// 发送响应
	if req.ResponseChan != nil {
		select {
		case req.ResponseChan <- response:
		case <-ctx.Done():
			return ctx.Err()
		default:
			utils.Log.Warnf("Response channel blocked for request %s", req.ID)
		}
	}

	return nil
}
