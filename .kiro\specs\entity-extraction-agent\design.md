# 实体提取Agent设计文档

## 概述

通用实体提取Agent是一个基于TaskD Agent架构的智能实体提取系统，专门用于从自然语言文本和结构化文本中提取符合业务模型的结构化数据。该Agent主要服务于商机新闻和招投标两个业务场景，通过LLM调用实现高精度的实体提取，并返回符合预定义模型结构的JSON数据。

## 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                Entity Extraction Agent                     │
├─────────────────────────────────────────────────────────────┤
│  Core Processing Layer                                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │ Schema Validator│ │ Prompt Builder  │ │ Response Parser ││
│  │ - JSON Schema   │ │ - 动态提示词生成 │ │ - JSON解析      ││
│  │ - 输入验证      │ │ - 多语言支持    │ │ - 结构验证      ││
│  │ - 格式检查      │ │ - 示例注入      │ │ - 置信度评估    ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  Error Handling                                            │
│  ┌─────────────────┐ ┌─────────────────┐                   │
│  │ Input Validator │ │ Error Handler   │                   │
│  │ - 文本预处理     │ │ - 提取失败处理   │                   │
│  │ - 长度检查      │ │ - 降级策略      │                   │
│  │ - 格式验证      │ │ - 错误分类      │                   │
│  └─────────────────┘ └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  TaskD Agent Framework                                     │
│  │ Agent Interface │ LLM Client │ Token Manager │ Registry │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. EntityExtractionAgent (主Agent类)

```go
type EntityExtractionAgent struct {
    *core.BaseAgent
    schemaValidator  *JSONSchemaValidator
    promptBuilder    *PromptBuilder
    responseParser   *ResponseParser
}
```

**支持的能力 (Capabilities):**

1. **extract_structured_data** - 通用结构化数据提取
   - 输入：文本内容、JSON Schema模型定义、语言类型
   - 输出：符合指定模型结构的JSON数据
   - 说明：这是唯一的核心能力，支持所有业务场景

#### 2. 业务模型注册表 (Business Model Registry)

**BusinessModelRegistry** - 业务模型注册表
```go
type BusinessModelRegistry struct {
    models map[string]*BusinessModel
    schemas map[string]*ModelSchema
}

type BusinessModel struct {
    Name        string      `json:"name"`
    Type        string      `json:"type"`        // "opportunity", "bidding", "custom"
    Schema      ModelSchema `json:"schema"`
    Examples    []string    `json:"examples"`
    Description string      `json:"description"`
}

type ModelSchema struct {
    Type       string                 `json:"type"`
    Properties map[string]Property    `json:"properties"`
    Required   []string               `json:"required"`
}

type Property struct {
    Type        string      `json:"type"`
    Description string      `json:"description"`
    Enum        []string    `json:"enum,omitempty"`
    Items       *Property   `json:"items,omitempty"`
    Default     interface{} `json:"default,omitempty"`
}
```

**JSON模型定义格式:**

实体提取Agent不预定义任何业务模型，而是接受调用方提供的JSON Schema格式的模型定义。

**标准JSON Schema格式:**
```json
{
  "name": "OpportunityDimension",
  "description": "商机维度模型，用于分析商业机会的各个维度",
  "type": "object",
  "properties": {
    "name": {
      "type": "string",
      "description": "维度名称，如'商业机会'、'技术创新'等"
    },
    "description": {
      "type": "string", 
      "description": "维度的详细描述，解释该维度的含义和作用"
    },
    "keywords": {
      "type": "array",
      "description": "与该维度相关的关键字列表，支持中英文",
      "items": {
        "type": "string"
      }
    }
  },
  "required": ["name", "description"],
  "examples": [
    {
      "name": "商业机会",
      "description": "与企业增长或盈利潜力相关的机会。",
      "keywords": ["市场扩张", "新客户", "收入增长", "market expansion", "new clients", "revenue growth"]
    }
  ]
}
```

**查询条件模型示例:**
```json
{
  "name": "QueryConditionsList",
  "description": "查询条件列表模型，用于解析用户的查询需求",
  "type": "object",
  "properties": {
    "query_conditions_list": {
      "type": "array",
      "description": "查询条件列表",
      "items": {
        "type": "object",
        "properties": {
          "query_fields": {
            "type": "string",
            "description": "需要查询的字段名",
            "enum": ["country", "update_at"]
          },
          "query_value": {
            "type": "string",
            "description": "查询字段的值"
          },
          "query_operate": {
            "type": "string",
            "description": "查询操作符",
            "enum": ["eq", "ne", "gt", "gte", "lt", "lte"]
          }
        },
        "required": ["query_fields", "query_value", "query_operate"]
      }
    }
  },
  "required": ["query_conditions_list"],
  "examples": [
    {
      "query_conditions_list": [
        {
          "query_fields": "country",
          "query_value": "china",
          "query_operate": "eq"
        },
        {
          "query_fields": "update_at",
          "query_value": "2025-7-18",
          "query_operate": "gt"
        }
      ]
    }
  ]
}
```

#### 3. LLM提取引擎 (LLM-based Extraction Engine)

**PromptBuilder** - 提示词构建器
```go
type PromptBuilder struct {
    modelRegistry *BusinessModelRegistry
    templates     map[string]*PromptTemplate
}

type PromptTemplate struct {
    SystemPrompt string `json:"system_prompt"`
    UserPrompt   string `json:"user_prompt"`
    Examples     []PromptExample `json:"examples"`
    Language     string `json:"language"` // "zh", "en", "mixed"
}

type PromptExample struct {
    Input  string `json:"input"`
    Output string `json:"output"`
}

// 构建提示词
func (pb *PromptBuilder) BuildPrompt(modelName string, inputText string, language string) (*PromptRequest, error) {
    model := pb.modelRegistry.GetModel(modelName)
    if model == nil {
        return nil, fmt.Errorf("未找到模型: %s", modelName)
    }
    
    // 构建系统提示词
    systemPrompt := pb.buildSystemPrompt(model, language)
    
    // 构建用户提示词
    userPrompt := pb.buildUserPrompt(model, inputText, language)
    
    return &PromptRequest{
        SystemPrompt: systemPrompt,
        UserPrompt:   userPrompt,
        ModelSchema:  model.Schema,
        Language:     language,
    }, nil
}

func (pb *PromptBuilder) buildSystemPrompt(model *BusinessModel, language string) string {
    var systemPrompt strings.Builder
    
    if language == "zh" || language == "mixed" {
        systemPrompt.WriteString("你是一个专业的实体提取专家。")
        systemPrompt.WriteString("请从用户输入的文本中提取符合指定数据模型的结构化信息。\n\n")
    } else {
        systemPrompt.WriteString("You are a professional entity extraction expert.")
        systemPrompt.WriteString("Please extract structured information from user input text that conforms to the specified data model.\n\n")
    }
    
    // 注入模型结构
    systemPrompt.WriteString("目标数据模型结构:\n")
    schemaJSON, _ := json.MarshalIndent(model.Schema, "", "  ")
    systemPrompt.WriteString(string(schemaJSON))
    systemPrompt.WriteString("\n\n")
    
    // 添加示例
    if len(model.Examples) > 0 {
        systemPrompt.WriteString("示例:\n")
        for _, example := range model.Examples {
            systemPrompt.WriteString(example)
            systemPrompt.WriteString("\n")
        }
    }
    
    // 输出要求
    if language == "zh" || language == "mixed" {
        systemPrompt.WriteString("要求:\n")
        systemPrompt.WriteString("1. 严格按照JSON格式输出\n")
        systemPrompt.WriteString("2. 如果无法从输入中提取有意义的信息，返回错误信息\n")
        systemPrompt.WriteString("3. 确保输出符合模型结构要求\n")
        systemPrompt.WriteString("4. 支持中英文混合输入\n")
    } else {
        systemPrompt.WriteString("Requirements:\n")
        systemPrompt.WriteString("1. Output strictly in JSON format\n")
        systemPrompt.WriteString("2. Return error message if meaningful information cannot be extracted\n")
        systemPrompt.WriteString("3. Ensure output conforms to model structure requirements\n")
        systemPrompt.WriteString("4. Support mixed Chinese and English input\n")
    }
    
    return systemPrompt.String()
}
```

**LLMProcessor** - LLM处理器
```go
type LLMProcessor struct {
    llmClient llm.LLMClient
    config    *LLMConfig
}

type LLMConfig struct {
    Model       string  `json:"model"`
    Temperature float32 `json:"temperature"`
    MaxTokens   int     `json:"max_tokens"`
    Timeout     int     `json:"timeout"`
    RetryCount  int     `json:"retry_count"`
}

type ExtractionRequest struct {
    InputText    string                 `json:"input_text"`
    ModelName    string                 `json:"model_name"`
    BusinessType string                 `json:"business_type"`
    Language     string                 `json:"language"`
    Config       map[string]interface{} `json:"config,omitempty"`
}

type ExtractionResult struct {
    Success     bool                   `json:"success"`
    Data        map[string]interface{} `json:"data,omitempty"`
    Confidence  float64                `json:"confidence"`
    Error       *ExtractionError       `json:"error,omitempty"`
    Metadata    ExtractionMetadata     `json:"metadata"`
    RawOutput   string                 `json:"raw_output,omitempty"`
}

type ExtractionMetadata struct {
    ModelUsed      string    `json:"model_used"`
    ProcessingTime int64     `json:"processing_time_ms"`
    TokenUsage     TokenUsage `json:"token_usage"`
    Language       string    `json:"language"`
    RetryCount     int       `json:"retry_count"`
    ProcessedAt    time.Time `json:"processed_at"`
}

type TokenUsage struct {
    InputTokens  int `json:"input_tokens"`
    OutputTokens int `json:"output_tokens"`
    TotalTokens  int `json:"total_tokens"`
}

// 处理提取请求
func (lp *LLMProcessor) ProcessExtraction(ctx context.Context, req *ExtractionRequest) (*ExtractionResult, error) {
    start := time.Now()
    
    // 构建提示词
    promptReq, err := lp.promptBuilder.BuildPrompt(req.ModelName, req.InputText, req.Language)
    if err != nil {
        return &ExtractionResult{
            Success: false,
            Error: &ExtractionError{
                Code: "PROMPT_BUILD_FAILED",
                Message: fmt.Sprintf("构建提示词失败: %v", err),
            },
            Metadata: ExtractionMetadata{
                ProcessingTime: time.Since(start).Milliseconds(),
                ProcessedAt: time.Now(),
            },
        }, nil
    }
    
    // 调用LLM
    response, err := lp.callLLMWithRetry(ctx, promptReq)
    if err != nil {
        return &ExtractionResult{
            Success: false,
            Error: &ExtractionError{
                Code: "LLM_CALL_FAILED",
                Message: fmt.Sprintf("LLM调用失败: %v", err),
            },
            Metadata: ExtractionMetadata{
                ProcessingTime: time.Since(start).Milliseconds(),
                ProcessedAt: time.Now(),
            },
        }, nil
    }
    
    // 解析响应
    result, err := lp.parseResponse(response, promptReq.ModelSchema)
    if err != nil {
        return &ExtractionResult{
            Success: false,
            Error: &ExtractionError{
                Code: "RESPONSE_PARSE_FAILED",
                Message: fmt.Sprintf("响应解析失败: %v", err),
            },
            RawOutput: response.Choices[0].Message.Content,
            Metadata: ExtractionMetadata{
                ProcessingTime: time.Since(start).Milliseconds(),
                ProcessedAt: time.Now(),
            },
        }, nil
    }
    
    return &ExtractionResult{
        Success: true,
        Data: result.Data,
        Confidence: result.Confidence,
        Metadata: ExtractionMetadata{
            ModelUsed: lp.config.Model,
            ProcessingTime: time.Since(start).Milliseconds(),
            TokenUsage: TokenUsage{
                InputTokens: response.Usage.PromptTokens,
                OutputTokens: response.Usage.CompletionTokens,
                TotalTokens: response.Usage.TotalTokens,
            },
            Language: req.Language,
            ProcessedAt: time.Now(),
        },
    }, nil
}
```

**ResponseParser** - 响应解析器
```go
type ResponseParser struct {
    validator *Validator
}

type ParseResult struct {
    Data       map[string]interface{} `json:"data"`
    Confidence float64                `json:"confidence"`
    Valid      bool                   `json:"valid"`
    Errors     []ValidationError      `json:"errors,omitempty"`
}

// 解析LLM响应
func (rp *ResponseParser) parseResponse(response *llm.ChatResponse, schema ModelSchema) (*ParseResult, error) {
    content := response.Choices[0].Message.Content
    
    // 尝试解析JSON
    var data map[string]interface{}
    if err := json.Unmarshal([]byte(content), &data); err != nil {
        // 如果JSON解析失败，尝试提取JSON部分
        jsonStr := rp.extractJSON(content)
        if jsonStr == "" {
            return nil, fmt.Errorf("无法从响应中提取有效JSON: %s", content)
        }
        
        if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
            return nil, fmt.Errorf("JSON解析失败: %v", err)
        }
    }
    
    // 验证数据结构
    validationResult := rp.validator.ValidateAgainstSchema(data, schema)
    
    // 计算置信度
    confidence := rp.calculateConfidence(data, validationResult)
    
    return &ParseResult{
        Data:       data,
        Confidence: confidence,
        Valid:      validationResult.Valid,
        Errors:     validationResult.Errors,
    }, nil
}

// 从文本中提取JSON
func (rp *ResponseParser) extractJSON(text string) string {
    // 查找JSON代码块
    jsonBlockRegex := regexp.MustCompile("```json\\s*([\\s\\S]*?)\\s*```")
    matches := jsonBlockRegex.FindStringSubmatch(text)
    if len(matches) > 1 {
        return strings.TrimSpace(matches[1])
    }
    
    // 查找花括号包围的JSON
    braceRegex := regexp.MustCompile("\\{[\\s\\S]*\\}")
    match := braceRegex.FindString(text)
    if match != "" {
        return match
    }
    
    return ""
}

// 计算置信度
func (rp *ResponseParser) calculateConfidence(data map[string]interface{}, validation ValidationResult) float64 {
    if !validation.Valid {
        return 0.0
    }
    
    // 基础置信度
    confidence := 0.8
    
    // 根据字段完整性调整
    requiredFieldsCount := len(validation.RequiredFields)
    presentFieldsCount := 0
    
    for _, field := range validation.RequiredFields {
        if _, exists := data[field]; exists {
            presentFieldsCount++
        }
    }
    
    if requiredFieldsCount > 0 {
        fieldCompleteness := float64(presentFieldsCount) / float64(requiredFieldsCount)
        confidence *= fieldCompleteness
    }
    
    // 根据数据质量调整
    if rp.hasHighQualityData(data) {
        confidence += 0.1
    }
    
    // 确保置信度在0-1范围内
    if confidence > 1.0 {
        confidence = 1.0
    }
    if confidence < 0.0 {
        confidence = 0.0
    }
    
    return confidence
}
```

#### 4. 配置管理 (Configuration Management)

**ConfigManager** - 配置管理器
```go
type ConfigManager struct {
    businessConfigs map[string]*BusinessConfig
    entitySchemas   map[string]*EntitySchema
    extractionRules map[string][]ExtractionRule
    hotReload       bool
}

type BusinessConfig struct {
    BusinessType    string                 `json:"business_type"`
    EntityTypes     []string               `json:"entity_types"`
    ExtractionMode  string                 `json:"extraction_mode"` // "llm", "rule", "hybrid"
    LLMConfig       map[string]interface{} `json:"llm_config"`
    RuleConfig      map[string]interface{} `json:"rule_config"`
    OutputFormat    string                 `json:"output_format"`
}

type EntitySchema struct {
    Name        string                 `json:"name"`
    Description string                 `json:"description"`
    DataType    string                 `json:"data_type"`
    Required    bool                   `json:"required"`
    Validation  map[string]interface{} `json:"validation"`
    Examples    []string               `json:"examples"`
}
```

#### 5. 结果存储和缓存 (Storage & Cache)

**ResultStore** - 结果存储
```go
type ResultStore struct {
    db store.Store
}

type ExtractionRecord struct {
    ID           string                 `json:"id" bson:"_id"`
    BusinessType string                 `json:"business_type" bson:"business_type"`
    InputText    string                 `json:"input_text" bson:"input_text"`
    InputHash    string                 `json:"input_hash" bson:"input_hash"`
    Result       ExtractionResult       `json:"result" bson:"result"`
    Config       map[string]interface{} `json:"config" bson:"config"`
    CreatedAt    time.Time              `json:"created_at" bson:"created_at"`
    UpdatedAt    time.Time              `json:"updated_at" bson:"updated_at"`
}
```

**CacheLayer** - 缓存层
```go
type CacheLayer struct {
    resultCache   map[string]*ExtractionResult
    templateCache map[string]*template.Template
    configCache   map[string]*BusinessConfig
    ttl           time.Duration
}
```

## 数据模型

### 输入数据模型

**通用提取请求:**
```json
{
  "input_text": "用户输入的自然语言文本或结构化文本",
  "model_name": "OpportunityDimension|StrategyAnalysisDimension|QueryConditionsList|BiddingProject",
  "business_type": "opportunity|bidding|query|custom",
  "language": "zh|en|mixed",
  "extraction_config": {
    "confidence_threshold": 0.7,
    "max_retries": 3,
    "timeout": 30,
    "enable_fallback": true
  }
}
```

**商机维度提取请求:**
```json
{
  "input_text": "请你帮我分析这条新闻的商业相关的机会",
  "model_name": "OpportunityDimension",
  "business_type": "opportunity",
  "language": "zh"
}
```

**查询条件提取请求:**
```json
{
  "input_text": "请你帮我查找中国的大于7月18号的新闻",
  "model_name": "QueryConditionsList", 
  "business_type": "query",
  "language": "zh"
}
```

### 输出数据模型

**成功响应:**
```json
{
  "success": true,
  "data": {
    "name": "商业机会",
    "description": "与企业增长或盈利潜力相关的机会。",
    "keywords": ["市场扩张", "新客户", "收入增长", "market expansion", "new clients", "revenue growth"]
  },
  "confidence": 0.92,
  "metadata": {
    "model_used": "deepseek-v3-250324",
    "processing_time_ms": 1250,
    "token_usage": {
      "input_tokens": 150,
      "output_tokens": 80,
      "total_tokens": 230
    },
    "language": "zh",
    "retry_count": 0,
    "processed_at": "2024-01-01T10:30:00Z"
  }
}
```

**失败响应 (无法提取):**
```json
{
  "success": false,
  "error": {
    "code": "EXTRACTION_IMPOSSIBLE",
    "message": "无法从输入文本中提取符合OpportunityDimension模型的有意义信息",
    "details": {
      "input_text": "今天天气很好",
      "model_name": "OpportunityDimension",
      "reason": "输入文本与商机维度模型无关联性"
    }
  },
  "confidence": 0.0,
  "metadata": {
    "model_used": "deepseek-v3-250324",
    "processing_time_ms": 800,
    "token_usage": {
      "input_tokens": 120,
      "output_tokens": 50,
      "total_tokens": 170
    },
    "language": "zh",
    "retry_count": 1,
    "processed_at": "2024-01-01T10:30:00Z"
  },
  "raw_output": "根据输入文本'今天天气很好'，无法识别出任何与商业机会相关的维度信息。"
}
```

**查询条件提取成功响应:**
```json
{
  "success": true,
  "data": {
    "query_conditions_list": [
      {
        "query_fields": "country",
        "query_value": "china",
        "query_operate": "eq"
      },
      {
        "query_fields": "update_at",
        "query_value": "2025-7-18",
        "query_operate": "gt"
      }
    ]
  },
  "confidence": 0.95,
  "metadata": {
    "model_used": "deepseek-v3-250324",
    "processing_time_ms": 950,
    "token_usage": {
      "input_tokens": 180,
      "output_tokens": 120,
      "total_tokens": 300
    },
    "language": "zh",
    "retry_count": 0,
    "processed_at": "2024-01-01T10:30:00Z"
  }
}

## 接口设计

### Agent能力接口

#### extract_structured_data - 通用结构化数据提取

这是唯一的核心能力，支持所有业务场景的实体提取需求。

**输入Schema:**
```json
{
  "type": "object",
  "properties": {
    "input_text": {
      "type": "string",
      "description": "输入文本内容（自然语言文本或结构化文本）"
    },
    "target_schema": {
      "type": "object",
      "description": "目标JSON Schema定义，包含实体模型结构",
      "properties": {
        "name": {
          "type": "string",
          "description": "模型名称"
        },
        "description": {
          "type": "string",
          "description": "模型描述"
        },
        "type": {
          "type": "string",
          "enum": ["object"],
          "description": "数据类型，固定为object"
        },
        "properties": {
          "type": "object",
          "description": "实体字段定义"
        },
        "required": {
          "type": "array",
          "items": {"type": "string"},
          "description": "必填字段列表"
        },
        "examples": {
          "type": "array",
          "description": "示例数据列表"
        }
      },
      "required": ["name", "description", "type", "properties"]
    },
    "language": {
      "type": "string",
      "enum": ["zh", "en", "mixed"],
      "default": "zh",
      "description": "输入文本语言"
    },
    "extraction_config": {
      "type": "object",
      "properties": {
        "confidence_threshold": {"type": "number", "default": 0.7, "description": "置信度阈值"},
        "max_retries": {"type": "integer", "default": 3, "description": "最大重试次数"},
        "timeout": {"type": "integer", "default": 30, "description": "超时时间(秒)"},
        "enable_fallback": {"type": "boolean", "default": true, "description": "是否启用降级策略"}
      }
    }
  },
  "required": ["input_text", "target_schema"]
}
```

**输出Schema:**
```json
{
  "type": "object",
  "properties": {
    "success": {
      "type": "boolean",
      "description": "提取是否成功"
    },
    "data": {
      "type": "object",
      "description": "提取的结构化数据，符合target_schema定义"
    },
    "confidence": {
      "type": "number",
      "minimum": 0,
      "maximum": 1,
      "description": "提取结果的置信度"
    },
    "error": {
      "type": "object",
      "properties": {
        "code": {"type": "string", "description": "错误代码"},
        "message": {"type": "string", "description": "错误信息"},
        "details": {"type": "object", "description": "错误详情"}
      },
      "description": "错误信息（仅当success为false时存在）"
    },
    "metadata": {
      "type": "object",
      "properties": {
        "model_used": {"type": "string", "description": "使用的LLM模型"},
        "processing_time_ms": {"type": "integer", "description": "处理时间（毫秒）"},
        "token_usage": {
          "type": "object",
          "properties": {
            "input_tokens": {"type": "integer"},
            "output_tokens": {"type": "integer"},
            "total_tokens": {"type": "integer"}
          }
        },
        "language": {"type": "string", "description": "检测到的语言"},
        "retry_count": {"type": "integer", "description": "实际重试次数"},
        "processed_at": {"type": "string", "format": "date-time", "description": "处理时间"}
      }
    },
    "raw_output": {
      "type": "string",
      "description": "LLM原始输出（仅在解析失败时提供）"
    }
  },
  "required": ["success", "confidence", "metadata"]
}
```

### 使用示例

#### 示例1: 商机维度提取

**请求:**
```json
{
  "input_text": "请你帮我分析这条新闻的商业相关的机会",
  "target_schema": {
    "name": "OpportunityDimension",
    "description": "商机维度模型，用于分析商业机会的各个维度",
    "type": "object",
    "properties": {
      "name": {
        "type": "string",
        "description": "维度名称，如'商业机会'、'技术创新'等"
      },
      "description": {
        "type": "string", 
        "description": "维度的详细描述，解释该维度的含义和作用"
      },
      "keywords": {
        "type": "array",
        "description": "与该维度相关的关键字列表，支持中英文",
        "items": {"type": "string"}
      }
    },
    "required": ["name", "description"],
    "examples": [
      {
        "name": "商业机会",
        "description": "与企业增长或盈利潜力相关的机会。",
        "keywords": ["市场扩张", "新客户", "收入增长"]
      }
    ]
  },
  "language": "zh"
}
```

#### 示例2: 招标关键词信息提取

**请求:**
```json
{
  "input_text": "我想找德国2025年5月后发布的低平板挂车采购项目",
  "target_schema": {
    "name": "BiddingKeyInfo",
    "description": "招标关键词信息模型，用于提取招标项目的关键信息",
    "type": "object",
    "properties": {
      "subject": {
        "type": "string",
        "description": "标题或主要采购内容"
      },
      "industry": {
        "type": "string",
        "description": "所属行业"
      },
      "country": {
        "type": "string",
        "description": "国家或地区"
      },
      "publication_date_from": {
        "type": "string",
        "format": "date-time",
        "description": "发布日期起始时间"
      },
      "publication_date_to": {
        "type": "string",
        "format": "date-time",
        "description": "发布日期结束时间"
      },
      "submission_deadline_from": {
        "type": "string",
        "format": "date-time",
        "description": "投标截止起始时间"
      },
      "submission_deadline_to": {
        "type": "string",
        "format": "date-time",
        "description": "投标截止结束时间"
      },
      "estimated_value_min": {
        "type": "number",
        "description": "预算下限"
      },
      "estimated_value_max": {
        "type": "number",
        "description": "预算上限"
      },
      "currency": {
        "type": "string",
        "description": "币种"
      },
      "procurement_type": {
        "type": "string",
        "description": "采购类型，如物品采购、服务采购、工程采购"
      },
      "issuing_organization": {
        "type": "string",
        "description": "发布机构名称"
      }
    },
    "required": ["subject"],
    "examples": [
      {
        "subject": "低平板挂车",
        "country": "德国",
        "publication_date_from": "2025-05-01T00:00:00Z",
        "procurement_type": "物品采购"
      }
    ]
  },
  "language": "zh"
}
```

#### 示例3: 分析角度提取与操作

**请求:**
```json
{
  "input_text": "增加一条竞争对手分析，减少摘要分析",
  "target_schema": {
    "name": "DirectionConditionsList",
    "description": "分析角度操作列表模型，用于解析用户对分析维度的操作需求",
    "type": "object",
    "properties": {
      "conditions_list": {
        "type": "array",
        "description": "分析角度操作列表",
        "items": {
          "type": "object",
          "properties": {
            "operator": {
              "type": "string",
              "enum": ["删除", "修改", "新增"],
              "description": "操作类型"
            },
            "name": {
              "type": "string",
              "description": "维度名称"
            },
            "description": {
              "type": "string",
              "description": "维度描述"
            }
          },
          "required": ["operator", "name", "description"]
        }
      }
    },
    "required": ["conditions_list"],
    "examples": [
      {
        "conditions_list": [
          {
            "operator": "新增",
            "name": "竞争对手分析",
            "description": "对主要竞争对手的市场表现、战略和优势进行分析。"
          },
          {
            "operator": "删除",
            "name": "摘要分析",
            "description": "对新闻内容进行简要总结。"
          }
        ]
      }
    ]
  },
  "language": "zh"
}
```

#### 示例4: 查询条件提取

**请求:**
```json
{
  "input_text": "请你帮我查找中国的大于7月18号的新闻",
  "target_schema": {
    "name": "QueryConditionsList",
    "description": "查询条件列表模型，用于解析用户的查询需求",
    "type": "object",
    "properties": {
      "query_conditions_list": {
        "type": "array",
        "description": "查询条件列表",
        "items": {
          "type": "object",
          "properties": {
            "query_fields": {
              "type": "string",
              "description": "需要查询的字段名",
              "enum": ["country", "update_at"]
            },
            "query_value": {
              "type": "string",
              "description": "查询字段的值"
            },
            "query_operate": {
              "type": "string",
              "description": "查询操作符",
              "enum": ["eq", "ne", "gt", "gte", "lt", "lte"]
            }
          },
          "required": ["query_fields", "query_value", "query_operate"]
        }
      }
    },
    "required": ["query_conditions_list"],
    "examples": [
      {
        "query_conditions_list": [
          {
            "query_fields": "country",
            "query_value": "china",
            "query_operate": "eq"
          }
        ]
      }
    ]
  },
  "language": "zh"
}
```

## 错误处理

### 错误类型定义

```go
type ExtractionError struct {
    Code    string                 `json:"code"`
    Message string                 `json:"message"`
    Details map[string]interface{} `json:"details,omitempty"`
}

const (
    // 输入相关错误
    ErrInvalidInput         = "INVALID_INPUT"
    ErrEmptyInput          = "EMPTY_INPUT"
    ErrInputTooLong        = "INPUT_TOO_LONG"
    ErrUnsupportedLanguage = "UNSUPPORTED_LANGUAGE"
    
    // 模型相关错误
    ErrModelNotFound       = "MODEL_NOT_FOUND"
    ErrUnsupportedModel    = "UNSUPPORTED_MODEL"
    ErrInvalidModelSchema  = "INVALID_MODEL_SCHEMA"
    
    // 业务相关错误
    ErrUnsupportedBusiness = "UNSUPPORTED_BUSINESS_TYPE"
    ErrExtractionImpossible = "EXTRACTION_IMPOSSIBLE"
    ErrLowConfidence       = "LOW_CONFIDENCE"
    
    // LLM相关错误
    ErrLLMCallFailed       = "LLM_CALL_FAILED"
    ErrLLMTimeout          = "LLM_TIMEOUT"
    ErrLLMRateLimit        = "LLM_RATE_LIMIT"
    ErrTokenExceeded       = "TOKEN_EXCEEDED"
    
    // 解析相关错误
    ErrResponseParseFailed = "RESPONSE_PARSE_FAILED"
    ErrInvalidJSON         = "INVALID_JSON"
    ErrSchemaValidation    = "SCHEMA_VALIDATION_FAILED"
    
    // 系统相关错误
    ErrConfigNotFound      = "CONFIG_NOT_FOUND"
    ErrStorageFailed       = "STORAGE_FAILED"
    ErrInternalError       = "INTERNAL_ERROR"
)
```

### 错误处理策略

#### 1. 输入验证错误处理
```go
func (ea *EntityExtractionAgent) validateInput(input map[string]interface{}) error {
    inputText, ok := input["input_text"].(string)
    if !ok || inputText == "" {
        return &ExtractionError{
            Code: ErrEmptyInput,
            Message: "输入文本不能为空",
            Details: map[string]interface{}{
                "received_input": input,
            },
        }
    }
    
    if len(inputText) > 50000 { // 50KB限制
        return &ExtractionError{
            Code: ErrInputTooLong,
            Message: "输入文本过长，请限制在50KB以内",
            Details: map[string]interface{}{
                "input_length": len(inputText),
                "max_length": 50000,
            },
        }
    }
    
    return nil
}
```

#### 2. 无法提取情况处理
```go
func (ea *EntityExtractionAgent) handleExtractionImpossible(inputText, modelName string) *models.AgentResponse {
    return &models.AgentResponse{
        Success: false,
        Error: &ExtractionError{
            Code: ErrExtractionImpossible,
            Message: fmt.Sprintf("无法从输入文本中提取符合%s模型的有意义信息", modelName),
            Details: map[string]interface{}{
                "input_text": inputText,
                "model_name": modelName,
                "reason": "输入文本与目标模型无关联性",
                "suggestions": []string{
                    "请提供与业务场景相关的文本内容",
                    "检查输入文本是否包含目标实体信息",
                    "尝试使用其他业务模型",
                },
            },
        },
        Confidence: 0.0,
    }
}
```

#### 3. LLM调用失败处理
```go
func (lp *LLMProcessor) callLLMWithRetry(ctx context.Context, req *PromptRequest) (*llm.ChatResponse, error) {
    var lastErr error
    
    for i := 0; i < lp.config.RetryCount; i++ {
        response, err := lp.llmClient.Chat(ctx, &llm.ChatRequest{
            Model: lp.config.Model,
            Messages: []llm.Message{
                {Role: "system", Content: req.SystemPrompt},
                {Role: "user", Content: req.UserPrompt},
            },
            Temperature: lp.config.Temperature,
            MaxTokens: lp.config.MaxTokens,
        })
        
        if err == nil {
            return response, nil
        }
        
        lastErr = err
        
        // 根据错误类型决定是否重试
        if isRetryableError(err) {
            time.Sleep(time.Duration(i+1) * time.Second) // 指数退避
            continue
        } else {
            break // 不可重试的错误，直接返回
        }
    }
    
    return nil, &ExtractionError{
        Code: ErrLLMCallFailed,
        Message: fmt.Sprintf("LLM调用失败，已重试%d次", lp.config.RetryCount),
        Details: map[string]interface{}{
            "last_error": lastErr.Error(),
            "retry_count": lp.config.RetryCount,
        },
    }
}

func isRetryableError(err error) bool {
    // 检查是否为可重试的错误类型
    errorStr := err.Error()
    retryableErrors := []string{
        "timeout",
        "connection reset",
        "temporary failure",
        "rate limit",
    }
    
    for _, retryableErr := range retryableErrors {
        if strings.Contains(strings.ToLower(errorStr), retryableErr) {
            return true
        }
    }
    
    return false
}
```

#### 4. 置信度过低处理
```go
func (ea *EntityExtractionAgent) handleLowConfidence(result *ExtractionResult, threshold float64) *models.AgentResponse {
    if result.Confidence < threshold {
        return &models.AgentResponse{
            Success: false,
            Error: &ExtractionError{
                Code: ErrLowConfidence,
                Message: fmt.Sprintf("提取结果置信度过低 (%.2f < %.2f)", result.Confidence, threshold),
                Details: map[string]interface{}{
                    "confidence": result.Confidence,
                    "threshold": threshold,
                    "extracted_data": result.Data,
                    "suggestions": []string{
                        "尝试提供更清晰的输入文本",
                        "降低置信度阈值",
                        "使用其他业务模型",
                    },
                },
            },
            Confidence: result.Confidence,
        }
    }
    
    return nil
}
```

#### 5. 降级策略
```go
func (ea *EntityExtractionAgent) executeWithFallback(ctx context.Context, req *ExtractionRequest) (*ExtractionResult, error) {
    // 主要提取方法
    result, err := ea.llmProcessor.ProcessExtraction(ctx, req)
    if err == nil && result.Success {
        return result, nil
    }
    
    // 降级策略1: 降低置信度阈值
    if req.Config != nil {
        if threshold, ok := req.Config["confidence_threshold"].(float64); ok && threshold > 0.5 {
            req.Config["confidence_threshold"] = 0.5
            result, err = ea.llmProcessor.ProcessExtraction(ctx, req)
            if err == nil && result.Success {
                return result, nil
            }
        }
    }
    
    // 降级策略2: 使用简化模型
    if req.ModelName != "SimpleExtraction" {
        req.ModelName = "SimpleExtraction"
        result, err = ea.llmProcessor.ProcessExtraction(ctx, req)
        if err == nil && result.Success {
            return result, nil
        }
    }
    
    // 最终降级: 返回错误但包含部分信息
    return &ExtractionResult{
        Success: false,
        Error: &ExtractionError{
            Code: ErrExtractionFailed,
            Message: "所有提取方法均失败，无法获取有效结果",
            Details: map[string]interface{}{
                "attempted_methods": []string{"primary_llm", "lowered_threshold", "simple_model"},
                "last_error": err.Error(),
            },
        },
        Confidence: 0.0,
    }, nil
}

## 测试策略

### 单元测试

1. **实体提取准确性测试**
   - 测试各种实体类型的识别准确率
   - 边界情况和异常输入处理

2. **业务适配器测试**
   - 不同业务类型的配置加载
   - 规则引擎的匹配逻辑

3. **配置管理测试**
   - 配置热更新功能
   - 配置验证和错误处理

### 集成测试

1. **端到端提取测试**
   - 完整的提取流程验证
   - 多业务类型混合测试

2. **性能测试**
   - 大文本处理性能
   - 并发请求处理能力

3. **Agent接口测试**
   - 符合TaskD Agent规范
   - API接口兼容性测试

### 业务场景测试

1. **商机新闻场景**
   - 真实新闻数据测试
   - 实体识别准确率验证

2. **招投标场景**
   - 招投标文档处理测试
   - 关键信息提取验证

## 部署和运维

### 配置文件结构

```yaml
entity_extraction_agent:
  # Agent基础配置
  agent_id: "entity-extraction-agent-001"
  name: "通用实体提取Agent"
  version: "1.0.0"
  
  # LLM配置
  llm:
    model: "deepseek-v3-250324"
    temperature: 0.1
    max_tokens: 4000
    timeout: 30s
  
  # 业务配置
  business_configs:
    news:
      entity_types: ["company", "product", "cooperation", "person", "location", "amount", "date"]
      extraction_mode: "hybrid"
      confidence_threshold: 0.7
    bidding:
      entity_types: ["project_name", "bidder", "budget", "deadline", "requirements", "qualification", "contact"]
      extraction_mode: "hybrid"
      confidence_threshold: 0.8
  
  # 缓存配置
  cache:
    enabled: true
    ttl: "1h"
    max_size: 1000
  
  # 存储配置
  storage:
    enabled: true
    retention_days: 30
```

### 监控指标

1. **性能指标**
   - 提取响应时间
   - 吞吐量 (requests/second)
   - 成功率和错误率

2. **业务指标**
   - 各业务类型的使用分布
   - 实体类型提取统计
   - 置信度分布

3. **资源指标**
   - CPU和内存使用率
   - LLM Token消耗
   - 存储空间使用

### 扩展指南

#### 添加新业务类型

1. **定义实体类型**
```go
var CustomEntityTypes = []EntityType{
    {Name: "custom_entity", Description: "自定义实体", Pattern: "..."},
}
```

2. **创建业务适配器**
```go
type CustomEntityAdapter struct {
    entityTypes []EntityType
    extractionRules map[string]ExtractionRule
}
```

3. **配置业务规则**
```yaml
custom:
  entity_types: ["custom_entity"]
  extraction_mode: "hybrid"
  confidence_threshold: 0.75
```

4. **注册到Agent**
```go
agent.RegisterBusinessAdapter("custom", customAdapter)
```

#### 添加新实体类型

1. **定义实体结构**
2. **编写提取规则**
3. **更新配置文件**
4. **添加测试用例**
## 部
署和运维

### 目录结构设计

按照TaskD项目的分层设计，实体提取Agent的代码组织如下：

```
internal/
├── services/
│   └── entity_extraction_agent.go          # 主Agent实现
├── modules/
│   └── entity_extraction/
│       ├── agents/
│       │   └── entity_extraction_agent.go  # Agent具体实现
│       ├── models/
│       │   ├── business_models.go          # 业务模型定义
│       │   ├── extraction_models.go        # 提取相关模型
│       │   └── error_models.go             # 错误模型定义
│       └── services/
│           ├── model_registry.go           # 模型注册表
│           ├── prompt_builder.go           # 提示词构建器
│           ├── llm_processor.go            # LLM处理器
│           ├── response_parser.go          # 响应解析器
│           └── validator.go                # 验证器
├── prompts/
│   ├── entity_extraction/
│   │   ├── opportunity_dimension.yaml      # 商机维度提取模板
│   │   ├── strategy_analysis.yaml          # 策略分析模板
│   │   ├── query_conditions.yaml          # 查询条件模板
│   │   └── bidding_entities.yaml          # 招投标实体模板
│   └── entity_extraction_prompt_manager.go
└── models/
    └── entity_extraction_models.go         # 通用模型定义
```

### 配置文件

```yaml
# configs/config.dev.yaml
entity_extraction_agent:
  agent_id: "entity-extraction-agent-001"
  name: "通用实体提取Agent"
  version: "1.0.0"
  enabled: true
  
  # LLM配置
  llm:
    model: "deepseek-v3-250324"
    temperature: 0.1
    max_tokens: 4000
    timeout: 30s
    retry_count: 3
  
  # 业务模型配置
  business_models:
    opportunity_dimension:
      enabled: true
      confidence_threshold: 0.7
    strategy_analysis:
      enabled: true
      confidence_threshold: 0.7
    query_conditions:
      enabled: true
      confidence_threshold: 0.8
    bidding_entities:
      enabled: true
      confidence_threshold: 0.8
  
  # 多语言支持
  languages:
    supported: ["zh", "en", "mixed"]
    default: "zh"
  
  # 性能配置
  performance:
    max_input_length: 50000
    max_concurrent_requests: 50
    request_timeout: 60s
```

### 扩展指南

#### 添加新业务模型
1. 定义模型结构
2. 创建提示词模板
3. 注册新能力
4. 更新配置文件

#### 多语言支持扩展
支持添加新语言的提示词模板和响应处理逻辑。

这个设计文档完整地描述了基于你提供的澄清和业务样例的实体提取Agent架构。它符合TaskD的现有架构，专注于LLM提取，支持多业务场景，并具备良好的扩展性。