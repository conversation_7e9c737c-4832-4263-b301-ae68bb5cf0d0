package models

import (
	"database/sql"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"gitlab.com/specific-ai/taskd/internal/models/common"
)

// SubscriptionType represents the subscription tier of a user/company
type SubscriptionType string

const (
	SubscriptionFree    SubscriptionType = "free"
	SubscriptionPro     SubscriptionType = "pro"
	SubscriptionMax     SubscriptionType = "max"
	SubscriptionPremium SubscriptionType = "premium"
	SubscriptionBasic   SubscriptionType = "basic"
)

// User represents a user in the system
type User struct {
	ID               int              `json:"id" db:"id"`
	UserID           string           `json:"user_id" db:"user_id"`
	Username         string           `json:"username" db:"username"`
	Email            sql.NullString   `json:"email" db:"email"`
	CompanyID        string           `json:"company_id" db:"company_id"`
	SubscriptionType SubscriptionType `json:"subscription_type" db:"subscription_type"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time        `json:"updated_at" db:"updated_at"`
}

// Company represents a company in the system
type Company struct {
	ID               int              `json:"id" db:"id"`
	CompanyID        string           `json:"company_id" db:"company_id"`
	CompanyName      string           `json:"company_name" db:"company_name"`
	SubscriptionType SubscriptionType `json:"subscription_type" db:"subscription_type"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time        `json:"updated_at" db:"updated_at"`
}

// TokenConsumption represents a token consumption record
type TokenConsumption struct {
	ID            int            `json:"id" db:"id"`
	UserID        string         `json:"user_id" db:"user_id"`
	CompanyID     string         `json:"company_id" db:"company_id"`
	ModelProvider string         `json:"model_provider" db:"model_provider"`
	ModelName     string         `json:"model_name" db:"model_name"`
	InputTokens   int            `json:"input_tokens" db:"input_tokens"`
	OutputTokens  int            `json:"output_tokens" db:"output_tokens"`
	TotalTokens   int            `json:"total_tokens" db:"total_tokens"`
	CostCents     sql.NullInt32  `json:"cost_cents" db:"cost_cents"`
	RequestID     sql.NullString `json:"request_id" db:"request_id"`
	APIEndpoint   sql.NullString `json:"api_endpoint" db:"api_endpoint"`
	ConsumedAt    time.Time      `json:"consumed_at" db:"consumed_at"`
	CreatedAt     time.Time      `json:"created_at" db:"created_at"`
}

// TokenRecord represents a token usage record for various operations
type TokenRecord struct {
	UserID         string            `json:"user_id"`
	OrganizationID string            `json:"organization_id"`
	RequestType    string            `json:"request_type"` // e.g., "intent_recognition", "chat_completion"
	ModelName      string            `json:"model_name"`
	TokenUsage     common.TokenUsage `json:"token_usage"`
	Timestamp      time.Time         `json:"timestamp"`
}

// TokenLimit represents token limits configuration
type TokenLimit struct {
	ID               int              `json:"id" db:"id"`
	SubscriptionType SubscriptionType `json:"subscription_type" db:"subscription_type"`
	LimitType        string           `json:"limit_type" db:"limit_type"` // monthly, weekly, daily
	TokenLimit       int              `json:"token_limit" db:"token_limit"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time        `json:"updated_at" db:"updated_at"`
}

// TokenUsageSummary represents aggregated token usage for efficient queries
type TokenUsageSummary struct {
	ID               int              `json:"id" db:"id"`
	UserID           string           `json:"user_id" db:"user_id"`
	CompanyID        string           `json:"company_id" db:"company_id"`
	SubscriptionType SubscriptionType `json:"subscription_type" db:"subscription_type"`
	PeriodType       string           `json:"period_type" db:"period_type"` // monthly, weekly, daily
	PeriodStart      time.Time        `json:"period_start" db:"period_start"`
	PeriodEnd        time.Time        `json:"period_end" db:"period_end"`
	TotalTokens      int              `json:"total_tokens" db:"total_tokens"`
	TotalCostCents   int              `json:"total_cost_cents" db:"total_cost_cents"`
	RequestCount     int              `json:"request_count" db:"request_count"`
	LastUpdated      time.Time        `json:"last_updated" db:"last_updated"`
}

// TokenConsumptionRequest represents a request to log token consumption
type TokenConsumptionRequest struct {
	UserID        string `json:"user_id" binding:"required"`
	CompanyID     string `json:"company_id" binding:"required"`
	ModelProvider string `json:"model_provider" binding:"required"`
	ModelName     string `json:"model_name" binding:"required"`
	InputTokens   int    `json:"input_tokens" binding:"min=0"`
	OutputTokens  int    `json:"output_tokens" binding:"min=0"`
	RequestID     string `json:"request_id"`
	APIEndpoint   string `json:"api_endpoint"`
}

// TokenUsageStats represents token usage statistics for a user
type TokenUsageStats struct {
	UserID           string           `json:"user_id"`
	CompanyID        string           `json:"company_id"`
	SubscriptionType SubscriptionType `json:"subscription_type"`
	TotalUsage       UsagePeriod      `json:"total_usage"`
	MonthlyUsage     UsagePeriod      `json:"monthly_usage"`
	WeeklyUsage      UsagePeriod      `json:"weekly_usage"`
	DailyUsage       UsagePeriod      `json:"daily_usage"`
	LastConsumedAt   *time.Time       `json:"last_consumed_at"`
}

// UsagePeriod represents usage statistics for a specific period
type UsagePeriod struct {
	PeriodStart    time.Time `json:"period_start"`
	PeriodEnd      time.Time `json:"period_end"`
	TotalTokens    int       `json:"total_tokens"`
	TotalCostCents int       `json:"total_cost_cents"`
	RequestCount   int       `json:"request_count"`
	Limit          int       `json:"limit"`
	Remaining      int       `json:"remaining"`
	UsagePercent   float64   `json:"usage_percent"`
}

// TokenLimitCheck represents the result of checking token limits
type TokenLimitCheck struct {
	UserID          string      `json:"user_id"`
	CompanyID       string      `json:"company_id"`
	RequestedTokens int         `json:"requested_tokens"`
	CanProceed      bool        `json:"can_proceed"`
	Reason          string      `json:"reason,omitempty"`
	MonthlyCheck    LimitStatus `json:"monthly_check"`
	WeeklyCheck     LimitStatus `json:"weekly_check"`
	DailyCheck      LimitStatus `json:"daily_check"`
}

// LimitStatus represents the status of a specific limit check
type LimitStatus struct {
	PeriodType   string  `json:"period_type"`
	CurrentUsage int     `json:"current_usage"`
	Limit        int     `json:"limit"`
	Remaining    int     `json:"remaining"`
	AfterRequest int     `json:"after_request"`
	WouldExceed  bool    `json:"would_exceed"`
	UsagePercent float64 `json:"usage_percent"`
}

// CompanyTokenStats represents token usage statistics for a company
type CompanyTokenStats struct {
	CompanyID        string                `json:"company_id"`
	CompanyName      string                `json:"company_name"`
	SubscriptionType SubscriptionType      `json:"subscription_type"`
	TotalUsers       int                   `json:"total_users"`
	MonthlyAggregate UsagePeriod           `json:"monthly_aggregate"`
	WeeklyAggregate  UsagePeriod           `json:"weekly_aggregate"`
	DailyAggregate   UsagePeriod           `json:"daily_aggregate"`
	TopUsers         []UserTokenUsage      `json:"top_users"`
	ModelBreakdown   []ModelUsageBreakdown `json:"model_breakdown"`
}

// UserTokenUsage represents token usage for a specific user
type UserTokenUsage struct {
	UserID       string `json:"user_id" db:"user_id"`
	Username     string `json:"username" db:"username"`
	TotalTokens  int    `json:"total_tokens" db:"total_tokens"`
	TotalCosts   int    `json:"total_costs" db:"total_costs"`
	RequestCount int    `json:"request_count" db:"request_count"`
}

// ModelUsageBreakdown represents token usage breakdown by model
type ModelUsageBreakdown struct {
	ModelProvider string `json:"model_provider" db:"model_provider"`
	ModelName     string `json:"model_name" db:"model_name"`
	TotalTokens   int    `json:"total_tokens" db:"total_tokens"`
	TotalCosts    int    `json:"total_costs" db:"total_costs"`
	RequestCount  int    `json:"request_count" db:"request_count"`
}

// AuthClaims represents the claims in a JWT token
type AuthClaims struct {
	UserID    string `json:"user_id"`
	CompanyID string `json:"company_id"`
	jwt.RegisteredClaims
}
