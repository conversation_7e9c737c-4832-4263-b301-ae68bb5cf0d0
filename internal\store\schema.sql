-- Token Management Database Schema
-- This schema tracks token consumption for different users and companies

-- Create and use taskd schema
CREATE SCHEMA IF NOT EXISTS taskd;
SET search_path TO taskd, public;

-- Create ovs-profile schema for overseas profile services
CREATE SCHEMA IF NOT EXISTS "ovs-profile";

-- Set search_path to include ovs-profile for profile-related operations
-- Note: Applications should set this per connection as needed

-- Create enum for subscription types in taskd schema
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type t 
        JOIN pg_namespace n ON t.typnamespace = n.oid 
        WHERE t.typname = 'subscription_type' AND n.nspname = 'taskd'
    ) THEN
        CREATE TYPE subscription_type AS ENUM ('free', 'pro', 'max', 'premium', 'basic');
    END IF;
END $$;

-- Create users table if not exists
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(320),
    company_id VARCHAR(255),
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create companies table if not exists
CREATE TABLE IF NOT EXISTS companies (
    id SERIAL PRIMARY KEY,
    company_id VARCHAR(255) UNIQUE NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create token consumption records table
CREATE TABLE IF NOT EXISTS token_consumption (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    model_provider VARCHAR(100) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    input_tokens INTEGER NOT NULL DEFAULT 0,
    output_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    cost_cents INTEGER DEFAULT 0, -- Cost in cents for precise calculation
    request_id VARCHAR(255),
    api_endpoint VARCHAR(255),
    consumed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_user_id FOREIGN KEY (user_id) REFERENCES users(user_id),
    CONSTRAINT fk_company_id FOREIGN KEY (company_id) REFERENCES companies(company_id)
);

-- Create token limits configuration table
CREATE TABLE IF NOT EXISTS token_limits (
    id SERIAL PRIMARY KEY,
    subscription_type subscription_type NOT NULL,
    limit_type VARCHAR(50) NOT NULL, -- 'monthly', 'weekly', 'daily'
    token_limit INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint to prevent duplicate limits for same subscription and type
    CONSTRAINT unique_subscription_limit UNIQUE (subscription_type, limit_type)
);

-- Create token usage summary table for efficient queries
CREATE TABLE IF NOT EXISTS token_usage_summary (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    subscription_type subscription_type NOT NULL,
    period_type VARCHAR(50) NOT NULL, -- 'monthly', 'weekly', 'daily'
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_tokens INTEGER DEFAULT 0,
    total_cost_cents INTEGER DEFAULT 0,
    request_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_summary_user_id FOREIGN KEY (user_id) REFERENCES users(user_id),
    CONSTRAINT fk_summary_company_id FOREIGN KEY (company_id) REFERENCES companies(company_id),
    
    -- Unique constraint to prevent duplicate summaries
    CONSTRAINT unique_period_summary UNIQUE (user_id, company_id, period_type, period_start)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_token_consumption_user_id ON token_consumption(user_id);
CREATE INDEX IF NOT EXISTS idx_token_consumption_company_id ON token_consumption(company_id);
CREATE INDEX IF NOT EXISTS idx_token_consumption_consumed_at ON token_consumption(consumed_at);
CREATE INDEX IF NOT EXISTS idx_token_consumption_model ON token_consumption(model_provider, model_name);
CREATE INDEX IF NOT EXISTS idx_token_usage_summary_user_period ON token_usage_summary(user_id, period_type, period_start);
CREATE INDEX IF NOT EXISTS idx_token_usage_summary_company_period ON token_usage_summary(company_id, period_type, period_start);

-- Insert default token limits for different subscription types
INSERT INTO token_limits (subscription_type, limit_type, token_limit) VALUES
    ('free', 'monthly', 100000),
    ('free', 'weekly', 25000),
    ('free', 'daily', 5000),
    ('pro', 'monthly', 1000000),
    ('pro', 'weekly', 250000),
    ('pro', 'daily', 50000),
    ('max', 'monthly', 10000000),
    ('max', 'weekly', 2500000),
    ('max', 'daily', 500000)
ON CONFLICT (subscription_type, limit_type) DO NOTHING;

-- Create function to update token usage summary
CREATE OR REPLACE FUNCTION update_token_usage_summary()
RETURNS TRIGGER AS $$
BEGIN
    -- Update monthly summary
    INSERT INTO token_usage_summary (
        user_id, company_id, subscription_type, period_type, 
        period_start, period_end, total_tokens, total_cost_cents, request_count
    )
    VALUES (
        NEW.user_id, NEW.company_id, 
        (SELECT subscription_type FROM users WHERE user_id = NEW.user_id),
        'monthly',
        DATE_TRUNC('month', NEW.consumed_at),
        (DATE_TRUNC('month', NEW.consumed_at) + INTERVAL '1 month - 1 second'),
        NEW.total_tokens, NEW.cost_cents, 1
    )
    ON CONFLICT (user_id, company_id, period_type, period_start)
    DO UPDATE SET 
        total_tokens = token_usage_summary.total_tokens + NEW.total_tokens,
        total_cost_cents = token_usage_summary.total_cost_cents + NEW.cost_cents,
        request_count = token_usage_summary.request_count + 1,
        last_updated = CURRENT_TIMESTAMP;
    
    -- Update weekly summary
    INSERT INTO token_usage_summary (
        user_id, company_id, subscription_type, period_type, 
        period_start, period_end, total_tokens, total_cost_cents, request_count
    )
    VALUES (
        NEW.user_id, NEW.company_id, 
        (SELECT subscription_type FROM users WHERE user_id = NEW.user_id),
        'weekly',
        DATE_TRUNC('week', NEW.consumed_at),
        (DATE_TRUNC('week', NEW.consumed_at) + INTERVAL '1 week - 1 second'),
        NEW.total_tokens, NEW.cost_cents, 1
    )
    ON CONFLICT (user_id, company_id, period_type, period_start)
    DO UPDATE SET 
        total_tokens = token_usage_summary.total_tokens + NEW.total_tokens,
        total_cost_cents = token_usage_summary.total_cost_cents + NEW.cost_cents,
        request_count = token_usage_summary.request_count + 1,
        last_updated = CURRENT_TIMESTAMP;
    
    -- Update daily summary
    INSERT INTO token_usage_summary (
        user_id, company_id, subscription_type, period_type, 
        period_start, period_end, total_tokens, total_cost_cents, request_count
    )
    VALUES (
        NEW.user_id, NEW.company_id, 
        (SELECT subscription_type FROM users WHERE user_id = NEW.user_id),
        'daily',
        DATE_TRUNC('day', NEW.consumed_at),
        (DATE_TRUNC('day', NEW.consumed_at) + INTERVAL '1 day - 1 second'),
        NEW.total_tokens, NEW.cost_cents, 1
    )
    ON CONFLICT (user_id, company_id, period_type, period_start)
    DO UPDATE SET 
        total_tokens = token_usage_summary.total_tokens + NEW.total_tokens,
        total_cost_cents = token_usage_summary.total_cost_cents + NEW.cost_cents,
        request_count = token_usage_summary.request_count + 1,
        last_updated = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update summary when new consumption record is added
DROP TRIGGER IF EXISTS token_consumption_summary_trigger ON token_consumption;
CREATE TRIGGER token_consumption_summary_trigger
    AFTER INSERT ON token_consumption
    FOR EACH ROW
    EXECUTE FUNCTION update_token_usage_summary();

-- Create function to check token limits
CREATE OR REPLACE FUNCTION check_token_limit(
    p_user_id VARCHAR(255),
    p_period_type VARCHAR(50),
    p_tokens INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
    current_usage INTEGER := 0;
    limit_amount INTEGER := 0;
    user_subscription subscription_type;
    period_start_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get user subscription type
    SELECT subscription_type INTO user_subscription 
    FROM users WHERE user_id = p_user_id;
    
    -- Get the limit for this subscription type and period
    SELECT token_limit INTO limit_amount
    FROM token_limits 
    WHERE subscription_type = user_subscription AND limit_type = p_period_type;
    
    -- Calculate period start based on type
    CASE p_period_type
        WHEN 'monthly' THEN
            period_start_date := DATE_TRUNC('month', CURRENT_TIMESTAMP);
        WHEN 'weekly' THEN
            period_start_date := DATE_TRUNC('week', CURRENT_TIMESTAMP);
        WHEN 'daily' THEN
            period_start_date := DATE_TRUNC('day', CURRENT_TIMESTAMP);
    END CASE;
    
    -- Get current usage for this period
    SELECT COALESCE(total_tokens, 0) INTO current_usage
    FROM token_usage_summary
    WHERE user_id = p_user_id 
    AND period_type = p_period_type 
    AND period_start = period_start_date;
    
    -- Check if adding new tokens would exceed limit
    RETURN (current_usage + p_tokens) <= limit_amount;
END;
$$ LANGUAGE plpgsql;

-- SocketIO Connection Management Tables
-- Create enum for connection status
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type t 
        JOIN pg_namespace n ON t.typnamespace = n.oid 
        WHERE t.typname = 'socket_status' AND n.nspname = 'taskd'
    ) THEN
        CREATE TYPE socket_status AS ENUM ('active', 'inactive', 'disconnected');
    END IF;
END $$;

-- SocketIO connections table for managing active connections
CREATE TABLE IF NOT EXISTS socket_connections (
    id SERIAL PRIMARY KEY,
    socket_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    status socket_status DEFAULT 'active',
    client_ip VARCHAR(45), -- IPv6 support
    user_agent TEXT,
    connected_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_ping_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    disconnected_at TIMESTAMP WITH TIME ZONE,
    
    -- Foreign key constraints
    CONSTRAINT fk_socket_user_id FOREIGN KEY (user_id) REFERENCES users(user_id),
    CONSTRAINT fk_socket_company_id FOREIGN KEY (company_id) REFERENCES companies(company_id)
);

-- Chat sessions table for managing chat sessions with 1-hour timeout
CREATE TABLE IF NOT EXISTS chat_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    company_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'inactive', 'expired'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '1 hour'),
    closed_at TIMESTAMP WITH TIME ZONE,
    context_token_limit INTEGER DEFAULT 4000,
    current_token_count INTEGER DEFAULT 0,
    
    -- Foreign key constraints
    CONSTRAINT fk_session_user_id FOREIGN KEY (user_id) REFERENCES users(user_id),
    CONSTRAINT fk_session_company_id FOREIGN KEY (company_id) REFERENCES companies(company_id)
);

-- Chat messages table for storing chat history
CREATE TABLE IF NOT EXISTS chat_messages (
    id SERIAL PRIMARY KEY,
    message_id VARCHAR(255) UNIQUE NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    token_count INTEGER DEFAULT 0,
    metadata JSONB, -- Store additional metadata like model used, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_message_session_id FOREIGN KEY (session_id) REFERENCES chat_sessions(session_id) ON DELETE CASCADE,
    CONSTRAINT fk_message_user_id FOREIGN KEY (user_id) REFERENCES users(user_id),
    
    -- Check constraints
    CONSTRAINT check_message_role CHECK (role IN ('user', 'assistant', 'system'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_socket_connections_user_id ON socket_connections(user_id);
CREATE INDEX IF NOT EXISTS idx_socket_connections_session_id ON socket_connections(session_id);
CREATE INDEX IF NOT EXISTS idx_socket_connections_status ON socket_connections(status);
CREATE INDEX IF NOT EXISTS idx_socket_connections_connected_at ON socket_connections(connected_at);

CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_expires_at ON chat_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_last_activity ON chat_sessions(last_activity_at);

CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_role ON chat_messages(role);

-- Function to cleanup expired sessions and connections
CREATE OR REPLACE FUNCTION cleanup_expired_chat_data()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
BEGIN
    -- Update expired sessions
    UPDATE chat_sessions 
    SET status = 'expired', closed_at = CURRENT_TIMESTAMP
    WHERE status = 'active' 
    AND expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS expired_count = ROW_COUNT;
    
    -- Disconnect related socket connections
    UPDATE socket_connections 
    SET status = 'disconnected', disconnected_at = CURRENT_TIMESTAMP
    WHERE session_id IN (
        SELECT session_id FROM chat_sessions 
        WHERE status = 'expired'
    ) AND status = 'active';
    
    -- Clean up old expired sessions (older than 24 hours)
    DELETE FROM chat_sessions 
    WHERE status = 'expired' 
    AND closed_at < (CURRENT_TIMESTAMP - INTERVAL '24 hours');
    
    -- Clean up old disconnected socket connections (older than 24 hours)
    DELETE FROM socket_connections 
    WHERE status = 'disconnected' 
    AND disconnected_at < (CURRENT_TIMESTAMP - INTERVAL '24 hours');
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update session activity and extend expiry
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id VARCHAR(255))
RETURNS VOID AS $$
BEGIN
    UPDATE chat_sessions 
    SET last_activity_at = CURRENT_TIMESTAMP,
        expires_at = CURRENT_TIMESTAMP + INTERVAL '1 hour'
    WHERE session_id = p_session_id 
    AND status = 'active';
END;
$$ LANGUAGE plpgsql;

-- Function to enforce max 100 socket connections
CREATE OR REPLACE FUNCTION enforce_max_socket_connections()
RETURNS TRIGGER AS $$
DECLARE
    active_count INTEGER;
    oldest_socket_id VARCHAR(255);
BEGIN
    -- Count current active connections
    SELECT COUNT(*) INTO active_count 
    FROM socket_connections 
    WHERE status = 'active';
    
    -- If we exceed 100 connections, disconnect the oldest one
    IF active_count >= 100 THEN
        SELECT socket_id INTO oldest_socket_id
        FROM socket_connections 
        WHERE status = 'active'
        ORDER BY connected_at ASC
        LIMIT 1;
        
        -- Disconnect the oldest connection
        UPDATE socket_connections 
        SET status = 'disconnected', disconnected_at = CURRENT_TIMESTAMP
        WHERE socket_id = oldest_socket_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce max connections
DROP TRIGGER IF EXISTS socket_connection_limit_trigger ON socket_connections;
CREATE TRIGGER socket_connection_limit_trigger
    AFTER INSERT ON socket_connections
    FOR EACH ROW
    EXECUTE FUNCTION enforce_max_socket_connections();

-- Company Profile Tables
-- Create company profiles table for storing generated profiles
CREATE TABLE IF NOT EXISTS company_profiles (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_name VARCHAR(500) NOT NULL,
    industry VARCHAR(200) NOT NULL,
    company_scale VARCHAR(100),
    business_scope TEXT,
    location VARCHAR(500),
    established_year INTEGER,
    description TEXT,
    website VARCHAR(1000),
    additional_info JSONB,
    
    -- Profile Analysis Results (stored as JSONB for flexibility)
    business_capabilities JSONB NOT NULL,
    tender_matching JSONB NOT NULL,
    competitive_profile JSONB NOT NULL,
    international_capabilities JSONB NOT NULL,
    risk_tolerance JSONB NOT NULL,
    
    -- Metadata
    confidence_score INTEGER NOT NULL DEFAULT 75 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    model_used VARCHAR(100) NOT NULL DEFAULT 'company_profile_model_structured',
    strategy_version VARCHAR(20) NOT NULL DEFAULT 'v2.0.0',
    processing_time_ms BIGINT NOT NULL DEFAULT 0,
    
    -- Timestamps
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_profile_user_id FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Create indexes for company profiles
CREATE INDEX IF NOT EXISTS idx_company_profiles_user_id ON company_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_company_profiles_company_name ON company_profiles(company_name);
CREATE INDEX IF NOT EXISTS idx_company_profiles_industry ON company_profiles(industry);
CREATE INDEX IF NOT EXISTS idx_company_profiles_generated_at ON company_profiles(generated_at);
CREATE INDEX IF NOT EXISTS idx_company_profiles_confidence_score ON company_profiles(confidence_score);

-- Create GIN index for JSONB fields for efficient searching
CREATE INDEX IF NOT EXISTS idx_company_profiles_business_capabilities ON company_profiles USING GIN(business_capabilities);
CREATE INDEX IF NOT EXISTS idx_company_profiles_additional_info ON company_profiles USING GIN(additional_info);

-- =============================================================================
-- OVS-PROFILE SCHEMA - Overseas Profile Services
-- =============================================================================

-- Switch to ovs-profile schema for profile-related tables
SET search_path TO "ovs-profile", public;

-- Create company profiles table in ovs-profile schema
CREATE TABLE IF NOT EXISTS company_profiles (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    company_name VARCHAR(500) NOT NULL,
    industry VARCHAR(200) NOT NULL,
    company_scale VARCHAR(100),
    business_scope TEXT,
    location VARCHAR(500),
    established_year INTEGER,
    description TEXT,
    website VARCHAR(1000),
    additional_info JSONB,
    
    -- Profile Analysis Results (stored as JSONB for flexibility)
    business_capabilities JSONB NOT NULL,
    tender_matching JSONB NOT NULL,
    competitive_profile JSONB NOT NULL,
    international_capabilities JSONB NOT NULL,
    risk_tolerance JSONB NOT NULL,
    
    -- Metadata
    confidence_score INTEGER NOT NULL DEFAULT 75 CHECK (confidence_score >= 0 AND confidence_score <= 100),
    model_used VARCHAR(100) NOT NULL DEFAULT 'company_profile_model_structured',
    strategy_version VARCHAR(20) NOT NULL DEFAULT 'v2.0.0',
    processing_time_ms BIGINT NOT NULL DEFAULT 0,
    
    -- Timestamps
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for company profiles in ovs-profile schema
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_user_id ON company_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_company_name ON company_profiles(company_name);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_industry ON company_profiles(industry);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_generated_at ON company_profiles(generated_at);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_confidence_score ON company_profiles(confidence_score);

-- Create GIN indexes for JSONB fields for efficient searching
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_business_capabilities ON company_profiles USING GIN(business_capabilities);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_additional_info ON company_profiles USING GIN(additional_info);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_tender_matching ON company_profiles USING GIN(tender_matching);
CREATE INDEX IF NOT EXISTS idx_ovs_company_profiles_competitive_profile ON company_profiles USING GIN(competitive_profile);

-- Create updated_at trigger for company_profiles
CREATE OR REPLACE FUNCTION update_modified_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS company_profiles_updated_at ON company_profiles;
CREATE TRIGGER company_profiles_updated_at
    BEFORE UPDATE ON company_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_modified_time();

-- Create profile generation history table (optional for tracking multiple versions)
CREATE TABLE IF NOT EXISTS profile_generation_history (
    id SERIAL PRIMARY KEY,
    profile_id INTEGER REFERENCES company_profiles(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    company_name VARCHAR(500) NOT NULL,
    request_params JSONB NOT NULL, -- Store original request parameters
    generation_metadata JSONB NOT NULL, -- Store generation metadata like tokens used, model info, etc.
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_ovs_profile_history_profile_id ON profile_generation_history(profile_id);
CREATE INDEX IF NOT EXISTS idx_ovs_profile_history_user_id ON profile_generation_history(user_id);
CREATE INDEX IF NOT EXISTS idx_ovs_profile_history_generated_at ON profile_generation_history(generated_at);

-- Reset search_path to default for the rest of the schema
SET search_path TO taskd, public;