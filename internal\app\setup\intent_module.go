package setup

import (
	"fmt"

	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// IntentModule 封装了意图识别模块的依赖
type IntentModule struct {
	IntentHandler *handlers.IntentHandler
	IntentService *services.IntentRecognitionService
}

// NewIntentModule 创建并返回一个新的 IntentModule
func NewIntentModule(deps AppDependencies) (*IntentModule, error) {
	// 从配置中获取默认的 LLM Model ID
	llmCfg := deps.Config.LLM // LLMConfigManager
	providerName := llmCfg.DefaultProvider
	if providerName == "" {
		if len(llmCfg.Providers) > 0 {
			for name := range llmCfg.Providers {
				providerName = name
				utils.Log.Warnf("LLM default_provider 未在配置中设置，IntentModule 将使用第一个可用的 provider: %s", providerName)
				break
			}
		} else {
			return nil, fmt.Errorf("LLM 配置错误: 未设置 default_provider 且没有可用的 providers")
		}
	}

	providerCfg, ok := llmCfg.Providers[providerName]
	if !ok {
		return nil, fmt.Errorf("LLM 配置错误: default_provider '%s' 在 providers 列表中未找到", providerName)
	}
	defaultModelID := providerCfg.DefaultModelID
	if defaultModelID == "" {
		// 可以考虑一个更通用的默认值，或者要求必须配置
		utils.Log.Warnf("LLM Provider '%s' 的 DefaultModelID 未设置，IntentModule 将可能无法正常工作。", providerName)
		// return nil, fmt.Errorf("LLM Provider '%s' 的 DefaultModelID 未设置", providerName) // 或者直接报错
	}

	// 定义 prompts 目录的路径。
	// NewPromptManager 在 promptsDir 为空时默认为 "prompts"，这是相对于运行时的当前目录。
	// 为了 IntentRecognitionService 能正确找到模板，我们需要一个更可靠的路径。
	// 假设我们的 .tmpl 文件总是位于 "internal/prompts" 目录下，相对于项目根目录下的 taskd 目录。
	// 如果程序从 taskd/ 目录运行，则路径是 "internal/prompts"
	// 如果程序从项目根目录运行 (例如 go run ./taskd), 那么就需要是 "taskd/internal/prompts"
	// 最好的做法是在配置中明确指定这个目录，或者 AppDependencies 提供一个 ProjectRoot/BasePath。
	// 此处暂时硬编码为相对于 taskd 模块的路径，需要根据实际部署结构调整。
	// promptsDirForService := "internal/prompts" // 相对于 taskd/ 目录
	// 如果 Prompts 配置中包含一个明确的目录，可以使用它:
	// if deps.Config.PromptsConfig != nil && deps.Config.PromptsConfig.Directory != "" {
	// promptsDirForService = deps.Config.PromptsConfig.Directory
	// }

	intentService := services.NewIntentRecognitionService(deps.LLMClient, defaultModelID)
	intentHandler := handlers.NewIntentHandler(intentService)

	return &IntentModule{
		IntentHandler: intentHandler,
		IntentService: intentService,
	}, nil
}
