package core

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"github.com/sirupsen/logrus"
)

// BaseAgent 基础Agent实现
type BaseAgent struct {
	ID           string
	Type         models.AgentType
	Name         string
	Description  string
	Capabilities []models.AgentCapability
	Config       map[string]interface{}
	LLMClient    llm.LLMClient
	Logger       *logrus.Logger
	Started      bool
	StartedAt    time.Time
	LastPing     time.Time
}

// NewBaseAgent 创建基础Agent
func NewBaseAgent(id string, agentType models.AgentType, name string, description string, capabilities []models.AgentCapability, llmClient llm.LLMClient) *BaseAgent {
	return &BaseAgent{
		ID:           id,
		Type:         agentType,
		Name:         name,
		Description:  description,
		Capabilities: capabilities,
		Config:       make(map[string]interface{}),
		LLMClient:    llmClient,
		Logger:       utils.Log, // 使用统一的logger
		Started:      false,
	}
}

// GetID 获取Agent ID
func (a *BaseAgent) GetID() string {
	return a.ID
}

// GetType 获取Agent类型
func (a *BaseAgent) GetType() models.AgentType {
	return a.Type
}

// GetCapabilities 获取Agent能力列表
func (a *BaseAgent) GetCapabilities() []models.AgentCapability {
	return a.Capabilities
}

// Execute 执行Agent能力 - 基础实现，子类需要重写
func (a *BaseAgent) Execute(ctx context.Context, capability string, input map[string]interface{}, config map[string]interface{}) (*models.AgentResponse, error) {
	start := time.Now()
	
	// 检查能力是否存在
	if !a.hasCapability(capability) {
		return &models.AgentResponse{
			ID:        fmt.Sprintf("resp_%d", time.Now().UnixNano()),
			RequestID: fmt.Sprintf("req_%d", time.Now().UnixNano()),
			Success:   false,
			Error:     fmt.Sprintf("capability '%s' not found", capability),
			Duration:  time.Since(start).Milliseconds(),
			CreatedAt: time.Now(),
		}, nil
	}
	
	// 记录日志
	a.Logger.WithFields(logrus.Fields{
		"agent_id":   a.ID,
		"capability": capability,
		"input":      input,
	}).Info("执行Agent能力")
	
	// 基础实现返回未实现错误
	return &models.AgentResponse{
		ID:        fmt.Sprintf("resp_%d", time.Now().UnixNano()),
		RequestID: fmt.Sprintf("req_%d", time.Now().UnixNano()),
		Success:   false,
		Error:     "execute method not implemented",
		Duration:  time.Since(start).Milliseconds(),
		CreatedAt: time.Now(),
	}, nil
}

// HealthCheck 健康检查
func (a *BaseAgent) HealthCheck(ctx context.Context) (*models.AgentHealthCheck, error) {
	healthy := true
	message := "Agent is healthy"
	
	// 检查是否启动
	if !a.Started {
		healthy = false
		message = "Agent is not started"
	}
	
	// 检查最后ping时间
	if time.Since(a.LastPing) > time.Minute*5 {
		healthy = false
		message = "Agent has not pinged for more than 5 minutes"
	}
	
	// 检查LLM客户端
	if a.LLMClient == nil {
		healthy = false
		message = "LLM client is not initialized"
	}
	
	status := models.AgentStatusIdle
	if !healthy {
		status = models.AgentStatusError
	}
	
	return &models.AgentHealthCheck{
		AgentID:   a.ID,
		Status:    status,
		Healthy:   healthy,
		Message:   message,
		CheckedAt: time.Now(),
	}, nil
}

// Initialize 初始化Agent
func (a *BaseAgent) Initialize(ctx context.Context, config map[string]interface{}) error {
	a.Config = config
	a.Started = true
	a.StartedAt = time.Now()
	a.LastPing = time.Now()
	
	a.Logger.WithFields(logrus.Fields{
		"agent_id": a.ID,
		"config":   config,
	}).Info("Agent初始化完成")
	
	return nil
}

// Shutdown 关闭Agent
func (a *BaseAgent) Shutdown(ctx context.Context) error {
	a.Started = false
	
	a.Logger.WithFields(logrus.Fields{
		"agent_id": a.ID,
	}).Info("Agent已关闭")
	
	return nil
}

// UpdatePing 更新ping时间
func (a *BaseAgent) UpdatePing() {
	a.LastPing = time.Now()
}

// hasCapability 检查是否具有指定能力
func (a *BaseAgent) hasCapability(capability string) bool {
	for _, cap := range a.Capabilities {
		if cap.Name == capability {
			return true
		}
	}
	return false
}

// GetCapability 获取指定能力
func (a *BaseAgent) GetCapability(capability string) *models.AgentCapability {
	for _, cap := range a.Capabilities {
		if cap.Name == capability {
			return &cap
		}
	}
	return nil
}

// ValidateInput 验证输入参数
func (a *BaseAgent) ValidateInput(capability string, input map[string]interface{}) error {
	cap := a.GetCapability(capability)
	if cap == nil {
		return fmt.Errorf("capability '%s' not found", capability)
	}
	
	// 检查必需参数
	for _, required := range cap.Required {
		if _, exists := input[required]; !exists {
			return fmt.Errorf("required parameter '%s' is missing", required)
		}
	}
	
	return nil
}

// SetConfig 设置配置
func (a *BaseAgent) SetConfig(key string, value interface{}) {
	if a.Config == nil {
		a.Config = make(map[string]interface{})
	}
	a.Config[key] = value
}

// GetConfig 获取配置
func (a *BaseAgent) GetConfig(key string) (interface{}, bool) {
	if a.Config == nil {
		return nil, false
	}
	value, exists := a.Config[key]
	return value, exists
}

// GetConfigString 获取字符串配置
func (a *BaseAgent) GetConfigString(key string, defaultValue string) string {
	if value, exists := a.GetConfig(key); exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return defaultValue
}

// GetConfigInt 获取整数配置
func (a *BaseAgent) GetConfigInt(key string, defaultValue int) int {
	if value, exists := a.GetConfig(key); exists {
		if i, ok := value.(int); ok {
			return i
		}
		if f, ok := value.(float64); ok {
			return int(f)
		}
	}
	return defaultValue
}

// GetConfigBool 获取布尔配置
func (a *BaseAgent) GetConfigBool(key string, defaultValue bool) bool {
	if value, exists := a.GetConfig(key); exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return defaultValue
}