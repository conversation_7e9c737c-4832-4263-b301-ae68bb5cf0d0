# TaskD 服务器手动测试步骤

在Ubuntu服务器上手动执行测试脚本，连接到Windows本地机器的TaskD服务。

## 📋 测试步骤

### 1. 环境准备

```bash
# 确保在项目根目录
cd /path/to/taskd

# 检查必要文件是否存在
ls -la run-tests.sh quick-start-test.sh
ls -la docker/test/Dockerfile*
```

### 2. 验证网络连通性

```bash
# 检查到Windows机器的连通性
ping **************

# 检查TaskD服务端口
telnet ************** 8601
# 或者
nc -z ************** 8601

# 检查HTTP服务
curl -v http://**************:8601/healthz
```

### 3. 构建测试镜像

```bash
# 构建优化版本镜像 (推荐)
./run-tests.sh build --size optimized

# 或者构建超精简版本
./run-tests.sh build --size ultra-slim

# 或者构建原始版本
./run-tests.sh build --size original
```
docker build --no-cache -f docker/test/Dockerfile.optimized -t taskd-test:latest .

### 4. 运行测试

```bash
# 运行P0级别测试 (最关键)
./run-tests.sh test p0

# 如果P0通过，运行P1测试
./run-tests.sh test p1

# 运行完整测试套件
./run-tests.sh test

# 运行特定模块测试
./run-tests.sh test bidding
./run-tests.sh test entity
```

### 5. 一键快速测试

```bash
# 使用快速启动脚本 (自动执行P0->P1->全量测试)
./quick-start-test.sh
```

### 6. 调试模式

```bash
# 进入测试容器调试
./run-tests.sh shell

# 在容器内手动检查
ping **************
curl http://**************:8601/healthz
uv run pytest test/test_bidding_agents.py -v
```

### 7. 查看测试结果

```bash
# 查看测试日志
ls -la test/logs/
tail -f test/logs/test_run_*.log

# 查看测试报告
ls -la test/reports/
```

### 8. 环境清理

```bash
# 清理Docker环境
./run-tests.sh clean

# 查看Docker镜像
docker images | grep taskd-test
```

## 🔧 常用命令组合

### 完整测试流程
```bash
./run-tests.sh build --size optimized
./run-tests.sh test p0
./run-tests.sh test p1  
./run-tests.sh test
```

### 快速验证流程
```bash
./quick-start-test.sh
```

### 故障排除流程
```bash
./run-tests.sh shell
# 在容器内调试...
```

## 📊 预期结果

- **P0测试**: 必须100%通过 (连通性测试)
- **P1测试**: ≥95%通过 (核心功能)
- **完整测试**: 根据模块不同有不同通过率要求

## 🎯 镜像大小对比

| 版本 | 描述 | 适用场景 |
|------|------|----------|
| `original` | 完整功能，包含所有开发工具 | 开发调试 |
| `optimized` | 平衡体积和功能性 | 测试环境 (推荐) |
| `ultra-slim` | 最小体积，distroless基础 | 生产环境 |

## 🔧 故障排除

### 网络连接问题
```bash
# 检查网络连通性
ping **************
nc -z ************** 8601
curl -v http://**************:8601/healthz
```

### Docker构建问题
```bash
# 清理Docker缓存
docker system prune -f
./run-tests.sh build --size optimized --no-cache
```

### 测试失败问题
```bash
# 查看详细日志
./run-tests.sh test p0 --verbose
tail -f test/logs/test_run_*.log
```

## 📝 环境变量

```bash
# 设置TaskD服务地址
export TASKD_BASE_URL=http://你的IP:8601

# 启用详细日志
export TEST_VERBOSE=true

# 设置镜像大小类型
export DOCKER_SIZE=optimized

# 运行测试
./run-tests.sh test
```

按照以上步骤顺序执行即可开始测试！