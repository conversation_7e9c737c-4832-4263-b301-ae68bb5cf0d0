# Stage 1: 构建 Go 应用
FROM golang:1.23-alpine AS builder

# 安装git和其他构建依赖
RUN apk --no-cache add git ca-certificates

WORKDIR /app

# 设置Go代理，使用国内镜像加速下载
ENV GOPROXY=https://proxy.golang.org,direct
ENV GOSUMDB=sum.golang.google.cn
# 设置私有模块，不通过代理下载
ENV GOPRIVATE=gitlab.com/specific-ai/*
# 临时禁用SSL验证（仅用于构建）
ENV GIT_SSL_NO_VERIFY=1

# 先复制所有源代码（这样Go就知道这些是本地模块）
COPY . .

# 整理依赖和下载外部依赖（带重试机制）
RUN for i in 1 2 3; do \
        go mod tidy && go mod download && break || \
        (echo "尝试 $i 失败，等待重试..." && sleep 5); \
    done

# 构建应用（跳过模块验证，因为有本地模块）
RUN CGO_ENABLED=0 GOOS=linux go build -mod=mod -ldflags="-s -w" -o /taskd ./cmd/taskd/main.go

# Stage 2: 创建最终的轻量级镜像
FROM alpine:latest

# 调试模式：添加完整的开发工具集
# ca-certificates: 支持 HTTPS 调用 (LLM API)
# postgresql-client: 数据库连接工具
# 调试工具：bash, vim, curl, wget, git
# Go 编译器：用于容器内重新编译
# tzdata: 时区支持
RUN apk --no-cache add ca-certificates postgresql-client bash vim curl wget git go tzdata

WORKDIR /app

# 从 builder 阶段复制构建好的二进制文件
COPY --from=builder /taskd /app/taskd

# 复制源代码用于调试
COPY . /app/

# 复制调试脚本并设置权限
# COPY k8s/debug-taskd.sh k8s/quick-restart.sh /app/
# RUN chmod +x /app/debug-taskd.sh /app/quick-restart.sh /app/taskd

# 暴露应用监听的端口
EXPOSE 8601

# 容器启动命令
CMD ["/app/taskd"]