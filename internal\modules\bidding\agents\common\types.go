package common

import (
	"time"
)

// PreprocessingStatus represents the processing status of tender data
type PreprocessingStatus string

const (
	StatusUnprocessed PreprocessingStatus = "unprocessed"
	StatusProcessing  PreprocessingStatus = "processing"
	StatusCompleted   PreprocessingStatus = "completed"
	StatusFailed      PreprocessingStatus = "failed"
	StatusRetry       PreprocessingStatus = "retry"
)

// TenderData represents the structured tender data after extraction
type TenderData struct {
	BasicInfo           BasicInfo           `json:"basic_info"`
	Organization        Organization        `json:"organization"`
	TenderRequirements  TenderRequirements  `json:"tender_requirements"`
	SupplierRequirements SupplierRequirements `json:"supplier_requirements"`
}

// BasicInfo contains basic project information
type BasicInfo struct {
	ProjectName   string `json:"project_name"`
	ProjectNumber string `json:"project_number"`
	Deadline      string `json:"deadline"`
	Budget        string `json:"budget"`
	ContactInfo   string `json:"contact_info"`
}

// Organization contains purchaser and agent information
type Organization struct {
	PurchaserName    string `json:"purchaser_name"`
	PurchaserAddress string `json:"purchaser_address"`
	AgentName        string `json:"agent_name"`
	AgentContact     string `json:"agent_contact"`
}

// TenderRequirements contains project requirements
type TenderRequirements struct {
	TechnicalRequirements  []string `json:"technical_requirements"`
	CommercialRequirements []string `json:"commercial_requirements"`
	DeliveryRequirements   string   `json:"delivery_requirements"`
	PerformanceRequirements string  `json:"performance_requirements"`
}

// SupplierRequirements contains supplier requirements
type SupplierRequirements struct {
	QualificationRequirements []string `json:"qualification_requirements"`
	FinancialRequirements     string   `json:"financial_requirements"`
	ExperienceRequirements    string   `json:"experience_requirements"`
	CertificationRequirements []string `json:"certification_requirements"`
}

// ValidationResult represents data validation result
type ValidationResult struct {
	Status           string   `json:"status"`
	CompletenessScore float64  `json:"completeness_score"`
	MissingFields    []string `json:"missing_fields"`
	ValidationErrors []string `json:"validation_errors"`
}

// IndustryClassification represents industry classification result
type IndustryClassification struct {
	Level1           string             `json:"level1"`
	Level2           string             `json:"level2"`
	Level3           string             `json:"level3"`
	ConfidenceScores ConfidenceScores   `json:"confidence_scores"`
}

// ConfidenceScores contains confidence scores for each level
type ConfidenceScores struct {
	Level1 float64 `json:"level1"`
	Level2 float64 `json:"level2"`
	Level3 float64 `json:"level3"`
}

// ProcurementType represents procurement type classification
type ProcurementType struct {
	MainType        string   `json:"main_type"`
	SubTypes        []string `json:"sub_types"`
	ConfidenceScore float64  `json:"confidence_score"`
}

// BusinessDomain represents business domain classification
type BusinessDomain struct {
	PrimaryDomain     string   `json:"primary_domain"`
	SecondaryDomains  []string `json:"secondary_domains"`
	DomainTags        []string `json:"domain_tags"`
}

// Keywords represents extracted keywords
type Keywords struct {
	TechnicalKeywords []string `json:"technical_keywords"`
	BusinessKeywords  []string `json:"business_keywords"`
	CoreProducts      []string `json:"core_products"`
}

// OptimizedTitle represents title optimization result
type OptimizedTitle struct {
	OriginalTitle       string `json:"original_title"`
	NewTitle            string `json:"new_title"`
	OptimizationReason  string `json:"optimization_reason"`
}

// QualityScore represents quality scoring result
type QualityScore struct {
	OverallScore     float64           `json:"overall_score"`
	DimensionScores  DimensionScores   `json:"dimension_scores"`
	ScoringReasons   []string          `json:"scoring_reasons"`
}

// DimensionScores contains scores for each dimension
type DimensionScores struct {
	ProjectValue            float64 `json:"project_value"`
	SupplierPreference      float64 `json:"supplier_preference"`
	ClientAuthority         float64 `json:"client_authority"`
	CooperationPotential    float64 `json:"cooperation_potential"`
	InformationCompleteness float64 `json:"information_completeness"`
}

// Summary represents content summary
type Summary struct {
	ExecutiveSummary string   `json:"executive_summary"`
	KeyPoints        []string `json:"key_points"`
	RiskFactors      []string `json:"risk_factors"`
}

// TranslationData represents translated data
type TranslationData struct {
	Language string                 `json:"language"`
	Data     map[string]interface{} `json:"data"`
}

// TranslationQuality represents translation quality metrics
type TranslationQuality struct {
	OverallQuality float64            `json:"overall_quality"`
	FieldQuality   map[string]float64 `json:"field_quality"`
	QualityIssues  []string           `json:"quality_issues"`
}

// ProcessingMetadata contains processing metadata
type ProcessingMetadata struct {
	ModelUsed      string  `json:"model_used"`
	TokensConsumed int     `json:"tokens_consumed"`
	ProcessingTime float64 `json:"processing_time"`
	ConfidenceScore float64 `json:"confidence_score,omitempty"`
}

// PreprocessingResult represents the final preprocessing result
type PreprocessingResult struct {
	ID                string                       `json:"id"`
	OriginalText      string                       `json:"original_text"`
	ExtractedData     *TenderData                  `json:"extracted_data"`
	Classification    *ClassificationResult        `json:"classification"`
	Enhancement       *EnhancementResult           `json:"enhancement"`
	Multilingual      map[string]*TranslationData  `json:"multilingual"`
	ProcessingSummary *ProcessingSummary           `json:"processing_summary"`
}

// ClassificationResult represents classification results
type ClassificationResult struct {
	IndustryClassification *IndustryClassification `json:"industry_classification"`
	ProcurementType        *ProcurementType        `json:"procurement_type"`
	BusinessDomain         *BusinessDomain         `json:"business_domain"`
	ProcessingMetadata     *ProcessingMetadata     `json:"processing_metadata"`
}

// EnhancementResult represents content enhancement results
type EnhancementResult struct {
	Keywords        *Keywords           `json:"keywords"`
	OptimizedTitle  *OptimizedTitle     `json:"optimized_title"`
	QualityScore    *QualityScore       `json:"quality_score"`
	Summary         *Summary            `json:"summary"`
	ProcessingMetadata *ProcessingMetadata `json:"processing_metadata"`
}

// ProcessingSummary contains overall processing summary
type ProcessingSummary struct {
	TotalProcessingTime float64  `json:"total_processing_time"`
	AgentsInvolved      []string `json:"agents_involved"`
	SuccessRate         float64  `json:"success_rate"`
	OverallConfidence   float64  `json:"overall_confidence"`
}

// OrchestrationStep represents a single processing step
type OrchestrationStep struct {
	Agent    string  `json:"agent"`
	Skill    string  `json:"skill"`
	Status   string  `json:"status"`
	Duration float64 `json:"duration"`
	Error    string  `json:"error,omitempty"`
}

// OrchestrationItemResult represents processing result for a single item
type OrchestrationItemResult struct {
	InputID         string               `json:"input_id"`
	Status          string               `json:"status"`
	Result          *PreprocessingResult `json:"result,omitempty"`
	Error           string               `json:"error,omitempty"`
	ProcessingSteps []*OrchestrationStep `json:"processing_steps"`
}

// OrchestrationResult represents the overall orchestration result
type OrchestrationResult struct {
	TotalItems      int                        `json:"total_items"`
	ProcessedItems  int                        `json:"processed_items"`
	SuccessfulItems int                        `json:"successful_items"`
	FailedItems     int                        `json:"failed_items"`
	ProcessingTime  float64                    `json:"processing_time"`
	Results         []*OrchestrationItemResult `json:"results"`
}

// DatabaseQueryConditions represents database query conditions
type DatabaseQueryConditions struct {
	TimeRange        *TimeRange         `json:"time_range,omitempty"`
	CrawlerNames     []string           `json:"crawler_names,omitempty"`
	CrawlModes       []string           `json:"crawl_modes,omitempty"`
	Status           []string           `json:"status,omitempty"`
	HasRawContent    *bool              `json:"has_raw_content,omitempty"`
	ErrorThreshold   *int               `json:"error_threshold,omitempty"`
	CustomFilters    map[string]interface{} `json:"custom_filters,omitempty"`
}

// TimeRange represents a time range for queries
type TimeRange struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	TimeField string `json:"time_field"`
}

// PaginationConfig represents pagination configuration
type PaginationConfig struct {
	Page   int `json:"page"`
	Limit  int `json:"limit"`
	Offset int `json:"offset,omitempty"`
}

// SortingConfig represents sorting configuration
type SortingConfig struct {
	Field string `json:"field"`
	Order string `json:"order"`
}

// FieldProjection represents field projection configuration
type FieldProjection struct {
	Include []string `json:"include,omitempty"`
	Exclude []string `json:"exclude,omitempty"`
}

// QueryResult represents database query result
type QueryResult struct {
	Data       []map[string]interface{} `json:"data"`
	Pagination *PaginationInfo          `json:"pagination"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	CurrentPage int  `json:"current_page"`
	TotalPages  int  `json:"total_pages"`
	PageSize    int  `json:"page_size"`
	TotalCount  int  `json:"total_count"`
	HasNext     bool `json:"has_next"`
	HasPrev     bool `json:"has_prev"`
}

// CandidateData represents candidate data for preprocessing
type CandidateData struct {
	ID             string                 `json:"_id"`
	Title          string                 `json:"title"`
	RawContent     string                 `json:"raw_content"`
	CrawlerName    string                 `json:"crawler_name"`
	CrawlTime      time.Time              `json:"crawl_time"`
	PriorityScore  float64                `json:"priority_score"`
	ContentQuality map[string]interface{} `json:"content_quality"`
}

// UpdateRequest represents status update request
type UpdateRequest struct {
	RecordIDs      []string               `json:"record_ids"`
	Updates        map[string]interface{} `json:"updates"`
	BatchOperation bool                   `json:"batch_operation"`
}

// UpdateResult represents update operation result
type UpdateResult struct {
	MatchedCount  int      `json:"matched_count"`
	ModifiedCount int      `json:"modified_count"`
	FailedUpdates []string `json:"failed_updates"`
}