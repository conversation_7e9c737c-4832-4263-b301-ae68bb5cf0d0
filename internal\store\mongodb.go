package store

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	// "go.mongodb.org/mongo-driver/bson/primitive" // 如果需要用到 ObjectID
)

var MongoClient *mongo.Client // MongoDB 客户端实例
var MongoDB *mongo.Database   // MongoDB 数据库实例

// InitMongoDB 初始化 MongoDB 连接
func InitMongoDB(cfg config.MongoDBConfig) error {
	if cfg.URI == "" {
		utils.Log.Error("MongoDB URI 未配置。")
		return fmt.Errorf("MongoDB URI 未配置")
	}
	ctx, cancel := context.WithTimeout(context.Background(), cfg.GetDBTimeout())
	defer cancel()

	clientOptions := options.Client().
		ApplyURI(cfg.URI).
		SetMaxPoolSize(20).                             // 最大连接池大小
		SetMinPoolSize(5).                              // 最小连接池大小
		SetMaxConnIdleTime(30 * time.Second).           // 连接空闲时间
		SetConnectTimeout(10 * time.Second).            // 连接超时
		SetSocketTimeout(60 * time.Second).             // Socket超时 - 增加到60秒以应对网络延迟
		SetServerSelectionTimeout(10 * time.Second).    // 服务器选择超时
		SetHeartbeatInterval(10 * time.Second)          // 心跳间隔
	
	utils.Log.Infof("Connecting to MongoDB with URI: %s", cfg.URI)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		utils.Log.Errorf("连接 MongoDB 失败: %v", err)
		return fmt.Errorf("mongo.Connect 错误: %w", err)
	}

	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		utils.Log.Errorf("Ping MongoDB 失败: %v", err)
		return fmt.Errorf("client.Ping 错误: %w", err)
	}

	MongoClient = client
	MongoDB = client.Database(cfg.Database)
	utils.Log.Infof("成功连接到 MongoDB 数据库: %s", cfg.Database)
	return nil
}

// CloseMongoDB 关闭 MongoDB 连接
func CloseMongoDB() {
	if MongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		if err := MongoClient.Disconnect(ctx); err != nil {
			utils.Log.Errorf("断开 MongoDB 连接失败: %v", err)
		} else {
			utils.Log.Info("MongoDB 连接已关闭。")
		}
	}
}

// GetCollection 获取 MongoDB 集合
func GetCollection(collectionName string) *mongo.Collection {
	if MongoDB == nil {
		utils.Log.Fatal("MongoDB 未初始化。请先调用 InitMongoDB。")
		return nil
	}
	return MongoDB.Collection(collectionName)
}

// MongoStore MongoDB存储实现
type MongoStore struct {
	Client *mongo.Client
	DB     *mongo.Database
}

// NewMongoStore 创建MongoDB存储实例
func NewMongoStore(client *mongo.Client, dbName string) *MongoStore {
	return &MongoStore{
		Client: client,
		DB:     client.Database(dbName),
	}
}

// GetMongoClient 返回MongoDB客户端实例
func (ms *MongoStore) GetMongoClient() *mongo.Client {
	return ms.Client
}

// SaveAgent 实现Store接口
func (ms *MongoStore) SaveAgent(ctx context.Context, agent *models.AgentInstance) error {
	// TODO: 实现具体的数据库操作
	return nil
}

func (ms *MongoStore) GetAgent(ctx context.Context, id string) (*models.AgentInstance, error) {
	// TODO: 实现具体的数据库操作
	return nil, nil
}

func (ms *MongoStore) UpdateAgent(ctx context.Context, agent *models.AgentInstance) error {
	// TODO: 实现具体的数据库操作
	return nil
}

func (ms *MongoStore) DeleteAgent(ctx context.Context, id string) error {
	// TODO: 实现具体的数据库操作
	return nil
}

func (ms *MongoStore) ListAgents(ctx context.Context, filter models.AgentListRequest) ([]models.AgentInstance, error) {
	// TODO: 实现具体的数据库操作
	return nil, nil
}

func (ms *MongoStore) SaveExecution(ctx context.Context, execution *models.AgentExecution) error {
	// TODO: 实现具体的数据库操作
	return nil
}

func (ms *MongoStore) GetExecution(ctx context.Context, id string) (*models.AgentExecution, error) {
	// TODO: 实现具体的数据库操作
	return nil, nil
}

func (ms *MongoStore) ListExecutions(ctx context.Context, agentID string, limit int) ([]models.AgentExecution, error) {
	// TODO: 实现具体的数据库操作
	return nil, nil
}

func (ms *MongoStore) HealthCheck(ctx context.Context) error {
	return ms.Client.Ping(ctx, nil)
}

func (ms *MongoStore) Close() error {
	return ms.Client.Disconnect(context.Background())
}
