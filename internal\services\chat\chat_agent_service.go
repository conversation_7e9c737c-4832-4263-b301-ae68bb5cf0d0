package chat

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.com/specific-ai/taskd/internal/interfaces"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ChatAgentService 闲聊Agent服务
type ChatAgentService struct {
	llmClient      llm.LLMClient
	sessionService *ChatSessionService
	messageService *ChatMessageService
	tokenService   interfaces.TokenServiceInterface
	config         *models.ChatConfig
}

// NewChatAgentService 创建闲聊Agent服务
func NewChatAgentService(
	llmClient llm.LLMClient,
	sessionService *ChatSessionService,
	messageService *ChatMessageService,
	tokenService interfaces.TokenServiceInterface,
	config *models.ChatConfig,
) *ChatAgentService {
	return &ChatAgentService{
		llmClient:      llmClient,
		sessionService: sessionService,
		messageService: messageService,
		tokenService:   tokenService,
		config:         config,
	}
}

// ProcessChatMessage 处理聊天消息
func (s *ChatAgentService) ProcessChatMessage(ctx context.Context, req *models.ChatMessageRequest) (*models.ChatMessageResponse, error) {
	// 验证会话
	session, err := s.sessionService.GetSession(ctx, req.SessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	if session.Status != models.ChatSessionStatusActive {
		return nil, fmt.Errorf("session is not active: %s", session.Status)
	}

	// 检查会话是否过期
	if time.Now().After(session.ExpiresAt) {
		// 标记会话为过期
		err = s.sessionService.CloseSession(ctx, req.SessionID)
		if err != nil {
			utils.Log.Errorf("Failed to close expired session: %v", err)
		}
		return nil, fmt.Errorf("session has expired")
	}

	// 更新会话活动时间
	err = s.sessionService.UpdateSessionActivity(ctx, req.SessionID)
	if err != nil {
		utils.Log.Errorf("Failed to update session activity: %v", err)
	}

	// 保存用户消息
	userMessage, err := s.messageService.SaveMessage(ctx,
		req.SessionID,
		session.UserID,
		models.MessageRoleUser,
		req.Content,
		s.estimateTokenCount(req.Content),
		req.Metadata,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to save user message: %w", err)
	}

	// 获取对话历史并构建LLM请求
	messages, err := s.buildConversationContext(ctx, req.SessionID, session.ContextTokenLimit)
	if err != nil {
		return nil, fmt.Errorf("failed to build conversation context: %w", err)
	}

	// 调用LLM生成回复
	llmResponse, err := s.generateResponse(ctx, messages, session.UserID, session.CompanyID)
	if err != nil {
		return nil, fmt.Errorf("failed to generate response: %w", err)
	}

	// 保存AI回复
	assistantTokenCount := s.estimateTokenCount(llmResponse)
	assistantMessage, err := s.messageService.SaveMessage(ctx,
		req.SessionID,
		session.UserID,
		models.MessageRoleAssistant,
		llmResponse,
		assistantTokenCount,
		map[string]interface{}{
			"model_provider": s.config.ModelProvider,
			"model_alias":    s.config.ModelAlias,
			"temperature":    s.config.Temperature,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to save assistant message: %w", err)
	}

	// 更新会话token计数
	totalTokens := userMessage.TokenCount + assistantMessage.TokenCount
	err = s.sessionService.UpdateTokenCount(ctx, req.SessionID, totalTokens)
	if err != nil {
		utils.Log.Errorf("Failed to update session token count: %v", err)
	}

	// 检查是否需要修剪上下文
	if session.CurrentTokenCount+totalTokens > session.ContextTokenLimit {
		err = s.messageService.TrimSessionContext(ctx, req.SessionID, session.ContextTokenLimit)
		if err != nil {
			utils.Log.Errorf("Failed to trim session context: %v", err)
		}
	}

	// 记录token消费（如果启用）
	if s.config.EnableTokenTracking && s.tokenService != nil {
		s.recordTokenConsumption(ctx, session.UserID, session.CompanyID, userMessage.TokenCount+assistantMessage.TokenCount)
	}

	return &models.ChatMessageResponse{
		MessageID:  assistantMessage.MessageID,
		SessionID:  assistantMessage.SessionID,
		Role:       assistantMessage.Role,
		Content:    assistantMessage.Content,
		TokenCount: assistantMessage.TokenCount,
		Metadata:   assistantMessage.Metadata,
		CreatedAt:  assistantMessage.CreatedAt,
	}, nil
}

// GetChatHistory 获取聊天历史
func (s *ChatAgentService) GetChatHistory(ctx context.Context, req *models.ChatHistoryRequest) (*models.ChatHistoryResponse, error) {
	// 验证会话
	_, err := s.sessionService.GetSession(ctx, req.SessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	limit := 50
	if req.Limit != nil && *req.Limit > 0 && *req.Limit <= 100 {
		limit = *req.Limit
	}

	offset := 0
	if req.Offset != nil && *req.Offset >= 0 {
		offset = *req.Offset
	}

	// 获取消息
	messages, err := s.messageService.GetSessionMessages(ctx, req.SessionID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get session messages: %w", err)
	}

	// 获取总消息数
	totalCount, err := s.messageService.GetSessionMessageCount(ctx, req.SessionID)
	if err != nil {
		utils.Log.Errorf("Failed to get total message count: %v", err)
		totalCount = len(messages) // fallback
	}

	// 转换为响应格式
	var responseMessages []models.ChatMessageResponse
	for _, msg := range messages {
		responseMessages = append(responseMessages, models.ChatMessageResponse{
			MessageID:  msg.MessageID,
			SessionID:  msg.SessionID,
			Role:       msg.Role,
			Content:    msg.Content,
			TokenCount: msg.TokenCount,
			Metadata:   msg.Metadata,
			CreatedAt:  msg.CreatedAt,
		})
	}

	return &models.ChatHistoryResponse{
		SessionID: req.SessionID,
		Messages:  responseMessages,
		Total:     totalCount,
		HasMore:   offset+len(messages) < totalCount,
	}, nil
}

// buildConversationContext 构建对话上下文
func (s *ChatAgentService) buildConversationContext(ctx context.Context, sessionID string, tokenLimit int) ([]common.LLMMessage, error) {
	// 获取会话消息
	messages, err := s.messageService.GetSessionMessages(ctx, sessionID, 0, 0) // 获取所有消息
	if err != nil {
		return nil, fmt.Errorf("failed to get session messages: %w", err)
	}

	var llmMessages []common.LLMMessage

	// 添加系统提示词
	if s.config.SystemPrompt != "" {
		llmMessages = append(llmMessages, common.LLMMessage{
			Role:    "system",
			Content: s.config.SystemPrompt,
		})
	}

	// 转换消息格式并控制token数量
	currentTokens := s.estimateTokenCount(s.config.SystemPrompt)

	// 从最新消息开始倒序处理，确保最近的对话被保留
	for i := len(messages) - 1; i >= 0; i-- {
		msg := messages[i]

		// 跳过系统消息（已经添加了系统提示词）
		if msg.Role == models.MessageRoleSystem {
			continue
		}

		// 检查添加这条消息是否会超过token限制
		if currentTokens+msg.TokenCount > tokenLimit {
			break
		}

		llmMessage := common.LLMMessage{
			Role:    string(msg.Role),
			Content: msg.Content,
		}

		// 在开头插入消息以保持时间顺序
		llmMessages = append([]common.LLMMessage{llmMessage}, llmMessages...)
		currentTokens += msg.TokenCount
	}

	return llmMessages, nil
}

// generateResponse 生成AI回复
func (s *ChatAgentService) generateResponse(ctx context.Context, messages []common.LLMMessage, userID, companyID string) (string, error) {
	// 构建LLM请求
	temperature := s.config.Temperature
	maxTokens := s.config.MaxTokens

	llmRequest := models.OpenAICompatibleRequestParams{
		Model:           s.resolveModelID(),
		Messages:        messages,
		Temperature:     &temperature,
		MaxTokens:       &maxTokens,
		AutoTrackTokens: s.config.EnableTokenTracking,
		UserContext: &models.UserContext{
			UserID:    userID,
			CompanyID: companyID,
		},
	}

	// 调用LLM
	response, err := s.llmClient.ChatCompletions(llmRequest)
	if err != nil {
		utils.Log.Errorf("LLM request failed: %v", err)
		return "", fmt.Errorf("LLM request failed: %w", err)
	}

	return strings.TrimSpace(response), nil
}

// resolveModelID 解析模型ID
func (s *ChatAgentService) resolveModelID() string {
	// 这里需要根据config中的ModelProvider和ModelAlias解析实际的模型ID
	// 简化实现，实际应该从配置中获取
	if s.config.ModelAlias != "" {
		return s.config.ModelAlias
	}
	return "doubao-1-5-pro-32k-250115" // 默认模型
}

// estimateTokenCount 估算token数量
func (s *ChatAgentService) estimateTokenCount(text string) int {
	// 简单的token估算：中文字符按1.5计算，英文单词按1计算
	// 实际应用中可以使用更精确的tokenizer
	if text == "" {
		return 0
	}

	chineseCount := 0
	englishWords := 0

	words := strings.Fields(text)
	for _, word := range words {
		hasChinese := false
		for _, r := range word {
			if r >= 0x4e00 && r <= 0x9fff {
				chineseCount++
				hasChinese = true
			}
		}
		if !hasChinese && len(word) > 0 {
			englishWords++
		}
	}

	// 中文字符 * 1.5 + 英文单词 * 1
	return int(float64(chineseCount)*1.5) + englishWords
}

// recordTokenConsumption 记录token消费
func (s *ChatAgentService) recordTokenConsumption(ctx context.Context, userID, companyID string, tokenCount int) {
	if s.tokenService == nil {
		return
	}

	consumptionReq := &models.TokenConsumptionRequest{
		UserID:        userID,
		CompanyID:     companyID,
		ModelProvider: s.config.ModelProvider,
		ModelName:     s.config.ModelAlias,
		InputTokens:   tokenCount / 2, // 简化：假设输入和输出各占一半
		OutputTokens:  tokenCount / 2,
		APIEndpoint:   "chat_agent",
	}

	_, err := s.tokenService.LogTokenConsumption(consumptionReq)
	if err != nil {
		utils.Log.Errorf("Failed to log token consumption: %v", err)
	}
}

// CreateChatSession 创建聊天会话
func (s *ChatAgentService) CreateChatSession(ctx context.Context, req *models.ChatSessionRequest) (*models.ChatSession, error) {
	// 验证用户token限制
	if s.config.EnableTokenTracking && s.tokenService != nil {
		limitCheck, err := s.tokenService.CheckTokenLimits(req.UserID, s.config.DefaultTokenLimit)
		if err != nil {
			utils.Log.Errorf("Failed to check token limits: %v", err)
		} else if !limitCheck.CanProceed {
			return nil, fmt.Errorf("user has exceeded token limits: %s", limitCheck.Reason)
		}
	}

	return s.sessionService.CreateSession(ctx, req)
}

// CloseChatSession 关闭聊天会话
func (s *ChatAgentService) CloseChatSession(ctx context.Context, sessionID string) error {
	return s.sessionService.CloseSession(ctx, sessionID)
}
