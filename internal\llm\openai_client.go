package llm

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/interfaces"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"golang.org/x/sync/semaphore"
)

// OpenAIClient 使用 go-openai 库与任何 OpenAI 兼容的 API 进行通信
type OpenAIClient struct {
	sdkClient    *openai.Client                   // go-openai SDK 客户端
	providerCfg  config.OpenAIProviderConfig      // 当前提供商的配置 (包含 BaseURL, APIKey, DefaultModelID)
	modelsMap    map[string]string                // 当前提供商的模型别名 -> 实际ID映射
	limiter      *semaphore.Weighted              // 控制并发请求
	tokenService interfaces.TokenServiceInterface // Token跟踪服务
	apiKeyPool   *APIKeyPool                      // API Key池
}

// NewOpenAIClient 创建一个新的 OpenAIClient 实例（向后兼容）
func NewOpenAIClient(providerCfg config.OpenAIProviderConfig) (LLMClient, error) {
	return NewOpenAIClientWithTokenService(providerCfg, nil)
}

// NewOpenAIClientWithTokenService 创建一个带Token跟踪功能的 OpenAIClient 实例
func NewOpenAIClientWithTokenService(providerCfg config.OpenAIProviderConfig, tokenService interfaces.TokenServiceInterface) (LLMClient, error) {
	availableKeys := providerCfg.GetAPIKeys()
	if len(availableKeys) == 0 {
		utils.Log.Warnf("提供商 (BaseURL: %s) 的 APIKey 未设置，API 调用可能失败。", providerCfg.BaseURL)
	}
	if providerCfg.BaseURL == "" {
		return nil, fmt.Errorf("OpenAI 兼容提供商的 BaseURL 未配置")
	}

	// 使用第一个API Key创建默认客户端
	defaultAPIKey := providerCfg.APIKey
	if defaultAPIKey == "" && len(availableKeys) > 0 {
		defaultAPIKey = availableKeys[0]
	}

	clientConfig := openai.DefaultConfig(defaultAPIKey)
	clientConfig.BaseURL = providerCfg.BaseURL
	clientConfig.HTTPClient = &http.Client{ // go-openai 默认使用标准库 http.Client，我们可以自定义超时
		Timeout: providerCfg.GetRequestTimeout(),
	}

	sdkClient := openai.NewClientWithConfig(clientConfig)

	// 创建API Key池
	var apiKeyPool *APIKeyPool
	if len(availableKeys) > 1 {
		apiKeyPool = NewAPIKeyPool(availableKeys, RoundRobin) // 默认使用轮询策略
		utils.Log.Infof("创建API Key池，包含 %d 个keys", len(availableKeys))
	} else if len(availableKeys) == 1 {
		utils.Log.Infof("使用单个API Key，未启用池化")
	}

	return &OpenAIClient{
		sdkClient:    sdkClient,
		providerCfg:  providerCfg, // 存储解析后的提供商配置
		modelsMap:    providerCfg.Models,
		limiter:      semaphore.NewWeighted(int64(providerCfg.MaxConcurrentRequests)),
		tokenService: tokenService,
		apiKeyPool:   apiKeyPool,
	}, nil
}

// ChatCompletions 实现 LLMClient 接口
func (c *OpenAIClient) ChatCompletions(params models.OpenAICompatibleRequestParams) (string, error) {
	if err := c.limiter.Acquire(context.Background(), 1); err != nil {
		return "", fmt.Errorf("获取信号量失败: %w", err)
	}
	defer c.limiter.Release(1)

	// 解析模型ID
	actualModelID := ""
	if params.Model != "" { // 如果请求中指定了模型 (可能是别名或直接ID)
		resolvedID, isAlias := c.modelsMap[params.Model]
		if isAlias {
			actualModelID = resolvedID
		} else {
			actualModelID = params.Model // 如果不是别名，则假定为直接的模型ID
		}
	} else {
		actualModelID = c.providerCfg.DefaultModelID // 使用提供商配置中解析的默认模型ID
	}

	if actualModelID == "" {
		return "", fmt.Errorf("模型名称无法解析 (BaseURL: %s, 请求参数中的模型: '%s')", c.providerCfg.BaseURL, params.Model)
	}

	// 检查token限额（如果启用了自动跟踪且提供了用户上下文）
	if params.AutoTrackTokens && params.UserContext != nil && c.tokenService != nil {
		// 简单估算输入token数用于限额检查
		estimatedInputTokens := 0
		for _, msg := range params.Messages {
			estimatedInputTokens += len(msg.Content) / 4
		}

		limitCheck, err := c.tokenService.CheckTokenLimits(params.UserContext.UserID, estimatedInputTokens+500) // 500是估算的输出token
		if err != nil {
			utils.Log.Errorf("检查token限额失败: %v", err)
		} else if !limitCheck.CanProceed {
			return "", fmt.Errorf("token限额不足: %s", limitCheck.Reason)
		}
	}

	var openAIMessages []openai.ChatCompletionMessage
	for _, msg := range params.Messages {
		openAIMessages = append(openAIMessages, openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	// 构建 go-openai 请求
	apiReq := openai.ChatCompletionRequest{
		Model:    actualModelID,
		Messages: openAIMessages,
		Stream:   params.Stream,
	}

	if params.Temperature != nil {
		apiReq.Temperature = float32(*params.Temperature)
	}
	if params.MaxTokens != nil {
		apiReq.MaxTokens = *params.MaxTokens
	}
	if params.FrequencyPenalty != nil {
		apiReq.FrequencyPenalty = float32(*params.FrequencyPenalty)
	}
	if params.PresencePenalty != nil {
		apiReq.PresencePenalty = float32(*params.PresencePenalty)
	}
	// 可以从 params.ProviderSpecificParams 中处理其他 OpenAI 标准参数

	utils.Log.Debugf("向 %s (模型: %s) 发送 OpenAI API 请求", c.providerCfg.BaseURL, actualModelID)

	// 使用API Key池进行调用
	resp, err := c.callWithAPIKeyPool(apiReq, actualModelID)
	if err != nil {
		return "", err
	}

	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("OpenAI API 响应中不包含任何 choice")
	}

	// 记录token使用情况到日志
	utils.Log.Infof("[LLM_CALL_COMPLETE] model=%s, prompt_tokens=%d, completion_tokens=%d, total_tokens=%d",
		actualModelID, resp.Usage.PromptTokens, resp.Usage.CompletionTokens, resp.Usage.TotalTokens)

	// 自动记录token消耗（如果启用了自动跟踪且提供了用户上下文）
	if params.AutoTrackTokens && params.UserContext != nil && c.tokenService != nil {
		utils.Log.Infof("[TOKEN_TRACKING_ENABLED] user_id=%s, company_id=%s, request_id=%s",
			params.UserContext.UserID, params.UserContext.CompanyID, params.UserContext.RequestID)

		// 只记录API返回的真实token数据
		inputTokens := resp.Usage.PromptTokens
		outputTokens := resp.Usage.CompletionTokens
		
		// 记录API返回的原始token数据用于调试
		utils.Log.Infof("[LLM_TOKEN_USAGE] API返回的token数据: prompt_tokens=%d, completion_tokens=%d, total_tokens=%d", 
			inputTokens, outputTokens, resp.Usage.TotalTokens)
		
		// 如果API返回的token数量为0，记录警告但不估算
		if inputTokens == 0 && outputTokens == 0 {
			utils.Log.Warnf("[TOKEN_ZERO_WARNING] API返回的token数量为0，请检查LLM API配置或响应格式")
			// 不进行token记录，让调用方知道没有token消耗
			utils.Log.Infof("[TOKEN_RECORDING_SKIPPED] 由于token数量为0，跳过token记录")
		} else {
			consumption := &models.TokenConsumptionRequest{
				UserID:        params.UserContext.UserID,
				CompanyID:     params.UserContext.CompanyID,
				ModelProvider: c.getProviderName(),
				ModelName:     actualModelID,
				InputTokens:   inputTokens,
				OutputTokens:  outputTokens,
				RequestID:     params.UserContext.RequestID,
				APIEndpoint:   params.UserContext.Endpoint,
			}

			utils.Log.Infof("[TOKEN_RECORDING_START] provider=%s, model=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d",
				consumption.ModelProvider, consumption.ModelName,
				consumption.InputTokens, consumption.OutputTokens,
				consumption.InputTokens+consumption.OutputTokens)

			tokenRecord, err := c.tokenService.LogTokenConsumption(consumption)
			if err != nil {
				utils.Log.Errorf("[TOKEN_RECORDING_FAILED] error=%v", err)
				utils.Log.Errorf("[TOKEN_RECORDING_FAILED] request_details=%+v", consumption)
				// 不中断主流程，只记录错误
			} else {
				costCents := int32(0)
				if tokenRecord.CostCents.Valid {
					costCents = tokenRecord.CostCents.Int32
				}
				utils.Log.Infof("[TOKEN_RECORDING_SUCCESS] db_id=%d, user_id=%s, company_id=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d, cost_cents=%d",
					tokenRecord.ID, params.UserContext.UserID, params.UserContext.CompanyID,
					consumption.InputTokens, consumption.OutputTokens, consumption.InputTokens+consumption.OutputTokens, costCents)
			}
		}
	} else {
		utils.Log.Warnf("[TOKEN_TRACKING_DISABLED] auto_track_tokens=%v, user_context_exists=%v, token_service_exists=%v",
			params.AutoTrackTokens, params.UserContext != nil, c.tokenService != nil)
	}


	return resp.Choices[0].Message.Content, nil
}

// ChatCompletionsWithUsage 实现带token使用信息的LLMClient接口
func (c *OpenAIClient) ChatCompletionsWithUsage(params models.OpenAICompatibleRequestParams) (*ChatResponse, error) {
	if err := c.limiter.Acquire(context.Background(), 1); err != nil {
		return nil, fmt.Errorf("获取信号量失败: %w", err)
	}
	defer c.limiter.Release(1)

	// 解析模型ID
	actualModelID := ""
	if params.Model != "" {
		resolvedID, isAlias := c.modelsMap[params.Model]
		if isAlias {
			actualModelID = resolvedID
		} else {
			actualModelID = params.Model
		}
	} else {
		actualModelID = c.providerCfg.DefaultModelID
	}

	if actualModelID == "" {
		return nil, fmt.Errorf("模型名称无法解析 (BaseURL: %s, 请求参数中的模型: '%s')", c.providerCfg.BaseURL, params.Model)
	}

	var openAIMessages []openai.ChatCompletionMessage
	for _, msg := range params.Messages {
		openAIMessages = append(openAIMessages, openai.ChatCompletionMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	// 构建 go-openai 请求
	apiReq := openai.ChatCompletionRequest{
		Model:    actualModelID,
		Messages: openAIMessages,
		Stream:   params.Stream,
	}

	if params.Temperature != nil {
		apiReq.Temperature = float32(*params.Temperature)
	}
	if params.MaxTokens != nil {
		apiReq.MaxTokens = *params.MaxTokens
	}
	if params.FrequencyPenalty != nil {
		apiReq.FrequencyPenalty = float32(*params.FrequencyPenalty)
	}
	if params.PresencePenalty != nil {
		apiReq.PresencePenalty = float32(*params.PresencePenalty)
	}

	utils.Log.Debugf("向 %s (模型: %s) 发送 OpenAI API 请求", c.providerCfg.BaseURL, actualModelID)

	// 使用API Key池进行调用
	resp, err := c.callWithAPIKeyPool(apiReq, actualModelID)
	if err != nil {
		return nil, err
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("OpenAI API 响应中不包含任何 choice")
	}

	// 记录token使用情况到日志
	utils.Log.Infof("[LLM_CALL_COMPLETE] model=%s, prompt_tokens=%d, completion_tokens=%d, total_tokens=%d",
		actualModelID, resp.Usage.PromptTokens, resp.Usage.CompletionTokens, resp.Usage.TotalTokens)

	// 自动记录token消耗（如果启用了自动跟踪且提供了用户上下文）
	if params.AutoTrackTokens && params.UserContext != nil && c.tokenService != nil {
		utils.Log.Infof("[TOKEN_TRACKING_ENABLED] user_id=%s, company_id=%s, request_id=%s",
			params.UserContext.UserID, params.UserContext.CompanyID, params.UserContext.RequestID)

		// 只记录API返回的真实token数据
		inputTokens := resp.Usage.PromptTokens
		outputTokens := resp.Usage.CompletionTokens
		
		// 记录API返回的原始token数据用于调试
		utils.Log.Infof("[LLM_TOKEN_USAGE] API返回的token数据: prompt_tokens=%d, completion_tokens=%d, total_tokens=%d", 
			inputTokens, outputTokens, resp.Usage.TotalTokens)
		
		// 如果API返回的token数量为0，记录警告但不估算
		if inputTokens == 0 && outputTokens == 0 {
			utils.Log.Warnf("[TOKEN_ZERO_WARNING] API返回的token数量为0，请检查LLM API配置或响应格式")
			utils.Log.Infof("[TOKEN_RECORDING_SKIPPED] 由于token数量为0，跳过token记录")
		} else {
			consumption := &models.TokenConsumptionRequest{
				UserID:        params.UserContext.UserID,
				CompanyID:     params.UserContext.CompanyID,
				ModelProvider: c.getProviderName(),
				ModelName:     actualModelID,
				InputTokens:   inputTokens,
				OutputTokens:  outputTokens,
				RequestID:     params.UserContext.RequestID,
				APIEndpoint:   params.UserContext.Endpoint,
			}

			utils.Log.Infof("[TOKEN_RECORDING_START] provider=%s, model=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d",
				consumption.ModelProvider, consumption.ModelName,
				consumption.InputTokens, consumption.OutputTokens,
				consumption.InputTokens+consumption.OutputTokens)

			tokenRecord, err := c.tokenService.LogTokenConsumption(consumption)
			if err != nil {
				utils.Log.Errorf("[TOKEN_RECORDING_FAILED] error=%v", err)
				utils.Log.Errorf("[TOKEN_RECORDING_FAILED] request_details=%+v", consumption)
			} else {
				costCents := int32(0)
				if tokenRecord.CostCents.Valid {
					costCents = tokenRecord.CostCents.Int32
				}
				utils.Log.Infof("[TOKEN_RECORDING_SUCCESS] db_id=%d, user_id=%s, company_id=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d, cost_cents=%d",
					tokenRecord.ID, params.UserContext.UserID, params.UserContext.CompanyID,
					consumption.InputTokens, consumption.OutputTokens, consumption.InputTokens+consumption.OutputTokens, costCents)
			}
		}
	}

	// 转换为标准的ChatResponse格式
	chatResponse := &ChatResponse{
		ID:      resp.ID,
		Object:  resp.Object,
		Created: resp.Created,
		Model:   resp.Model,
		Choices: make([]Choice, len(resp.Choices)),
		Usage: Usage{
			PromptTokens:     resp.Usage.PromptTokens,
			CompletionTokens: resp.Usage.CompletionTokens,
			TotalTokens:      resp.Usage.TotalTokens,
		},
	}

	for i, choice := range resp.Choices {
		chatResponse.Choices[i] = Choice{
			Index: choice.Index,
			Message: Message{
				Role:    choice.Message.Role,
				Content: choice.Message.Content,
			},
			Finish: string(choice.FinishReason),
		}
	}

	// 查找并处理 Function Call

	return chatResponse, nil
}


// callWithAPIKeyPool 使用API Key池进行调用
func (c *OpenAIClient) callWithAPIKeyPool(apiReq openai.ChatCompletionRequest, modelID string) (*openai.ChatCompletionResponse, error) {
	var lastErr error
	maxRetries := 3

	for attempt := 0; attempt < maxRetries; attempt++ {
		// 获取API Key
		var apiKey string
		if c.apiKeyPool != nil {
			key, err := c.apiKeyPool.GetNextKey()
			if err != nil {
				return nil, fmt.Errorf("获取API Key失败: %w", err)
			}
			apiKey = key
		} else {
			apiKey = c.providerCfg.APIKey
		}

		// 创建临时客户端使用当前API Key
		clientConfig := openai.DefaultConfig(apiKey)
		clientConfig.BaseURL = c.providerCfg.BaseURL
		clientConfig.HTTPClient = &http.Client{
			Timeout: c.providerCfg.GetRequestTimeout(),
		}
		tempClient := openai.NewClientWithConfig(clientConfig)

		// 执行API调用
		ctx, cancel := context.WithTimeout(context.Background(), c.providerCfg.GetRequestTimeout())
		resp, err := tempClient.CreateChatCompletion(ctx, apiReq)
		cancel()

		if err == nil {
			// 调用成功，返回结果
			utils.Log.Debugf("API调用成功，使用API Key: %s", c.maskAPIKey(apiKey))
			return &resp, nil
		}

		// 记录错误
		lastErr = err
		if c.apiKeyPool != nil {
			c.apiKeyPool.ReportError(apiKey, err)
		}

		// 检查是否是 APIError 类型
		if apiErr, ok := err.(*openai.APIError); ok {
			utils.Log.Errorf("OpenAI API 调用失败 (BaseURL: %s, 模型: %s, Key: %s): Status=%s, Type=%s, Message=%s, Code=%v",
				c.providerCfg.BaseURL, modelID, c.maskAPIKey(apiKey), apiErr.HTTPStatusCode, apiErr.Type, apiErr.Message, apiErr.Code)

			// 对于某些错误类型，不需要重试
			if apiErr.HTTPStatusCode == http.StatusUnauthorized ||
				apiErr.HTTPStatusCode == http.StatusForbidden ||
				apiErr.HTTPStatusCode == http.StatusBadRequest {
				utils.Log.Warnf("API Key %s 出现不可重试错误，跳过重试", c.maskAPIKey(apiKey))
				if c.apiKeyPool == nil {
					// 如果没有API Key池，直接返回错误
					return nil, fmt.Errorf("OpenAI API 错误: %s (类型: %s, Code: %v)", apiErr.Message, apiErr.Type, apiErr.Code)
				}
				// 有API Key池则继续尝试下一个Key
				continue
			}

			// 对于429错误，如果没有其他Key可用，则返回错误
			if apiErr.HTTPStatusCode == http.StatusTooManyRequests && c.apiKeyPool == nil {
				return nil, fmt.Errorf("OpenAI API 错误: %s (类型: %s, Code: %v)", apiErr.Message, apiErr.Type, apiErr.Code)
			}
		} else {
			utils.Log.Errorf("OpenAI API 调用时发生未知错误 (BaseURL: %s, 模型: %s, Key: %s): %v",
				c.providerCfg.BaseURL, modelID, c.maskAPIKey(apiKey), err)
		}

		// 如果没有API Key池，直接返回错误
		if c.apiKeyPool == nil {
			break
		}

		utils.Log.Warnf("API Key %s 调用失败，尝试下一个 (attempt %d/%d)", c.maskAPIKey(apiKey), attempt+1, maxRetries)
	}

	// 所有重试都失败了
	if lastErr != nil {
		if apiErr, ok := lastErr.(*openai.APIError); ok {
			return nil, fmt.Errorf("OpenAI API 错误: %s (类型: %s, Code: %v)", apiErr.Message, apiErr.Type, apiErr.Code)
		}
		return nil, fmt.Errorf("调用 LLM API 失败: %w", lastErr)
	}

	return nil, fmt.Errorf("所有API Key都不可用")
}

// maskAPIKey 掩码API Key用于日志显示
func (c *OpenAIClient) maskAPIKey(key string) string {
	if len(key) <= 8 {
		return "****"
	}
	return key[:4] + "****" + key[len(key)-4:]
}

// GetAPIKeyPoolStats 获取API Key池统计信息
func (c *OpenAIClient) GetAPIKeyPoolStats() map[string]interface{} {
	if c.apiKeyPool == nil {
		return map[string]interface{}{
			"enabled": false,
			"message": "API Key池未启用",
		}
	}

	return map[string]interface{}{
		"enabled":   true,
		"pool_info": c.apiKeyPool.GetPoolInfo(),
		"key_stats": c.apiKeyPool.GetStats(),
	}
}

// Chat 实现 Client 接口的 Chat 方法（兼容性）
func (c *OpenAIClient) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	// 转换请求格式
	var temperature *float64
	if req.Temperature != 0 {
		temp := float64(req.Temperature)
		temperature = &temp
	}

	var maxTokens *int
	if req.MaxTokens != 0 {
		maxTokens = &req.MaxTokens
	}

	params := models.OpenAICompatibleRequestParams{
		Model:       req.Model,
		Temperature: temperature,
		MaxTokens:   maxTokens,
		Messages:    make([]common.LLMMessage, len(req.Messages)),
	}

	// 转换消息格式
	for i, msg := range req.Messages {
		params.Messages[i] = common.LLMMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// 调用原有方法
	content, err := c.ChatCompletions(params)
	if err != nil {
		return nil, err
	}

	// 构造响应格式
	response := &ChatResponse{
		ID:      fmt.Sprintf("chatcmpl-%d", time.Now().UnixNano()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   req.Model,
		Choices: []Choice{
			{
				Index: 0,
				Message: Message{
					Role:    "assistant",
					Content: content,
				},
				Finish: "stop",
			},
		},
		Usage: Usage{
			PromptTokens:     0, // 这里可以根据需要计算
			CompletionTokens: 0,
			TotalTokens:      0,
		},
	}

	return response, nil
}

// getProviderName 获取提供商名称（从BaseURL推断）
func (c *OpenAIClient) getProviderName() string {
	baseURL := c.providerCfg.BaseURL
	if baseURL == "https://api.openai.com/v1" {
		return "openai"
	} else if strings.Contains(baseURL, "volces.com") {
		return "volcengine"
	} else if strings.Contains(baseURL, "anthropic.com") {
		return "anthropic"
	}
	return "openai_compatible"
}
