server:
  port: "8601"
  mode: "release"

logger:
  level: "info"

# mongodb.uri 将通过环境变量 MONGODB_URI_ENV 从 K8s Secret 注入
# pulsar.service_url 将通过环境变量 PULSAR_SERVICE_URL_ENV 从 K8s Secret 或 ConfigMap 注入

# llm.providers.<name>.api_key 将通过环境变量 LLM_PROVIDERS_<NAME>_API_KEY 从 K8s Secret 注入
# 例如 LLM_PROVIDERS_VOLCENGINE_ARK_API_KEY

# 其余配置可以硬编码或通过 K8s ConfigMap 覆盖
mongodb:
  database: "taskd_prod_db"
  timeout_seconds: 15

pulsar:
  consumer:
    topic_report_summary: "persistent://public/default/taskd-prod-report-summary-requests"
    subscription_name: "taskd-prod-summary-sub"

llm:
  default_provider: "volcengine_ark"
  providers:
    volcengine_ark:
      base_url: "https://ark.cn-beijing.volces.com/api/v3" # 确保这是生产环境的正确地址
      models:
        "report-summarizer": "your-production-model-id-on-ark" # 替换为生产模型ID
      default_model_alias: "report-summarizer"
      request_timeout_seconds: 90
      max_concurrent_requests: 20

prompts:
  report_summary:
    sys_prompt: |
      你是一位报告分析专家。请对给定的报告内容进行客观总结，要求：
      - 提取报告核心内容和关键结论
      - 保持客观、准确
      - 总结长度控制在50字以内
      - 仅做内容总结，不加入任何建议或评价
      输出语言：{{.language}}
    user_prompt: |
      #### 企业名称
      {{.target_company}}

      需要总结的报告内容如下：
      {{.text}}