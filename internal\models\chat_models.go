package models

import (
	"time"
)

// SocketStatus 定义Socket连接状态
type SocketStatus string

const (
	SocketStatusActive       SocketStatus = "active"
	SocketStatusInactive     SocketStatus = "inactive"
	SocketStatusDisconnected SocketStatus = "disconnected"
)

// ChatSessionStatus 定义聊天会话状态
type ChatSessionStatus string

const (
	ChatSessionStatusActive   ChatSessionStatus = "active"
	ChatSessionStatusInactive ChatSessionStatus = "inactive"
	ChatSessionStatusExpired  ChatSessionStatus = "expired"
)

// MessageRole 定义消息角色
type MessageRole string

const (
	MessageRoleUser      MessageRole = "user"
	MessageRoleAssistant MessageRole = "assistant"
	MessageRoleSystem    MessageRole = "system"
)

// SocketConnection SocketIO连接记录
type SocketConnection struct {
	ID             int          `json:"id" db:"id"`
	SocketID       string       `json:"socket_id" db:"socket_id"`
	UserID         string       `json:"user_id" db:"user_id"`
	CompanyID      string       `json:"company_id" db:"company_id"`
	SessionID      string       `json:"session_id" db:"session_id"`
	Status         SocketStatus `json:"status" db:"status"`
	ClientIP       *string      `json:"client_ip,omitempty" db:"client_ip"`
	UserAgent      *string      `json:"user_agent,omitempty" db:"user_agent"`
	ConnectedAt    time.Time    `json:"connected_at" db:"connected_at"`
	LastPingAt     time.Time    `json:"last_ping_at" db:"last_ping_at"`
	DisconnectedAt *time.Time   `json:"disconnected_at,omitempty" db:"disconnected_at"`
}

// ChatSession 聊天会话
type ChatSession struct {
	ID                 int               `json:"id" db:"id"`
	SessionID          string            `json:"session_id" db:"session_id"`
	UserID             string            `json:"user_id" db:"user_id"`
	CompanyID          string            `json:"company_id" db:"company_id"`
	Status             ChatSessionStatus `json:"status" db:"status"`
	CreatedAt          time.Time         `json:"created_at" db:"created_at"`
	LastActivityAt     time.Time         `json:"last_activity_at" db:"last_activity_at"`
	ExpiresAt          time.Time         `json:"expires_at" db:"expires_at"`
	ClosedAt           *time.Time        `json:"closed_at,omitempty" db:"closed_at"`
	ContextTokenLimit  int               `json:"context_token_limit" db:"context_token_limit"`
	CurrentTokenCount  int               `json:"current_token_count" db:"current_token_count"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	ID         int                    `json:"id" db:"id"`
	MessageID  string                 `json:"message_id" db:"message_id"`
	SessionID  string                 `json:"session_id" db:"session_id"`
	UserID     string                 `json:"user_id" db:"user_id"`
	Role       MessageRole            `json:"role" db:"role"`
	Content    string                 `json:"content" db:"content"`
	TokenCount int                    `json:"token_count" db:"token_count"`
	Metadata   map[string]interface{} `json:"metadata,omitempty" db:"metadata"`
	CreatedAt  time.Time              `json:"created_at" db:"created_at"`
}

// SocketConnectionRequest 创建Socket连接请求
type SocketConnectionRequest struct {
	UserID    string  `json:"user_id" binding:"required"`
	CompanyID string  `json:"company_id" binding:"required"`
	ClientIP  *string `json:"client_ip,omitempty"`
	UserAgent *string `json:"user_agent,omitempty"`
}

// ChatSessionRequest 创建聊天会话请求
type ChatSessionRequest struct {
	UserID            string `json:"user_id" binding:"required"`
	CompanyID         string `json:"company_id" binding:"required"`
	ContextTokenLimit *int   `json:"context_token_limit,omitempty"`
}

// ChatMessageRequest 发送聊天消息请求
type ChatMessageRequest struct {
	SessionID string                 `json:"session_id" binding:"required"`
	Content   string                 `json:"content" binding:"required"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ChatMessageResponse 聊天消息响应
type ChatMessageResponse struct {
	MessageID  string                 `json:"message_id"`
	SessionID  string                 `json:"session_id"`
	Role       MessageRole            `json:"role"`
	Content    string                 `json:"content"`
	TokenCount int                    `json:"token_count"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt  time.Time              `json:"created_at"`
}

// ChatHistoryRequest 获取聊天历史请求
type ChatHistoryRequest struct {
	SessionID string `json:"session_id" binding:"required"`
	Limit     *int   `json:"limit,omitempty"`
	Offset    *int   `json:"offset,omitempty"`
}

// ChatHistoryResponse 聊天历史响应
type ChatHistoryResponse struct {
	SessionID string                `json:"session_id"`
	Messages  []ChatMessageResponse `json:"messages"`
	Total     int                   `json:"total"`
	HasMore   bool                  `json:"has_more"`
}

// SocketEvent 定义Socket事件类型
type SocketEvent string

const (
	SocketEventConnect     SocketEvent = "connect"
	SocketEventDisconnect  SocketEvent = "disconnect"
	SocketEventMessage     SocketEvent = "message"
	SocketEventResponse    SocketEvent = "response"
	SocketEventError       SocketEvent = "error"
	SocketEventPing        SocketEvent = "ping"
	SocketEventPong        SocketEvent = "pong"
	SocketEventJoinSession SocketEvent = "join_session"
	SocketEventLeaveSession SocketEvent = "leave_session"
)

// SocketMessageData Socket消息数据
type SocketMessageData struct {
	Event     SocketEvent            `json:"event"`
	SessionID string                 `json:"session_id,omitempty"`
	MessageID string                 `json:"message_id,omitempty"`
	Content   string                 `json:"content,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Error     *string                `json:"error,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// ChatConfig 闲聊配置
type ChatConfig struct {
	MaxSocketConnections  int           `json:"max_socket_connections"`
	SessionTimeoutMinutes int           `json:"session_timeout_minutes"`
	DefaultTokenLimit     int           `json:"default_token_limit"`
	CleanupIntervalHours  int           `json:"cleanup_interval_hours"`
	PingIntervalSeconds   int           `json:"ping_interval_seconds"`
	SystemPrompt          string        `json:"system_prompt"`
	ModelProvider         string        `json:"model_provider"`
	ModelAlias            string        `json:"model_alias"`
	Temperature           float64       `json:"temperature"`
	MaxTokens             int           `json:"max_tokens"`
	EnableTokenTracking   bool          `json:"enable_token_tracking"`
}