package services

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	extractionModels "gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// EntityExtractionLLMProcessor 实体提取LLM处理器
type EntityExtractionLLMProcessor struct {
	llmClient      llm.LLMClient
	promptBuilder  PromptBuilder
	responseParser ResponseParser
	errorHandler   ErrorHandler
	config         *EntityExtractionLLMProcessorConfig
}

// EntityExtractionLLMProcessorConfig LLM处理器配置
type EntityExtractionLLMProcessorConfig struct {
	Model           string  `json:"model"`
	Temperature     float64 `json:"temperature"`
	MaxTokens       int     `json:"max_tokens"`
	Timeout         int     `json:"timeout"`
	MaxRetries      int     `json:"max_retries"`
	RetryDelay      int     `json:"retry_delay_ms"`
	AutoTrackTokens bool    `json:"auto_track_tokens"`
}

// DefaultEntityExtractionLLMProcessorConfig 默认配置
func DefaultEntityExtractionLLMProcessorConfig() *EntityExtractionLLMProcessorConfig {
	return &EntityExtractionLLMProcessorConfig{
		Model:           "deepseek-v3-250324",
		Temperature:     0.1,
		MaxTokens:       4000,
		Timeout:         30,
		MaxRetries:      3,
		RetryDelay:      1000,
		AutoTrackTokens: true,
	}
}

// NewEntityExtractionLLMProcessor 创建LLM处理器
func NewEntityExtractionLLMProcessor(
	llmClient llm.LLMClient,
	promptBuilder PromptBuilder,
	responseParser ResponseParser,
	errorHandler ErrorHandler,
	config *EntityExtractionLLMProcessorConfig,
) *EntityExtractionLLMProcessor {
	if config == nil {
		config = DefaultEntityExtractionLLMProcessorConfig()
	}

	return &EntityExtractionLLMProcessor{
		llmClient:      llmClient,
		promptBuilder:  promptBuilder,
		responseParser: responseParser,
		errorHandler:   errorHandler,
		config:         config,
	}
}

// ProcessExtraction 处理实体提取请求
func (lp *EntityExtractionLLMProcessor) ProcessExtraction(ctx context.Context, req *extractionModels.ExtractionRequest) (*extractionModels.ExtractionResult, error) {
	start := time.Now()

	// 构建提示词
	promptReq, err := lp.promptBuilder.BuildPrompt(ctx, req)
	if err != nil {
		return lp.handleError(ctx, fmt.Errorf("构建提示词失败: %v", err), req, start), nil
	}

	// 添加调试日志，像意图识别那样
	fmt.Printf("\n=== 实体提取服务开始 ===\n")
	fmt.Printf("请求参数: 用户ID=%s, 公司ID=%s\n", ctx.Value("user_id"), ctx.Value("company_id"))
	fmt.Printf("使用模型: %s\n", lp.config.Model)
	fmt.Printf("Token自动跟踪: 已启用\n")

	// 调用LLM（带重试）
	response, err := lp.callLLMWithRetry(ctx, promptReq, req)
	if err != nil {
		fmt.Printf("LLM调用失败: %v\n", err)
		return lp.handleError(ctx, fmt.Errorf("LLM调用失败: %v", err), req, start), nil
	}

	// 添加调试日志
	fmt.Printf("LLM响应接收: 内容长度=%d, 模型=%s\n", len(response.Content), response.Model)
	fmt.Printf("原始LLM响应: %s\n", response.Content)
	fmt.Printf("Token使用: 输入=%d, 输出=%d, 总计=%d\n",
		response.TokenUsage.InputTokens, response.TokenUsage.OutputTokens, response.TokenUsage.TotalTokens)

	// 解析响应
	parseResult, err := lp.responseParser.ParseResponse(ctx, response.Content, req.TargetSchema)
	if err != nil {
		fmt.Printf("解析错误: %v\n", err)
		fmt.Printf("解析失败的响应: '%s'\n", response.Content)
		return &extractionModels.ExtractionResult{
			Success: false,
			Error: &extractionModels.ExtractionError{
				Code:    extractionModels.ErrResponseParseFailed,
				Message: fmt.Sprintf("响应解析失败: %v", err),
				Details: map[string]interface{}{
					"raw_response": response.Content,
					"schema_name":  req.TargetSchema.Name,
					"error_detail": err.Error(),
				},
			},
			RawOutput: response.Content,
			Metadata:  lp.buildMetadata(response, req.Language, 0, start),
		}, nil
	}

	// 检查置信度阈值
	confidenceThreshold := 0.7
	if req.ExtractionConfig != nil && req.ExtractionConfig.ConfidenceThreshold > 0 {
		confidenceThreshold = req.ExtractionConfig.ConfidenceThreshold
	}

	if parseResult.Confidence < confidenceThreshold {
		return &extractionModels.ExtractionResult{
			Success: false,
			Error: &extractionModels.ExtractionError{
				Code:    extractionModels.ErrLowConfidence,
				Message: fmt.Sprintf("提取结果置信度过低: %.2f < %.2f", parseResult.Confidence, confidenceThreshold),
				Details: map[string]interface{}{
					"confidence":           parseResult.Confidence,
					"confidence_threshold": confidenceThreshold,
					"validation_errors":    parseResult.Errors,
				},
			},
			Data:       parseResult.Data,
			Confidence: parseResult.Confidence,
			RawOutput:  response.Content,
			Metadata:   lp.buildMetadata(response, req.Language, 0, start),
		}, nil
	}

	// 成功返回结果
	return &extractionModels.ExtractionResult{
		Success:    true,
		Data:       parseResult.Data,
		Confidence: parseResult.Confidence,
		Metadata:   lp.buildMetadata(response, req.Language, 0, start),
	}, nil
}

// callLLMWithRetry 带重试的LLM调用
func (lp *EntityExtractionLLMProcessor) callLLMWithRetry(ctx context.Context, promptReq *extractionModels.PromptRequest, req *extractionModels.ExtractionRequest) (*common.LLMResponse, error) {
	var lastErr error
	maxRetries := lp.config.MaxRetries

	if req.ExtractionConfig != nil && req.ExtractionConfig.MaxRetries > 0 {
		maxRetries = req.ExtractionConfig.MaxRetries
	}

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// 重试延迟
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(lp.config.RetryDelay) * time.Millisecond):
			}
		}

		response, err := lp.callLLM(ctx, promptReq, req)
		if err == nil {
			return response, nil
		}

		lastErr = err

		// 检查是否应该重试
		if !lp.errorHandler.ShouldRetry(err, attempt) {
			break
		}
	}

	return nil, fmt.Errorf("LLM调用失败，已重试%d次: %v", maxRetries, lastErr)
}

// callLLM 单次LLM调用
func (lp *EntityExtractionLLMProcessor) callLLM(ctx context.Context, promptReq *extractionModels.PromptRequest, req *extractionModels.ExtractionRequest) (*common.LLMResponse, error) {
	// 构建LLM请求参数
	messages := []common.LLMMessage{
		{
			Role:    "system",
			Content: promptReq.SystemPrompt,
		},
		{
			Role:    "user",
			Content: promptReq.UserPrompt,
		},
	}

	// 设置超时
	timeout := lp.config.Timeout
	if req.ExtractionConfig != nil && req.ExtractionConfig.Timeout > 0 {
		timeout = req.ExtractionConfig.Timeout
	}

	_, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	// 构建OpenAI兼容请求参数
	temperature := lp.config.Temperature
	maxTokens := lp.config.MaxTokens

	// 直接从A2A上下文构建UserContext，像意图识别那样
	requestID := fmt.Sprintf("extraction_%d", time.Now().UnixNano())

	// 从上下文获取用户信息
	userID := "entity-extraction-agent"
	companyID := "system"

	if uid, ok := ctx.Value("user_id").(string); ok && uid != "" {
		userID = uid
	}
	if cid, ok := ctx.Value("company_id").(string); ok && cid != "" {
		companyID = cid
	}

	userContext := &models.UserContext{
		UserID:    userID,
		CompanyID: companyID,
		RequestID: requestID,
		Endpoint:  "/agents/execute/entity_extraction",
	}

	fmt.Printf("正在调用LLM服务...\n")
	fmt.Printf("AutoTrackTokens配置: %v\n", lp.config.AutoTrackTokens)
	fmt.Printf("UserContext详情: UserID=%s, CompanyID=%s, RequestID=%s\n", 
		userContext.UserID, userContext.CompanyID, userContext.RequestID)

	params := models.OpenAICompatibleRequestParams{
		Model:           lp.config.Model,
		Messages:        messages,
		Temperature:     &temperature,
		MaxTokens:       &maxTokens,
		AutoTrackTokens: lp.config.AutoTrackTokens,
		UserContext:     userContext,
	}

	// 调用LLM客户端获取完整响应包含Token统计
	start := time.Now()
	llmResponse, err := lp.llmClient.ChatCompletionsWithUsage(params)
	if err != nil {
		fmt.Printf("LLM调用失败: %v\n", err)
		return nil, fmt.Errorf("LLM API调用失败: %v", err)
	}

	fmt.Printf("LLM调用成功\n")

	// 提取响应内容
	responseContent := ""
	if len(llmResponse.Choices) > 0 {
		responseContent = llmResponse.Choices[0].Message.Content
		fmt.Printf("提取响应内容长度: %d\n", len(responseContent))
	} else {
		fmt.Printf("LLM响应中没有choices - 这将导致解析失败\n")
		return nil, fmt.Errorf("LLM响应中没有choices: response_id=%s", llmResponse.ID)
	}

	// 构建响应对象，使用真实的Token统计
	response := &common.LLMResponse{
		Content:   responseContent,
		Model:     lp.config.Model,
		Duration:  time.Since(start).Milliseconds(),
		Timestamp: time.Now(),
		TokenUsage: common.TokenUsage{
			InputTokens:  llmResponse.Usage.PromptTokens,
			OutputTokens: llmResponse.Usage.CompletionTokens,
			TotalTokens:  llmResponse.Usage.TotalTokens,
		},
	}

	// 添加token使用日志，像意图识别那样
	fmt.Printf("Token使用情况: 输入=%d, 输出=%d, 总计=%d\n",
		llmResponse.Usage.PromptTokens, llmResponse.Usage.CompletionTokens, llmResponse.Usage.TotalTokens)

	return response, nil
}

// getUserContextFromRequest 从请求上下文中获取用户信息 (此方法现在不再使用，保留用于兼容性)
func (lp *EntityExtractionLLMProcessor) getUserContextFromRequest(ctx context.Context, req *extractionModels.ExtractionRequest) *models.UserContext {
	// 尝试从上下文中获取用户信息
	if userCtx, ok := ctx.Value("user_context").(*models.UserContext); ok {
		return userCtx
	}

	// 尝试从上下文中获取用户ID和公司ID
	userID := "entity-extraction-agent"
	companyID := "system"

	if uid, ok := ctx.Value("user_id").(string); ok && uid != "" {
		userID = uid
	}
	if cid, ok := ctx.Value("company_id").(string); ok && cid != "" {
		companyID = cid
	}

	// 如果都没有，尝试从上下文的其他可能位置获取
	if userID == "entity-extraction-agent" {
		// 检查是否有X-User-ID等头信息存储在上下文中
		if headers, ok := ctx.Value("headers").(map[string]string); ok {
			if uid, exists := headers["X-User-ID"]; exists && uid != "" {
				userID = uid
			}
			if cid, exists := headers["X-Company-ID"]; exists && cid != "" {
				companyID = cid
			}
		}
	}

	return &models.UserContext{
		UserID:    userID,
		CompanyID: companyID,
		RequestID: fmt.Sprintf("extraction_%d", time.Now().UnixNano()),
		Endpoint:  "extract_structured_data",
	}
}

// estimateTokens 估算token数量（简单实现）
func (lp *EntityExtractionLLMProcessor) estimateTokens(text string) int {
	// 简单的token估算：大约4个字符=1个token（英文），2个字符=1个token（中文）
	// 这是一个粗略的估算，实际应该使用专门的tokenizer
	runes := []rune(text)
	tokenCount := 0

	for _, r := range runes {
		if r < 128 { // ASCII字符
			tokenCount += 1
		} else { // 非ASCII字符（主要是中文）
			tokenCount += 2
		}
	}

	return tokenCount / 4 // 平均每4个字符1个token
}

// handleError 处理错误
func (lp *EntityExtractionLLMProcessor) handleError(ctx context.Context, err error, req *extractionModels.ExtractionRequest, start time.Time) *extractionModels.ExtractionResult {
	// 使用错误处理器处理错误
	result := lp.errorHandler.HandleError(ctx, err, req)

	// 补充处理时间信息
	if result.Metadata.ProcessingTime == 0 {
		result.Metadata.ProcessingTime = time.Since(start).Milliseconds()
	}
	if result.Metadata.ProcessedAt.IsZero() {
		result.Metadata.ProcessedAt = time.Now()
	}
	if result.Metadata.ModelUsed == "" {
		result.Metadata.ModelUsed = lp.config.Model
	}
	if result.Metadata.Language == "" {
		result.Metadata.Language = req.Language
	}

	return result
}

// buildMetadata 构建元数据
func (lp *EntityExtractionLLMProcessor) buildMetadata(response *common.LLMResponse, language string, retryCount int, start time.Time) extractionModels.ExtractionMetadata {
	return extractionModels.ExtractionMetadata{
		ModelUsed:      response.Model,
		ProcessingTime: time.Since(start).Milliseconds(),
		TokenUsage: extractionModels.TokenUsage{
			InputTokens:  response.TokenUsage.InputTokens,
			OutputTokens: response.TokenUsage.OutputTokens,
			TotalTokens:  response.TokenUsage.TotalTokens,
		},
		Language:    language,
		RetryCount:  retryCount,
		ProcessedAt: time.Now(),
	}
}

// ProcessBatch 批量处理提取请求
func (lp *EntityExtractionLLMProcessor) ProcessBatch(ctx context.Context, requests []*extractionModels.ExtractionRequest) ([]*extractionModels.ExtractionResult, error) {
	results := make([]*extractionModels.ExtractionResult, len(requests))

	// 并发处理（可以根据需要调整并发数）
	semaphore := make(chan struct{}, 5) // 最多5个并发

	for i, req := range requests {
		go func(index int, request *extractionModels.ExtractionRequest) {
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result, err := lp.ProcessExtraction(ctx, request)
			if err != nil {
				results[index] = &extractionModels.ExtractionResult{
					Success: false,
					Error: &extractionModels.ExtractionError{
						Code:    extractionModels.ErrInternalError,
						Message: fmt.Sprintf("批量处理失败: %v", err),
					},
					Metadata: extractionModels.ExtractionMetadata{
						ModelUsed:   lp.config.Model,
						Language:    request.Language,
						ProcessedAt: time.Now(),
					},
				}
			} else {
				results[index] = result
			}
		}(i, req)
	}

	// 等待所有goroutine完成
	for i := 0; i < cap(semaphore); i++ {
		semaphore <- struct{}{}
	}

	return results, nil
}

// GetConfig 获取配置
func (lp *EntityExtractionLLMProcessor) GetConfig() *EntityExtractionLLMProcessorConfig {
	return lp.config
}

// UpdateConfig 更新配置
func (lp *EntityExtractionLLMProcessor) UpdateConfig(config *EntityExtractionLLMProcessorConfig) {
	if config != nil {
		lp.config = config
	}
}

// GetStats 获取处理统计信息
func (lp *EntityExtractionLLMProcessor) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"model":       lp.config.Model,
		"temperature": lp.config.Temperature,
		"max_tokens":  lp.config.MaxTokens,
		"max_retries": lp.config.MaxRetries,
		"timeout":     lp.config.Timeout,
		"retry_delay": lp.config.RetryDelay,
	}
}
