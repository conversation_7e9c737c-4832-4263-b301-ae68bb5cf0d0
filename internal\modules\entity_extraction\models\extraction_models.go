package models

import (
	"time"
)

// ExtractionRequest 实体提取请求
type ExtractionRequest struct {
	InputText        string                 `json:"input_text" binding:"required"`
	TargetSchema     JSONSchema             `json:"target_schema" binding:"required"`
	Language         string                 `json:"language,omitempty"`
	ExtractionConfig *ExtractionConfig      `json:"extraction_config,omitempty"`
}

// JSONSchema JSON Schema定义
type JSONSchema struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description" binding:"required"`
	Type        string                 `json:"type" binding:"required"`
	Properties  map[string]Property    `json:"properties" binding:"required"`
	Required    []string               `json:"required,omitempty"`
	Examples    []interface{}          `json:"examples,omitempty"`
}

// Property Schema属性定义
type Property struct {
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Format      string                 `json:"format,omitempty"`
	Enum        []string               `json:"enum,omitempty"`
	Items       *Property              `json:"items,omitempty"`
	Properties  map[string]Property    `json:"properties,omitempty"`
	Required    []string               `json:"required,omitempty"`
	Default     interface{}            `json:"default,omitempty"`
}

// ExtractionConfig 提取配置
type ExtractionConfig struct {
	ConfidenceThreshold float64 `json:"confidence_threshold,omitempty"`
	MaxRetries          int     `json:"max_retries,omitempty"`
	Timeout             int     `json:"timeout,omitempty"`
	EnableFallback      bool    `json:"enable_fallback,omitempty"`
}

// ExtractionResult 提取结果
type ExtractionResult struct {
	Success    bool                   `json:"success"`
	Data       map[string]interface{} `json:"data,omitempty"`
	Confidence float64                `json:"confidence"`
	Error      *ExtractionError       `json:"error,omitempty"`
	Metadata   ExtractionMetadata     `json:"metadata"`
	RawOutput  string                 `json:"raw_output,omitempty"`
}

// ExtractionError 提取错误
type ExtractionError struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// ExtractionMetadata 提取元数据
type ExtractionMetadata struct {
	ModelUsed      string     `json:"model_used"`
	ProcessingTime int64      `json:"processing_time_ms"`
	TokenUsage     TokenUsage `json:"token_usage"`
	Language       string     `json:"language"`
	RetryCount     int        `json:"retry_count"`
	ProcessedAt    time.Time  `json:"processed_at"`
}

// TokenUsage Token使用情况
type TokenUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
	TotalTokens  int `json:"total_tokens"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid          bool              `json:"valid"`
	Errors         []ValidationError `json:"errors,omitempty"`
	RequiredFields []string          `json:"required_fields,omitempty"`
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// PromptRequest 提示词请求
type PromptRequest struct {
	SystemPrompt string     `json:"system_prompt"`
	UserPrompt   string     `json:"user_prompt"`
	ModelSchema  JSONSchema `json:"model_schema"`
	Language     string     `json:"language"`
}

// ParseResult 解析结果
type ParseResult struct {
	Data       map[string]interface{} `json:"data"`
	Confidence float64                `json:"confidence"`
	Valid      bool                   `json:"valid"`
	Errors     []ValidationError      `json:"errors,omitempty"`
}

// ProcessedSchema 处理后的Schema
type ProcessedSchema struct {
	Schema           JSONSchema         `json:"schema"`
	ValidationResult ValidationResult   `json:"validation_result"`
	FieldTypes       map[string]string  `json:"field_types"`
	RequiredFields   []string           `json:"required_fields"`
	OptionalFields   []string           `json:"optional_fields"`
	NestedStructures []NestedStructure  `json:"nested_structures"`
	EnumFields       map[string][]string `json:"enum_fields"`
	FormatFields     map[string]string  `json:"format_fields"`
	Complexity       SchemaComplexity   `json:"complexity"`
}

// NestedStructure 嵌套结构信息
type NestedStructure struct {
	FieldName  string                 `json:"field_name"`
	Type       string                 `json:"type"` // "object" or "array_of_objects"
	Properties map[string]Property    `json:"properties"`
	Required   []string               `json:"required,omitempty"`
}

// SchemaComplexity Schema复杂度信息
type SchemaComplexity struct {
	TotalFields    int `json:"total_fields"`
	RequiredFields int `json:"required_fields"`
	OptionalFields int `json:"optional_fields"`
	NestedLevels   int `json:"nested_levels"`
	ArrayFields    int `json:"array_fields"`
	ObjectFields   int `json:"object_fields"`
	EnumFields     int `json:"enum_fields"`
	FormatFields   int `json:"format_fields"`
}

// SchemaInfo Schema信息摘要
type SchemaInfo struct {
	Name             string `json:"name"`
	Description      string `json:"description"`
	TotalFields      int    `json:"total_fields"`
	RequiredFields   int    `json:"required_fields"`
	OptionalFields   int    `json:"optional_fields"`
	HasNestedObjects bool   `json:"has_nested_objects"`
	HasArrays        bool   `json:"has_arrays"`
	HasEnums         bool   `json:"has_enums"`
	HasFormats       bool   `json:"has_formats"`
	ExampleCount     int    `json:"example_count"`
}