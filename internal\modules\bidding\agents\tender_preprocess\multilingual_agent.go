package tender_preprocess

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// MultilingualAgent 多语言处理Agent
type MultilingualAgent struct {
	*agents.BaseAgent
	llmService services.LLMService
}

// NewMultilingualAgent 创建多语言处理Agent
func NewMultilingualAgent(llmService services.LLMService) *MultilingualAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentMultilingualName,
		Description: "对招投标数据进行多语言翻译和本地化处理",
		URL:         "http://taskd-service:8601/agents/tender-multilingual",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillProcessMultilingual,
				Name:        "多语言处理",
				Description: "将中文处理结果翻译为英文并进行本地化处理",
				Tags:        []string{"translation", "i18n", "ai", "batch"},
				Examples: []string{
					"将中文招投标摘要翻译为英文",
					"本地化项目标题和关键词",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentMultilingualName, agentCard)

	return &MultilingualAgent{
		BaseAgent:  baseAgent,
		llmService: llmService,
	}
}

// ExecuteSkill 执行技能
func (a *MultilingualAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillProcessMultilingual:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.processMultilingualHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// processMultilingualHandler 多语言处理处理器
func (a *MultilingualAgent) processMultilingualHandler(input map[string]interface{}, a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseMultilingualParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Starting multilingual processing for %d languages", len(params.TargetLanguages))

	// 验证目标语言
	for _, lang := range params.TargetLanguages {
		if !agentCommon.ValidateLanguage(lang) {
			return nil, fmt.Errorf("unsupported target language: %s", lang)
		}
	}

	// 执行多语言处理
	translatedData, translationQuality, metadata, err := a.performTranslation(params, a2aContext)
	if err != nil {
		return nil, fmt.Errorf("multilingual processing failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"translated_data":     translatedData,
		"translation_quality": translationQuality,
		"processing_metadata": metadata,
	}

	return output, nil
}

// MultilingualParams 多语言处理参数
type MultilingualParams struct {
	ChineseData       map[string]interface{} `json:"chinese_data"`
	TargetLanguages   []string               `json:"target_languages"`
	TranslationMode   string                 `json:"translation_mode"`
	FieldsToTranslate []string               `json:"fields_to_translate"`
}

// parseMultilingualParams 解析多语言处理参数
func (a *MultilingualAgent) parseMultilingualParams(input map[string]interface{}) (*MultilingualParams, error) {
	chineseData, ok := input["chinese_data"]
	if !ok {
		return nil, fmt.Errorf("chinese_data is required")
	}

	chineseDataMap, ok := chineseData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("chinese_data must be an object")
	}

	// 解析目标语言
	var targetLanguages []string
	if targetLangsInterface, ok := input["target_languages"]; ok {
		if targetLangsArray, ok := targetLangsInterface.([]interface{}); ok {
			for _, lang := range targetLangsArray {
				if langStr, ok := lang.(string); ok {
					targetLanguages = append(targetLanguages, langStr)
				}
			}
		}
	}
	if len(targetLanguages) == 0 {
		targetLanguages = []string{agentCommon.LanguageEnglish} // 默认翻译为英文
	}

	translationMode, _ := input["translation_mode"].(string)
	if translationMode == "" {
		translationMode = agentCommon.TranslationModeBatch
	}

	// 解析需要翻译的字段
	var fieldsToTranslate []string
	if fieldsInterface, ok := input["fields_to_translate"]; ok {
		if fieldsArray, ok := fieldsInterface.([]interface{}); ok {
			for _, field := range fieldsArray {
				if fieldStr, ok := field.(string); ok {
					fieldsToTranslate = append(fieldsToTranslate, fieldStr)
				}
			}
		}
	}

	return &MultilingualParams{
		ChineseData:       chineseDataMap,
		TargetLanguages:   targetLanguages,
		TranslationMode:   translationMode,
		FieldsToTranslate: fieldsToTranslate,
	}, nil
}

// performTranslation 执行翻译
func (a *MultilingualAgent) performTranslation(params *MultilingualParams, a2aContext biddingModels.A2AContext) (map[string]*agentCommon.TranslationData, *agentCommon.TranslationQuality, *agentCommon.ProcessingMetadata, error) {
	startTime := time.Now()

	translatedData := make(map[string]*agentCommon.TranslationData)

	var totalTokens int
	qualityScores := make(map[string]float64)

	// 为每种目标语言执行翻译
	for _, targetLang := range params.TargetLanguages {
		utils.Log.Infof("Translating to %s", targetLang)

		translatedContent, tokens, quality, err := a.translateToLanguage(params.ChineseData, targetLang, params.FieldsToTranslate, a2aContext)
		if err != nil {
			utils.Log.Errorf("Translation to %s failed: %v", targetLang, err)
			continue
		}

		translatedData[targetLang] = &agentCommon.TranslationData{
			Language: targetLang,
			Data:     translatedContent,
		}

		totalTokens += tokens
		qualityScores[targetLang] = quality
	}

	// 计算整体翻译质量
	overallQuality := a.calculateOverallQuality(qualityScores)

	translationQuality := &agentCommon.TranslationQuality{
		OverallQuality: overallQuality,
		FieldQuality:   qualityScores,
		QualityIssues:  []string{}, // 简化处理
	}

	// 构建处理元数据
	metadata := &agentCommon.ProcessingMetadata{
		ModelUsed:      "gpt-4",
		TokensConsumed: totalTokens,
		ProcessingTime: time.Since(startTime).Seconds(),
	}

	return translatedData, translationQuality, metadata, nil
}

// translateToLanguage 翻译到指定语言
func (a *MultilingualAgent) translateToLanguage(chineseData map[string]interface{}, targetLang string, fieldsToTranslate []string, a2aContext biddingModels.A2AContext) (map[string]interface{}, int, float64, error) {
	// 提取需要翻译的内容
	contentToTranslate := a.extractContentForTranslation(chineseData, fieldsToTranslate)

	if len(contentToTranslate) == 0 {
		return map[string]interface{}{}, 0, 1.0, nil
	}

	// 构建翻译prompt
	systemPrompt := a.getTranslationSystemPrompt(targetLang)
	userPrompt := a.buildTranslationUserPrompt(contentToTranslate, targetLang)

	// 构建LLM请求
	temperature := 0.1
	maxTokens := 2000
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model: "gpt-4",
		Messages: []common.LLMMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: userPrompt,
			},
		},
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	// 调用LLM服务
	ctx := context.Background()
	response, err := a.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return nil, 0, 0.0, fmt.Errorf("LLM request failed: %v", err)
	}

	// 解析翻译结果
	translatedContent, err := a.parseTranslationResult(response.Result)
	if err != nil {
		return nil, 0, 0.0, fmt.Errorf("failed to parse translation result: %v", err)
	}

	// 简化的质量评估
	quality := a.assessTranslationQuality(contentToTranslate, translatedContent)

	return translatedContent, response.TokenUsage.TotalTokens, quality, nil
}

// extractContentForTranslation 提取需要翻译的内容
func (a *MultilingualAgent) extractContentForTranslation(data map[string]interface{}, fieldsToTranslate []string) map[string]interface{} {
	contentToTranslate := make(map[string]interface{})

	// 如果没有指定字段，则翻译所有可翻译的字段
	if len(fieldsToTranslate) == 0 {
		fieldsToTranslate = []string{"optimized_title", "keywords", "summary"}
	}

	for _, field := range fieldsToTranslate {
		if value, exists := data[field]; exists {
			contentToTranslate[field] = value
		}
	}

	return contentToTranslate
}

// getTranslationSystemPrompt 获取翻译系统提示词
func (a *MultilingualAgent) getTranslationSystemPrompt(targetLang string) string {
	switch targetLang {
	case agentCommon.LanguageEnglish:
		return `You are a professional translator specializing in tender and procurement documents. Translate the provided Chinese content to English while maintaining professional terminology and context accuracy. 

Requirements:
1. Maintain technical accuracy for procurement and tender terms
2. Keep the structure and format of the original content
3. Use appropriate business English terminology
4. Return only the translated JSON content without additional explanation`

	case agentCommon.LanguageJapanese:
		return `あなたは入札・調達文書の専門翻訳者です。提供された中国語コンテンツを日本語に翻訳し、専門用語と文脈の正確性を保ってください。

要件：
1. 調達・入札用語の技術的正確性を維持
2. 元のコンテンツの構造と形式を保持  
3. 適切なビジネス日本語用語を使用
4. 追加説明なしに翻訳されたJSONコンテンツのみ返却`

	case agentCommon.LanguageKorean:
		return `당신은 입찰 및 조달 문서 전문 번역자입니다. 제공된 중국어 콘텐츠를 한국어로 번역하면서 전문 용어와 맥락의 정확성을 유지하십시오.

요구사항:
1. 조달 및 입찰 용어의 기술적 정확성 유지
2. 원본 콘텐츠의 구조와 형식 유지
3. 적절한 비즈니스 한국어 용어 사용
4. 추가 설명 없이 번역된 JSON 콘텐츠만 반환`

	default:
		return `You are a professional translator. Translate the provided Chinese content accurately while maintaining the original structure and meaning.`
	}
}

// buildTranslationUserPrompt 构建翻译用户提示词
func (a *MultilingualAgent) buildTranslationUserPrompt(contentToTranslate map[string]interface{}, targetLang string) string {
	contentJSON, _ := json.MarshalIndent(contentToTranslate, "", "  ")

	langName := map[string]string{
		agentCommon.LanguageEnglish:  "English",
		agentCommon.LanguageJapanese: "Japanese",
		agentCommon.LanguageKorean:   "Korean",
	}[targetLang]

	if langName == "" {
		langName = targetLang
	}

	return fmt.Sprintf("Please translate the following Chinese tender-related content to %s. Maintain the JSON structure and translate all text values:\n\n%s\n\nReturn only the translated JSON without any additional text.", langName, string(contentJSON))
}

// parseTranslationResult 解析翻译结果
func (a *MultilingualAgent) parseTranslationResult(jsonResult string) (map[string]interface{}, error) {
	var result map[string]interface{}

	// 清理可能的markdown格式
	jsonResult = strings.TrimSpace(jsonResult)
	jsonResult = strings.TrimPrefix(jsonResult, "```json")
	jsonResult = strings.TrimPrefix(jsonResult, "```")
	jsonResult = strings.TrimSuffix(jsonResult, "```")
	jsonResult = strings.TrimSpace(jsonResult)

	if err := json.Unmarshal([]byte(jsonResult), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %v", err)
	}

	return result, nil
}

// assessTranslationQuality 评估翻译质量
func (a *MultilingualAgent) assessTranslationQuality(original, translated map[string]interface{}) float64 {
	// 简化的质量评估：检查结构完整性
	if len(translated) == 0 {
		return 0.0
	}

	structureScore := float64(len(translated)) / float64(len(original))
	if structureScore > 1.0 {
		structureScore = 1.0
	}

	// 基础质量分数（可以根据需要添加更复杂的评估逻辑）
	baseQuality := 0.85

	return structureScore * baseQuality
}

// calculateOverallQuality 计算整体质量分数
func (a *MultilingualAgent) calculateOverallQuality(qualityScores map[string]float64) float64 {
	if len(qualityScores) == 0 {
		return 0.0
	}

	var total float64
	for _, score := range qualityScores {
		total += score
	}

	return total / float64(len(qualityScores))
}
