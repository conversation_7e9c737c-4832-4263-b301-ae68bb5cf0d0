#!/bin/bash
# Dockerfile for taskd-bvt-runner
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies including network tools
RUN apt-get update && apt-get install -y dnsutils curl iputils-ping

# Copy the test directory contents from the build context
# The context should be the 'taskd' directory.
COPY ./test/ /app/test/

# Install dependencies from the test directory's requirements.txt
RUN pip install --no-cache-dir -r /app/test/requirements.txt

# Make the main test runner script executable
RUN chmod +x /app/test/run_all_bvt_tests.sh

# Set the default command to run all BVT tests
CMD ["/app/test/run_all_bvt_tests.sh"] 