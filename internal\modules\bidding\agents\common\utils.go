package common

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gitlab.com/specific-ai/taskd/internal/utils"
)

// GenerateTaskID generates a unique task ID
func GenerateTaskID(prefix string, skillID string) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("task_%s_%d_%s", prefix, timestamp, skillID)
}

// GenerateTraceID generates a unique trace ID
func GenerateTraceID(prefix string) string {
	timestamp := time.Now().UnixNano()
	hash := md5.Sum([]byte(fmt.Sprintf("%s_%d", prefix, timestamp)))
	return fmt.Sprintf("%s_%x", prefix, hash[:8])
}

// ValidateLanguage validates if the language is supported
func ValidateLanguage(language string) bool {
	supportedLanguages := []string{
		LanguageChinese,
		LanguageEnglish,
		LanguageJapanese,
		LanguageKorean,
	}
	
	for _, lang := range supportedLanguages {
		if language == lang {
			return true
		}
	}
	return false
}

// ValidateExtractionMode validates extraction mode
func ValidateExtractionMode(mode string) bool {
	validModes := []string{
		ExtractionModeStandard,
		ExtractionModeDetailed,
		ExtractionModeQuick,
	}
	
	for _, validMode := range validModes {
		if mode == validMode {
			return true
		}
	}
	return false
}

// ValidateClassificationDepth validates classification depth
func ValidateClassificationDepth(depth int) bool {
	return depth >= ClassificationDepthLevel1 && depth <= ClassificationDepthLevel3
}

// ValidatePaginationConfig validates pagination configuration
func ValidatePaginationConfig(config *PaginationConfig) error {
	if config == nil {
		return nil
	}
	
	if config.Page < 1 {
		return fmt.Errorf("page must be >= 1")
	}
	
	if config.Limit < 1 {
		return fmt.Errorf("limit must be >= 1")
	}
	
	if config.Limit > DefaultMaxPageSize {
		return fmt.Errorf("limit cannot exceed %d", DefaultMaxPageSize)
	}
	
	if config.Offset < 0 {
		return fmt.Errorf("offset must be >= 0")
	}
	
	return nil
}

// ValidateTimeRange validates time range
func ValidateTimeRange(timeRange *TimeRange) error {
	if timeRange == nil {
		return nil
	}
	
	if timeRange.StartTime == "" || timeRange.EndTime == "" {
		return fmt.Errorf("start_time and end_time are required")
	}
	
	startTime, err := time.Parse(time.RFC3339, timeRange.StartTime)
	if err != nil {
		return fmt.Errorf("invalid start_time format: %v", err)
	}
	
	endTime, err := time.Parse(time.RFC3339, timeRange.EndTime)
	if err != nil {
		return fmt.Errorf("invalid end_time format: %v", err)
	}
	
	if startTime.After(endTime) {
		return fmt.Errorf("start_time must be before end_time")
	}
	
	return nil
}

// ValidateQualityScore validates quality score range
func ValidateQualityScore(score float64) bool {
	return score >= QualityScoreMinimum && score <= QualityScoreMaximum
}

// ValidateConfidenceScore validates confidence score range
func ValidateConfidenceScore(score float64) bool {
	return score >= ConfidenceScoreMinimum && score <= ConfidenceScoreMaximum
}

// SanitizeText removes unwanted characters and normalizes text
func SanitizeText(text string) string {
	// Remove control characters
	re := regexp.MustCompile(`[\x00-\x1F\x7F]`)
	text = re.ReplaceAllString(text, "")
	
	// Normalize whitespace
	re = regexp.MustCompile(`\s+`)
	text = re.ReplaceAllString(text, " ")
	
	return strings.TrimSpace(text)
}

// ExtractNumbers extracts numeric values from text
func ExtractNumbers(text string) []float64 {
	re := regexp.MustCompile(`\d+(?:\.\d+)?`)
	matches := re.FindAllString(text, -1)
	
	var numbers []float64
	for _, match := range matches {
		if num, err := strconv.ParseFloat(match, 64); err == nil {
			numbers = append(numbers, num)
		}
	}
	return numbers
}

// CalculateCompleteness calculates data completeness score
func CalculateCompleteness(data interface{}) float64 {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return 0.0
	}
	
	var dataMap map[string]interface{}
	if err := json.Unmarshal(dataBytes, &dataMap); err != nil {
		return 0.0
	}
	
	totalFields := countFields(dataMap)
	nonEmptyFields := countNonEmptyFields(dataMap)
	
	if totalFields == 0 {
		return 0.0
	}
	
	return float64(nonEmptyFields) / float64(totalFields)
}

// countFields recursively counts all fields in a map
func countFields(data map[string]interface{}) int {
	count := 0
	for _, value := range data {
		count++
		if nested, ok := value.(map[string]interface{}); ok {
			count += countFields(nested)
			count-- // Don't double-count the nested map itself
		}
	}
	return count
}

// countNonEmptyFields recursively counts non-empty fields
func countNonEmptyFields(data map[string]interface{}) int {
	count := 0
	for _, value := range data {
		if !isEmptyValue(value) {
			count++
			if nested, ok := value.(map[string]interface{}); ok {
				count += countNonEmptyFields(nested)
				count-- // Don't double-count the nested map itself
			}
		}
	}
	return count
}

// isEmptyValue checks if a value is considered empty
func isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}
	
	switch v := value.(type) {
	case string:
		return strings.TrimSpace(v) == ""
	case []interface{}:
		return len(v) == 0
	case map[string]interface{}:
		return len(v) == 0
	case int, int32, int64:
		return v == 0
	case float32, float64:
		return v == 0
	case bool:
		return false // boolean values are never considered empty
	default:
		return false
	}
}

// MergeMetadata merges processing metadata
func MergeMetadata(metadatas ...*ProcessingMetadata) *ProcessingMetadata {
	if len(metadatas) == 0 {
		return &ProcessingMetadata{}
	}
	
	merged := &ProcessingMetadata{
		TokensConsumed: 0,
		ProcessingTime: 0,
	}
	
	for _, metadata := range metadatas {
		if metadata != nil {
			merged.TokensConsumed += metadata.TokensConsumed
			merged.ProcessingTime += metadata.ProcessingTime
			if metadata.ModelUsed != "" {
				merged.ModelUsed = metadata.ModelUsed
			}
		}
	}
	
	return merged
}

// CalculatePriorityScore calculates priority score for candidates
func CalculatePriorityScore(data *CandidateData) float64 {
	score := 0.0
	
	// Content length factor (normalized to 0-2 points)
	if len(data.RawContent) > 0 {
		lengthScore := float64(len(data.RawContent)) / 1000.0
		if lengthScore > 2.0 {
			lengthScore = 2.0
		}
		score += lengthScore
	}
	
	// Title quality factor (0-2 points)
	if data.Title != "" {
		titleScore := 1.0
		if len(data.Title) > 50 && len(data.Title) < 200 {
			titleScore = 2.0
		}
		score += titleScore
	}
	
	// Crawler reliability factor (0-2 points)
	crawlerScore := getCrawlerReliabilityScore(data.CrawlerName)
	score += crawlerScore
	
	// Recency factor (0-2 points)
	daysSinceUpdate := time.Since(data.CrawlTime).Hours() / 24
	if daysSinceUpdate <= 1 {
		score += 2.0
	} else if daysSinceUpdate <= 7 {
		score += 1.5
	} else if daysSinceUpdate <= 30 {
		score += 1.0
	} else {
		score += 0.5
	}
	
	// Content quality factor (0-2 points)
	if data.ContentQuality != nil {
		if completeness, ok := data.ContentQuality["completeness"].(float64); ok {
			score += completeness * 2.0
		}
	}
	
	return score
}

// getCrawlerReliabilityScore returns reliability score for different crawlers
func getCrawlerReliabilityScore(crawlerName string) float64 {
	reliabilityMap := map[string]float64{
		"ungm":                    2.0,
		"world_bank":             2.0,
		"ted_europa":             1.8,
		"government_procurement": 1.8,
		"enterprise_procurement": 1.5,
		"construction_tenders":   1.3,
	}
	
	if score, exists := reliabilityMap[crawlerName]; exists {
		return score
	}
	return 1.0 // default score
}

// LogError logs error with structured format
func LogError(operation string, err error, context map[string]interface{}) {
	logData := map[string]interface{}{
		"operation": operation,
		"error":     err.Error(),
	}
	
	// Merge context
	for k, v := range context {
		logData[k] = v
	}
	
	utils.Log.WithFields(logData).Error("Operation failed")
}

// LogInfo logs info with structured format
func LogInfo(operation string, message string, context map[string]interface{}) {
	logData := map[string]interface{}{
		"operation": operation,
		"message":   message,
	}
	
	// Merge context
	for k, v := range context {
		logData[k] = v
	}
	
	utils.Log.WithFields(logData).Info(message)
}

// ConvertToMap converts struct to map for flexible processing
func ConvertToMap(data interface{}) (map[string]interface{}, error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	
	var result map[string]interface{}
	if err := json.Unmarshal(dataBytes, &result); err != nil {
		return nil, err
	}
	
	return result, nil
}