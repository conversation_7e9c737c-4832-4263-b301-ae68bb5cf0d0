package services

import (
	"context"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"gopkg.in/yaml.v2"
)

// enhancedLLMServiceImpl 增强LLM服务实现
type enhancedLLMServiceImpl struct {
	baseService    LLMService
	tokenService   TokenService
	templateLoader TemplateLoader

	// 统计信息
	stats      *LLMServiceStats
	statsMutex sync.RWMutex

	// 批量处理配置
	maxBatchSize       int
	maxConcurrentBatch int
	defaultTimeout     time.Duration
}

// NewEnhancedLLMService 创建增强的LLM服务
func NewEnhancedLLMService(
	baseService LLMService,
	tokenService TokenService,
	templateLoader TemplateLoader,
) EnhancedLLMService {
	return &enhancedLLMServiceImpl{
		baseService:        baseService,
		tokenService:       tokenService,
		templateLoader:     templateLoader,
		stats:              &LLMServiceStats{},
		maxBatchSize:       50, // 默认最大批次大小
		maxConcurrentBatch: 5,  // 默认最大并发批次数
		defaultTimeout:     30 * time.Second,
	}
}

// ProcessRequest 处理单个请求（实现原有接口）
func (s *enhancedLLMServiceImpl) ProcessRequest(ctx context.Context, params models.OpenAICompatibleRequestParams) (*models.LLMResponseMessage, error) {
	startTime := time.Now()

	s.updateStatsStart()

	result, err := s.baseService.ProcessRequest(ctx, params)

	duration := time.Since(startTime)
	s.updateStatsEnd(err == nil, duration, result)

	return result, err
}

// ProcessBatchRequest 处理批量请求
func (s *enhancedLLMServiceImpl) ProcessBatchRequest(ctx context.Context, requests []*BatchRequestItem) (*BatchResponseResult, error) {
	startTime := time.Now()

	// 验证批量请求
	if len(requests) == 0 {
		return nil, fmt.Errorf("batch request cannot be empty")
	}

	if len(requests) > s.maxBatchSize {
		return nil, fmt.Errorf("batch size %d exceeds maximum %d", len(requests), s.maxBatchSize)
	}

	utils.Log.Infof("Processing batch request with %d items", len(requests))

	// 按优先级排序
	s.sortRequestsByPriority(requests)

	// 并发处理
	results := make([]*BatchResponseItem, len(requests))
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, s.maxConcurrentBatch)

	for i, request := range requests {
		wg.Add(1)
		go func(index int, req *BatchRequestItem) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 设置超时
			reqCtx := ctx
			if req.Timeout > 0 {
				var cancel context.CancelFunc
				reqCtx, cancel = context.WithTimeout(ctx, req.Timeout)
				defer cancel()
			}

			// 处理单个请求
			itemStartTime := time.Now()
			response, err := s.baseService.ProcessRequest(reqCtx, req.Params)
			duration := time.Since(itemStartTime)

			// 构建响应项
			results[index] = &BatchResponseItem{
				ID:       req.ID,
				Success:  err == nil,
				Response: response,
				Duration: duration,
			}

			if err != nil {
				results[index].Error = err.Error()
				utils.Log.Errorf("Batch item %s failed: %v", req.ID, err)
			}

		}(i, request)
	}

	wg.Wait()

	// 聚合结果
	totalDuration := time.Since(startTime)
	batchResult := s.aggregateBatchResults(results, totalDuration)

	// 更新统计
	s.updateBatchStats(batchResult)

	utils.Log.Infof("Batch request completed: %d/%d successful in %v",
		batchResult.SuccessfulCount, batchResult.TotalRequests, totalDuration)

	return batchResult, nil
}

// ProcessTemplateRequest 处理模板化请求
func (s *enhancedLLMServiceImpl) ProcessTemplateRequest(ctx context.Context, template *TemplateRequest) (*models.LLMResponseMessage, error) {
	// 加载模板
	promptTemplate, err := s.templateLoader.LoadTemplate(template.TemplateName, template.Language)
	if err != nil {
		return nil, fmt.Errorf("failed to load template: %v", err)
	}

	// 渲染模板
	prompt, err := promptTemplate.Render(template.Variables)
	if err != nil {
		return nil, fmt.Errorf("failed to render template: %v", err)
	}

	// 构建请求参数
	temperature := float64(template.Temperature)
	maxTokens := template.MaxTokens

	params := models.OpenAICompatibleRequestParams{
		Model: template.Model,
		Messages: []common.LLMMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Temperature: &temperature,
		MaxTokens:   &maxTokens,
	}

	// 处理请求
	return s.ProcessRequest(ctx, params)
}

// GetServiceStats 获取服务统计信息
func (s *enhancedLLMServiceImpl) GetServiceStats() *LLMServiceStats {
	s.statsMutex.RLock()
	defer s.statsMutex.RUnlock()

	statsCopy := *s.stats
	return &statsCopy
}

// sortRequestsByPriority 按优先级排序请求
func (s *enhancedLLMServiceImpl) sortRequestsByPriority(requests []*BatchRequestItem) {
	// 使用简单的冒泡排序按优先级降序排列
	for i := 0; i < len(requests)-1; i++ {
		for j := 0; j < len(requests)-i-1; j++ {
			if requests[j].Priority < requests[j+1].Priority {
				requests[j], requests[j+1] = requests[j+1], requests[j]
			}
		}
	}
}

// aggregateBatchResults 聚合批量处理结果
func (s *enhancedLLMServiceImpl) aggregateBatchResults(items []*BatchResponseItem, totalDuration time.Duration) *BatchResponseResult {
	result := &BatchResponseResult{
		TotalRequests: len(items),
		TotalDuration: totalDuration,
		Items:         items,
		TokenUsage: &BatchTokenUsage{
			TotalInputTokens:  0,
			TotalOutputTokens: 0,
			TotalTokens:       0,
		},
	}

	for _, item := range items {
		if item.Success {
			result.SuccessfulCount++

			// 聚合token使用情况
			if item.Response != nil {
				result.TokenUsage.TotalInputTokens += item.Response.TokenUsage.InputTokens
				result.TokenUsage.TotalOutputTokens += item.Response.TokenUsage.OutputTokens
				result.TokenUsage.TotalTokens += item.Response.TokenUsage.TotalTokens
			}
		} else {
			result.FailedCount++
		}
	}

	return result
}

// updateStatsStart 更新开始处理的统计信息
func (s *enhancedLLMServiceImpl) updateStatsStart() {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	s.stats.TotalRequests++
	s.stats.LastRequestTime = time.Now()
}

// updateStatsEnd 更新处理结束的统计信息
func (s *enhancedLLMServiceImpl) updateStatsEnd(success bool, duration time.Duration, response *models.LLMResponseMessage) {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	if success {
		s.stats.SuccessfulRequests++
		if response != nil {
			s.stats.TotalTokensUsed += int64(response.TokenUsage.TotalTokens)
		}
	} else {
		s.stats.FailedRequests++
	}

	// 更新平均响应时间
	if s.stats.TotalRequests > 0 {
		totalDuration := s.stats.AverageResponseTime * float64(s.stats.TotalRequests-1)
		s.stats.AverageResponseTime = (totalDuration + duration.Seconds()) / float64(s.stats.TotalRequests)
	}
}

// updateBatchStats 更新批量处理的统计信息
func (s *enhancedLLMServiceImpl) updateBatchStats(result *BatchResponseResult) {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	s.stats.BatchRequestsCount++
}

// TemplateLoader 模板加载器接口
type TemplateLoader interface {
	LoadTemplate(templateName, language string) (*PromptTemplate, error)
}

// PromptTemplate prompt模板
type PromptTemplate struct {
	Name      string            `json:"name"`
	Language  string            `json:"language"`
	Content   string            `json:"content"`
	Variables map[string]string `json:"variables"`
}

// Render 渲染模板
func (t *PromptTemplate) Render(variables map[string]interface{}) (string, error) {
	content := t.Content

	// 简单的变量替换实现
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		valueStr := fmt.Sprintf("%v", value)
		content = strings.Replace(content, placeholder, valueStr, -1)
	}

	return content, nil
}

// fileTemplateLoader 基于文件的模板加载器实现
type fileTemplateLoader struct {
	templateDir string
}

// NewFileTemplateLoader 创建文件模板加载器
func NewFileTemplateLoader(templateDir string) TemplateLoader {
	return &fileTemplateLoader{
		templateDir: templateDir,
	}
}

// LoadTemplate 加载模板
func (f *fileTemplateLoader) LoadTemplate(templateName, language string) (*PromptTemplate, error) {
	// 构建文件路径
	filename := fmt.Sprintf("%s_%s.yaml", templateName, language)
	filePath := filepath.Join(f.templateDir, filename)

	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read template file %s: %v", filePath, err)
	}

	// 解析YAML
	var templateData struct {
		Name         string            `yaml:"name"`
		Description  string            `yaml:"description"`
		Language     string            `yaml:"language"`
		SystemPrompt string            `yaml:"system_prompt"`
		UserPrompt   string            `yaml:"user_prompt"`
		Variables    map[string]string `yaml:"variables"`
	}

	if err := yaml.Unmarshal(data, &templateData); err != nil {
		return nil, fmt.Errorf("failed to parse template YAML: %v", err)
	}

	// 组合system和user prompt
	content := templateData.SystemPrompt
	if templateData.UserPrompt != "" {
		content += "\n\n" + templateData.UserPrompt
	}

	return &PromptTemplate{
		Name:      templateData.Name,
		Language:  templateData.Language,
		Content:   content,
		Variables: templateData.Variables,
	}, nil
}
