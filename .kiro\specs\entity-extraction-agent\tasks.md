# 实体提取Agent实现计划

## 任务列表

- [x] 1. 创建基础项目结构和核心接口


  - 创建entity_extraction模块目录结构
  - 定义核心数据模型和接口
  - 实现基础的Agent接口框架
  - _需求: 1.1, 1.2, 6.1_


- [x] 2. 实现JSON Schema验证和处理


  - [x] 2.1 创建JSON Schema验证器


    - 实现标准JSON Schema格式验证
    - 编写Schema结构解析和验证逻辑
    - 支持复杂嵌套对象和数组类型验证
    - _需求: 1.1, 3.1_









  - [x] 2.2 实现Schema处理服务
    - 编写Schema解析和验证功能
    - 实现Schema合法性检查和错误提示
    - 添加Schema示例数据验证
    - _需求: 2.1, 3.2_




- [ ] 3. 实现LLM提取引擎核心组件
  - [x] 3.1 创建PromptBuilder提示词构建器
    - 实现系统提示词和用户提示词构建逻辑
    - 支持多语言提示词模板（中英文）
    - 实现模型Schema注入和示例构建功能
    - _需求: 4.1, 7.1_

  - [x] 3.2 创建LLMProcessor处理器
    - 创建LLMProcessor服务类集成现有LLMClient
    - 实现LLM调用的重试逻辑和错误处理
    - 集成Token使用统计和管理
    - 实现提取请求的完整处理流程
    - _需求: 5.1, 5.2, 9.1_

  - [x] 3.3 创建ResponseParser响应解析器
    - 实现JSON响应解析和提取逻辑
    - 编写置信度计算算法
    - 实现数据结构验证功能
    - _需求: 4.2, 4.3_


- [x] 4. 实现输入输出验证器

  - [x] 4.1 创建输入验证器
    - 实现文本长度、格式、语言检查
    - 编写业务类型和模型名称验证
    - 添加参数完整性检查
    - _需求: 3.1, 3.2_

  - [x] 4.2 实现输出验证器
    - 编写模型Schema匹配验证
    - 实现必填字段和数据类型检查
    - 添加数据质量评估逻辑
    - _需求: 3.3, 4.3_



- [x] 5. 实现错误处理和降级机制
  - [x] 5.1 创建错误类型定义和处理器
    - 定义所有错误类型常量和结构
    - 实现错误分类和处理逻辑
    - 编写错误信息本地化支持
    - _需求: 4.4, 8.1_

  - [x] 5.2 实现降级策略和重试机制
    - 编写LLM调用失败的重试逻辑
    - 实现置信度阈值动态调整
    - 添加无法提取情况的处理机制
    - _需求: 4.4, 8.1_



- [ ] 6. 创建EntityExtractionAgent主类
  - [x] 6.1 实现Agent核心接口
    - 继承BaseAgent并实现所有必需接口方法
    - 定义支持的能力列表和Schema
    - 实现Agent生命周期管理
    - _需求: 6.1, 9.1_

  - [x] 6.2 实现通用结构化数据提取能力
    - 编写extract_structured_data唯一核心能力
    - 实现基于JSON Schema的动态提取逻辑
    - 支持任意业务模型的实体提取
    - 添加多种复杂数据类型处理（日期、枚举、嵌套对象等）
    - _需求: 1.1, 1.2, 1.3_


- [x] 7. 实现动态提示词构建系统


  - [x] 7.1 创建通用提示词模板引擎
    - 实现基于JSON Schema的动态提示词生成
    - 支持多语言提示词模板（中英文混合）
    - 编写Schema到自然语言描述的转换逻辑
    - 实现示例数据的智能注入机制
    - _需求: 1.1, 7.1_






  - [x] 7.2 实现复杂数据类型处理


    - 支持日期时间格式的智能识别和转换
    - 实现枚举类型的约束处理




    - 添加嵌套对象和数组的递归处理
    - 编写可选字段和必填字段的区分逻辑
    - _需求: 7.1, 7.2_

- [x] 8. 实现配置管理


  - [ ] 8.1 创建配置管理器
    - 实现Agent配置加载和验证
    - 编写基础配置管理（LLM模型、超时等）
    - 添加运行时配置更新支持
    - _需求: 7.2, 9.1_



- [ ] 9. 集成TaskD Agent框架
  - [ ] 9.1 实现Agent注册和管理
    - 将EntityExtractionAgent注册到AgentManager
    - 实现健康检查和状态监控
    - 符合TaskD Agent管理规范
    - _需求: 6.1, 9.1_



  - [ ] 9.2 集成LLM客户端和Token管理
    - 使用现有LLM客户端进行API调用
    - 集成Token管理和使用统计
    - 实现并发控制和限流

    - _需求: 5.2, 9.1_

- [ ] 10. 编写Python测试脚本
  - [ ] 10.1 创建A2A接口层级测试
    - 编写Python测试脚本针对Agent API接口
    - 实现商机维度提取测试用例
    - 添加招标关键词信息提取测试用例
    - 创建分析角度操作提取测试用例
    - 实现查询条件提取测试用例
    - _需求: 1.1, 1.2, 1.3_

  - [ ] 10.2 实现错误处理和边界测试
    - 编写无效输入测试用例
    - 实现Schema验证失败测试
    - 添加LLM调用失败测试
    - 创建置信度过低测试用例
    - _需求: 4.4, 8.1_

- [ ] 11. 创建文档和说明
  - [ ] 11.1 编写Agent说明文档
    - 创建docs/ENTITY_EXTRACTION_AGENT.md文档
    - 编写Agent能力详细说明
    - 添加输入输出Schema规范
    - 包含错误码和处理说明
    - _需求: 3.1, 3.2, 3.3_

  - [x] 11.2 创建消息结构定义文档

    - 编写docs/ENTITY_EXTRACTION_MESSAGE_STRUCTURE.md
    - 定义所有请求和响应消息结构
    - 添加业务场景使用示例
    - 包含复杂数据类型处理说明
    - _需求: 1.1, 1.2, 4.4_

  - [x] 11.3 创建使用示例和最佳实践



    - 编写商机维度提取示例（OpportunityDimension）
    - 实现招标关键词信息提取示例（BiddingKeyInfo）
    - 创建分析角度操作提取示例（DirectionConditionsList）
    - 添加查询条件提取示例（QueryConditionsList）
    - 编写复杂嵌套结构处理最佳实践
    - 添加日期时间格式处理示例
    - 实现枚举类型约束处理示例
    - 编写错误处理和降级策略最佳实践
    - _需求: 1.1, 1.2, 4.4_