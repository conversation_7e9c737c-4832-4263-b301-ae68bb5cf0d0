package entity_extraction

import (
	"context"
	"fmt"

	"gitlab.com/specific-ai/taskd/internal/core"
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"github.com/sirupsen/logrus"
)

// Registry 实体提取Agent注册器
type Registry struct {
	agentManager core.AgentManager
	factory      *Factory
	logger       *logrus.Logger
}

// NewRegistry 创建注册器
func NewRegistry(agentManager core.AgentManager) *Registry {
	return &Registry{
		agentManager: agentManager,
		factory:      NewFactory(),
		logger:       utils.Log,
	}
}

// RegisterEntityExtractionAgent 注册实体提取Agent
func (r *Registry) RegisterEntityExtractionAgent(ctx context.Context, llmClient llm.LLMClient, config map[string]interface{}) error {
	// 生成Agent ID
	agentID := "entity-extraction-agent-001"
	if customID, exists := config["agent_id"]; exists {
		if id, ok := customID.(string); ok && id != "" {
			agentID = id
		}
	}

	r.logger.WithFields(logrus.Fields{
		"agent_id": agentID,
		"config":   config,
	}).Info("开始注册实体提取Agent")

	// 创建Agent实例
	agent := r.factory.CreateWithConfig(agentID, llmClient, config)

	// 注册到Agent管理器
	if err := r.agentManager.RegisterAgent(ctx, agent); err != nil {
		return fmt.Errorf("注册实体提取Agent失败: %w", err)
	}

	r.logger.WithField("agent_id", agentID).Info("实体提取Agent注册成功")

	return nil
}

// UnregisterEntityExtractionAgent 注销实体提取Agent
func (r *Registry) UnregisterEntityExtractionAgent(ctx context.Context, agentID string) error {
	r.logger.WithField("agent_id", agentID).Info("开始注销实体提取Agent")

	if err := r.agentManager.UnregisterAgent(ctx, agentID); err != nil {
		return fmt.Errorf("注销实体提取Agent失败: %w", err)
	}

	r.logger.WithField("agent_id", agentID).Info("实体提取Agent注销成功")

	return nil
}

// GetEntityExtractionAgent 获取实体提取Agent
func (r *Registry) GetEntityExtractionAgent(ctx context.Context, agentID string) (*EntityExtractionAgent, error) {
	agent, err := r.agentManager.GetAgent(ctx, agentID)
	if err != nil {
		return nil, fmt.Errorf("获取实体提取Agent失败: %w", err)
	}

	// 类型断言
	entityAgent, ok := agent.(*EntityExtractionAgent)
	if !ok {
		return nil, fmt.Errorf("Agent %s 不是实体提取Agent类型", agentID)
	}

	return entityAgent, nil
}

// ListEntityExtractionAgents 列出所有实体提取Agent
func (r *Registry) ListEntityExtractionAgents(ctx context.Context) ([]*EntityExtractionAgent, error) {
	// 获取所有Agent
	response, err := r.agentManager.ListAgents(ctx, models.AgentListRequest{
		Type:  models.AgentTypeCustom, // 实体提取Agent使用自定义类型
		Limit: 100,                    // 获取所有
	})
	if err != nil {
		return nil, fmt.Errorf("获取Agent列表失败: %w", err)
	}

	var entityAgents []*EntityExtractionAgent

	// 过滤出实体提取Agent
	for _, instance := range response.Agents {
		agent, err := r.agentManager.GetAgent(ctx, instance.ID)
		if err != nil {
			r.logger.WithError(err).WithField("agent_id", instance.ID).Warn("获取Agent实例失败")
			continue
		}

		// 检查是否为实体提取Agent
		if entityAgent, ok := agent.(*EntityExtractionAgent); ok {
			entityAgents = append(entityAgents, entityAgent)
		}
	}

	return entityAgents, nil
}

// ExecuteEntityExtraction 执行实体提取
func (r *Registry) ExecuteEntityExtraction(ctx context.Context, agentID string, req *models.AgentExecutionRequest) (*models.AgentResponse, error) {
	// 验证能力
	if req.Capability != "extract_structured_data" {
		return nil, fmt.Errorf("实体提取Agent只支持 extract_structured_data 能力，收到: %s", req.Capability)
	}

	// 执行Agent
	response, err := r.agentManager.ExecuteAgent(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("执行实体提取失败: %w", err)
	}

	return response, nil
}

// HealthCheckEntityExtractionAgent 检查实体提取Agent健康状态
func (r *Registry) HealthCheckEntityExtractionAgent(ctx context.Context, agentID string) (*models.AgentHealthCheck, error) {
	agent, err := r.GetEntityExtractionAgent(ctx, agentID)
	if err != nil {
		return nil, err
	}

	return agent.HealthCheck(ctx)
}

// GetEntityExtractionAgentMetrics 获取实体提取Agent指标
func (r *Registry) GetEntityExtractionAgentMetrics(ctx context.Context, agentID string) (*models.AgentMetrics, error) {
	return r.agentManager.GetAgentMetrics(ctx, agentID)
}

// RegisterDefaultEntityExtractionAgent 注册默认的实体提取Agent
func (r *Registry) RegisterDefaultEntityExtractionAgent(ctx context.Context, llmClient llm.LLMClient) error {
	defaultConfig := map[string]interface{}{
		"agent_id":    "entity-extraction-agent-001",
		"name":        "通用实体提取Agent",
		"version":     "1.0.0",
		"model":       "deepseek-v3-250324",
		"temperature": 0.1,
		"max_tokens":  4000,
	}

	return r.RegisterEntityExtractionAgent(ctx, llmClient, defaultConfig)
}

// CreateAgentCard 创建Agent卡片
func (r *Registry) CreateAgentCard() *models.AgentCard {
	return &models.AgentCard{
		ID:          "entity-extraction-agent",
		Name:        "通用实体提取Agent",
		Type:        models.AgentTypeCustom,
		Description: "专门用于从文本中提取符合JSON Schema的结构化数据",
		Version:     "1.0.0",
		Author:      "TaskD Team",
		Capabilities: []models.AgentCapability{
			{
				Name:        "extract_structured_data",
				Description: "通用结构化数据提取，支持任意JSON Schema定义的业务模型",
				InputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"input_text": map[string]interface{}{
							"type":        "string",
							"description": "输入文本内容",
						},
						"target_schema": map[string]interface{}{
							"type":        "object",
							"description": "目标JSON Schema定义",
						},
						"language": map[string]interface{}{
							"type":        "string",
							"enum":        []string{"zh", "en", "mixed"},
							"description": "输入文本语言",
						},
					},
					"required": []string{"input_text", "target_schema"},
				},
				OutputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"success": map[string]interface{}{
							"type":        "boolean",
							"description": "提取是否成功",
						},
						"data": map[string]interface{}{
							"type":        "object",
							"description": "提取的结构化数据",
						},
						"confidence": map[string]interface{}{
							"type":        "number",
							"description": "提取结果的置信度",
						},
					},
				},
				Required: []string{"input_text", "target_schema"},
			},
		},
		Dependencies: []string{"llm-client"},
		Metadata: map[string]interface{}{
			"category":    "extraction",
			"tags":        []string{"nlp", "extraction", "json-schema"},
			"supported_languages": []string{"zh", "en", "mixed"},
		},
	}
}

// ValidateAgentConfig 验证Agent配置
func (r *Registry) ValidateAgentConfig(config map[string]interface{}) error {
	// 验证必需的配置项
	requiredFields := []string{"agent_id", "model"}
	
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("缺少必需的配置项: %s", field)
		}
	}

	// 验证agent_id
	if agentID, ok := config["agent_id"].(string); !ok || agentID == "" {
		return fmt.Errorf("agent_id必须是非空字符串")
	}

	// 验证model
	if model, ok := config["model"].(string); !ok || model == "" {
		return fmt.Errorf("model必须是非空字符串")
	}

	// 验证可选配置项
	if temperature, exists := config["temperature"]; exists {
		if temp, ok := temperature.(float64); !ok || temp < 0 || temp > 2 {
			return fmt.Errorf("temperature必须是0-2之间的数字")
		}
	}

	if maxTokens, exists := config["max_tokens"]; exists {
		if tokens, ok := maxTokens.(float64); !ok || tokens <= 0 {
			return fmt.Errorf("max_tokens必须是正整数")
		}
	}

	return nil
}