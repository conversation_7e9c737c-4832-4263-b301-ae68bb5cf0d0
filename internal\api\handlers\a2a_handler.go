package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// A2AHandler handles A2A protocol requests
type A2AHandler struct {
	agentRegistry AgentRegistry
	validator     *validator.Validate
}

// AgentRegistry interface for agent management
type AgentRegistry interface {
	GetAgent(agentID string) (Agent, error)
	ListAgents() []biddingModels.RegisteredAgent
	RegisterAgent(agent biddingModels.RegisteredAgent) error
	UnregisterAgent(agentID string) error
	UpdateAgentStatus(agentID string, status biddingModels.AgentStatus) error
}

// Agent interface represents an A2A agent
type Agent interface {
	ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error)
	GetAgentCard() biddingModels.AgentCard
	HealthCheck() biddingModels.HealthStatus
	GetID() string
}

// NewA2AHandler creates a new A2A handler
func NewA2AHandler(registry AgentRegistry) *A2AHandler {
	return &A2AHandler{
		agentRegistry: registry,
		validator:     validator.New(),
	}
}

// HandleAgentRequest handles A2A protocol requests to specific agents
func (h *A2AHandler) HandleAgentRequest(c *gin.Context) {
	// Extract agent ID from path
	agentID := strings.TrimPrefix(c.Param("agentPath"), "/")
	if agentID == "" {
		h.sendError(c, biddingModels.ErrorCodeInvalidRequest, "Agent ID is required", nil)
		return
	}

	// Parse request body
	var request biddingModels.A2ARequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.sendError(c, biddingModels.ErrorCodeParseError, "Invalid JSON format", err.Error())
		return
	}

	// Validate request
	if err := h.validator.Struct(&request); err != nil {
		h.sendError(c, biddingModels.ErrorCodeInvalidRequest, "Request validation failed", err.Error())
		return
	}

	// Get agent from registry
	agent, err := h.agentRegistry.GetAgent(agentID)
	if err != nil {
		h.sendError(c, biddingModels.ErrorCodeMethodNotFound, fmt.Sprintf("Agent not found: %s", agentID), err.Error())
		return
	}

	// Prepare context
	context := biddingModels.A2AContext{
		UserID:    c.GetHeader("X-User-ID"),
		CompanyID: c.GetHeader("X-Company-ID"),
		TraceID:   c.GetHeader("X-Trace-ID"),
		Language:  c.GetHeader("X-Language"),
		RequestID: request.ID,
		Timestamp: time.Now(),
	}

	// Execute skill
	result, err := agent.ExecuteSkill(request.Params.SkillID, request.Params.Input, context)
	if err != nil {
		h.sendError(c, biddingModels.ErrorCodeInternalError, "Skill execution failed", err.Error())
		return
	}

	// Send response
	response := biddingModels.A2AResponse{
		JsonRPC: "2.0",
		ID:      request.ID,
		Result:  result,
	}

	c.JSON(http.StatusOK, response)
}

// HandleAgentCard serves the agent card for a specific agent
func (h *A2AHandler) HandleAgentCard(c *gin.Context) {
	agentID := strings.TrimPrefix(c.Param("agentPath"), "/")
	if agentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	agent, err := h.agentRegistry.GetAgent(agentID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Agent not found: %s", agentID)})
		return
	}

	agentCard := agent.GetAgentCard()
	c.JSON(http.StatusOK, agentCard)
}

// HandleAgentHealth handles health check requests
func (h *A2AHandler) HandleAgentHealth(c *gin.Context) {
	agentID := c.Param("agentName")  // 修正参数名从 agentPath 改为 agentName
	if agentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Agent ID is required"})
		return
	}

	agent, err := h.agentRegistry.GetAgent(agentID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": fmt.Sprintf("Agent not found: %s", agentID)})
		return
	}

	healthStatus := agent.HealthCheck()

	statusCode := http.StatusOK
	if healthStatus.Status != biddingModels.AgentStatusHealthy {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, healthStatus)
}

// HandleListAgents lists all registered agents
func (h *A2AHandler) HandleListAgents(c *gin.Context) {
	agents := h.agentRegistry.ListAgents()

	response := gin.H{
		"agents":    agents,
		"count":     len(agents),
		"timestamp": time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// HandleAgentRegistry handles agent registration
func (h *A2AHandler) HandleAgentRegistry(c *gin.Context) {
	var agent biddingModels.RegisteredAgent
	if err := c.ShouldBindJSON(&agent); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	agent.RegisterTime = time.Now()
	agent.LastSeen = time.Now()

	if err := h.agentRegistry.RegisterAgent(agent); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message":  "Agent registered successfully",
		"agent_id": agent.ID,
	})
}

// HandleAgentDocumentation serves a web interface for viewing agent documentation
func (h *A2AHandler) HandleAgentDocumentation(c *gin.Context) {
	agents := h.agentRegistry.ListAgents()

	// Generate HTML documentation page
	html := h.generateDocumentationHTML(agents)

	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(http.StatusOK, html)
}

// generateDocumentationHTML generates HTML documentation for agents
func (h *A2AHandler) generateDocumentationHTML(agents []biddingModels.RegisteredAgent) string {
	html := `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaskD Agents Documentation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
        .agent { margin-bottom: 40px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        .agent-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .agent-name { font-size: 24px; font-weight: bold; color: #333; }
        .agent-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-healthy { background: #d4edda; color: #155724; }
        .status-unhealthy { background: #f8d7da; color: #721c24; }
        .agent-description { color: #666; margin-bottom: 20px; line-height: 1.6; }
        .skills { margin-top: 20px; }
        .skill { background: white; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .skill-name { font-weight: bold; color: #007bff; margin-bottom: 8px; }
        .skill-description { color: #555; margin-bottom: 10px; }
        .skill-tags { margin-top: 10px; }
        .tag { display: inline-block; background: #e9ecef; color: #495057; padding: 2px 8px; border-radius: 4px; font-size: 12px; margin-right: 5px; }
        .endpoint { background: #f8f9fa; padding: 8px 12px; border-radius: 4px; font-family: monospace; color: #495057; margin: 10px 0; }
        .json-code { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; font-size: 12px; overflow-x: auto; margin: 10px 0; }
        .section-title { font-size: 18px; font-weight: bold; margin-top: 25px; margin-bottom: 15px; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .nav { text-align: center; margin-bottom: 30px; }
        .nav a { margin: 0 15px; color: #007bff; text-decoration: none; font-weight: bold; }
        .nav a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TaskD Agents Documentation</h1>
            <p>A2A Protocol Compatible Agents Registry</p>
        </div>
        
        <div class="nav">
            <a href="/agents/docs">🏠 Home</a>
            <a href="/agents">📋 API List</a>
            <a href="/health">💚 Health</a>
        </div>`

	if len(agents) == 0 {
		html += `
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>No agents registered</h3>
            <p>No agents are currently registered in the system.</p>
        </div>`
	} else {
		for _, agent := range agents {
			statusClass := "status-healthy"
			if agent.Status.Status != biddingModels.AgentStatusHealthy {
				statusClass = "status-unhealthy"
			}

			html += fmt.Sprintf(`
        <div class="agent" id="%s">
            <div class="agent-header">
                <div class="agent-name">%s</div>
                <div class="agent-status %s">%s</div>
            </div>
            <div class="agent-description">%s</div>
            
            <div class="section-title">📡 Endpoints</div>
            <div class="endpoint">POST %s</div>
            <div class="endpoint">GET %s/.well-known/agent.json</div>
            <div class="endpoint">GET %s/health</div>
            
            <div class="section-title">🔧 Skills</div>
            <div class="skills">`,
				agent.ID, agent.AgentCard.Name, statusClass, agent.Status.Status,
				agent.AgentCard.Description, agent.AgentCard.URL, agent.AgentCard.URL, agent.AgentCard.URL)

			for _, skill := range agent.AgentCard.Skills {
				html += fmt.Sprintf(`
                <div class="skill">
                    <div class="skill-name">%s</div>
                    <div class="skill-description">%s</div>
                    <div class="skill-tags">`, skill.Name, skill.Description)

				for _, tag := range skill.Tags {
					html += fmt.Sprintf(`<span class="tag">%s</span>`, tag)
				}

				html += `</div>`

				if len(skill.Examples) > 0 {
					html += `<div style="margin-top: 10px;"><strong>Examples:</strong><ul>`
					for _, example := range skill.Examples {
						html += fmt.Sprintf(`<li>%s</li>`, example)
					}
					html += `</ul></div>`
				}

				// Add input schema if available
				if skill.InputSchema != nil {
					schemaJSON, _ := json.MarshalIndent(skill.InputSchema, "", "  ")
					html += fmt.Sprintf(`
                    <div style="margin-top: 15px;">
                        <strong>Input Schema:</strong>
                        <div class="json-code">%s</div>
                    </div>`, string(schemaJSON))
				}

				html += `</div>`
			}

			html += `</div>`

			// Performance metrics
			html += fmt.Sprintf(`
            <div class="section-title">📊 Performance</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #007bff;">%dms</div>
                    <div style="color: #666; font-size: 12px;">Avg Response Time</div>
                </div>
                <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #28a745;">%.1f%%</div>
                    <div style="color: #666; font-size: 12px;">Success Rate</div>
                </div>
                <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #ffc107;">%d</div>
                    <div style="color: #666; font-size: 12px;">Req/Min</div>
                </div>
                <div style="background: white; padding: 15px; border-radius: 6px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #6f42c1;">%s</div>
                    <div style="color: #666; font-size: 12px;">Version</div>
                </div>
            </div>
        </div>`,
				agent.Status.Performance.AvgResponseTime,
				agent.Status.Performance.SuccessRate*100,
				agent.Status.Performance.RequestsPerMinute,
				agent.AgentCard.Version)
		}
	}

	html += `
    </div>
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>`

	return html
}

// sendError sends an A2A protocol error response
func (h *A2AHandler) sendError(c *gin.Context, code int, message string, data interface{}) {
	response := biddingModels.A2AResponse{
		JsonRPC: "2.0",
		ID:      c.GetHeader("X-Request-ID"),
		Error: &biddingModels.A2AError{
			Code:    code,
			Message: message,
			Data:    data,
		},
	}

	statusCode := http.StatusBadRequest
	switch code {
	case biddingModels.ErrorCodeMethodNotFound:
		statusCode = http.StatusNotFound
	case biddingModels.ErrorCodeInternalError, biddingModels.ErrorCodeAIService:
		statusCode = http.StatusInternalServerError
	case biddingModels.ErrorCodeTimeout:
		statusCode = http.StatusRequestTimeout
	case biddingModels.ErrorCodeRateLimit:
		statusCode = http.StatusTooManyRequests
	}

	utils.Log.Warnf("A2A Error: code=%d, message=%s, data=%v", code, message, data)
	c.JSON(statusCode, response)
}
