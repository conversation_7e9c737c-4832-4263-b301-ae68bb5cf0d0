apiVersion: v1
kind: Secret
metadata:
  name: taskd-secret
  namespace: ovs
  labels:
    app: taskd
    app.kubernetes.io/name: taskd
    app.kubernetes.io/component: service
type: Opaque
data:
  # MongoDB连接URI (base64编码)
  # 原值: mongodb://formula:<EMAIL>:27017/overseas?authSource=admin
  MONGODB_URI: ****************************************************************************************************************
  
  # PostgreSQL连接配置 (base64编码) - 与backend项目一致
  # 原值: admin
  POSTGRESQL_USER: YWRtaW4=
  # 原值: SecurePass123!
  POSTGRESQL_PASSWORD: U2VjdXJlUGFzczEyMyE=
  
  # 可选：完整数据库连接字符串（与backend保持一致）
  # 原值: **********************************************************************************/overseas
  DATABASE_URL: ****************************************************************************************************************************
  
  # Pulsar服务URL (base64编码)
  # 原值: pulsar://43.153.104.17:30164
  PULSAR_SERVICE_URL: cHVsc2FyOi8vNDMuMTUzLjEwNC4xNzozMDE2NA==
  
  # LLM API密钥 (base64编码)
  # 火山方舟API密钥 - 原值: cdb5ba17-1758-4d11-be9b-379c9453fb16
  LLM_PROVIDERS_VOLCENGINE_ARK_API_KEY: Y2RiNWJhMTctMTc1OC00ZDExLWJlOWItMzc5Yzk0NTNmYjE2
  
  # LLM API密钥池（多个密钥，用逗号分隔）
  # 原值: cdb5ba17-1758-4d11-be9b-379c9453fb16,08eb4227-fd5c-49cc-a0e2-a9c176a4dfa2,ce2ded6b-dc20-478b-b138-657b33419a96
  LLM_PROVIDERS_VOLCENGINE_ARK_API_KEYS: Y2RiNWJhMTctMTc1OC00ZDExLWJlOWItMzc5Yzk0NTNmYjE2LDA4ZWI0MjI3LWZkNWMtNDljYy1hMGUyLWE5YzE3NmE0ZGZhMixjZTJkZWQ2Yi1kYzIwLTQ3OGItYjEzOC02NTdiMzM0MTlhOTY=
  
  # 生产环境模型ID (base64编码)
  # 原值: your-production-model-id-on-ark（需要替换为实际模型ID）
  LLM_PROVIDERS_VOLCENGINE_ARK_MODEL_REPORT_SUMMARIZER: eW91ci1wcm9kdWN0aW9uLW1vZGVsLWlkLW9uLWFyaw==

---
# 注意：实际部署时需要更新这些敏感值
# 可以使用以下命令生成base64编码：
# echo -n "your-secret-value" | base64
# 
# 重要提醒：
# 1. MongoDB URI需要更新为生产环境地址
# 2. PostgreSQL密码需要更新为生产环境密码
# 3. Pulsar服务URL需要更新为集群内地址
# 4. LLM API密钥需要更新为有效的密钥
# 5. 模型ID需要更新为生产环境的实际模型ID 