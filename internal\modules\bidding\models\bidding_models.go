package models

import (
	"time"
)

// TenderData represents the raw tender data from database
type TenderData struct {
	ID           string                 `json:"id" bson:"_id"`
	Title        string                 `json:"title" bson:"title"`
	Content      string                 `json:"content" bson:"content"`
	Deadline     time.Time              `json:"deadline" bson:"deadline"`
	Budget       BudgetInfo             `json:"budget" bson:"budget"`
	Requirements RequirementInfo        `json:"requirements" bson:"requirements"`
	ContactInfo  ContactInfo            `json:"contact_info" bson:"contact_info"`
	Metadata     map[string]interface{} `json:"metadata" bson:"metadata"`
}

// BudgetInfo represents budget information
type BudgetInfo struct {
	Total       string `json:"total" bson:"total"`
	Currency    string `json:"currency" bson:"currency"`
	BudgetType  string `json:"budget_type" bson:"budget_type"`
	PaymentTerms string `json:"payment_terms,omitempty" bson:"payment_terms,omitempty"`
}

// RequirementInfo represents requirement information
type RequirementInfo struct {
	Technical   []string `json:"technical" bson:"technical"`
	Business    []string `json:"business" bson:"business"`
	Compliance  []string `json:"compliance" bson:"compliance"`
}

// ContactInfo represents contact information
type ContactInfo struct {
	Organization  string `json:"organization" bson:"organization"`
	ContactPerson string `json:"contact_person" bson:"contact_person"`
	Phone         string `json:"phone" bson:"phone"`
	Email         string `json:"email,omitempty" bson:"email,omitempty"`
}

// DataValidationStatus represents data validation result
type DataValidationStatus struct {
	Status       string   `json:"status"`
	ErrorFields  []string `json:"error_fields,omitempty"`
	MissingFields []string `json:"missing_fields,omitempty"`
	Quality      float64  `json:"quality"`
}

// TenderSummary represents AI-generated summary
type TenderSummary struct {
	Title         string      `json:"title"`
	NewTitle      string      `json:"new_title"`
	Deadline      string      `json:"deadline"`
	SummaryText   string      `json:"summary_text"`
	KeyRequirements []string  `json:"key_requirements"`
	BudgetInfo    BudgetSummary `json:"budget_info"`
	Timeline      TimelineInfo  `json:"timeline"`
}

// BudgetSummary represents budget summary information
type BudgetSummary struct {
	Amount       string   `json:"amount"`
	Type         string   `json:"type"`
	PaymentTerms string   `json:"payment_terms,omitempty"`
}

// TimelineInfo represents timeline information
type TimelineInfo struct {
	BiddingDeadline string   `json:"bidding_deadline"`
	ProjectDuration string   `json:"project_duration"`
	KeyMilestones   []string `json:"key_milestones"`
}

// RequirementAnalysis represents requirement analysis result
type RequirementAnalysis struct {
	KeyRequirements     []string `json:"key_requirements"`
	TechnicalKeywords   []string `json:"technical_keywords"`
	BusinessKeywords    []string `json:"business_keywords"`
}

// SearchResult represents a single search result
type SearchResult struct {
	Title         string    `json:"title"`
	URL           string    `json:"url"`
	Snippet       string    `json:"snippet"`
	Source        string    `json:"source"`
	RelevanceScore float64  `json:"relevance_score"`
	PublishedDate string    `json:"published_date"`
}

// SearchResults represents collection of search results
type SearchResults struct {
	Results  []SearchResult `json:"results"`
	Summary  string         `json:"summary"`
	Metadata SearchMetadata `json:"metadata"`
}

// SearchMetadata represents search execution metadata
type SearchMetadata struct {
	TotalResults      int      `json:"total_results"`
	SearchTime        float64  `json:"search_time"`
	SearchEnginesUsed []string `json:"search_engines_used"`
}

// FinalReport represents the final analysis report
type FinalReport struct {
	ExecutiveSummary     string           `json:"executive_summary"`
	ProjectOverview      string           `json:"project_overview"`
	MarketAnalysis       string           `json:"market_analysis"`
	TechnicalBackground  string           `json:"technical_background"`
	CompetitiveLandscape string           `json:"competitive_landscape"`
	Recommendations      []string         `json:"recommendations"`
	RiskAssessment       []RiskItem       `json:"risk_assessment"`
}

// RiskItem represents a risk assessment item
type RiskItem struct {
	Risk       string `json:"risk"`
	Impact     string `json:"impact"`
	Mitigation string `json:"mitigation"`
}

// ReportMetadata represents report generation metadata
type ReportMetadata struct {
	GenerationTime  time.Time `json:"generation_time"`
	WordCount       int       `json:"word_count"`
	ConfidenceScore float64   `json:"confidence_score"`
	SourcesCount    int       `json:"sources_count"`
	ReportVersion   string    `json:"report_version"`
}

// ReportAppendices represents report appendices
type ReportAppendices struct {
	SourceReferences []SourceReference `json:"source_references"`
	DataTables       []DataTable       `json:"data_tables"`
}

// SourceReference represents a source reference
type SourceReference struct {
	ID        int    `json:"id"`
	Title     string `json:"title"`
	URL       string `json:"url"`
	Relevance string `json:"relevance"`
}

// DataTable represents a data table
type DataTable struct {
	Title string                 `json:"title"`
	Data  map[string]interface{} `json:"data"`
}

// Agent input/output models

// DataRetrievalInput represents input for data retrieval agent
type DataRetrievalInput struct {
	TenderID        string   `json:"tender_id" validate:"required"`
	Language        string   `json:"language,omitempty"`
	IncludeMetadata bool     `json:"include_metadata"`
	Fields          []string `json:"fields,omitempty"`
}

// DataRetrievalOutput represents output from data retrieval agent
type DataRetrievalOutput struct {
	TenderData       TenderData           `json:"tender_data"`
	ValidationStatus DataValidationStatus `json:"validation_status"`
	Metadata         RetrievalMetadata    `json:"metadata"`
}

// RetrievalMetadata represents data retrieval metadata
type RetrievalMetadata struct {
	Source      string    `json:"source"`
	Timestamp   time.Time `json:"timestamp"`
	DataQuality float64   `json:"data_quality"`
	FieldCount  int       `json:"field_count"`
	Schema      string    `json:"schema"`
}

// SummaryInput represents input for AI summary agent
type SummaryInput struct {
	TenderData   TenderData `json:"tender_data" validate:"required"`
	Language     string     `json:"language,omitempty"`
	SummaryType  string     `json:"summary_type,omitempty"`
	FocusAreas   []string   `json:"focus_areas,omitempty"`
}

// SummaryOutput represents output from AI summary agent
type SummaryOutput struct {
	Summary             TenderSummary       `json:"summary"`
	ConfidenceScore     float64             `json:"confidence_score"`
	ProcessingMetadata  ProcessingMetadata  `json:"processing_metadata"`
}

// ProcessingMetadata represents AI processing metadata
type ProcessingMetadata struct {
	ModelUsed       string  `json:"model_used"`
	TokensConsumed  int     `json:"tokens_consumed"`
	ProcessingTime  float64 `json:"processing_time"`
}

// AnalysisInput represents input for requirement analysis agent
type AnalysisInput struct {
	SummaryData    TenderSummary `json:"summary_data" validate:"required"`
	SearchDepth    string        `json:"search_depth,omitempty"`
	SearchDomains  []string      `json:"search_domains,omitempty"`
	MaxResults     int           `json:"max_results,omitempty"`
}

// AnalysisOutput represents output from requirement analysis agent
type AnalysisOutput struct {
	RequirementAnalysis RequirementAnalysis `json:"requirement_analysis"`
	SearchResults       []SearchResult      `json:"search_results"`
	SearchSummary       string              `json:"search_summary"`
	SearchMetadata      SearchMetadata      `json:"search_metadata"`
}

// ReportInput represents input for report generation agent
type ReportInput struct {
	SearchResults        []SearchResult      `json:"search_results" validate:"required"`
	SummaryData          TenderSummary       `json:"summary_data" validate:"required"`
	RequirementAnalysis  RequirementAnalysis `json:"requirement_analysis" validate:"required"`
	ReportTemplate       string              `json:"report_template,omitempty"`
	FocusAreas          []string            `json:"focus_areas,omitempty"`
	TargetAudience      string              `json:"target_audience,omitempty"`
}

// ReportOutput represents output from report generation agent
type ReportOutput struct {
	FinalReport      FinalReport       `json:"final_report"`
	ReportMetadata   ReportMetadata    `json:"report_metadata"`
	Appendices       ReportAppendices  `json:"appendices"`
}