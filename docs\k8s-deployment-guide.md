# TaskD K8s 部署指南

## 项目概述

TaskD是一个基于Go语言开发的任务处理服务，提供LLM相关服务、意图识别、并发控制等功能。

### 主要功能
- LLM服务接口 (`/v1/llms/*`)
- 意图识别服务 (`/v1/intent/*`)
- 令牌管理 (`/v1/tokens/*`)
- 并发控制服务 (`/v1/concurrent/*`)
- 工具服务 (`/v1/tools/*`)
- 健康检查端点 (`/healthz`)

### 依赖服务
- MongoDB数据库（主要数据存储）
- PostgreSQL数据库（令牌管理）
- Pulsar消息队列（异步任务处理）
- LLM服务提供商（火山方舟等）

## 部署架构

```
[Internet] -> [NodePort Service 30601] -> [ClusterIP Service 8601] -> [Pod 8601]
                                                |
                                                v
[MongoDB] <- [TaskD Service] -> [PostgreSQL]
                |
                v
            [Pulsar MQ] -> [LLM Provider]
```

## 快速部署

### 前提条件
- K3s集群已运行
- kubectl已配置且可访问集群
- 已构建Docker镜像并推送到镜像仓库
- MongoDB、PostgreSQL、Pulsar服务已部署或可访问

### 部署步骤

1. **创建命名空间**
   ```bash
   kubectl apply -f k8s/namespace.yaml
   ```

2. **部署配置和密钥**
   ```bash
   kubectl apply -f k8s/configmap.yaml
   kubectl apply -f k8s/secret.yaml
   ```

3. **部署应用**
   ```bash
   kubectl apply -f k8s/deployment.yaml
   ```

4. **创建服务**
   ```bash
   kubectl apply -f k8s/service.yaml
   ```

### 一键部署
```bash
kubectl apply -f k8s/
```

## 配置说明

### 环境变量配置

#### ConfigMap配置 (`k8s/configmap.yaml`)
- `SERVER_PORT`: 服务端口 (8601)
- `SERVER_MODE`: 运行模式 (release)
- `LOGGER_LEVEL`: 日志级别 (info)
- `MONGODB_DATABASE`: MongoDB数据库名
- `POSTGRESQL_*`: PostgreSQL连接配置
- `PULSAR_*`: Pulsar消息队列配置
- `LLM_*`: LLM服务配置

#### Secret配置 (`k8s/secret.yaml`)
- `MONGODB_URI`: MongoDB连接URI
- `POSTGRESQL_USER/PASSWORD`: PostgreSQL凭据
- `PULSAR_SERVICE_URL`: Pulsar服务地址
- `LLM_PROVIDERS_*_API_KEY`: LLM API密钥
- `LLM_PROVIDERS_*_API_KEYS`: LLM API密钥池
- `LLM_PROVIDERS_*_MODEL_*`: LLM模型ID配置

**重要提醒**: 部署前请更新Secret中的敏感信息！

### 服务配置

#### ClusterIP服务
- 名称: `taskd-service`
- 端口: 8601
- 用途: 集群内部服务访问（与specific-ai-bd通信）

#### NodePort服务
- 名称: `taskd-nodeport`
- 内部端口: 8601
- 外部端口: 30601
- 用途: 外部调试访问

## 验证部署

### 1. 检查Pod状态
```bash
kubectl get pods -n ovs -l app=taskd
```

预期输出:
```
NAME                     READY   STATUS    RESTARTS   AGE
taskd-xxxxxxxxx-xxxxx    1/1     Running   0          2m
taskd-xxxxxxxxx-xxxxx    1/1     Running   0          2m
```

### 2. 检查服务状态
```bash
kubectl get svc -n ovs | grep taskd
```

预期输出:
```
taskd-service     ClusterIP   10.43.xxx.xxx   <none>        8601/TCP         2m
taskd-nodeport    NodePort    10.43.xxx.xxx   <none>        8601:30601/TCP   2m
```

### 3. 健康检查
```bash
# 集群内检查
kubectl exec -n ovs deployment/taskd -- curl -f http://localhost:8601/healthz

# 外部检查（K3s节点IP）
curl http://<K3S_NODE_IP>:30601/healthz
```

预期响应:
```json
{"status": "healthy"}
```

### 4. 查看日志
```bash
kubectl logs -n ovs deployment/taskd -f
```

### 5. API测试
```bash
# 测试LLM服务（需要认证）
curl -X POST http://<K3S_NODE_IP>:30601/v1/llms/summary_report \
  -H "Content-Type: application/json" \
  -d '{"text":"测试文本","target_company":"测试公司","language":"zh"}'

# 查看Swagger文档（仅开发环境）
curl http://<K3S_NODE_IP>:30601/swagger/index.html
```

## 依赖服务检查

### MongoDB连接测试
```bash
kubectl exec -n ovs deployment/taskd -- \
  mongosh $MONGODB_URI --eval "db.adminCommand('ping')"
```

### PostgreSQL连接测试
```bash
kubectl exec -n ovs deployment/taskd -- \
  psql -h $POSTGRESQL_HOST -U $POSTGRESQL_USER -d $POSTGRESQL_DATABASE -c "SELECT 1;"
```

### Pulsar连接测试
```bash
kubectl logs -n ovs deployment/taskd | grep -i "pulsar\|consumer"
```

## 故障排查

### 常见问题

#### 1. Pod一直处于Pending状态
```bash
kubectl describe pod -n ovs -l app=taskd
```
检查事件信息，通常是资源不足或镜像拉取失败。

#### 2. Pod一直重启
```bash
kubectl logs -n ovs deployment/taskd --previous
```
检查上一次的日志，通常是：
- MongoDB/PostgreSQL连接失败
- LLM API密钥无效
- Pulsar连接失败
- 配置文件格式错误

#### 3. 健康检查失败
```bash
kubectl describe pod -n ovs -l app=taskd
```
检查livenessProbe和readinessProbe失败原因，可能是：
- 服务启动时间过长
- 依赖服务不可用
- 端口配置错误

#### 4. LLM服务调用失败
```bash
kubectl logs -n ovs deployment/taskd | grep -i "llm\|volcengine\|api"
```
检查LLM相关日志，常见问题：
- API密钥无效或过期
- 模型ID不存在
- 网络连接问题
- 并发限制超出

### 调试命令
```bash
# 进入Pod进行调试
kubectl exec -it -n ovs deployment/taskd -- /bin/sh

# 检查环境变量
kubectl exec -n ovs deployment/taskd -- env | grep -E "(MONGODB|POSTGRESQL|PULSAR|LLM)"

# 端口转发本地调试
kubectl port-forward -n ovs svc/taskd-service 8601:8601
```

## 性能优化

### 水平扩容
```bash
kubectl scale deployment taskd -n ovs --replicas=5
```

### 更新镜像
```bash
kubectl set image deployment/taskd -n ovs taskd=your-registry/taskd:new-tag
```

### 资源调整
编辑deployment.yaml中的resources配置：
```yaml
resources:
  requests:
    memory: "1Gi"      # 根据实际使用调整
    cpu: "1000m"
  limits:
    memory: "2Gi"
    cpu: "2000m"
```

## 监控和告警

### 关键监控指标
- Pod CPU/内存使用率
- API响应时间
- LLM令牌消耗率
- 数据库连接池状态
- Pulsar消息堆积

### 推荐监控方案
- Prometheus + Grafana
- 自定义指标导出
- 日志聚合（ELK Stack）

## 清理资源

### 删除应用
```bash
kubectl delete -f k8s/
```

### 删除命名空间（谨慎操作）
```bash
kubectl delete namespace ovs
```

## 注意事项

1. **镜像地址**: 部署前请更新`deployment.yaml`中的镜像地址
2. **敏感信息**: 生产环境请更新Secret中的所有敏感配置
3. **依赖服务**: 确保MongoDB、PostgreSQL、Pulsar已正确部署
4. **资源限制**: 根据实际负载调整Pod的资源限制
5. **LLM配置**: 确保LLM API密钥有效且配额充足
6. **网络策略**: 根据安全要求配置网络策略
7. **备份策略**: 建议配置数据库备份策略
8. **令牌跟踪**: TaskD具有完整的令牌使用跟踪功能

## 联系信息

如有问题，请联系开发团队或查看项目文档。 