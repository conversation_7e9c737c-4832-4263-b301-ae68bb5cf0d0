package chat

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// SocketConnectionService SocketIO连接管理服务
type SocketConnectionService struct {
	store  store.DBTX
	config *models.ChatConfig
}

// NewSocketConnectionService 创建SocketIO连接管理服务
func NewSocketConnectionService(store store.DBTX, config *models.ChatConfig) *SocketConnectionService {
	return &SocketConnectionService{
		store:  store,
		config: config,
	}
}

// CreateConnection 创建Socket连接
func (s *SocketConnectionService) CreateConnection(ctx context.Context, req *models.SocketConnectionRequest) (*models.SocketConnection, error) {
	// 生成唯一的socket ID和session ID
	socketID := uuid.New().String()
	sessionID := uuid.New().String()

	// 创建连接记录
	connection := &models.SocketConnection{
		SocketID:    socketID,
		UserID:      req.UserID,
		CompanyID:   req.CompanyID,
		SessionID:   sessionID,
		Status:      models.SocketStatusActive,
		ClientIP:    req.ClientIP,
		UserAgent:   req.UserAgent,
		ConnectedAt: time.Now(),
		LastPingAt:  time.Now(),
	}

	// 插入数据库
	query := `
		INSERT INTO socket_connections (socket_id, user_id, company_id, session_id, status, client_ip, user_agent, connected_at, last_ping_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id`

	err := s.store.QueryRowContext(ctx, query,
		connection.SocketID,
		connection.UserID,
		connection.CompanyID,
		connection.SessionID,
		connection.Status,
		connection.ClientIP,
		connection.UserAgent,
		connection.ConnectedAt,
		connection.LastPingAt,
	).Scan(&connection.ID)

	if err != nil {
		utils.Log.Errorf("Failed to create socket connection: %v", err)
		return nil, fmt.Errorf("failed to create socket connection: %w", err)
	}

	utils.Log.Infof("Created socket connection: %s for user: %s", socketID, req.UserID)
	return connection, nil
}

// GetConnection 获取Socket连接
func (s *SocketConnectionService) GetConnection(ctx context.Context, socketID string) (*models.SocketConnection, error) {
	query := `
		SELECT id, socket_id, user_id, company_id, session_id, status, client_ip, user_agent, 
		       connected_at, last_ping_at, disconnected_at
		FROM socket_connections 
		WHERE socket_id = $1`

	var connection models.SocketConnection
	err := s.store.QueryRowContext(ctx, query, socketID).Scan(
		&connection.ID,
		&connection.SocketID,
		&connection.UserID,
		&connection.CompanyID,
		&connection.SessionID,
		&connection.Status,
		&connection.ClientIP,
		&connection.UserAgent,
		&connection.ConnectedAt,
		&connection.LastPingAt,
		&connection.DisconnectedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("socket connection not found: %s", socketID)
		}
		utils.Log.Errorf("Failed to get socket connection: %v", err)
		return nil, fmt.Errorf("failed to get socket connection: %w", err)
	}

	return &connection, nil
}

// UpdateLastPing 更新最后ping时间
func (s *SocketConnectionService) UpdateLastPing(ctx context.Context, socketID string) error {
	query := `
		UPDATE socket_connections 
		SET last_ping_at = CURRENT_TIMESTAMP 
		WHERE socket_id = $1 AND status = 'active'`

	result, err := s.store.ExecContext(ctx, query, socketID)
	if err != nil {
		utils.Log.Errorf("Failed to update last ping: %v", err)
		return fmt.Errorf("failed to update last ping: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("socket connection not found or not active: %s", socketID)
	}

	return nil
}

// DisconnectSocket 断开Socket连接
func (s *SocketConnectionService) DisconnectSocket(ctx context.Context, socketID string) error {
	query := `
		UPDATE socket_connections 
		SET status = 'disconnected', disconnected_at = CURRENT_TIMESTAMP 
		WHERE socket_id = $1 AND status = 'active'`

	result, err := s.store.ExecContext(ctx, query, socketID)
	if err != nil {
		utils.Log.Errorf("Failed to disconnect socket: %v", err)
		return fmt.Errorf("failed to disconnect socket: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		utils.Log.Warnf("Socket connection not found or already disconnected: %s", socketID)
	} else {
		utils.Log.Infof("Disconnected socket connection: %s", socketID)
	}

	return nil
}

// GetActiveConnections 获取活跃连接列表
func (s *SocketConnectionService) GetActiveConnections(ctx context.Context, userID *string, limit int, offset int) ([]models.SocketConnection, error) {
	var query string
	var args []interface{}

	if userID != nil {
		query = `
			SELECT id, socket_id, user_id, company_id, session_id, status, client_ip, user_agent, 
			       connected_at, last_ping_at, disconnected_at
			FROM socket_connections 
			WHERE status = 'active' AND user_id = $1
			ORDER BY connected_at DESC
			LIMIT $2 OFFSET $3`
		args = []interface{}{*userID, limit, offset}
	} else {
		query = `
			SELECT id, socket_id, user_id, company_id, session_id, status, client_ip, user_agent, 
			       connected_at, last_ping_at, disconnected_at
			FROM socket_connections 
			WHERE status = 'active'
			ORDER BY connected_at DESC
			LIMIT $1 OFFSET $2`
		args = []interface{}{limit, offset}
	}

	rows, err := s.store.QueryContext(ctx, query, args...)
	if err != nil {
		utils.Log.Errorf("Failed to get active connections: %v", err)
		return nil, fmt.Errorf("failed to get active connections: %w", err)
	}
	defer rows.Close()

	var connections []models.SocketConnection
	for rows.Next() {
		var conn models.SocketConnection
		err := rows.Scan(
			&conn.ID,
			&conn.SocketID,
			&conn.UserID,
			&conn.CompanyID,
			&conn.SessionID,
			&conn.Status,
			&conn.ClientIP,
			&conn.UserAgent,
			&conn.ConnectedAt,
			&conn.LastPingAt,
			&conn.DisconnectedAt,
		)
		if err != nil {
			utils.Log.Errorf("Failed to scan connection: %v", err)
			continue
		}
		connections = append(connections, conn)
	}

	return connections, nil
}

// GetActiveConnectionCount 获取活跃连接数量
func (s *SocketConnectionService) GetActiveConnectionCount(ctx context.Context) (int, error) {
	query := `SELECT COUNT(*) FROM socket_connections WHERE status = 'active'`

	var count int
	err := s.store.QueryRowContext(ctx, query).Scan(&count)
	if err != nil {
		utils.Log.Errorf("Failed to get active connection count: %v", err)
		return 0, fmt.Errorf("failed to get active connection count: %w", err)
	}

	return count, nil
}

// CleanupStaleConnections 清理过期连接
func (s *SocketConnectionService) CleanupStaleConnections(ctx context.Context, timeoutMinutes int) (int, error) {
	// 将长时间未ping的连接标记为断开
	query := `
		UPDATE socket_connections 
		SET status = 'disconnected', disconnected_at = CURRENT_TIMESTAMP 
		WHERE status = 'active' 
		AND last_ping_at < (CURRENT_TIMESTAMP - INTERVAL '%d minutes')`

	result, err := s.store.ExecContext(ctx, fmt.Sprintf(query, timeoutMinutes))
	if err != nil {
		utils.Log.Errorf("Failed to cleanup stale connections: %v", err)
		return 0, fmt.Errorf("failed to cleanup stale connections: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected > 0 {
		utils.Log.Infof("Cleaned up %d stale socket connections", rowsAffected)
	}

	return int(rowsAffected), nil
}

// GetConnectionsBySessionID 根据SessionID获取连接
func (s *SocketConnectionService) GetConnectionsBySessionID(ctx context.Context, sessionID string) ([]models.SocketConnection, error) {
	query := `
		SELECT id, socket_id, user_id, company_id, session_id, status, client_ip, user_agent, 
		       connected_at, last_ping_at, disconnected_at
		FROM socket_connections 
		WHERE session_id = $1
		ORDER BY connected_at DESC`

	rows, err := s.store.QueryContext(ctx, query, sessionID)
	if err != nil {
		utils.Log.Errorf("Failed to get connections by session ID: %v", err)
		return nil, fmt.Errorf("failed to get connections by session ID: %w", err)
	}
	defer rows.Close()

	var connections []models.SocketConnection
	for rows.Next() {
		var conn models.SocketConnection
		err := rows.Scan(
			&conn.ID,
			&conn.SocketID,
			&conn.UserID,
			&conn.CompanyID,
			&conn.SessionID,
			&conn.Status,
			&conn.ClientIP,
			&conn.UserAgent,
			&conn.ConnectedAt,
			&conn.LastPingAt,
			&conn.DisconnectedAt,
		)
		if err != nil {
			utils.Log.Errorf("Failed to scan connection: %v", err)
			continue
		}
		connections = append(connections, conn)
	}

	return connections, nil
}
