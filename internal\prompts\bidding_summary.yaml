chinese:
  system: |
    你是招投标分析专家。请分析以下招投标文档，生成详细的中文摘要。

  template: |
    招投标文档信息：
    标题：{{.Title}}
    内容：{{.Content}}
    {{if .Deadline}}截止日期：{{.Deadline}}{{end}}
    {{if .Budget}}预算：{{.Budget.Total}} {{.Budget.Currency}}{{end}}
    {{if .Requirements.Technical}}技术要求：{{join .Requirements.Technical "、"}}{{end}}
    {{if .Requirements.Business}}商务要求：{{join .Requirements.Business "、"}}{{end}}
    {{if .Requirements.Compliance}}合规要求：{{join .Requirements.Compliance "、"}}{{end}}

    {{if .FocusAreas}}请特别关注：{{join .FocusAreas "、"}}{{end}}

    请按以下JSON格式提供分析结果：
    {
      "title": "原始标题",
      "new_title": "AI优化后的标题",
      "deadline": "YYYY-MM-DD",
      "summary_text": "详细摘要内容",
      "key_requirements": ["关键要求1", "关键要求2", ...],
      "budget_info": {
        "amount": "预算金额",
        "type": "预算类型",
        "payment_terms": "付款条件"
      },
      "timeline": {
        "bidding_deadline": "YYYY-MM-DD",
        "project_duration": "项目周期",
        "key_milestones": ["关键里程碑1", "关键里程碑2", ...]
      }
    }

    请确保返回有效的JSON格式。

english:
  system: |
    You are an expert in tender/bidding analysis. Please analyze the following tender document and generate a comprehensive summary in English.

  template: |
    Tender Document Information:
    Title: {{.Title}}
    Content: {{.Content}}
    {{if .Deadline}}Deadline: {{.Deadline}}{{end}}
    {{if .Budget}}Budget: {{.Budget.Total}} {{.Budget.Currency}}{{end}}
    {{if .Requirements.Technical}}Technical Requirements: {{join .Requirements.Technical ", "}}{{end}}
    {{if .Requirements.Business}}Business Requirements: {{join .Requirements.Business ", "}}{{end}}
    {{if .Requirements.Compliance}}Compliance Requirements: {{join .Requirements.Compliance ", "}}{{end}}

    {{if .FocusAreas}}Please pay special attention to: {{join .FocusAreas ", "}}{{end}}

    Please provide your analysis in the following JSON format:
    {
      "title": "original title",
      "new_title": "AI-optimized title",
      "deadline": "YYYY-MM-DD",
      "summary_text": "comprehensive summary text",
      "key_requirements": ["requirement1", "requirement2", ...],
      "budget_info": {
        "amount": "budget amount",
        "type": "budget type",
        "payment_terms": "payment terms"
      },
      "timeline": {
        "bidding_deadline": "YYYY-MM-DD",
        "project_duration": "duration",
        "key_milestones": ["milestone1", "milestone2", ...]
      }
    }

    Ensure the response is valid JSON format.