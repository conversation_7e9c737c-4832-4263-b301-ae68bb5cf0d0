package profile

import (
	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/prompts"
)

// ServiceFactory 企业画像服务工厂
type ServiceFactory struct {
	llmClient     llm.LLMClient
	promptManager *prompts.PromptManager
}

// NewServiceFactory 创建服务工厂
func NewServiceFactory(llmClient llm.LLMClient, promptManager *prompts.PromptManager) *ServiceFactory {
	return &ServiceFactory{
		llmClient:     llmClient,
		promptManager: promptManager,
	}
}

// CreateProfileService 创建企业画像生成服务
func (f *ServiceFactory) CreateProfileService() services.ProfileServiceInterface {
	return services.NewProfileService(f.llmClient, f.promptManager)
}

// CreateStorageService 创建存储服务
func (f *ServiceFactory) CreateStorageService() services.StorageServiceInterface {
	return services.NewStorageService()
}

// CreateSchemaService 创建Schema服务
func (f *ServiceFactory) CreateSchemaService() services.SchemaServiceInterface {
	return services.NewSchemaService()
}

// CreateAllServices 创建所有服务
func (f *ServiceFactory) CreateAllServices() (
	services.ProfileServiceInterface,
	services.StorageServiceInterface,
	services.SchemaServiceInterface,
) {
	return f.CreateProfileService(), f.CreateStorageService(), f.CreateSchemaService()
}