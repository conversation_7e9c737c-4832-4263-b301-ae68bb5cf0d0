package agents

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	rootModels "gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/prompts"
	"gitlab.com/specific-ai/taskd/internal/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// AISummaryAgent handles AI-powered tender summary generation
type AISummaryAgent struct {
	*BaseAgent
	llmService    services.LLMService
	promptManager *prompts.BiddingPromptManager
	modelID       string
}

// NewAISummaryAgent creates a new AI summary agent
func NewAISummaryAgent(llmService services.LLMService, promptManager *prompts.BiddingPromptManager, modelID string) *AISummaryAgent {
	agentCard := biddingModels.AgentCard{
		Name:        "AI摘要生成器",
		Description: "基于AI的招投标文件摘要生成器",
		Version:     "1.0.0",
		Provider: biddingModels.AgentProvider{
			Organization: "System",
		},
		Capabilities: biddingModels.AgentCapabilities{
			Streaming: false,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"none"},
		},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          "generate_tender_summary",
				Name:        "生成招标摘要",
				Description: "根据招标文件内容生成结构化摘要",
				InputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"tender_data": map[string]interface{}{
							"type":        "object",
							"description": "招标数据",
						},
						"language": map[string]interface{}{
							"type":        "string",
							"description": "输出语言",
							"default":     "chinese",
						},
					},
					"required": []string{"tender_data"},
				},
				OutputSchema: map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"summary": map[string]interface{}{
							"type":        "object",
							"description": "生成的摘要",
						},
					},
				},
			},
		},
	}

	baseAgent := NewBaseAgent("ai-summary", agentCard)

	return &AISummaryAgent{
		BaseAgent:     baseAgent,
		llmService:    llmService,
		promptManager: promptManager,
		modelID:       modelID,
	}
}

// GetID returns the agent ID
func (agent *AISummaryAgent) GetID() string {
	return agent.id
}

// GetCard returns the agent card
func (agent *AISummaryAgent) GetCard() biddingModels.AgentCard {
	return agent.agentCard
}

// ExecuteSkill executes the specified skill
func (agent *AISummaryAgent) ExecuteSkill(skillID string, input map[string]interface{},
	context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {

	switch skillID {
	case "generate_tender_summary":
		return agent.ExecuteSkillWithHandler(skillID, input, context, agent.generateTenderSummary)
	default:
		return nil, fmt.Errorf("unknown skill: %s", skillID)
	}
}

// generateTenderSummary handles the AI summary generation logic
func (agent *AISummaryAgent) generateTenderSummary(input map[string]interface{},
	a2aContext biddingModels.A2AContext) (map[string]interface{}, error) {

	// Parse input parameters
	tenderDataRaw, ok := input["tender_data"]
	if !ok {
		return nil, fmt.Errorf("tender_data is required")
	}

	// Convert tender data to proper structure
	tenderData, err := agent.parseTenderData(tenderDataRaw)
	if err != nil {
		return nil, fmt.Errorf("failed to parse tender data: %w", err)
	}

	language := "chinese"
	if lang, exists := input["language"]; exists {
		if langStr, ok := lang.(string); ok {
			language = langStr
		}
	}

	summaryType := "detailed"
	if sType, exists := input["summary_type"]; exists {
		if sTypeStr, ok := sType.(string); ok {
			summaryType = sTypeStr
		}
	}

	var focusAreas []string
	if areas, exists := input["focus_areas"]; exists {
		if areasArray, ok := areas.([]interface{}); ok {
			for _, area := range areasArray {
				if areaStr, ok := area.(string); ok {
					focusAreas = append(focusAreas, areaStr)
				}
			}
		}
	}

	utils.Log.Infof("Generating AI summary: Type=%s, Language=%s, Focus=%v",
		summaryType, language, focusAreas)

	// Generate summary using LLM
	var summary biddingModels.TenderSummary
	var processingMetadata biddingModels.ProcessingMetadata
	var confidenceScore float64

	ctx, cancel := context.WithTimeout(context.Background(), 180*time.Second)
	defer cancel()

	err = agent.generateSummaryWithLLM(ctx, tenderData, language, summaryType, focusAreas,
		&summary, &processingMetadata, &confidenceScore, a2aContext)

	if err != nil {
		return nil, fmt.Errorf("failed to generate summary: %w", err)
	}

	// Update agent metrics with token usage
	agent.UpdateMetrics(processingMetadata.TokensConsumed)

	// Build response
	response := map[string]interface{}{
		"summary":             summary,
		"confidence_score":    confidenceScore,
		"processing_metadata": processingMetadata,
	}

	utils.Log.Infof("Successfully generated summary (tokens: %d, confidence: %.2f)",
		processingMetadata.TokensConsumed, confidenceScore)

	return response, nil
}

// parseTenderData converts raw tender data to structured format
func (agent *AISummaryAgent) parseTenderData(raw interface{}) (biddingModels.TenderData, error) {
	var tenderData biddingModels.TenderData

	// Convert to JSON and back to properly structure the data
	jsonData, err := json.Marshal(raw)
	if err != nil {
		return tenderData, fmt.Errorf("failed to marshal tender data: %w", err)
	}

	err = json.Unmarshal(jsonData, &tenderData)
	if err != nil {
		return tenderData, fmt.Errorf("failed to unmarshal tender data: %w", err)
	}

	return tenderData, nil
}

// generateSummaryWithLLM uses the LLM service to generate the summary
func (agent *AISummaryAgent) generateSummaryWithLLM(ctx context.Context,
	tenderData biddingModels.TenderData, language, summaryType string, focusAreas []string,
	summary *biddingModels.TenderSummary, metadata *biddingModels.ProcessingMetadata,
	confidenceScore *float64, a2aContext biddingModels.A2AContext) error {

	// Build the prompt using prompt manager
	systemMsg, userMsg, err := agent.buildPromptWithManager(tenderData, language, focusAreas)
	if err != nil {
		return fmt.Errorf("failed to build prompt: %w", err)
	}

	// Prepare LLM request messages
	var messages []common.LLMMessage
	if systemMsg != "" {
		messages = append(messages, common.LLMMessage{
			Role:    "system",
			Content: systemMsg,
		})
	}
	if userMsg != "" {
		messages = append(messages, common.LLMMessage{
			Role:    "user",
			Content: userMsg,
		})
	}

	// Prepare LLM request
	maxTokens := 4000
	temperature := 0.3
	llmRequest := rootModels.OpenAICompatibleRequestParams{
		Model:       agent.modelID, // Or get from config
		Messages:    messages,
		MaxTokens:   &maxTokens,
		Temperature: &temperature,
		UserContext: &rootModels.UserContext{
			UserID:    a2aContext.UserID,
			CompanyID: a2aContext.CompanyID,
			RequestID: a2aContext.RequestID,
		},
		AutoTrackTokens: true,
	}

	startTime := time.Now()

	// Call LLM service
	// 注意：这里的 llmService 是一个接口，它可能是一个并发服务，也可能是一个增强的服务
	// 假设 NewAISummaryAgent 在构建时传入的是一个实现了 ProcessRequest 的实例
	response, err := agent.llmService.ProcessRequest(ctx, llmRequest)
	if err != nil {
		return fmt.Errorf("LLM service error: %w", err)
	}

	processingTime := time.Since(startTime).Seconds()

	// Parse response
	err = agent.parseSummaryResponse(response.Result, summary, confidenceScore)
	if err != nil {
		return fmt.Errorf("failed to parse LLM response: %w", err)
	}

	// Set metadata
	*metadata = biddingModels.ProcessingMetadata{
		ModelUsed:      agent.modelID, // Using the requested model
		TokensConsumed: response.TokenUsage.TotalTokens,
		ProcessingTime: processingTime,
	}

	// 不再需要手动记录token消耗，因为LLM服务会自动记录
	// 如果需要额外的日志记录，可以保留以下代码
	utils.Log.Debugf("AI摘要Agent %s 消耗tokens: input=%d, output=%d, total=%d",
		agent.modelID, response.TokenUsage.InputTokens, response.TokenUsage.OutputTokens, response.TokenUsage.TotalTokens)

	return nil
}

// buildPromptWithManager constructs the prompt using the prompt manager
func (agent *AISummaryAgent) buildPromptWithManager(tenderData biddingModels.TenderData,
	language string, focusAreas []string) (string, string, error) {

	if agent.promptManager == nil {
		return "", "", fmt.Errorf("prompt manager not initialized")
	}

	// Prepare template data
	data := struct {
		Title        string
		Content      string
		Deadline     string
		Budget       biddingModels.BudgetInfo
		Requirements biddingModels.RequirementInfo
		FocusAreas   []string
	}{
		Title:        tenderData.Title,
		Content:      tenderData.Content,
		Budget:       tenderData.Budget,
		Requirements: tenderData.Requirements,
		FocusAreas:   focusAreas,
	}

	if !tenderData.Deadline.IsZero() {
		data.Deadline = tenderData.Deadline.Format("2006-01-02")
	}

	// Build prompt using prompt manager
	systemMsg, userMsg, err := agent.promptManager.BuildPrompt("bidding_summary", language, data)
	if err != nil {
		return "", "", fmt.Errorf("failed to build prompt from template: %w", err)
	}

	return systemMsg, userMsg, nil
}

// parseSummaryResponse parses the LLM response into structured summary
func (agent *AISummaryAgent) parseSummaryResponse(content string,
	summary *biddingModels.TenderSummary, confidenceScore *float64) error {

	// Clean the content to extract JSON
	content = strings.TrimSpace(content)

	// Find JSON content (remove any markdown formatting)
	start := strings.Index(content, "{")
	end := strings.LastIndex(content, "}") + 1

	if start == -1 || end <= start {
		return fmt.Errorf("no valid JSON found in response")
	}

	jsonContent := content[start:end]

	// Parse JSON response
	var response map[string]interface{}
	err := json.Unmarshal([]byte(jsonContent), &response)
	if err != nil {
		return fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// Extract summary fields
	if title, ok := response["title"].(string); ok {
		summary.Title = title
	}
	if newTitle, ok := response["new_title"].(string); ok {
		summary.NewTitle = newTitle
	}
	if deadline, ok := response["deadline"].(string); ok {
		summary.Deadline = deadline
	}
	if summaryText, ok := response["summary_text"].(string); ok {
		summary.SummaryText = summaryText
	}

	// Extract key requirements
	if requirements, ok := response["key_requirements"].([]interface{}); ok {
		for _, req := range requirements {
			if reqStr, ok := req.(string); ok {
				summary.KeyRequirements = append(summary.KeyRequirements, reqStr)
			}
		}
	}

	// Extract budget info
	if budgetInfo, ok := response["budget_info"].(map[string]interface{}); ok {
		budget := biddingModels.BudgetSummary{}
		if amount, ok := budgetInfo["amount"].(string); ok {
			budget.Amount = amount
		}
		if budgetType, ok := budgetInfo["type"].(string); ok {
			budget.Type = budgetType
		}
		if paymentTerms, ok := budgetInfo["payment_terms"].(string); ok {
			budget.PaymentTerms = paymentTerms
		}
		summary.BudgetInfo = budget
	}

	// Extract timeline
	if timeline, ok := response["timeline"].(map[string]interface{}); ok {
		timelineInfo := biddingModels.TimelineInfo{}
		if biddingDeadline, ok := timeline["bidding_deadline"].(string); ok {
			timelineInfo.BiddingDeadline = biddingDeadline
		}
		if projectDuration, ok := timeline["project_duration"].(string); ok {
			timelineInfo.ProjectDuration = projectDuration
		}
		if milestones, ok := timeline["key_milestones"].([]interface{}); ok {
			for _, milestone := range milestones {
				if milestoneStr, ok := milestone.(string); ok {
					timelineInfo.KeyMilestones = append(timelineInfo.KeyMilestones, milestoneStr)
				}
			}
		}
		summary.Timeline = timelineInfo
	}

	// Calculate confidence score based on response completeness
	*confidenceScore = agent.calculateConfidenceScore(*summary)

	return nil
}

// calculateConfidenceScore calculates confidence based on summary completeness
func (agent *AISummaryAgent) calculateConfidenceScore(summary biddingModels.TenderSummary) float64 {
	score := 0.0
	maxScore := 7.0 // Total possible points

	if summary.Title != "" {
		score += 1.0
	}
	if summary.NewTitle != "" {
		score += 1.0
	}
	if summary.Deadline != "" {
		score += 1.0
	}
	if summary.SummaryText != "" {
		score += 2.0
	} // More weight for summary text
	if len(summary.KeyRequirements) > 0 {
		score += 1.0
	}
	if summary.BudgetInfo.Amount != "" {
		score += 1.0
	}

	return score / maxScore
}

// HealthCheck returns enhanced health status for this agent
func (agent *AISummaryAgent) HealthCheck() biddingModels.HealthStatus {
	status := agent.BaseAgent.HealthCheck()

	// Check LLM service availability
	if agent.llmService == nil {
		status.Status = biddingModels.AgentStatusUnhealthy
		status.Dependencies["llm_service"] = "unavailable"
	} else {
		status.Dependencies["llm_service"] = "available"
	}

	return status
}
