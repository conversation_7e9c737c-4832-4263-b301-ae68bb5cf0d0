#!/bin/sh

# TaskD 超精简测试环境入口脚本
# 适用于 distroless 基础镜像的极简化版本

set -e

# 基础配置
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# 显示环境信息
echo "=== TaskD 超精简测试环境 ==="
echo "基础镜像: distroless/python3"
echo "Python版本: $(python3 --version)"
echo "工作目录: $(pwd)"
echo "TaskD地址: ${TASKD_BASE_URL:-未设置}"
echo ""

# 命令处理
case "${1:-test}" in
    "test")
        TEST_TYPE="${2:-all}"
        echo "开始运行测试: $TEST_TYPE"
        
        # 基础连通性检查
        if [ -n "$TASKD_BASE_URL" ]; then
            echo "检查TaskD连通性..."
            python3 -c "
import requests
import sys
try:
    resp = requests.get('$TASKD_BASE_URL/healthz', timeout=10)
    print(f'✅ TaskD连通性检查通过: {resp.status_code}')
except Exception as e:
    print(f'⚠️ TaskD连通性检查失败: {e}')
    print('继续执行测试...')
"
        fi
        
        # 运行测试
        cd /app
        if [ "$TEST_TYPE" = "all" ]; then
            python3 -m pytest test/ -v
        elif [ "$TEST_TYPE" = "health-check" ]; then
            python3 -c "
import requests
try:
    resp = requests.get('$TASKD_BASE_URL/healthz', timeout=5)
    print('✅ 健康检查通过')
    exit(0)
except:
    print('❌ 健康检查失败')
    exit(1)
"
        else
            # 运行指定的测试
            case "$TEST_TYPE" in
                "p0"|"p1"|"p2"|"p3"|"p4")
                    python3 -m pytest test/ -m "$TEST_TYPE" -v
                    ;;
                "bidding")
                    python3 -m pytest test/test_bidding_agents.py -v
                    ;;
                "entity")
                    python3 -m pytest test/test_entity_extraction_agent.py -v
                    ;;
                "token")
                    python3 -m pytest test/test_token_management.py -v
                    ;;
                "concurrent")
                    python3 -m pytest test/test_concurrent_control.py -v
                    ;;
                "chat")
                    python3 -m pytest test/test_chat_agent.py -v
                    ;;
                "preprocessing")
                    python3 -m pytest test/test_preprocessing_agents.py -v
                    ;;
                "intent")
                    python3 -m pytest test/test_intent_recognition.py -v
                    ;;
                "agent")
                    python3 -m pytest test/test_agent_module.py -v
                    ;;
                *)
                    echo "❌ 未知的测试类型: $TEST_TYPE"
                    echo "支持的类型: all, p0-p4, bidding, entity, token, concurrent, chat, preprocessing, intent, agent"
                    exit 1
                    ;;
            esac
        fi
        ;;
    
    "health-check")
        echo "执行健康检查..."
        python3 -c "
import requests
import sys
try:
    resp = requests.get('${TASKD_BASE_URL:-http://192.168.50.254:8601}/healthz', timeout=5)
    if resp.status_code == 200:
        print('✅ TaskD服务健康')
        sys.exit(0)
    else:
        print(f'❌ TaskD服务不健康: {resp.status_code}')
        sys.exit(1)
except Exception as e:
    print(f'❌ 无法连接TaskD服务: {e}')
    sys.exit(1)
"
        ;;
    
    "shell")
        echo "进入shell模式..."
        echo "注意: 超精简镜像仅提供基础Python环境"
        echo "可用工具: python3, pytest"
        exec sh
        ;;
    
    *)
        echo "❌ 未知命令: $1"
        echo "可用命令: test, health-check, shell"
        exit 1
        ;;
esac