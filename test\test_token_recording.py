#!/usr/bin/env python3
"""
Token管理功能测试脚本
测试TaskD项目中的token记录、统计、限额等功能
"""

import json
import requests
import os
import time
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime
import pytest

# 测试用户常量
TEST_USER_ID = "test_user_intent"
TEST_COMPANY_ID = "test_company_intent"

class TestTokenManagement:
    """Token管理功能测试套件"""
    
    def test_token_consumption_api(self):
        """测试token消耗记录API"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://**************:8601")
        
        print("\n=== 测试Token消耗记录API ===")
        
        payload = {
            "user_id": TEST_USER_ID,
            "company_id": TEST_COMPANY_ID,
            "model_provider": "volcengine_ark",
            "model_name": "doubao-1-5-pro-32k-250115",
            "input_tokens": 200,
            "output_tokens": 100,
            "request_id": f"test_api_{int(datetime.now().timestamp())}",
            "api_endpoint": "/v1/test/api"
        }
        
        url = f"{base_url}/v1/tokens/consumption"
        print(f"POST {url}")
        print(f"请求数据: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=payload, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        assert response.status_code == 201, f"token消耗记录API失败: {response.text}"
        
        data = response.json()
        assert data["user_id"] == TEST_USER_ID
        assert data["input_tokens"] == 200
        assert data["output_tokens"] == 100
        assert data["total_tokens"] == 300
        
        print("Token消耗记录API测试通过")
    
    def test_token_stats_api(self):
        """测试token统计API"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://**************:8601")
        
        print("\n=== 测试Token统计API ===")
        
        url = f"{base_url}/v1/tokens/stats/user/{TEST_USER_ID}"
        print(f"GET {url}")
        
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        assert response.status_code == 200, f"token统计API失败: {response.text}"
        
        data = response.json()
        assert data["user_id"] == TEST_USER_ID
        assert data["company_id"] == TEST_COMPANY_ID
        assert "monthly_usage" in data
        assert "weekly_usage" in data
        assert "daily_usage" in data
        
        print("Token统计API测试通过")
    
    def test_token_history_api(self):
        """测试token历史记录API"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://**************:8601")
        
        print("\n=== 测试Token历史记录API ===")
        
        url = f"{base_url}/v1/tokens/history/{TEST_USER_ID}?page=1&limit=5"
        print(f"GET {url}")
        
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        assert response.status_code == 200, f"token历史记录API失败: {response.text}"
        
        data = response.json()
        assert "data" in data
        assert "pagination" in data
        
        # Handle case where data might be None or empty
        records = data["data"] if data["data"] is not None else []
        if len(records) > 0:
            record = records[0]
            assert "id" in record
            assert "user_id" in record
            assert "input_tokens" in record
            assert "output_tokens" in record
            assert "total_tokens" in record
            assert "consumed_at" in record
            print(f"找到 {len(records)} 条历史记录")
        else:
            print("历史记录为空")
        
        print("Token历史记录API测试通过")
    
    def test_token_limits_api(self):
        """测试token限额检查API"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://**************:8601")
        
        print("\n=== 测试Token限额检查API ===")
        
        url = f"{base_url}/v1/tokens/limits/{TEST_USER_ID}?tokens=1000"
        print(f"GET {url}")
        
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        assert response.status_code == 200, f"token限额检查API失败: {response.text}"
        
        data = response.json()
        assert data["user_id"] == TEST_USER_ID
        assert data["company_id"] == TEST_COMPANY_ID
        assert "requested_tokens" in data
        assert "can_proceed" in data
        assert "monthly_check" in data
        assert "weekly_check" in data
        assert "daily_check" in data
        
        print("Token限额检查API测试通过")
    
    def test_company_token_stats_api(self):
        """测试公司token统计API"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://**************:8601")
        
        print("\n=== 测试公司Token统计API ===")
        
        url = f"{base_url}/v1/tokens/stats/company/{TEST_COMPANY_ID}"
        print(f"GET {url}")
        
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        
        assert response.status_code == 200, f"公司token统计API失败: {response.text}"
        
        data = response.json()
        assert data["company_id"] == TEST_COMPANY_ID
        assert "company_name" in data
        assert "subscription_type" in data
        assert "total_users" in data
        assert "monthly_aggregate" in data
        assert "weekly_aggregate" in data
        assert "daily_aggregate" in data
        
        print("公司Token统计API测试通过")
    
    def test_database_token_records(self):
        """测试数据库中的token记录"""
        print("\n=== 测试数据库Token记录 ===")
        
        try:
            conn = psycopg2.connect(
                host=os.environ.get("DB_HOST", "***********"),
                port=int(os.environ.get("DB_PORT", "5433")),
                user=os.environ.get("DB_USER", "admin"),
                password=os.environ.get("DB_PASSWORD", "SecurePass123!"),
                database=os.environ.get("DB_NAME", "overseas")
            )
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute("""
                    SELECT id, user_id, company_id, model_name, model_provider, 
                           input_tokens, output_tokens, total_tokens, consumed_at
                    FROM taskd.token_consumption 
                    WHERE user_id = %s 
                    ORDER BY consumed_at DESC
                    LIMIT 10
                """, (TEST_USER_ID,))
                
                records = cursor.fetchall()
                print(f"数据库中找到 {len(records)} 条token消耗记录")
                
                for record in records:
                    print(f"  - ID: {record['id']}, 模型: {record['model_name']}, "
                          f"输入token: {record['input_tokens']}, 输出token: {record['output_tokens']}, "
                          f"总计: {record['total_tokens']}, 时间: {record['consumed_at']}")
                
                conn.close()
                
                # 验证至少有一些记录（如果之前的测试都执行了）
                print("数据库Token记录查询测试通过")
                
        except Exception as e:
            print(f"数据库查询失败: {e}")
            # 不强制失败，允许数据库暂时不可用
            pytest.skip(f"数据库连接失败，跳过测试: {e}")
    
    def test_token_limit_validation(self):
        """测试token限额验证逻辑"""
        base_url = os.environ.get("TASKD_BASE_URL", "http://**************:8601")
        
        print("\n=== 测试Token限额验证逻辑 ===")
        
        # 测试正常范围内的token请求
        url = f"{base_url}/v1/tokens/limits/{TEST_USER_ID}?tokens=500"
        response = requests.get(url, timeout=30)
        
        assert response.status_code == 200
        data = response.json()
        normal_can_proceed = data["can_proceed"]
        print(f"500 tokens请求: can_proceed = {normal_can_proceed}")
        
        # 测试大量token请求（可能超限）
        url = f"{base_url}/v1/tokens/limits/{TEST_USER_ID}?tokens=999999"
        response = requests.get(url, timeout=30)
        
        assert response.status_code == 200
        data = response.json()
        large_can_proceed = data["can_proceed"]
        print(f"999999 tokens请求: can_proceed = {large_can_proceed}")
        
        # 验证限额检查逻辑的合理性
        assert isinstance(normal_can_proceed, bool)
        assert isinstance(large_can_proceed, bool)
        
        print("Token限额验证逻辑测试通过")

def test_token_management_integration():
    """集成测试：完整的token管理流程"""
    print("\n=== Token管理集成测试 ===")
    
    test_suite = TestTokenManagement()
    
    # 按顺序执行测试
    test_suite.test_token_consumption_api()
    time.sleep(1)  # 等待数据写入
    
    test_suite.test_token_stats_api()
    test_suite.test_token_history_api()
    test_suite.test_token_limits_api()
    test_suite.test_company_token_stats_api()
    test_suite.test_database_token_records()
    test_suite.test_token_limit_validation()
    
    print("\nToken管理集成测试完成!")

if __name__ == "__main__":
    # 设置环境变量
    if "TASKD_BASE_URL" not in os.environ:
        os.environ["TASKD_BASE_URL"] = "http://**************:8601"
    
    # 运行集成测试
    test_token_management_integration()
    print("\n所有Token管理测试通过!")