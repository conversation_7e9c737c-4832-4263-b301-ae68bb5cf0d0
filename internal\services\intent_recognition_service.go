package services

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models/business"
)

type IntentRecognitionService struct {
	llmClient      llm.LLMClient
	defaultModelID string
}

func NewIntentRecognitionService(
	llmClient llm.LLMClient,
	defaultModelID string,
) *IntentRecognitionService {
	return &IntentRecognitionService{
		llmClient:      llmClient,
		defaultModelID: defaultModelID,
	}
}

// RecognizeIntent performs intent recognition using LLM
func (s *IntentRecognitionService) RecognizeIntent(
	ctx context.Context,
	req *business.IntentRecognizeRequest,
) (*business.IntentRecognizeResponse, error) {
	startTime := time.Now()

	// TODO: 修复promptManager依赖问题 - 暂时返回错误
	errorMsg := "IntentRecognitionService暂时不可用，需要修复promptManager依赖"
	return &business.IntentRecognizeResponse{
		Error: &errorMsg,
		Metadata: business.IntentMetadata{
			DurationMs:  time.Since(startTime).Milliseconds(),
			ProcessedAt: time.Now(),
		},
	}, fmt.Errorf(errorMsg)
}

// GetSupportedIntents returns the list of supported intents
func (s *IntentRecognitionService) GetSupportedIntents() business.SupportedIntents {
	return business.GetSupportedIntents()
}
