package services

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/specific-ai/taskd/internal/llm"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/business"
	"gitlab.com/specific-ai/taskd/internal/models/common"
)

type IntentRecognitionService struct {
	llmClient      llm.LLMClient
	defaultModelID string
}

func NewIntentRecognitionService(
	llmClient llm.LLMClient,
	defaultModelID string,
) *IntentRecognitionService {
	return &IntentRecognitionService{
		llmClient:      llmClient,
		defaultModelID: defaultModelID,
	}
}

// RecognizeIntent performs intent recognition using LLM
func (s *IntentRecognitionService) RecognizeIntent(
	ctx context.Context,
	req *business.IntentRecognizeRequest,
) (*business.IntentRecognizeResponse, error) {
	startTime := time.Now()

	// 获取 intent 分类的 prompt 模板
	promptSet, err := s.promptManager.GetPromptSet("intent_classification")
	if err != nil {
		errorMsg := fmt.Sprintf("获取 intent_classification prompts 失败: %v", err)
		return &business.IntentRecognizeResponse{
			Error: &errorMsg,
			Metadata: business.IntentMetadata{
				DurationMs:  time.Since(startTime).Milliseconds(),
				ProcessedAt: time.Now(),
			},
		}, err
	}

	// 构建prompt数据
	promptData := map[string]interface{}{
		"Messages": req.Messages,
	}

	// 格式化系统和用户prompt
	sysContent, err := s.promptManager.FormatPrompt(promptSet.SysPrompt, promptData)
	if err != nil {
		errorMsg := fmt.Sprintf("格式化 system prompt 失败: %v", err)
		return &business.IntentRecognizeResponse{
			Error: &errorMsg,
			Metadata: business.IntentMetadata{
				DurationMs:  time.Since(startTime).Milliseconds(),
				ProcessedAt: time.Now(),
			},
		}, err
	}

	userContent, err := s.promptManager.FormatPrompt(promptSet.UserPrompt, promptData)
	if err != nil {
		errorMsg := fmt.Sprintf("格式化 user prompt 失败: %v", err)
		return &business.IntentRecognizeResponse{
			Error: &errorMsg,
			Metadata: business.IntentMetadata{
				DurationMs:  time.Since(startTime).Milliseconds(),
				ProcessedAt: time.Now(),
			},
		}, err
	}

	// Build the conversation for LLM
	messages := []common.LLMMessage{
		{
			Role:    "system",
			Content: sysContent,
		},
		{
			Role:    "user",
			Content: userContent,
		},
	}

	// Prepare LLM request with internal configuration
	temperature := 0.1
	maxTokens := 500
	requestID := fmt.Sprintf("intent_%s_%d", req.UserID, time.Now().UnixNano())

	// 记录意图识别请求开始
	fmt.Printf("\n=== 意图识别服务开始 ===\n")
	fmt.Printf("📝 请求参数: 用户ID=%s, 组织ID=%s, 请求ID=%s\n", req.UserID, req.OrganizationID, requestID)
	fmt.Printf("🤖 使用模型: %s\n", s.defaultModelID)
	fmt.Printf("🔍 待识别文本: %s\n", req.Messages[len(req.Messages)-1].Content)
	fmt.Printf("📊 Token自动跟踪: 已启用\n")

	llmRequest := models.OpenAICompatibleRequestParams{
		Model:           s.defaultModelID,
		Messages:        messages,
		Temperature:     &temperature,
		MaxTokens:       &maxTokens,
		AutoTrackTokens: true,
		UserContext: &models.UserContext{
			UserID:    req.UserID,
			CompanyID: req.OrganizationID,
			RequestID: requestID,
			Endpoint:  "/v1/intent/recognize",
		},
	}

	// Execute directly through LLM client with token usage tracking
	fmt.Printf("🚀 正在调用LLM服务...\n")
	llmResponseWithUsage, err := s.llmClient.ChatCompletionsWithUsage(llmRequest)
	if err != nil {
		errorMsg := fmt.Sprintf("LLM request failed: %v", err)
		fmt.Printf("❌ LLM调用失败: %v\n", err)
		return &business.IntentRecognizeResponse{
			Error: &errorMsg,
			Metadata: business.IntentMetadata{
				DurationMs:  time.Since(startTime).Milliseconds(),
				ProcessedAt: time.Now(),
			},
		}, err
	}

	// 获取响应内容和token使用信息
	var llmResponse string
	var tokenUsage common.TokenUsage

	if len(llmResponseWithUsage.Choices) > 0 {
		llmResponse = llmResponseWithUsage.Choices[0].Message.Content
		tokenUsage = common.TokenUsage{
			InputTokens:  llmResponseWithUsage.Usage.PromptTokens,
			OutputTokens: llmResponseWithUsage.Usage.CompletionTokens,
			TotalTokens:  llmResponseWithUsage.Usage.TotalTokens,
		}
	} else {
		errorMsg := "LLM响应中没有choices"
		return &business.IntentRecognizeResponse{
			Error: &errorMsg,
			Metadata: business.IntentMetadata{
				DurationMs:  time.Since(startTime).Milliseconds(),
				ProcessedAt: time.Now(),
			},
		}, fmt.Errorf("LLM响应中没有choices")
	}

	// Parse the LLM response
	fmt.Printf("[LLM_CALL_SUCCESS] parsing response...\n")
	fmt.Printf("[LLM_RAW_RESPONSE] content=%s\n", llmResponse)
	fmt.Printf("[LLM_TOKEN_USAGE] input_tokens=%d, output_tokens=%d, total_tokens=%d\n",
		tokenUsage.InputTokens, tokenUsage.OutputTokens, tokenUsage.TotalTokens)

	intentResult, err := s.parseIntentResponse(llmResponse)
	if err != nil {
		errorMsg := fmt.Sprintf("Failed to parse intent response: %v", err)
		fmt.Printf("[INTENT_PARSE_FAILED] error=%v\n", err)
		return &business.IntentRecognizeResponse{
			Error: &errorMsg,
			Metadata: business.IntentMetadata{
				DurationMs:  time.Since(startTime).Milliseconds(),
				ProcessedAt: time.Now(),
				ModelUsed:   s.defaultModelID,
				TokensUsed:  tokenUsage,
			},
		}, err
	}

	// 意图识别完成
	fmt.Printf("[INTENT_RECOGNITION_SUCCESS] intent=%s, confidence=%.2f\n", intentResult.Intent, intentResult.Confidence)
	fmt.Printf("[PERFORMANCE] duration_ms=%d\n", time.Since(startTime).Milliseconds())
	fmt.Printf("=== [INTENT_RECOGNITION_END] ===\n\n")

	return &business.IntentRecognizeResponse{
		Intent:      intentResult.Intent,
		Confidence:  intentResult.Confidence,
		Explanation: intentResult.Explanation,
		Metadata: business.IntentMetadata{
			ModelUsed:   s.defaultModelID,
			TokensUsed:  tokenUsage, // 使用LLM API返回的真实token数据
			DurationMs:  time.Since(startTime).Milliseconds(),
			ProcessedAt: time.Now(),
		},
	}, nil
}

// IntentParseResult represents the parsed intent from LLM response
type IntentParseResult struct {
	Intent      string  `json:"intent"`
	Confidence  float64 `json:"confidence"`
	Explanation string  `json:"explanation"`
}

// parseIntentResponse parses the LLM response and extracts intent information
func (s *IntentRecognitionService) parseIntentResponse(content string) (*IntentParseResult, error) {
	// Clean the response to extract JSON
	content = strings.TrimSpace(content)

	// Try to find JSON in the response
	startIdx := strings.Index(content, "{")
	endIdx := strings.LastIndex(content, "}")

	if startIdx == -1 || endIdx == -1 {
		return nil, fmt.Errorf("no valid JSON found in response: %s", content)
	}

	jsonStr := content[startIdx : endIdx+1]

	var result IntentParseResult
	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// Validate the intent
	supportedIntents := business.GetSupportedIntents()
	validIntents := []string{
		supportedIntents.BiddingAnalysis,
		supportedIntents.BusinessNews,
		supportedIntents.ChatSummary,
		supportedIntents.CasualChat,
	}

	isValid := false
	for _, validIntent := range validIntents {
		if result.Intent == validIntent {
			isValid = true
			break
		}
	}

	if !isValid {
		return nil, fmt.Errorf("invalid intent returned: %s", result.Intent)
	}

	// Ensure confidence is within valid range
	if result.Confidence < 0 {
		result.Confidence = 0
	}
	if result.Confidence > 1 {
		result.Confidence = 1
	}

	return &result, nil
}

// GetSupportedIntents returns the list of supported intents
func (s *IntentRecognitionService) GetSupportedIntents() business.SupportedIntents {
	return business.GetSupportedIntents()
}
