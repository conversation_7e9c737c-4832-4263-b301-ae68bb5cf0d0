package database

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gitlab.com/specific-ai/taskd/internal/modules/bidding/agents"
	agentCommon "gitlab.com/specific-ai/taskd/internal/modules/bidding/agents/common"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DatabaseAgent 数据库查询Agent
type DatabaseAgent struct {
	*agents.BaseAgent
	mongoClient *mongo.Client
	database    *mongo.Database
	collection  *mongo.Collection
}

// NewDatabaseAgent 创建数据库查询Agent
func NewDatabaseAgent(mongoClient *mongo.Client, databaseName, collectionName string) *DatabaseAgent {
	agentCard := biddingModels.AgentCard{
		Name:        agentCommon.AgentDatabaseName,
		Description: "执行MongoDB数据库查询操作，支持候选数据筛选和状态更新",
		URL:         "http://taskd-service:8601/agents/database-query",
		Provider: biddingModels.AgentProvider{
			Organization: "TaskD Platform",
			URL:          "https://taskd.platform",
		},
		Version: "1.0.0",
		Capabilities: biddingModels.AgentCapabilities{
			Streaming:              false,
			PushNotifications:      false,
			StateTransitionHistory: true,
		},
		Authentication: biddingModels.AgentAuth{
			Schemes: []string{"Bearer"},
		},
		DefaultInputModes:  []string{"application/json"},
		DefaultOutputModes: []string{"application/json"},
		Skills: []biddingModels.AgentSkill{
			{
				ID:          agentCommon.SkillQueryCandidateData,
				Name:        "候选数据查询",
				Description: "根据优先级策略查询待处理的招投标候选数据",
				Tags:        []string{"database", "query", "mongodb", "priority"},
				Examples: []string{
					"查询top 100优先级最高的候选数据",
					"按行业和地区筛选候选数据",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
			{
				ID:          agentCommon.SkillUpdateProcessingStatus,
				Name:        "处理状态更新",
				Description: "更新招投标数据的处理状态和结果",
				Tags:        []string{"database", "update", "status", "results"},
				Examples: []string{
					"更新数据处理状态为已完成",
					"批量更新处理结果到数据库",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
			{
				ID:          agentCommon.SkillExecuteComplexQuery,
				Name:        "复杂查询执行",
				Description: "执行复杂的聚合查询和统计分析",
				Tags:        []string{"database", "aggregation", "analytics", "complex"},
				Examples: []string{
					"按时间维度统计处理数据量",
					"分析不同行业的数据质量分布",
				},
				InputModes:  []string{"application/json"},
				OutputModes: []string{"application/json"},
			},
		},
	}

	baseAgent := agents.NewBaseAgent(agentCommon.AgentDatabaseName, agentCard)

	database := mongoClient.Database(databaseName)
	collection := database.Collection(collectionName)

	return &DatabaseAgent{
		BaseAgent:   baseAgent,
		mongoClient: mongoClient,
		database:    database,
		collection:  collection,
	}
}

// ExecuteSkill 执行技能
func (a *DatabaseAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	switch skillID {
	case agentCommon.SkillQueryCandidateData:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.queryCandidateDataHandler)
	case agentCommon.SkillUpdateProcessingStatus:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.updateProcessingStatusHandler)
	case agentCommon.SkillExecuteComplexQuery:
		return a.BaseAgent.ExecuteSkillWithHandler(skillID, input, context, a.executeComplexQueryHandler)
	default:
		return nil, fmt.Errorf("unsupported skill: %s", skillID)
	}
}

// queryCandidateDataHandler 候选数据查询处理器
func (a *DatabaseAgent) queryCandidateDataHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseQueryCandidateParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Querying candidate data with limit: %d", params.Limit)

	// 执行查询
	candidates, metadata, err := a.performCandidateQuery(params)
	if err != nil {
		return nil, fmt.Errorf("candidate query failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"candidates":          candidates,
		"query_metadata":      metadata,
		"processing_metadata": a.generateQueryMetadata(len(candidates)),
	}

	return output, nil
}

// updateProcessingStatusHandler 处理状态更新处理器
func (a *DatabaseAgent) updateProcessingStatusHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseUpdateStatusParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Updating processing status for %d records", len(params.Updates))

	// 执行更新
	result, err := a.performStatusUpdate(params)
	if err != nil {
		return nil, fmt.Errorf("status update failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"update_result":       result,
		"processing_metadata": a.generateUpdateMetadata(result),
	}

	return output, nil
}

// executeComplexQueryHandler 复杂查询执行处理器
func (a *DatabaseAgent) executeComplexQueryHandler(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error) {
	// 解析输入参数
	params, err := a.parseComplexQueryParams(input)
	if err != nil {
		return nil, fmt.Errorf("invalid input parameters: %v", err)
	}

	utils.Log.Infof("Executing complex query: %s", params.QueryType)

	// 执行复杂查询
	result, err := a.performComplexQuery(params)
	if err != nil {
		return nil, fmt.Errorf("complex query failed: %v", err)
	}

	// 构建输出结果
	output := map[string]interface{}{
		"query_result":        result,
		"processing_metadata": a.generateComplexQueryMetadata(params.QueryType),
	}

	return output, nil
}

// QueryCandidateParams 候选数据查询参数
type QueryCandidateParams struct {
	Limit             int                    `json:"limit"`
	ProcessingStatus  string                 `json:"processing_status"`
	PriorityThreshold float64                `json:"priority_threshold"`
	IndustryFilter    []string               `json:"industry_filter"`
	RegionFilter      []string               `json:"region_filter"`
	BudgetRange       *BudgetRange           `json:"budget_range"`
	TimeRange         *TimeRange             `json:"time_range"`
	SortBy            string                 `json:"sort_by"`
	SortOrder         string                 `json:"sort_order"`
	AdditionalFilters map[string]interface{} `json:"additional_filters"`
}

// UpdateStatusParams 状态更新参数
type UpdateStatusParams struct {
	Updates []StatusUpdate `json:"updates"`
}

// ComplexQueryParams 复杂查询参数
type ComplexQueryParams struct {
	QueryType   string                 `json:"query_type"`
	Parameters  map[string]interface{} `json:"parameters"`
	Aggregation bool                   `json:"aggregation"`
	GroupBy     []string               `json:"group_by"`
	TimeRange   *TimeRange             `json:"time_range"`
}

// BudgetRange 预算范围
type BudgetRange struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// StatusUpdate 状态更新项
type StatusUpdate struct {
	ID               string                 `json:"id"`
	ProcessingStatus string                 `json:"processing_status"`
	ProcessingResult map[string]interface{} `json:"processing_result"`
	UpdatedAt        time.Time              `json:"updated_at"`
}

// CandidateData 候选数据
type CandidateData struct {
	ID               primitive.ObjectID     `bson:"_id" json:"id"`
	Title            string                 `bson:"title" json:"title"`
	Content          string                 `bson:"content" json:"content"`
	Source           string                 `bson:"source" json:"source"`
	ProcessingStatus string                 `bson:"processing_status" json:"processing_status"`
	Priority         float64                `bson:"priority" json:"priority"`
	Industry         string                 `bson:"industry" json:"industry"`
	Region           string                 `bson:"region" json:"region"`
	Budget           float64                `bson:"budget" json:"budget"`
	PublishTime      time.Time              `bson:"publish_time" json:"publish_time"`
	CreatedAt        time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt        time.Time              `bson:"updated_at" json:"updated_at"`
	Metadata         map[string]interface{} `bson:"metadata" json:"metadata"`
}

// parseQueryCandidateParams 解析候选数据查询参数
func (a *DatabaseAgent) parseQueryCandidateParams(input map[string]interface{}) (*QueryCandidateParams, error) {
	params := &QueryCandidateParams{
		Limit:             100,       // 默认限制100条
		ProcessingStatus:  "pending", // 默认查询待处理状态
		PriorityThreshold: 0.0,       // 默认无优先级阈值
		SortBy:            "priority",
		SortOrder:         "desc",
	}

	// 解析limit
	if limitFloat, ok := input["limit"].(float64); ok {
		params.Limit = int(limitFloat)
	}

	// 解析processing_status
	if status, ok := input["processing_status"].(string); ok {
		params.ProcessingStatus = status
	}

	// 解析priority_threshold
	if threshold, ok := input["priority_threshold"].(float64); ok {
		params.PriorityThreshold = threshold
	}

	// 解析industry_filter
	if industryInterface, ok := input["industry_filter"]; ok {
		if industryArray, ok := industryInterface.([]interface{}); ok {
			for _, item := range industryArray {
				if industry, ok := item.(string); ok {
					params.IndustryFilter = append(params.IndustryFilter, industry)
				}
			}
		}
	}

	// 解析region_filter
	if regionInterface, ok := input["region_filter"]; ok {
		if regionArray, ok := regionInterface.([]interface{}); ok {
			for _, item := range regionArray {
				if region, ok := item.(string); ok {
					params.RegionFilter = append(params.RegionFilter, region)
				}
			}
		}
	}

	// 解析budget_range
	if budgetRangeInterface, ok := input["budget_range"]; ok {
		budgetRangeBytes, err := json.Marshal(budgetRangeInterface)
		if err == nil {
			var budgetRange BudgetRange
			if json.Unmarshal(budgetRangeBytes, &budgetRange) == nil {
				params.BudgetRange = &budgetRange
			}
		}
	}

	// 解析time_range
	if timeRangeInterface, ok := input["time_range"]; ok {
		timeRangeBytes, err := json.Marshal(timeRangeInterface)
		if err == nil {
			var timeRange TimeRange
			if json.Unmarshal(timeRangeBytes, &timeRange) == nil {
				params.TimeRange = &timeRange
			}
		}
	}

	// 解析sort_by和sort_order
	if sortBy, ok := input["sort_by"].(string); ok {
		params.SortBy = sortBy
	}
	if sortOrder, ok := input["sort_order"].(string); ok {
		params.SortOrder = sortOrder
	}

	// 解析additional_filters
	if additionalFilters, ok := input["additional_filters"].(map[string]interface{}); ok {
		params.AdditionalFilters = additionalFilters
	}

	return params, nil
}

// parseUpdateStatusParams 解析状态更新参数
func (a *DatabaseAgent) parseUpdateStatusParams(input map[string]interface{}) (*UpdateStatusParams, error) {
	updatesInterface, ok := input["updates"]
	if !ok {
		return nil, fmt.Errorf("updates is required")
	}

	updatesBytes, err := json.Marshal(updatesInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal updates: %v", err)
	}

	var updates []StatusUpdate
	if err := json.Unmarshal(updatesBytes, &updates); err != nil {
		return nil, fmt.Errorf("failed to parse updates: %v", err)
	}

	return &UpdateStatusParams{
		Updates: updates,
	}, nil
}

// parseComplexQueryParams 解析复杂查询参数
func (a *DatabaseAgent) parseComplexQueryParams(input map[string]interface{}) (*ComplexQueryParams, error) {
	queryType, ok := input["query_type"].(string)
	if !ok {
		return nil, fmt.Errorf("query_type is required")
	}

	params := &ComplexQueryParams{
		QueryType:   queryType,
		Aggregation: false,
	}

	// 解析parameters
	if parametersInterface, ok := input["parameters"]; ok {
		if parameters, ok := parametersInterface.(map[string]interface{}); ok {
			params.Parameters = parameters
		}
	}

	// 解析aggregation
	if aggregation, ok := input["aggregation"].(bool); ok {
		params.Aggregation = aggregation
	}

	// 解析group_by
	if groupByInterface, ok := input["group_by"]; ok {
		if groupByArray, ok := groupByInterface.([]interface{}); ok {
			for _, item := range groupByArray {
				if field, ok := item.(string); ok {
					params.GroupBy = append(params.GroupBy, field)
				}
			}
		}
	}

	// 解析time_range
	if timeRangeInterface, ok := input["time_range"]; ok {
		timeRangeBytes, err := json.Marshal(timeRangeInterface)
		if err == nil {
			var timeRange TimeRange
			if json.Unmarshal(timeRangeBytes, &timeRange) == nil {
				params.TimeRange = &timeRange
			}
		}
	}

	return params, nil
}

// performCandidateQuery 执行候选数据查询
func (a *DatabaseAgent) performCandidateQuery(params *QueryCandidateParams) ([]*CandidateData, map[string]interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}

	// 处理状态过滤
	if params.ProcessingStatus != "" {
		filter["processing_status"] = params.ProcessingStatus
	}

	// 优先级阈值过滤
	if params.PriorityThreshold > 0 {
		filter["priority"] = bson.M{"$gte": params.PriorityThreshold}
	}

	// 行业过滤
	if len(params.IndustryFilter) > 0 {
		filter["industry"] = bson.M{"$in": params.IndustryFilter}
	}

	// 区域过滤
	if len(params.RegionFilter) > 0 {
		filter["region"] = bson.M{"$in": params.RegionFilter}
	}

	// 预算范围过滤
	if params.BudgetRange != nil {
		budgetFilter := bson.M{}
		if params.BudgetRange.Min > 0 {
			budgetFilter["$gte"] = params.BudgetRange.Min
		}
		if params.BudgetRange.Max > 0 {
			budgetFilter["$lte"] = params.BudgetRange.Max
		}
		if len(budgetFilter) > 0 {
			filter["budget"] = budgetFilter
		}
	}

	// 时间范围过滤
	if params.TimeRange != nil {
		timeFilter := bson.M{}
		if !params.TimeRange.StartTime.IsZero() {
			timeFilter["$gte"] = params.TimeRange.StartTime
		}
		if !params.TimeRange.EndTime.IsZero() {
			timeFilter["$lte"] = params.TimeRange.EndTime
		}
		if len(timeFilter) > 0 {
			filter["publish_time"] = timeFilter
		}
	}

	// 额外过滤条件
	for key, value := range params.AdditionalFilters {
		filter[key] = value
	}

	// 构建排序选项
	sort := bson.D{}
	sortOrder := 1
	if params.SortOrder == "desc" {
		sortOrder = -1
	}
	sort = append(sort, bson.E{Key: params.SortBy, Value: sortOrder})

	// 构建查询选项
	findOptions := options.Find().
		SetSort(sort).
		SetLimit(int64(params.Limit))

	// 执行查询
	cursor, err := a.collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, nil, fmt.Errorf("find query failed: %v", err)
	}
	defer cursor.Close(ctx)

	// 解析结果
	var candidates []*CandidateData
	for cursor.Next(ctx) {
		var candidate CandidateData
		if err := cursor.Decode(&candidate); err != nil {
			utils.Log.Warnf("Failed to decode candidate data: %v", err)
			continue
		}
		candidates = append(candidates, &candidate)
	}

	if err := cursor.Err(); err != nil {
		return nil, nil, fmt.Errorf("cursor error: %v", err)
	}

	// 构建查询元数据
	metadata := map[string]interface{}{
		"filter_applied":  filter,
		"total_returned":  len(candidates),
		"limit_requested": params.Limit,
		"sort_criteria":   fmt.Sprintf("%s %s", params.SortBy, params.SortOrder),
		"query_timestamp": time.Now(),
	}

	return candidates, metadata, nil
}

// performStatusUpdate 执行状态更新
func (a *DatabaseAgent) performStatusUpdate(params *UpdateStatusParams) (map[string]interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	var successCount int
	var failedCount int
	var errors []string

	for _, update := range params.Updates {
		// 转换ID
		objectID, err := primitive.ObjectIDFromHex(update.ID)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("invalid ID %s: %v", update.ID, err))
			continue
		}

		// 构建更新文档
		updateDoc := bson.M{
			"$set": bson.M{
				"processing_status": update.ProcessingStatus,
				"updated_at":        update.UpdatedAt,
			},
		}

		// 如果有处理结果，添加到更新文档
		if update.ProcessingResult != nil {
			updateDoc["$set"].(bson.M)["processing_result"] = update.ProcessingResult
		}

		// 执行更新
		result, err := a.collection.UpdateOne(ctx, bson.M{"_id": objectID}, updateDoc)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("update failed for ID %s: %v", update.ID, err))
			continue
		}

		if result.ModifiedCount > 0 {
			successCount++
		} else {
			failedCount++
			errors = append(errors, fmt.Sprintf("no document updated for ID %s", update.ID))
		}
	}

	return map[string]interface{}{
		"total_requested":  len(params.Updates),
		"success_count":    successCount,
		"failed_count":     failedCount,
		"errors":           errors,
		"update_timestamp": time.Now(),
	}, nil
}

// performComplexQuery 执行复杂查询
func (a *DatabaseAgent) performComplexQuery(params *ComplexQueryParams) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	switch params.QueryType {
	case "processing_status_summary":
		return a.getProcessingStatusSummary(ctx, params)
	case "industry_distribution":
		return a.getIndustryDistribution(ctx, params)
	case "daily_processing_stats":
		return a.getDailyProcessingStats(ctx, params)
	case "quality_analysis":
		return a.getQualityAnalysis(ctx, params)
	case "custom_aggregation":
		return a.executeCustomAggregation(ctx, params)
	default:
		return nil, fmt.Errorf("unsupported query type: %s", params.QueryType)
	}
}

// getProcessingStatusSummary 获取处理状态摘要
func (a *DatabaseAgent) getProcessingStatusSummary(ctx context.Context, params *ComplexQueryParams) (interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$group", Value: bson.M{
			"_id":          "$processing_status",
			"count":        bson.M{"$sum": 1},
			"avg_priority": bson.M{"$avg": "$priority"},
		}}},
		{{Key: "$sort", Value: bson.M{"count": -1}}},
	}

	cursor, err := a.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

// getIndustryDistribution 获取行业分布
func (a *DatabaseAgent) getIndustryDistribution(ctx context.Context, params *ComplexQueryParams) (interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$group", Value: bson.M{
			"_id":          "$industry",
			"count":        bson.M{"$sum": 1},
			"avg_budget":   bson.M{"$avg": "$budget"},
			"total_budget": bson.M{"$sum": "$budget"},
		}}},
		{{Key: "$sort", Value: bson.M{"count": -1}}},
		{{Key: "$limit", Value: 20}}, // 限制前20个行业
	}

	cursor, err := a.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

// getDailyProcessingStats 获取每日处理统计
func (a *DatabaseAgent) getDailyProcessingStats(ctx context.Context, params *ComplexQueryParams) (interface{}, error) {
	matchFilter := bson.M{}

	// 添加时间范围过滤
	if params.TimeRange != nil {
		timeFilter := bson.M{}
		if !params.TimeRange.StartTime.IsZero() {
			timeFilter["$gte"] = params.TimeRange.StartTime
		}
		if !params.TimeRange.EndTime.IsZero() {
			timeFilter["$lte"] = params.TimeRange.EndTime
		}
		if len(timeFilter) > 0 {
			matchFilter["updated_at"] = timeFilter
		}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: matchFilter}},
		{{Key: "$group", Value: bson.M{
			"_id": bson.M{
				"date": bson.M{
					"$dateToString": bson.M{
						"format": "%Y-%m-%d",
						"date":   "$updated_at",
					},
				},
				"status": "$processing_status",
			},
			"count": bson.M{"$sum": 1},
		}}},
		{{Key: "$sort", Value: bson.M{"_id.date": 1, "_id.status": 1}}},
	}

	cursor, err := a.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

// getQualityAnalysis 获取质量分析
func (a *DatabaseAgent) getQualityAnalysis(ctx context.Context, params *ComplexQueryParams) (interface{}, error) {
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"processing_result": bson.M{"$exists": true}}}},
		{{Key: "$group", Value: bson.M{
			"_id":             nil,
			"total_processed": bson.M{"$sum": 1},
			"avg_priority":    bson.M{"$avg": "$priority"},
			"quality_distribution": bson.M{
				"$push": "$processing_result.overall_quality",
			},
		}}},
	}

	cursor, err := a.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

// executeCustomAggregation 执行自定义聚合
func (a *DatabaseAgent) executeCustomAggregation(ctx context.Context, params *ComplexQueryParams) (interface{}, error) {
	// 从参数中获取自定义pipeline
	pipelineInterface, ok := params.Parameters["pipeline"]
	if !ok {
		return nil, fmt.Errorf("custom aggregation requires 'pipeline' parameter")
	}

	// 转换为MongoDB pipeline
	pipelineBytes, err := json.Marshal(pipelineInterface)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal pipeline: %v", err)
	}

	var pipeline mongo.Pipeline
	if err := bson.UnmarshalExtJSON(pipelineBytes, false, &pipeline); err != nil {
		return nil, fmt.Errorf("failed to parse pipeline: %v", err)
	}

	cursor, err := a.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err := cursor.All(ctx, &results); err != nil {
		return nil, err
	}

	return results, nil
}

// generateQueryMetadata 生成查询元数据
func (a *DatabaseAgent) generateQueryMetadata(resultCount int) *agentCommon.ProcessingMetadata {
	return &agentCommon.ProcessingMetadata{
		ModelUsed:       "database-query-v1.0",
		TokensConsumed:  0,   // 数据库查询不消耗LLM tokens
		ProcessingTime:  0.1, // 简化的处理时间
		ConfidenceScore: 1.0,
	}
}

// generateUpdateMetadata 生成更新元数据
func (a *DatabaseAgent) generateUpdateMetadata(result map[string]interface{}) *agentCommon.ProcessingMetadata {
	successRate := 0.0
	if totalRequested, ok := result["total_requested"].(int); ok && totalRequested > 0 {
		if successCount, ok := result["success_count"].(int); ok {
			successRate = float64(successCount) / float64(totalRequested)
		}
	}

	return &agentCommon.ProcessingMetadata{
		ModelUsed:       "database-update-v1.0",
		TokensConsumed:  0,
		ProcessingTime:  0.2,
		ConfidenceScore: successRate,
	}
}

// generateComplexQueryMetadata 生成复杂查询元数据
func (a *DatabaseAgent) generateComplexQueryMetadata(queryType string) *agentCommon.ProcessingMetadata {
	return &agentCommon.ProcessingMetadata{
		ModelUsed:       fmt.Sprintf("database-%s-v1.0", strings.ReplaceAll(queryType, "_", "-")),
		TokensConsumed:  0,
		ProcessingTime:  0.5,
		ConfidenceScore: 1.0,
	}
}
