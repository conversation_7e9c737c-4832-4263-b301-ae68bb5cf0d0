package agents

import (
	"fmt"
	"time"

	"github.com/go-playground/validator/v10"
	biddingInterfaces "gitlab.com/specific-ai/taskd/internal/modules/bidding/interfaces"
	biddingModels "gitlab.com/specific-ai/taskd/internal/modules/bidding/models"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// Ensure BaseAgent implements Agent interface
var _ biddingInterfaces.Agent = (*BaseAgent)(nil)

// BaseAgent provides common functionality for all A2A agents
type BaseAgent struct {
	id        string
	agentCard biddingModels.AgentCard
	validator *validator.Validate
	startTime time.Time
	metrics   *AgentMetrics
}

// AgentMetrics tracks agent performance metrics
type AgentMetrics struct {
	RequestCount      int64
	SuccessCount      int64
	FailureCount      int64
	TotalResponseTime int64
	LastRequestTime   time.Time
}

// SkillHandler represents a function that handles a specific skill
type SkillHandler func(input map[string]interface{}, context biddingModels.A2AContext) (map[string]interface{}, error)

// NewBaseAgent creates a new base agent with the given configuration
func NewBaseAgent(id string, agentCard biddingModels.AgentCard) *BaseAgent {
	return &BaseAgent{
		id:        id,
		agentCard: biddingModels.AgentCard{
			Name:        agentCard.Name,
			Version:     agentCard.Version,
			Description: agentCard.Description,
			Skills:      agentCard.Skills,
		},
		validator: validator.New(),
		startTime: time.Now(),
		metrics:   &AgentMetrics{},
	}
}

// InitializeAgent initializes an agent with the provided configuration
func (a *BaseAgent) InitializeAgent(config map[string]interface{}) error {
	// Create a copy of the agent card with updated values from config
	updatedCard := a.agentCard
	
	// Apply configuration updates if provided
	if config != nil {
		if name, ok := config["name"].(string); ok && name != "" {
			updatedCard.Name = name
		}
		
		if version, ok := config["version"].(string); ok && version != "" {
			updatedCard.Version = version
		}
		
		if description, ok := config["description"].(string); ok && description != "" {
			updatedCard.Description = description
		}
	}
	
	a.agentCard = updatedCard
	return nil
}

// GetID returns the agent's unique identifier
func (a *BaseAgent) GetID() string {
	return a.id
}

// GetAgentCard returns the agent's card information
func (a *BaseAgent) GetAgentCard() biddingModels.AgentCard {
	return a.agentCard
}

// HealthCheck returns the current health status of the agent
func (a *BaseAgent) HealthCheck() biddingModels.HealthStatus {
	uptime := time.Since(a.startTime).Milliseconds()

	// Calculate performance metrics
	var avgResponseTime int
	var successRate float64

	if a.metrics.RequestCount > 0 {
		avgResponseTime = int(a.metrics.TotalResponseTime / a.metrics.RequestCount)
		successRate = float64(a.metrics.SuccessCount) / float64(a.metrics.RequestCount)
	}

	// Calculate requests per minute
	requestsPerMinute := 0
	if time.Since(a.metrics.LastRequestTime) < time.Minute {
		// Simple approximation based on recent activity
		requestsPerMinute = int(a.metrics.RequestCount)
	}

	return biddingModels.HealthStatus{
		Status:  biddingModels.AgentStatusHealthy,
		Version: a.agentCard.Version,
		Uptime:  uptime,
		Dependencies: map[string]string{
			"validator": "ok",
		},
		Performance: biddingModels.AgentPerformance{
			AvgResponseTime:   avgResponseTime,
			RequestsPerMinute: requestsPerMinute,
			ErrorRate:         1.0 - successRate,
			SuccessRate:       successRate,
		},
		Timestamp: time.Now(),
	}
}

// ExecuteSkill executes a skill with the given input and context
func (a *BaseAgent) ExecuteSkill(skillID string, input map[string]interface{}, context biddingModels.A2AContext) (*biddingModels.A2AResult, error) {
	return nil, fmt.Errorf("ExecuteSkill not implemented - use specific agent implementations")
}

// ExecuteSkillWithHandler executes a skill using the provided handler
func (a *BaseAgent) ExecuteSkillWithHandler(skillID string, input map[string]interface{},
	context biddingModels.A2AContext, handler SkillHandler) (*biddingModels.A2AResult, error) {

	startTime := time.Now()
	a.metrics.RequestCount++
	a.metrics.LastRequestTime = startTime

	// Generate unique task ID
	taskID := fmt.Sprintf("task_%d_%s", startTime.Unix(), skillID)

	// Validate skill exists
	skill := a.findSkill(skillID)
	if skill == nil {
		a.metrics.FailureCount++
		return nil, fmt.Errorf("skill not found: %s", skillID)
	}

	// Validate input against schema if available
	if skill.InputSchema != nil {
		if err := a.validateInput(input, skill.InputSchema); err != nil {
			a.metrics.FailureCount++
			return nil, fmt.Errorf("input validation failed: %w", err)
		}
	}

	utils.Log.Infof("Executing skill %s for agent %s (task: %s)", skillID, a.id, taskID)

	// Execute the skill handler
	output, err := handler(input, context)
	if err != nil {
		a.metrics.FailureCount++
		utils.Log.Errorf("Skill execution failed for %s: %v", skillID, err)
		return nil, fmt.Errorf("skill execution failed: %w", err)
	}

	// Calculate execution time
	executionTime := time.Since(startTime).Milliseconds()
	a.metrics.SuccessCount++
	a.metrics.TotalResponseTime += executionTime

	utils.Log.Infof("Skill %s completed successfully in %dms (task: %s)", skillID, executionTime, taskID)

	return &biddingModels.A2AResult{
		TaskID: taskID,
		Status: biddingModels.TaskStatusCompleted,
		Output: output,
		Metadata: biddingModels.A2AMetadata{
			ExecutionTime: int(executionTime),
			TokensUsed:    0, // Will be updated by specific agents
			AgentVersion:  a.agentCard.Version,
		},
	}, nil
}

// findSkill finds a skill by ID in the agent's skill list
func (a *BaseAgent) findSkill(skillID string) *biddingModels.AgentSkill {
	for _, skill := range a.agentCard.Skills {
		if skill.ID == skillID {
			return &skill
		}
	}
	return nil
}

// validateInput validates input data against a JSON schema
func (a *BaseAgent) validateInput(input map[string]interface{}, schema interface{}) error {
	// For now, we'll do basic validation
	// In a full implementation, you might want to use a JSON schema validator

	// Check required fields if schema is a map
	if schemaMap, ok := schema.(map[string]interface{}); ok {
		if _, hasProps := schemaMap["properties"].(map[string]interface{}); hasProps {
			if required, hasRequired := schemaMap["required"].([]interface{}); hasRequired {
				for _, reqField := range required {
					if fieldName, ok := reqField.(string); ok {
						if _, exists := input[fieldName]; !exists {
							return fmt.Errorf("required field missing: %s", fieldName)
						}
					}
				}
			}
		}
	}

	return nil
}

// UpdateMetrics allows external components to update metrics (e.g., token usage)
func (a *BaseAgent) UpdateMetrics(tokensUsed int) {
	// This can be called by specific agent implementations to update token usage
	// Currently just logging, but could be expanded
	if tokensUsed > 0 {
		utils.Log.Debugf("Agent %s used %d tokens", a.id, tokensUsed)
	}
}

// UpdateTokenConsumption logs token consumption information
func (a *BaseAgent) UpdateTokenConsumption(userID, companyID, modelProvider, modelName string, inputTokens, outputTokens int) {
	if inputTokens == 0 && outputTokens == 0 {
		utils.Log.Debugf("Agent %s: No token consumption to record", a.id)
		return
	}

	// Log token consumption
	utils.Log.Infof("Agent %s recording token consumption: user=%s, company=%s, provider=%s, model=%s, input=%d, output=%d, total=%d",
		a.id, userID, companyID, modelProvider, modelName, inputTokens, outputTokens, inputTokens+outputTokens)
}

// CreateErrorResult creates a standardized error result
