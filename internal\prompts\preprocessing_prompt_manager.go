package prompts

import (
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v2"
)

// PreprocessingPromptManager 预处理prompt管理器
type PreprocessingPromptManager struct {
	prompts map[string]*PreprocessingPromptTemplate
	baseDir string
}

// PreprocessingPromptTemplate 预处理prompt模板
type PreprocessingPromptTemplate struct {
	Name               string              `yaml:"name"`
	Description        string              `yaml:"description"`
	Language           string              `yaml:"language"`
	Version            string              `yaml:"version"`
	SystemPrompt       string              `yaml:"system_prompt"`
	UserPromptTemplate string              `yaml:"user_prompt_template"`
	Variables          map[string]Variable `yaml:"variables"`
}

// NewPreprocessingPromptManager 创建预处理prompt管理器
func NewPreprocessingPromptManager(baseDir string) (*PreprocessingPromptManager, error) {
	if baseDir == "" {
		baseDir = "internal/prompts/preprocessing"
	}

	pm := &PreprocessingPromptManager{
		prompts: make(map[string]*PreprocessingPromptTemplate),
		baseDir: baseDir,
	}

	// 加载所有prompt文件
	if err := pm.loadPrompts(); err != nil {
		return nil, fmt.Errorf("failed to load prompts: %v", err)
	}

	return pm, nil
}

// loadPrompts 加载prompt文件
func (pm *PreprocessingPromptManager) loadPrompts() error {
	// 扫描预处理prompt目录
	modules := []string{"extraction", "classification", "enhancement", "multilingual"}

	for _, module := range modules {
		moduleDir := filepath.Join(pm.baseDir, module)
		if err := pm.loadModulePrompts(module, moduleDir); err != nil {
			log.Printf("Failed to load prompts for module %s: %v", module, err)
			continue
		}
	}

	log.Printf("Loaded %d preprocessing prompt templates", len(pm.prompts))
	return nil
}

// loadModulePrompts 加载模块的prompt文件
func (pm *PreprocessingPromptManager) loadModulePrompts(module, moduleDir string) error {
	files, err := ioutil.ReadDir(moduleDir)
	if err != nil {
		return fmt.Errorf("failed to read directory %s: %v", moduleDir, err)
	}

	for _, file := range files {
		if file.IsDir() || !strings.HasSuffix(file.Name(), ".yaml") {
			continue
		}

		filePath := filepath.Join(moduleDir, file.Name())
		if err := pm.loadPromptFile(filePath); err != nil {
			log.Printf("Failed to load prompt file %s: %v", filePath, err)
			continue
		}
	}

	return nil
}

// loadPromptFile 加载单个prompt文件
func (pm *PreprocessingPromptManager) loadPromptFile(filePath string) error {
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %v", filePath, err)
	}

	var template PreprocessingPromptTemplate
	if err := yaml.Unmarshal(data, &template); err != nil {
		return fmt.Errorf("failed to parse YAML file %s: %v", filePath, err)
	}

	// 验证模板
	if template.Name == "" || template.SystemPrompt == "" {
		return fmt.Errorf("invalid template in file %s: missing name or system_prompt", filePath)
	}

	pm.prompts[template.Name] = &template
	log.Printf("Loaded prompt template: %s (%s)", template.Name, template.Language)

	return nil
}

// GetPromptTemplate 获取prompt模板
func (pm *PreprocessingPromptManager) GetPromptTemplate(name, language string) (*PreprocessingPromptTemplate, error) {
	// 构建模板名称：name_language
	templateName := fmt.Sprintf("%s_%s", name, language)

	template, exists := pm.prompts[templateName]
	if !exists {
		// 如果找不到指定语言的模板，尝试查找默认中文模板
		fallbackName := fmt.Sprintf("%s_zh", name)
		if template, exists = pm.prompts[fallbackName]; !exists {
			return nil, fmt.Errorf("prompt template not found: %s (language: %s)", name, language)
		}
		log.Printf("Using fallback template %s for requested %s", fallbackName, templateName)
	}

	return template, nil
}

// RenderPrompt 渲染prompt
func (pm *PreprocessingPromptManager) RenderPrompt(template *PreprocessingPromptTemplate, variables map[string]interface{}) (string, string, error) {
	// 验证必需变量
	for varName, varDef := range template.Variables {
		if varDef.Required {
			if _, exists := variables[varName]; !exists {
				return "", "", fmt.Errorf("required variable missing: %s", varName)
			}
		}
	}

	// 设置默认值
	for varName, varDef := range template.Variables {
		if _, exists := variables[varName]; !exists && varDef.Default != nil {
			variables[varName] = varDef.Default
		}
	}

	// 渲染系统prompt
	systemPrompt, err := pm.renderTemplate(template.SystemPrompt, variables)
	if err != nil {
		return "", "", fmt.Errorf("failed to render system prompt: %v", err)
	}

	// 渲染用户prompt
	userPrompt := ""
	if template.UserPromptTemplate != "" {
		userPrompt, err = pm.renderTemplate(template.UserPromptTemplate, variables)
		if err != nil {
			return "", "", fmt.Errorf("failed to render user prompt: %v", err)
		}
	}

	return systemPrompt, userPrompt, nil
}

// renderTemplate 渲染模板字符串
func (pm *PreprocessingPromptManager) renderTemplate(templateStr string, variables map[string]interface{}) (string, error) {
	// 简单的变量替换实现
	result := templateStr
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		valueStr := fmt.Sprintf("%v", value)
		result = strings.ReplaceAll(result, placeholder, valueStr)
	}

	// 处理条件语句 {{#if variable}}...{{/if}}
	result = pm.processConditionals(result, variables)

	return result, nil
}

// processConditionals 处理条件语句
func (pm *PreprocessingPromptManager) processConditionals(template string, variables map[string]interface{}) string {
	// 简化的条件处理，支持 {{#if variable}}content{{/if}}
	result := template

	// 这是一个简化实现，实际项目中可能需要更复杂的模板引擎
	for varName, value := range variables {
		ifStart := fmt.Sprintf("{{#if %s}}", varName)
		ifEnd := "{{/if}}"

		for {
			startIdx := strings.Index(result, ifStart)
			if startIdx == -1 {
				break
			}

			endIdx := strings.Index(result[startIdx:], ifEnd)
			if endIdx == -1 {
				break
			}
			endIdx += startIdx

			// 提取条件内容
			content := result[startIdx+len(ifStart) : endIdx]

			// 判断变量是否为真
			replacement := ""
			if pm.isTruthy(value) {
				replacement = content
			}

			// 替换整个条件块
			result = result[:startIdx] + replacement + result[endIdx+len(ifEnd):]
		}
	}

	return result
}

// isTruthy 判断值是否为真
func (pm *PreprocessingPromptManager) isTruthy(value interface{}) bool {
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case string:
		return v != ""
	case int, int64, float64:
		return v != 0
	case []interface{}:
		return len(v) > 0
	case map[string]interface{}:
		return len(v) > 0
	default:
		return true
	}
}

// ListTemplates 列出所有可用模板
func (pm *PreprocessingPromptManager) ListTemplates() []string {
	var names []string
	for name := range pm.prompts {
		names = append(names, name)
	}
	return names
}
