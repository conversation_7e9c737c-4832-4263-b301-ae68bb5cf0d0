package setup

import (
	"gitlab.com/specific-ai/taskd/internal/api/handlers"
	"gitlab.com/specific-ai/taskd/internal/config"
	"gitlab.com/specific-ai/taskd/internal/services/concurrent"
	"gitlab.com/specific-ai/taskd/internal/store"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// TokenModuleDependencies 包含 Token 模块的所有依赖
type TokenModuleDependencies struct {
	TokenService *concurrent.TokenService
	TokenHandler *handlers.TokenHandler
}

// SetupTokenModule 初始化 Token 模块
func SetupTokenModule(cfg *config.Config) (*TokenModuleDependencies, error) {
	utils.Log.Info("正在初始化 Token 模块...")

	// 初始化 PostgreSQL 连接
	pgConfig := store.PostgresConfig{
		Host:            cfg.PostgreSQL.Host,
		Port:            cfg.PostgreSQL.Port,
		User:            cfg.PostgreSQL.User,
		Password:        cfg.PostgreSQL.Password,
		Database:        cfg.PostgreSQL.Database,
		SSLMode:         cfg.PostgreSQL.SSLMode,
		TimeoutSeconds:  cfg.PostgreSQL.TimeoutSeconds,
		MaxOpenConns:    cfg.PostgreSQL.MaxOpenConns,
		MaxIdleConns:    cfg.PostgreSQL.MaxIdleConns,
		ConnMaxLifetime: cfg.PostgreSQL.ConnMaxLifetime,
	}

	if err := store.InitPostgres(pgConfig); err != nil {
		utils.Log.Errorf("Failed to initialize PostgreSQL: %v", err)
		return nil, err
	}

	// 运行数据库迁移
	if err := store.RunMigration(); err != nil {
		utils.Log.Errorf("Failed to run database migration: %v", err)
		return nil, err
	}

	// 创建服务实例
	tokenService := concurrent.NewTokenService()

	// 创建处理器实例
	tokenHandler := handlers.NewTokenHandler(tokenService)

	utils.Log.Info("Token 模块初始化完成")

	return &TokenModuleDependencies{
		TokenService: tokenService,
		TokenHandler: tokenHandler,
	}, nil
}