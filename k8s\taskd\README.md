# TaskD K8s 部署配置

## 目录结构

```
k8s/taskd/
├── README.md           # 本文件
├── namespace.yaml      # 命名空间配置
├── configmap.yaml      # 应用配置（包含etcd配置）
├── secret.yaml         # 敏感信息配置
├── deployment.yaml     # 应用部署配置
├── service.yaml        # 服务配置
├── deploy-taskd.sh     # 自动部署脚本
├── debug-taskd.sh      # 调试脚本
└── quick-restart.sh    # 快速重启脚本
```

## 部署前置条件

⚠️ **重要**：taskd强依赖etcd配置中心，部署前请确保：

1. etcd已成功部署并运行在`etcd` namespace中
2. etcd配置数据已初始化

如果未部署etcd，请先执行：
```bash
cd ../etcd/
./deploy-etcd.sh
./init-config.sh
```

## 快速部署

### 方式1: 自动部署脚本（推荐）

```bash
# 在taskd目录下执行
./deploy-taskd.sh
```

脚本会自动：
- 检查etcd依赖
- 部署所有k8s资源
- 验证部署状态
- 显示访问信息

### 方式2: 手动部署

```bash
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f service.yaml
kubectl apply -f deployment.yaml
```

## 配置说明

### ConfigMap配置

包含taskd的非敏感配置：
- 服务器配置（端口、模式、日志级别）
- 数据库连接配置（非密码部分）
- **etcd配置中心配置**（新增）

### Secret配置

包含敏感信息：
- 数据库连接密码
- LLM API密钥
- 其他敏感配置

⚠️ **生产环境警告**：请更新Secret中的所有敏感值！

### etcd集成配置

taskd现在从etcd配置中心读取配置，相关环境变量：

```yaml
ETCD_ENABLED: "true"
ETCD_ENDPOINTS: "etcd-service.etcd.svc.cluster.local:2379"
ETCD_CONFIG_PREFIX: "/config/taskd"
```

## 验证部署

### 检查Pod状态

```bash
kubectl get pods -n ovs
kubectl logs -n ovs -l app=taskd -f
```

### 检查etcd配置加载

```bash
# 查看启动日志中的etcd相关信息
kubectl logs -n ovs -l app=taskd | grep -E "(etcd|config)"
```

### 检查服务访问

```bash
kubectl get svc -n ovs taskd-service
```

## 配置管理

### 查看etcd中的配置

```bash
kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd --prefix
```

### 修改配置

```bash
# 修改MongoDB数据库名
kubectl exec -n etcd etcd-0 -- etcdctl put /config/taskd/mongodb/database "new_database"

# 修改LLM API密钥
kubectl exec -n etcd etcd-0 -- etcdctl put /config/taskd/llm/providers/volcengine_ark/api_key "new-key"

# 重启taskd应用新配置
kubectl delete pod -n ovs -l app=taskd
```

## 故障排除

### 常见问题

1. **taskd启动失败，提示etcd连接失败**
   ```bash
   # 检查etcd状态
   kubectl get pods -n etcd
   
   # 检查etcd服务
   kubectl get svc -n etcd etcd-service
   
   # 测试网络连通性
   kubectl exec -n ovs <taskd-pod> -- nc -zv etcd-service.etcd.svc.cluster.local 2379
   ```

2. **配置未从etcd加载**
   ```bash
   # 检查etcd中是否有配置数据
   kubectl exec -n etcd etcd-0 -- etcdctl get /config/taskd --prefix
   
   # 如果没有，重新初始化
   cd ../etcd && ./init-config.sh
   ```

3. **Pod一直重启**
   ```bash
   # 查看详细日志
   kubectl logs -n ovs -l app=taskd --previous
   kubectl describe pod -n ovs -l app=taskd
   ```

### 调试模式

使用调试脚本进入调试模式：

```bash
./debug-taskd.sh
```

### 快速重启

```bash
./quick-restart.sh
```

## 清理部署

```bash
kubectl delete namespace ovs
```

⚠️ **注意**：这会删除整个ovs命名空间中的所有资源。

## 本地开发

对于本地开发，可以禁用etcd依赖：

```bash
# 设置环境变量禁用etcd
export ETCD_ENABLED=false

# 使用本地YAML配置启动
./taskd
```

## 监控和维护

### 资源监控

```bash
# 查看资源使用情况
kubectl top pod -n ovs

# 查看Pod事件
kubectl get events -n ovs --sort-by=.metadata.creationTimestamp
```

### 日志管理

```bash
# 实时查看日志
kubectl logs -n ovs -l app=taskd -f

# 查看历史日志
kubectl logs -n ovs -l app=taskd --previous
```

## 安全建议

1. **更新敏感配置**：生产环境必须更新所有API密钥和密码
2. **网络策略**：配置网络策略限制Pod间通信
3. **RBAC**：配置适当的权限控制
4. **镜像安全**：使用可信的镜像仓库
5. **定期更新**：定期更新镜像和配置

## 相关文档

- [etcd部署指南](../etcd/README.md)
- [完整部署流程](../DEPLOYMENT_WITH_ETCD.md)
- [k8s总体架构](../README.md)