# 数据库查询Agent规格说明

## 概述

数据库查询Agent是一个通用的MongoDB数据查询服务，专门用于查询bidding.bidding_result等招投标相关数据表。该Agent支持多条件组合查询、分页、排序、统计等功能，为招投标预处理流程提供数据源支持。

## Agent基本信息

### Agent Card
```json
{
  "name": "Database Query Agent",
  "description": "通用的MongoDB数据库查询Agent，支持多条件查询、分页、排序和统计功能",
  "url": "http://taskd-service:8601/agents/database-query",
  "provider": {
    "organization": "TaskD Platform",
    "url": "https://taskd.platform"
  },
  "version": "1.0.0",
  "documentationUrl": "https://docs.taskd.platform/agents/database-query",
  "capabilities": {
    "streaming": false,
    "pushNotifications": false,
    "stateTransitionHistory": true
  },
  "authentication": {
    "schemes": ["Bearer"]
  },
  "defaultInputModes": ["application/json"],
  "defaultOutputModes": ["application/json"],
  "skills": [
    {
      "id": "query_bidding_data",
      "name": "查询招投标数据",
      "description": "从bidding_result表中查询招投标数据，支持多条件筛选、分页和排序",
      "tags": ["database", "mongodb", "query", "bidding"],
      "examples": [
        "查询最近一周的招投标数据",
        "按爬虫名称和状态筛选数据",
        "获取待处理的招投标记录"
      ],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "collection": {
            "type": "string",
            "enum": ["bidding_result", "tender_result", "awarded_result"],
            "default": "bidding_result",
            "description": "查询的数据表名"
          },
          "query_conditions": {
            "type": "object",
            "properties": {
              "time_range": {
                "type": "object",
                "properties": {
                  "start_time": {"type": "string", "format": "date-time", "description": "开始时间"},
                  "end_time": {"type": "string", "format": "date-time", "description": "结束时间"},
                  "time_field": {"type": "string", "enum": ["crawl_time", "update_time"], "default": "update_time"}
                }
              },
              "crawler_names": {
                "type": "array",
                "items": {"type": "string"},
                "description": "爬虫名称列表"
              },
              "crawl_modes": {
                "type": "array",
                "items": {"type": "string", "enum": ["normal", "automation_robot", "drone", "is_continue"]},
                "description": "爬取模式列表"
              },
              "status": {
                "type": "array",
                "items": {"type": "string", "enum": ["pending", "processing", "completed", "failed"]},
                "description": "数据状态"
              },
              "has_raw_content": {
                "type": "boolean",
                "description": "是否包含原始内容"
              },
              "has_processed_content": {
                "type": "boolean", 
                "description": "是否已处理过内容"
              },
              "error_threshold": {
                "type": "number",
                "minimum": 0,
                "description": "错误次数阈值"
              },
              "custom_filters": {
                "type": "object",
                "description": "自定义MongoDB查询条件"
              }
            }
          },
          "pagination": {
            "type": "object",
            "properties": {
              "page": {"type": "integer", "minimum": 1, "default": 1},
              "limit": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100},
              "offset": {"type": "integer", "minimum": 0, "description": "偏移量，与page二选一"}
            }
          },
          "sorting": {
            "type": "object",
            "properties": {
              "field": {
                "type": "string",
                "enum": ["crawl_time", "update_time", "title", "crawler_name"],
                "default": "update_time"
              },
              "order": {
                "type": "string",
                "enum": ["asc", "desc"],
                "default": "desc"
              }
            }
          },
          "fields": {
            "type": "object",
            "properties": {
              "include": {
                "type": "array",
                "items": {"type": "string"},
                "description": "需要返回的字段列表"
              },
              "exclude": {
                "type": "array",
                "items": {"type": "string"},
                "description": "需要排除的字段列表"
              }
            }
          },
          "aggregation": {
            "type": "object",
            "properties": {
              "group_by": {
                "type": "array",
                "items": {"type": "string"},
                "description": "分组字段"
              },
              "count_by": {"type": "string", "description": "计数字段"},
              "statistics": {
                "type": "array",
                "items": {"type": "string", "enum": ["count", "distinct_count"]},
                "description": "统计类型"
              }
            }
          }
        }
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "query_result": {
            "type": "object",
            "properties": {
              "data": {
                "type": "array",
                "items": {"type": "object"},
                "description": "查询结果数据"
              },
              "pagination": {
                "type": "object",
                "properties": {
                  "current_page": {"type": "integer"},
                  "total_pages": {"type": "integer"},
                  "page_size": {"type": "integer"},
                  "total_count": {"type": "integer"},
                  "has_next": {"type": "boolean"},
                  "has_prev": {"type": "boolean"}
                }
              },
              "aggregation_result": {
                "type": "object",
                "description": "聚合统计结果"
              }
            }
          },
          "query_metadata": {
            "type": "object",
            "properties": {
              "executed_query": {"type": "object", "description": "实际执行的MongoDB查询"},
              "execution_time": {"type": "number", "description": "查询执行时间(毫秒)"},
              "result_count": {"type": "integer", "description": "返回记录数"},
              "total_scanned": {"type": "integer", "description": "扫描的文档数"},
              "index_used": {"type": "string", "description": "使用的索引"}
            }
          },
          "data_summary": {
            "type": "object",
            "properties": {
              "crawler_stats": {
                "type": "object",
                "description": "按爬虫统计"
              },
              "time_distribution": {
                "type": "object", 
                "description": "时间分布统计"
              },
              "status_distribution": {
                "type": "object",
                "description": "状态分布统计"
              }
            }
          }
        }
      }
    },
    {
      "id": "get_preprocessing_candidates",
      "name": "获取预处理候选数据",
      "description": "专门用于获取需要进行预处理的招投标数据",
      "tags": ["preprocessing", "candidates", "batch"],
      "examples": [
        "获取未处理的招投标数据",
        "按优先级获取待处理数据",
        "获取失败重试的数据"
      ],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "candidate_criteria": {
            "type": "object",
            "properties": {
              "processing_status": {
                "type": "string",
                "enum": ["unprocessed", "failed", "retry", "all"],
                "default": "unprocessed",
                "description": "处理状态筛选"
              },
              "priority": {
                "type": "string",
                "enum": ["high", "medium", "low", "all"],
                "default": "all",
                "description": "优先级筛选"
              },
              "content_completeness": {
                "type": "string",
                "enum": ["complete", "partial", "minimal", "all"],
                "default": "complete",
                "description": "内容完整度要求"
              },
              "max_error_count": {
                "type": "integer",
                "minimum": 0,
                "default": 3,
                "description": "最大错误次数"
              },
              "min_content_length": {
                "type": "integer",
                "minimum": 0,
                "default": 100,
                "description": "最小内容长度"
              }
            }
          },
          "batch_config": {
            "type": "object",
            "properties": {
              "batch_size": {"type": "integer", "minimum": 1, "maximum": 500, "default": 50},
              "randomize": {"type": "boolean", "default": false, "description": "是否随机排序"},
              "diversity_factor": {"type": "number", "minimum": 0, "maximum": 1, "description": "多样性因子"}
            }
          },
          "time_constraints": {
            "type": "object",
            "properties": {
              "min_age_hours": {"type": "integer", "minimum": 0, "description": "最小数据年龄(小时)"},
              "max_age_days": {"type": "integer", "minimum": 0, "description": "最大数据年龄(天)"}
            }
          }
        }
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "candidates": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "_id": {"type": "string"},
                "title": {"type": "string"},
                "raw_content": {"type": "string"},
                "crawler_name": {"type": "string"},
                "crawl_time": {"type": "string"},
                "priority_score": {"type": "number"},
                "content_quality": {"type": "object"}
              }
            }
          },
          "selection_metadata": {
            "type": "object",
            "properties": {
              "total_candidates": {"type": "integer"},
              "selected_count": {"type": "integer"},
              "selection_criteria": {"type": "object"},
              "quality_distribution": {"type": "object"}
            }
          }
        }
      }
    },
    {
      "id": "update_processing_status",
      "name": "更新处理状态",
      "description": "更新招投标数据的处理状态和元数据",
      "tags": ["update", "status", "metadata"],
      "inputModes": ["application/json"],
      "outputModes": ["application/json"],
      "inputSchema": {
        "type": "object",
        "properties": {
          "record_ids": {
            "type": "array",
            "items": {"type": "string"},
            "description": "需要更新的记录ID列表"
          },
          "updates": {
            "type": "object",
            "properties": {
              "processing_status": {"type": "string"},
              "error_count": {"type": "integer"},
              "last_processing_time": {"type": "string"},
              "processing_result": {"type": "object"},
              "custom_fields": {"type": "object"}
            }
          },
          "batch_operation": {
            "type": "boolean",
            "default": true,
            "description": "是否批量操作"
          }
        },
        "required": ["record_ids", "updates"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "update_result": {
            "type": "object",
            "properties": {
              "matched_count": {"type": "integer"},
              "modified_count": {"type": "integer"},
              "failed_updates": {"type": "array"}
            }
          }
        }
      }
    }
  ]
}
```

## 核心查询能力

### 1. 多条件组合查询
支持以下查询条件的任意组合：
- **时间范围**：按crawl_time或update_time筛选
- **爬虫名称**：支持单个或多个爬虫数据
- **爬取模式**：normal、automation_robot、drone、is_continue
- **数据状态**：pending、processing、completed、failed
- **内容完整性**：是否包含raw_content等
- **错误次数**：筛选错误次数在阈值内的数据
- **自定义条件**：支持原生MongoDB查询语法

### 2. 分页和排序
- **分页**：支持基于页码或偏移量的分页
- **排序**：支持按时间、标题、爬虫名称等字段排序
- **限制**：单次查询最多1000条记录

### 3. 字段投影
- **包含字段**：只返回指定字段
- **排除字段**：排除指定字段（如大文本字段）
- **智能优化**：自动排除不必要的大字段

### 4. 聚合统计
- **分组统计**：按爬虫、状态、时间等维度分组
- **计数统计**：总数、去重计数等
- **分布分析**：时间分布、状态分布等

## 专业化查询功能

### 1. 预处理候选数据获取
专门为招投标预处理设计的智能数据选择功能：

#### 候选条件
- **处理状态筛选**：未处理、失败、重试等
- **内容质量评估**：完整、部分、最少等级别
- **优先级排序**：高、中、低优先级
- **错误容忍度**：最大错误次数限制
- **内容长度要求**：最小内容长度阈值

#### 批量配置
- **批次大小**：1-500条记录
- **随机化**：避免数据偏差
- **多样性因子**：保证数据多样性

#### 时间约束
- **数据新鲜度**：最小/最大数据年龄
- **处理间隔**：避免重复处理

### 2. 处理状态更新
支持批量更新数据处理状态：
- **状态更新**：processing、completed、failed等
- **元数据更新**：处理时间、结果等
- **错误计数**：自动递增错误次数
- **批量操作**：高效的批量更新

## 查询优化策略

### 1. 索引优化
基于bidding-crawler的索引配置：
```javascript
// 复合索引
{ "crawler_name": 1, "unique_key": 1 }  // 唯一约束
{ "update_time": 1 }                     // 时间查询
{ "crawl_mode": 1, "status": 1 }        // 状态查询
{ "error_times": 1 }                     // 错误筛选
```

### 2. 查询性能监控
- **执行时间统计**：记录查询耗时
- **扫描文档数**：监控查询效率
- **索引使用情况**：确保索引被正确使用
- **慢查询检测**：自动报警慢查询

### 3. 内存优化
- **流式处理**：大结果集使用游标
- **字段投影**：减少网络传输
- **批量操作**：减少数据库连接开销

## 安全和权限

### 1. 查询安全
- **SQL注入防护**：参数化查询
- **查询复杂度限制**：防止过于复杂的查询
- **结果集大小限制**：防止内存溢出
- **访问频率限制**：防止恶意查询

### 2. 数据权限
- **表级权限**：控制可访问的表
- **字段级权限**：敏感字段访问控制
- **数据范围权限**：按公司、用户限制数据范围

## 监控和日志

### 1. 查询监控
- **查询频率统计**：按技能统计调用次数
- **性能指标监控**：响应时间、吞吐量等
- **错误率监控**：查询成功率统计
- **资源使用监控**：CPU、内存、网络使用

### 2. 审计日志
- **查询日志**：记录所有查询请求
- **数据访问日志**：敏感数据访问记录
- **操作日志**：数据更新操作记录
- **异常日志**：查询异常和错误记录

## 配置示例

### 环境变量配置
```bash
# MongoDB连接配置
MONGO_BIDDING_URI=********************************:port/database
MONGO_CONNECTION_TIMEOUT=5000
MONGO_QUERY_TIMEOUT=30000

# 查询限制配置
MAX_QUERY_RESULT_SIZE=1000
MAX_QUERY_EXECUTION_TIME=60000
ENABLE_QUERY_CACHE=true
QUERY_CACHE_TTL=300

# 安全配置
ENABLE_QUERY_AUDIT=true
ALLOWED_COLLECTIONS=bidding_result,tender_result,awarded_result
```

### Agent配置
```yaml
database_query:
  mongodb:
    connection_uri: ${MONGO_BIDDING_URI}
    connection_pool_size: 10
    timeout:
      connection: 5000
      query: 30000
  
  query_limits:
    max_result_size: 1000
    max_execution_time: 60000
    enable_cache: true
    cache_ttl: 300
  
  security:
    enable_audit: true
    allowed_collections:
      - "bidding_result"
      - "tender_result" 
      - "awarded_result"
    rate_limit:
      requests_per_minute: 100
      burst_size: 20
```

## 使用场景

### 1. 招投标预处理数据准备
```json
{
  "skill_id": "get_preprocessing_candidates",
  "input": {
    "candidate_criteria": {
      "processing_status": "unprocessed",
      "content_completeness": "complete",
      "max_error_count": 3
    },
    "batch_config": {
      "batch_size": 100,
      "randomize": true
    },
    "time_constraints": {
      "min_age_hours": 1,
      "max_age_days": 30
    }
  }
}
```

### 2. 数据质量监控
```json
{
  "skill_id": "query_bidding_data", 
  "input": {
    "query_conditions": {
      "time_range": {
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-01-31T23:59:59Z"
      },
      "has_raw_content": true
    },
    "aggregation": {
      "group_by": ["crawler_name", "status"],
      "statistics": ["count", "distinct_count"]
    }
  }
}
```

### 3. 失败数据重试
```json
{
  "skill_id": "query_bidding_data",
  "input": {
    "query_conditions": {
      "status": ["failed"],
      "error_threshold": 3
    },
    "sorting": {
      "field": "update_time",
      "order": "asc"
    },
    "pagination": {
      "limit": 50
    }
  }
}
```