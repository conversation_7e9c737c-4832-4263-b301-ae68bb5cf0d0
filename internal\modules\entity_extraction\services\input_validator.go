package services

import (
	"fmt"
	"strings"
	"unicode/utf8"

	"gitlab.com/specific-ai/taskd/internal/modules/entity_extraction/models"
)

// inputValidator 输入验证器实现
type inputValidator struct {
	schemaValidator JSONSchemaValidator
}

// NewInputValidator 创建输入验证器
func NewInputValidator(schemaValidator JSONSchemaValidator) InputValidator {
	return &inputValidator{
		schemaValidator: schemaValidator,
	}
}

// ValidateInput 验证输入参数
func (iv *inputValidator) ValidateInput(req *models.ExtractionRequest) *models.ValidationResult {
	result := &models.ValidationResult{
		Valid:  true,
		Errors: []models.ValidationError{},
	}

	// 验证文本内容
	if err := iv.ValidateText(req.InputText); err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, models.NewValidationError(
			"input_text", err.Error(), models.ErrInvalidInput,
		))
	}

	// 验证语言参数
	if err := iv.ValidateLanguage(req.Language); err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, models.NewValidationError(
			"language", err.Error(), models.ErrUnsupportedLanguage,
		))
	}

	// 验证Schema
	schemaValidation := iv.schemaValidator.ValidateSchema(req.TargetSchema)
	if !schemaValidation.Valid {
		result.Valid = false
		for _, err := range schemaValidation.Errors {
			result.Errors = append(result.Errors, models.NewValidationError(
				"target_schema."+err.Field, err.Message, models.ErrInvalidModelSchema,
			))
		}
	}

	// 验证配置参数
	if req.ExtractionConfig != nil {
		if err := iv.ValidateConfig(req.ExtractionConfig); err != nil {
			result.Valid = false
			result.Errors = append(result.Errors, models.NewValidationError(
				"extraction_config", err.Error(), models.ErrInvalidInput,
			))
		}
	}

	return result
}

// ValidateText 验证文本内容
func (iv *inputValidator) ValidateText(text string) error {
	// 检查是否为空
	if text == "" {
		return fmt.Errorf("输入文本不能为空")
	}

	// 去除空白字符后检查
	trimmed := strings.TrimSpace(text)
	if trimmed == "" {
		return fmt.Errorf("输入文本不能只包含空白字符")
	}

	// 检查长度限制 (50KB)
	const maxLength = 50000
	if len(text) > maxLength {
		return fmt.Errorf("输入文本过长，最大支持%d字符，当前%d字符", maxLength, len(text))
	}

	// 检查最小长度
	const minLength = 2
	if utf8.RuneCountInString(trimmed) < minLength {
		return fmt.Errorf("输入文本过短，至少需要%d个字符", minLength)
	}

	// 检查是否包含有效字符
	if !iv.containsValidCharacters(text) {
		return fmt.Errorf("输入文本包含过多无效字符")
	}

	// 检查是否为纯符号或数字
	if iv.isPureSymbolsOrNumbers(trimmed) {
		return fmt.Errorf("输入文本不能只包含符号或数字")
	}

	return nil
}

// containsValidCharacters 检查是否包含有效字符
func (iv *inputValidator) containsValidCharacters(text string) bool {
	validCharCount := 0
	totalChars := utf8.RuneCountInString(text)

	for _, r := range text {
		// 中文字符、英文字母、数字、常用标点符号都算有效字符
		if (r >= 0x4e00 && r <= 0x9fff) || // 中文字符
			(r >= 'a' && r <= 'z') || // 小写字母
			(r >= 'A' && r <= 'Z') || // 大写字母
			(r >= '0' && r <= '9') || // 数字
			r == ' ' || r == '.' || r == ',' || r == '!' || r == '?' || // 常用标点
			r == ':' || r == ';' || r == '-' || r == '_' || r == '(' || r == ')' ||
			r == '[' || r == ']' || r == '{' || r == '}' || r == '"' || r == '\'' ||
			r == '\n' || r == '\r' || r == '\t' {
			validCharCount++
		}
	}

	// 有效字符比例应该大于70%
	return float64(validCharCount)/float64(totalChars) > 0.7
}

// isPureSymbolsOrNumbers 检查是否为纯符号或数字
func (iv *inputValidator) isPureSymbolsOrNumbers(text string) bool {
	letterCount := 0
	totalChars := utf8.RuneCountInString(text)

	for _, r := range text {
		// 中文字符或英文字母
		if (r >= 0x4e00 && r <= 0x9fff) ||
			(r >= 'a' && r <= 'z') ||
			(r >= 'A' && r <= 'Z') {
			letterCount++
		}
	}

	// 如果字母字符少于20%，认为是纯符号或数字
	return float64(letterCount)/float64(totalChars) < 0.2
}

// ValidateLanguage 验证语言参数
func (iv *inputValidator) ValidateLanguage(language string) error {
	if language == "" {
		return nil // 空值使用默认语言
	}

	supportedLanguages := []string{"zh", "en", "mixed"}
	for _, supported := range supportedLanguages {
		if language == supported {
			return nil
		}
	}

	return fmt.Errorf("不支持的语言类型: %s，支持的语言: %s", language, strings.Join(supportedLanguages, ", "))
}

// ValidateConfig 验证配置参数
func (iv *inputValidator) ValidateConfig(config *models.ExtractionConfig) error {
	// 验证置信度阈值
	if config.ConfidenceThreshold < 0 || config.ConfidenceThreshold > 1 {
		return fmt.Errorf("置信度阈值必须在0-1之间，当前值: %f", config.ConfidenceThreshold)
	}

	// 验证最大重试次数
	if config.MaxRetries < 0 || config.MaxRetries > 10 {
		return fmt.Errorf("最大重试次数必须在0-10之间，当前值: %d", config.MaxRetries)
	}

	// 验证超时时间
	if config.Timeout < 5 || config.Timeout > 300 {
		return fmt.Errorf("超时时间必须在5-300秒之间，当前值: %d", config.Timeout)
	}

	return nil
}

// ValidateBusinessType 验证业务类型（扩展方法）
func (iv *inputValidator) ValidateBusinessType(businessType string) error {
	if businessType == "" {
		return fmt.Errorf("业务类型不能为空")
	}

	// 这里可以根据需要添加业务类型验证逻辑
	// 由于我们使用JSON Schema驱动，不限制特定的业务类型
	return nil
}

// ValidateModelName 验证模型名称（扩展方法）
func (iv *inputValidator) ValidateModelName(modelName string) error {
	if modelName == "" {
		return fmt.Errorf("模型名称不能为空")
	}

	// 检查模型名称格式
	if len(modelName) > 100 {
		return fmt.Errorf("模型名称过长，最大支持100字符")
	}

	// 检查是否包含非法字符
	invalidChars := []string{"<", ">", "\"", "'", "&", "\\", "/"}
	for _, char := range invalidChars {
		if strings.Contains(modelName, char) {
			return fmt.Errorf("模型名称包含非法字符: %s", char)
		}
	}

	return nil
}

// ValidateTextLanguage 检测和验证文本语言（扩展方法）
func (iv *inputValidator) ValidateTextLanguage(text, expectedLanguage string) error {
	if expectedLanguage == "mixed" {
		return nil // 混合语言不需要验证
	}

	detectedLanguage := iv.detectLanguage(text)
	
	if expectedLanguage != "" && detectedLanguage != expectedLanguage && detectedLanguage != "mixed" {
		return fmt.Errorf("文本语言(%s)与期望语言(%s)不匹配", detectedLanguage, expectedLanguage)
	}

	return nil
}

// detectLanguage 简单的语言检测
func (iv *inputValidator) detectLanguage(text string) string {
	chineseCount := 0
	englishCount := 0
	totalChars := 0

	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			chineseCount++
			totalChars++
		} else if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			englishCount++
			totalChars++
		}
	}

	if totalChars == 0 {
		return "unknown"
	}

	chineseRatio := float64(chineseCount) / float64(totalChars)
	englishRatio := float64(englishCount) / float64(totalChars)

	// 如果中英文都占一定比例，认为是混合语言
	if chineseRatio > 0.2 && englishRatio > 0.2 {
		return "mixed"
	}

	// 否则按主要语言判断
	if chineseRatio > englishRatio {
		return "zh"
	} else if englishRatio > chineseRatio {
		return "en"
	}

	return "mixed"
}

// ValidateInputLength 验证输入长度是否适合指定的模型复杂度
func (iv *inputValidator) ValidateInputLength(text string, schemaComplexity int) error {
	textLength := utf8.RuneCountInString(text)
	
	// 根据Schema复杂度调整长度要求
	var maxRecommendedLength int
	switch {
	case schemaComplexity <= 5: // 简单模型
		maxRecommendedLength = 10000
	case schemaComplexity <= 15: // 中等复杂度模型
		maxRecommendedLength = 25000
	default: // 复杂模型
		maxRecommendedLength = 50000
	}

	if textLength > maxRecommendedLength {
		return fmt.Errorf("输入文本长度(%d)对于当前模型复杂度可能过长，建议不超过%d字符", 
			textLength, maxRecommendedLength)
	}

	return nil
}

// calculateSchemaComplexity 计算Schema复杂度
func (iv *inputValidator) calculateSchemaComplexity(schema models.JSONSchema) int {
	complexity := len(schema.Properties)
	
	for _, prop := range schema.Properties {
		complexity += iv.calculatePropertyComplexity(prop)
	}
	
	return complexity
}

// calculatePropertyComplexity 计算属性复杂度
func (iv *inputValidator) calculatePropertyComplexity(prop models.Property) int {
	complexity := 1
	
	if prop.Type == "array" && prop.Items != nil {
		complexity += iv.calculatePropertyComplexity(*prop.Items)
	}
	
	if prop.Type == "object" && len(prop.Properties) > 0 {
		for _, subProp := range prop.Properties {
			complexity += iv.calculatePropertyComplexity(subProp)
		}
	}
	
	return complexity
}