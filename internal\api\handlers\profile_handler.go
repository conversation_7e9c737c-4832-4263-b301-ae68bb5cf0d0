package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.com/specific-ai/taskd/internal/models"
	"gitlab.com/specific-ai/taskd/internal/models/business"
	"gitlab.com/specific-ai/taskd/internal/modules/profile/services"
	"gitlab.com/specific-ai/taskd/internal/utils"
)

// ProfileHandler 企业画像 API 处理器
type ProfileHandler struct {
	profileService services.ProfileServiceInterface
}

// NewProfileHandler 创建 ProfileHandler 实例
func NewProfileHandler(profileService services.ProfileServiceInterface) *ProfileHandler {
	return &ProfileHandler{
		profileService: profileService,
	}
}

// GenerateCompanyProfile 生成公司画像
// @Summary 生成企业画像
// @Description 基于企业基础信息生成五维度企业画像分析报告
// @Tags Profile
// @Accept json
// @Produce json
// @Param profileRequest body business.CompanyProfileRequest true "企业画像生成请求"
// @Success 200 {object} business.CompanyProfileResponse "生成成功"
// @Failure 400 {object} models.StandardErrorResponse "请求参数错误"
// @Failure 500 {object} models.StandardErrorResponse "服务器内部错误"
// @Router /v1/profiles/company/generate [post]
func (h *ProfileHandler) GenerateCompanyProfile(c *gin.Context) {
	var req business.CompanyProfileRequest

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Log.Warnf("绑定企业画像请求 JSON 失败: %v", err)
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{
			Error: "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 验证必要字段
	if req.UserContext.UserID == "" {
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{
			Error: "用户ID不能为空",
		})
		return
	}

	if req.CompanyName == "" {
		c.JSON(http.StatusBadRequest, models.StandardErrorResponse{
			Error: "企业名称不能为空",
		})
		return
	}

	utils.Log.Infof("收到企业画像生成请求 - 企业: %s, 用户: %s",
		req.CompanyName, req.UserContext.UserID)

	// 调用服务生成画像
	resp, err := h.profileService.GenerateCompanyProfile(&req)
	if err != nil {
		utils.Log.Errorf("生成企业画像失败: %v", err)
		c.JSON(http.StatusInternalServerError, models.StandardErrorResponse{
			Error: "生成企业画像失败: " + err.Error(),
		})
		return
	}

	utils.Log.Infof("成功生成企业画像 - 企业: %s, 置信度: %d",
		resp.CompanyName, resp.ConfidenceScore)

	// 返回成功响应
	c.JSON(http.StatusOK, resp)
}

// GetCompanyProfileInfo 获取企业画像服务信息 (可选的辅助接口)
// @Summary 获取画像服务信息
// @Description 获取企业画像服务的基本信息和支持的功能
// @Tags Profile
// @Produce json
// @Success 200 {object} map[string]interface{} "服务信息"
// @Router /v1/profiles/info [get]
func (h *ProfileHandler) GetCompanyProfileInfo(c *gin.Context) {
	info := map[string]interface{}{
		"service":     "企业画像生成服务",
		"version":     "v1.0.0",
		"description": "基于企业基础信息生成五维度企业画像分析报告",
		"capabilities": []string{
			"业务能力画像分析",
			"招投标适配性分析",
			"竞争力分析",
			"海外业务能力分析",
			"风险承受能力分析",
		},
		"supported_industries": []string{
			"制造业", "建筑业", "信息技术", "金融服务", "贸易",
			"能源", "交通运输", "房地产", "教育", "医疗健康",
			"其他",
		},
		"input_fields": map[string]string{
			"user_id":          "必填 - 用户ID",
			"company_name":     "必填 - 企业名称",
			"industry":         "必填 - 所属行业",
			"company_scale":    "可选 - 企业规模",
			"business_scope":   "可选 - 业务范围",
			"location":         "可选 - 企业位置",
			"established_year": "可选 - 成立年份",
			"description":      "可选 - 企业描述",
			"website":          "可选 - 官网地址",
			"additional_info":  "可选 - 补充信息",
		},
	}

	c.JSON(http.StatusOK, info)
}
