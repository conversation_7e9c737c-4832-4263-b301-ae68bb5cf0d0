# TaskD 手动 Docker 测试命令

直接使用 Docker 命令进行手动测试，无需脚本。

## 📋 测试步骤

### 1. 启动持久化容器

```bash
docker run -d \
    --name taskd-test-persistent \
    --network host \
    -e TASKD_BASE_URL=http://**************:8601 \
    -e TASKD_TIMEOUT=120 \
    -e TEST_VERBOSE=true \
    -e TEST_DATA_PREFIX=ubuntu_test_ \
    -e PYTHONPATH=/app \
    -v $(pwd)/test/logs:/app/test/logs \
    -v $(pwd)/test/reports:/app/test/reports \
    taskd-test:latest \
    tail -f /dev/null
```

### 2. 进入容器执行测试

```bash
# 进入容器
docker exec -it taskd-test-persistent /bin/bash
```

**在容器内执行以下命令：**

```bash
# 检查网络连通性
curl -v http://**************:8601/healthz

# 检查Python环境
python3 --version
ls -la /app/

# 运行P0测试（最关键）
cd /app && python3 -m pytest test/ -m p0 -v

# 运行P1测试（核心功能）
cd /app && python3 -m pytest test/ -m p1 -v

# 运行招投标测试
cd /app && python3 -m pytest test/test_bidding_agents.py -v

# 运行实体提取测试
cd /app && python3 -m pytest test/test_entity_extraction_agent.py -v

# 运行所有测试
cd /app && python3 -m pytest test/ -v

# 使用入口脚本（如果存在）
./entrypoint.sh test p0
```

### 3. 一次性测试命令（不保持容器）

```bash
# P0测试
docker run --rm \
    --network host \
    -e TASKD_BASE_URL=http://**************:8601 \
    -e TASKD_TIMEOUT=120 \
    -e TEST_VERBOSE=true \
    taskd-test:latest \
    bash -c "cd /app && python3 -m pytest test/ -m p0 -v"

# 招投标测试
docker run --rm \
    --network host \
    -e TASKD_BASE_URL=http://**************:8601 \
    taskd-test:latest \
    bash -c "cd /app && python3 -m pytest test/test_bidding_agents.py -v"

# 网络连通性测试
docker run --rm \
    --network host \
    -e TASKD_BASE_URL=http://**************:8601 \
    taskd-test:latest \
    bash -c "curl -v http://**************:8601/healthz"
```

### 4. 容器管理命令

```bash
# 查看容器日志
docker logs taskd-test-persistent

# 查看容器状态
docker ps -a | grep taskd-test

# 停止容器
docker stop taskd-test-persistent

# 删除容器
docker rm taskd-test-persistent

# 重启容器
docker restart taskd-test-persistent
```

### 5. 镜像管理命令

```bash
# 查看镜像
docker images | grep taskd-test

# 重新构建镜像（优化版本）
docker build -t taskd-test:latest -f docker/test/Dockerfile.optimized .

# 重新构建镜像（超精简版本）
docker build -t taskd-test:latest -f docker/test/Dockerfile.ultra-slim .

# 删除镜像
docker rmi taskd-test:latest

# 清理无用镜像
docker system prune -f
```

## 🔧 测试顺序建议

1. **启动持久化容器** → **进入容器** → **检查网络连通性**
2. **运行P0测试** → 如果通过 → **运行P1测试**
3. **运行特定模块测试** → **查看测试日志**

## 📊 预期结果

- **网络连通性**: curl 命令应返回 200 状态码
- **P0测试**: 必须 100% 通过（连通性测试）
- **P1测试**: ≥95% 通过（核心功能）
- **模块测试**: 根据具体模块有不同通过率要求

## 🚨 故障排除

### 容器启动失败
```bash
# 检查镜像是否存在
docker images | grep taskd-test

# 检查端口占用
netstat -tlnp | grep 8601
```

### 网络连接失败
```bash
# 在宿主机测试连通性
ping **************
curl -v http://**************:8601/healthz

# 检查防火墙
# Windows: 确保8601端口入站规则允许
```

### 测试执行失败
```bash
# 查看详细日志
docker logs taskd-test-persistent

# 检查挂载的日志文件
ls -la test/logs/
cat test/logs/test_run_*.log
```

---

**推荐使用持久化容器方式进行测试，可以避免重复启动的开销。**