# TaskD K8s 部署测试步骤

## 测试前准备

### 1. 构建Docker镜像
```bash
# 在taskd目录下
cd taskd
docker build -f deploy/Dockerfile -t your-registry/taskd:latest .
docker push your-registry/taskd:latest
```

### 2. 更新部署配置
编辑 `k8s/deployment.yaml`，更新镜像地址：
```yaml
image: your-registry/taskd:latest  # 替换为实际镜像地址
```

### 3. 更新敏感配置
编辑 `k8s/secret.yaml`，更新以下敏感信息：
- MongoDB连接URI
- PostgreSQL凭据
- Pulsar服务地址
- LLM API密钥
- LLM模型ID

## 部署测试

### 步骤1: 部署应用
```bash
# 确保在taskd目录下
cd taskd

# 一键部署
kubectl apply -f k8s/

# 或分步部署
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
```

### 步骤2: 验证部署状态
```bash
# 检查所有资源
kubectl get all -n ovs | grep taskd

# 检查Pod详细状态
kubectl get pods -n ovs -l app=taskd -o wide

# 检查配置是否正确加载
kubectl exec -n ovs deployment/taskd -- env | grep -E "(SERVER|MONGODB|LLM)"
```

### 步骤3: 网络连通性测试
```bash
# 获取K3s节点IP
kubectl get nodes -o wide

# 测试外部访问（替换为实际节点IP）
NODE_IP="<YOUR_K3S_NODE_IP>"
curl -v http://$NODE_IP:30601/healthz

# 预期响应: {"status": "healthy"}
```

### 步骤4: 依赖服务连接测试
```bash
# 测试MongoDB连接
kubectl exec -n ovs deployment/taskd -- \
  sh -c 'mongosh $MONGODB_URI --eval "db.adminCommand(\"ping\")"'

# 测试PostgreSQL连接
kubectl exec -n ovs deployment/taskd -- \
  sh -c 'psql -h $POSTGRESQL_HOST -U $POSTGRESQL_USER -d $POSTGRESQL_DATABASE -c "SELECT 1;"'

# 检查Pulsar连接日志
kubectl logs -n ovs deployment/taskd | grep -i "pulsar\|consumer"
```

### 步骤5: API功能测试
```bash
# 测试健康检查
curl http://$NODE_IP:30601/healthz

# 测试LLM报告摘要服务
curl -X POST http://$NODE_IP:30601/v1/llms/summary_report \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是一个测试报告内容，用于验证LLM摘要功能。",
    "target_company": "测试公司",
    "language": "zh"
  }'

# 测试LLM情感分析服务
curl -X POST http://$NODE_IP:30601/v1/llms/sentiment_analysis \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是一个正面的评价内容",
    "language": "zh"
  }'

# 查看Swagger文档（如果是开发环境）
curl http://$NODE_IP:30601/swagger/index.html
```

### 步骤6: 令牌管理测试
```bash
# 测试令牌使用情况查询
curl -X GET "http://$NODE_IP:30601/v1/tokens/usage?provider=volcengine_ark&date_range=today"

# 测试令牌统计
curl -X GET "http://$NODE_IP:30601/v1/tokens/stats"
```

## 日志和监控测试

### 查看应用日志
```bash
# 实时查看所有Pod日志
kubectl logs -n ovs deployment/taskd -f

# 查看特定Pod日志
POD_NAME=$(kubectl get pods -n ovs -l app=taskd -o jsonpath='{.items[0].metadata.name}')
kubectl logs -n ovs $POD_NAME

# 查看上一次重启的日志（如果Pod重启了）
kubectl logs -n ovs $POD_NAME --previous

# 过滤特定类型的日志
kubectl logs -n ovs deployment/taskd | grep -E "(ERROR|FATAL|LLM|MongoDB|PostgreSQL)"
```

### 性能和资源测试
```bash
# 查看资源使用情况
kubectl top pods -n ovs | grep taskd
kubectl describe pod -n ovs -l app=taskd

# 测试Pod扩容
kubectl scale deployment taskd -n ovs --replicas=3
kubectl get pods -n ovs -l app=taskd -w

# 恢复原有副本数
kubectl scale deployment taskd -n ovs --replicas=2
```

## 故障测试场景

### 1. 数据库连接故障测试
```bash
# 模拟MongoDB连接失败
kubectl patch secret taskd-secret -n ovs -p '{"data":{"MONGODB_URI":"aW52YWxpZC1tb25nb2RiLXVyaQ=="}}'
kubectl rollout restart deployment/taskd -n ovs
kubectl get pods -n ovs -l app=taskd -w

# 查看错误日志
kubectl logs -n ovs deployment/taskd | grep -i "mongodb\|connection\|error"

# 恢复正确配置
kubectl apply -f k8s/secret.yaml
kubectl rollout restart deployment/taskd -n ovs
```

### 2. LLM服务故障测试
```bash
# 模拟API密钥失效
kubectl patch secret taskd-secret -n ovs -p '{"data":{"LLM_PROVIDERS_VOLCENGINE_ARK_API_KEY":"aW52YWxpZC1hcGkta2V5"}}'
kubectl rollout restart deployment/taskd -n ovs

# 测试LLM调用
curl -X POST http://$NODE_IP:30601/v1/llms/summary_report \
  -H "Content-Type: application/json" \
  -d '{"text":"测试","target_company":"测试","language":"zh"}'

# 查看LLM错误日志
kubectl logs -n ovs deployment/taskd | grep -i "llm\|volcengine\|unauthorized"
```

### 3. 资源限制测试
```bash
# 查看当前资源使用
kubectl top pods -n ovs | grep taskd

# 模拟内存压力（通过多次API调用）
for i in {1..100}; do
  curl -X POST http://$NODE_IP:30601/v1/llms/summary_report \
    -H "Content-Type: application/json" \
    -d "{\"text\":\"长文本测试 $i\",\"target_company\":\"测试\",\"language\":\"zh\"}" &
done
wait

# 监控资源使用和Pod状态
kubectl top pods -n ovs | grep taskd
kubectl get pods -n ovs -l app=taskd
```

## 压力测试

### 基本压力测试
```bash
# 健康检查压力测试
ab -n 1000 -c 10 http://$NODE_IP:30601/healthz

# LLM服务压力测试（注意API配额限制）
ab -n 50 -c 5 -p llm_test_data.json -T application/json \
  http://$NODE_IP:30601/v1/llms/summary_report
```

### 创建测试数据文件
```bash
cat > llm_test_data.json << EOF
{
  "text": "这是一个用于压力测试的示例文本内容。内容需要足够长以测试LLM处理能力。",
  "target_company": "压力测试公司",
  "language": "zh"
}
EOF
```

### 监控压力测试期间的状态
```bash
# 在另一个终端中监控
watch -n 2 "kubectl top pods -n ovs | grep taskd && kubectl get pods -n ovs -l app=taskd"

# 监控令牌使用情况
watch -n 5 "curl -s http://$NODE_IP:30601/v1/tokens/stats"
```

## 集成测试

### 与specific-ai-bd的集成测试
```bash
# 确保specific-ai-bd也已部署
kubectl get pods -n ovs -l app=specific-ai-bd

# 从specific-ai-bd Pod测试taskd连接
kubectl exec -n ovs deployment/specific-ai-bd -- \
  curl -f http://taskd-service.ovs.svc.cluster.local:8601/healthz

# 测试完整的集成流程（需要根据实际业务逻辑调整）
kubectl logs -n ovs deployment/specific-ai-bd | grep -i taskd
```

## 清理测试

### 完整清理
```bash
# 删除所有taskd资源
kubectl delete -f k8s/ --ignore-not-found=true

# 确认清理完成
kubectl get all -n ovs | grep taskd
```

### 保留配置的清理
```bash
# 只删除deployment和service，保留配置
kubectl delete -f k8s/deployment.yaml
kubectl delete -f k8s/service.yaml
```

## 测试检查清单

- [ ] Pod启动成功且状态为Running
- [ ] 健康检查端点(/healthz)可访问
- [ ] 外部通过NodePort(30601)可访问服务
- [ ] 环境变量正确加载
- [ ] MongoDB连接正常
- [ ] PostgreSQL连接正常
- [ ] Pulsar连接正常
- [ ] LLM API调用正常
- [ ] 令牌跟踪功能正常
- [ ] API响应格式正确
- [ ] 日志输出简洁明了
- [ ] 资源使用在限制范围内
- [ ] Pod扩容功能正常
- [ ] 服务发现正常工作
- [ ] 与specific-ai-bd集成正常

## 常见测试问题

### 镜像拉取失败
```bash
# 检查镜像拉取策略和镜像地址
kubectl describe pod -n ovs -l app=taskd | grep -A 10 "Events:"
```

### LLM API调用失败
```bash
# 检查API密钥和网络连接
kubectl exec -n ovs deployment/taskd -- \
  curl -v "https://ark.cn-beijing.volces.com/api/v3/chat/completions" \
  -H "Authorization: Bearer $LLM_API_KEY"
```

### 数据库连接超时
```bash
# 检查网络策略和防火墙规则
kubectl exec -n ovs deployment/taskd -- nslookup db-dev.specific-ai.com
kubectl exec -n ovs deployment/taskd -- telnet db-dev.specific-ai.com 27017
```

### 配置加载问题
```bash
# 检查ConfigMap和Secret内容
kubectl get configmap taskd-config -n ovs -o yaml
kubectl get secret taskd-secret -n ovs -o yaml
```

## 测试完成标准

测试通过的标准：
1. 所有Pod正常运行
2. 健康检查通过
3. 外部访问正常
4. 所有依赖服务连接正常
5. LLM API调用成功
6. 令牌跟踪功能正常
7. 日志无严重错误
8. 资源使用合理
9. 基本功能可用
10. 与other服务集成正常

测试失败时，请参考故障排查指南或联系开发团队。

## 自动化测试脚本

```bash
#!/bin/bash
# taskd-k8s-test.sh - TaskD K8s部署自动化测试脚本

set -e

NAMESPACE="ovs"
APP_NAME="taskd"
NODE_IP="<YOUR_K3S_NODE_IP>"  # 请替换为实际节点IP

echo "开始TaskD K8s部署测试..."

# 基础检查
echo "1. 检查Pod状态..."
kubectl get pods -n $NAMESPACE -l app=$APP_NAME

echo "2. 检查服务状态..."
kubectl get svc -n $NAMESPACE | grep $APP_NAME

echo "3. 测试健康检查..."
if curl -f http://$NODE_IP:30601/healthz; then
  echo "✓ 健康检查通过"
else
  echo "✗ 健康检查失败"
  exit 1
fi

echo "4. 测试LLM服务..."
if curl -X POST http://$NODE_IP:30601/v1/llms/summary_report \
  -H "Content-Type: application/json" \
  -d '{"text":"自动化测试","target_company":"测试","language":"zh"}'; then
  echo "✓ LLM服务测试通过"
else
  echo "✓ LLM服务测试完成（可能需要有效API密钥）"
fi

echo "TaskD K8s部署测试完成！"
``` 